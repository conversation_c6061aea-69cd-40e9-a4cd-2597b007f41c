2025-07-16 21:00:02,132  [INFO] [W][11:28:04][GNSS][RTK]enough, report now.
 
2025-07-16 21:00:02,190  [INFO] [D][11:28:04][COMM]Main Task receive event:13
 
2025-07-16 21:00:02,192  [INFO] [D][11:28:04][GNSS]stop event:1
 
2025-07-16 21:00:02,195  [INFO] [D][11:28:04][GNSS]GPS stop. ret=0
 
2025-07-16 21:00:02,198  [INFO] [D][11:28:04][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:00:02,207  [INFO] [D][11:28:04][COMM]frm_mc_bat_power_on EXT_BAT_STATE_POWERON_TIMEOUT, bat is on:0
 
2025-07-16 21:00:02,213  [INFO] [D][11:28:04][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON, Ext48v = 2.
 
2025-07-16 21:00:02,218  [INFO] [D][11:28:04][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:00:02,218  [INFO] 
 
2025-07-16 21:00:02,220  [INFO] [D][11:28:04][COMM]open bat mos
 
2025-07-16 21:00:02,223  [INFO] [D][11:28:04][COMM]1x1 tx_id:3,7, tx_len:2
 
2025-07-16 21:00:02,227  [INFO] [D][11:28:04][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:00:02,256  [INFO] [D][11:28:04][CAT1]<<< 
 
2025-07-16 21:00:02,256  [INFO] OK
 
2025-07-16 21:00:02,266  [INFO] 
 
2025-07-16 21:00:02,269  [INFO] [D][11:28:04][CAT1]tx ret[13] >>> AT+GPSRTK=0
 
2025-07-16 21:00:02,270  [INFO] 
 
2025-07-16 21:00:02,289  [INFO] [D][11:28:04][CAT1]<<< 
 
2025-07-16 21:00:02,289  [INFO] OK
 
2025-07-16 21:00:02,299  [INFO] 
 
2025-07-16 21:00:02,301  [INFO] [D][11:28:04][CAT1]tx ret[12] >>> AT+GPSDR=0
 
2025-07-16 21:00:02,302  [INFO] 
 
2025-07-16 21:00:02,320  [INFO] [D][11:28:04][CAT1]<<< 
 
2025-07-16 21:00:02,321  [INFO] OK
 
2025-07-16 21:00:02,322  [INFO] 
 
2025-07-16 21:00:02,326  [INFO] [D][11:28:04][CAT1]exec over: func id: 24, ret: 6
 
2025-07-16 21:00:02,328  [INFO] [D][11:28:04][CAT1]sub id: 24, ret: 6
 
2025-07-16 21:00:02,329  [INFO] 
 
2025-07-16 21:00:02,596  [INFO] [E][11:28:04][COMM]1x1 rx timeout
 
2025-07-16 21:00:02,598  [INFO] [D][11:28:04][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:00:02,999  [INFO] [E][11:28:05][COMM]1x1 rx timeout
 
2025-07-16 21:00:03,002  [INFO] [E][11:28:05][COMM]1x1 tp timeout
 
2025-07-16 21:00:03,005  [INFO] [E][11:28:05][COMM]1x1 error -3.
 
2025-07-16 21:00:03,008  [INFO] [E][11:28:05][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:00:03,014  [INFO] [D][11:28:05][COMM]frm_peripheral_device_poweron type 12.... 
 
2025-07-16 21:00:03,016  [INFO] [D][11:28:05][COMM]get Acckey 1 and value:1
 
2025-07-16 21:00:03,022  [INFO] [D][11:28:05][COMM]First location,do verification
 
2025-07-16 21:00:03,028  [INFO] [D][11:28:05][M2M ]m2m_task:m_m2m_thread_setting_queue:0
 
2025-07-16 21:00:03,030  [INFO] [D][11:28:05][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:03,036  [INFO] [D][11:28:05][COMM][frmJournal_read][210] read fail
 
2025-07-16 21:00:03,038  [INFO] [D][11:28:05][COMM]get hw mark: ffffffff
 
2025-07-16 21:00:03,041  [INFO] [D][11:28:05][COMM]bat type 255
 
2025-07-16 21:00:03,049  [INFO] [W][11:28:05][PROT]remove success[1730201285],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:00:03,058  [INFO] [D][11:28:05][HSDK][0] flush to flash addr:[0xE45D00] --- write len --- [256]
 
2025-07-16 21:00:03,066  [INFO] [W][11:28:05][PROT]add success [1730201285],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 21:00:03,072  [INFO] [D][11:28:05][COMM]photo upload succeed!!! send ack
 
2025-07-16 21:00:03,074  [INFO] [D][11:28:05][COMM]photo upload taskid:[00000000 00000000]
 
2025-07-16 21:00:03,086  [INFO] [W][11:28:05][PROT]remove success[1730201285],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:00:03,094  [INFO] [W][11:28:05][PROT]add success [1730201285],send_path[2],type[6A01],priority[0],index[1],used[1]
 
2025-07-16 21:00:03,097  [INFO] [D][11:28:05][GNSS]nTotalNumSatsInView = 18
 
2025-07-16 21:00:03,103  [INFO] [D][11:28:05][GNSS]nPRN = 21, nSignalQuality = 22, Elev = 35, Azimuth = 009A
 
2025-07-16 21:00:03,107  [INFO] [D][11:28:05][PROT]index:0 1730201285
 
2025-07-16 21:00:03,110  [INFO] [D][11:28:05][PROT]is_send:0
 
2025-07-16 21:00:03,113  [INFO] [D][11:28:05][PROT]sequence_num:3
 
2025-07-16 21:00:03,116  [INFO] [D][11:28:05][PROT]retry_timeout:0
 
2025-07-16 21:00:03,118  [INFO] [D][11:28:05][PROT]retry_times:10
 
2025-07-16 21:00:03,122  [INFO] [D][11:28:05][PROT]send_path:0x2
 
2025-07-16 21:00:03,127  [INFO] [D][11:28:05][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:03,134  [INFO] [D][11:28:05][PROT]===========================================================
 
2025-07-16 21:00:03,139  [INFO] [W][11:28:05][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201285]
 
2025-07-16 21:00:03,147  [INFO] [D][11:28:05][PROT]===========================================================
 
2025-07-16 21:00:03,153  [INFO] [D][11:28:05][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:03,155  [INFO] [D][11:28:05][PROT]Send_TO_M2M [1730201285]
 
2025-07-16 21:00:03,160  [INFO] [D][11:28:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:03,163  [INFO] [D][11:28:05][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:03,170  [INFO] [D][11:28:05][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:03,172  [INFO] [D][11:28:05][M2M ]m2m send data len[198]
 
2025-07-16 21:00:03,175  [INFO] [D][11:28:05][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:03,184  [INFO] [D][11:28:05][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:03,192  [INFO] [D][11:28:05][GNSS]nPRN = 17, nSignalQuality = 21, Elev = 2D, Azimuth = 010C
 
2025-07-16 21:00:03,197  [INFO] [D][11:28:05][GNSS]nPRN = 0D, nSignalQuality = 1F, Elev = 33, Azimuth = 00DD
 
2025-07-16 21:00:03,206  [INFO] [D][11:28:05][GNSS]nPRN = 26, nSignalQuality = 1F, Elev = 25, Azimuth = 00BF
 
2025-07-16 21:00:03,212  [INFO] [D][11:28:05][GNSS]nPRN = 21, nSignalQuality = 17, Elev = 35, Azimuth = 009A
 
2025-07-16 21:00:03,220  [INFO] [D][11:28:05][GNSS]nPRN = 17, nSignalQuality = 15, Elev = 2D, Azimuth = 010C
 
2025-07-16 21:00:03,226  [INFO] [D][11:28:05][GNSS]nPRN = 26, nSignalQuality = 14, Elev = 25, Azimuth = 00BF
 
2025-07-16 21:00:03,231  [INFO] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,240  [INFO] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,244  [INFO] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,253  [INFO] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,260  [INFO] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,264  [INFO] [D][11:28:05][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:03,267  [INFO] [D][11:28:05][GNSS]nSatsAvgSNR 28, nSatsSNROver35 0
 
2025-07-16 21:00:03,273  [INFO] [D][11:28:05][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:03,273  [INFO] 
 
2025-07-16 21:00:03,278  [INFO] [D][11:28:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:03,281  [INFO] [D][11:28:05][GNSS]nSatsInUse 4
 
2025-07-16 21:00:03,284  [INFO] [D][11:28:05][GNSS]DOP: 9.670000 4.270000 8.680000
 
2025-07-16 21:00:03,295  [INFO] [W][11:28:05][COMM]5A07 LocFail:reason:0x07;diff:5517;LocUsedTime:58;LocStatus|Type:3|000;HDOP:04;SatsView:18;SatsSNR35:00
 
2025-07-16 21:00:03,304  [INFO] [W][11:28:05][COMM]5A07 LocFail:GpsSpeed:00;alt:0069;lon:114340339   lat:23012004
 
2025-07-16 21:00:03,311  [INFO] [W][11:28:05][PROT]remove success[1730201285],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:00:03,320  [INFO] [W][11:28:05][PROT]add success [1730201285],send_path[2],type[5A07],priority[0],index[2],used[1]
 
2025-07-16 21:00:03,325  [INFO] [D][11:28:05][COMM]f:frm_rs485_park_cam_start_get_ver_timer. no 48v
 
2025-07-16 21:00:03,331  [INFO] [D][11:28:05][COMM]frm_peripheral_device_poweroff type 0.... 
 
2025-07-16 21:00:03,336  [INFO] [D][11:28:05][COMM]Main Task receive event:13 finished processing
 
2025-07-16 21:00:03,339  [INFO] [D][11:28:05][GNSS]recv submsg id[1]
 
2025-07-16 21:00:03,345  [INFO] [D][11:28:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
 
2025-07-16 21:00:03,350  [INFO] [D][11:28:05][GNSS]location stop evt done evt
 
2025-07-16 21:00:03,352  [INFO] [D][11:28:05][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:03,374  [INFO] 0063B98C113311331133113311331B88B578FBF441C6D66DA30AF54A32DB79F21799AE6446B132481B9E3317450587FB8BDFEA7AE129540BEB958735BBB44DD0BF63848FC683075AC5D8110EB2F9A098582A97DDC971D4087770C6690CF3628D50D685
 
2025-07-16 21:00:03,375  [INFO] [D][11:28:05][CAT1]<<< 
 
2025-07-16 21:00:03,375  [INFO] SEND OK
 
2025-07-16 21:00:03,375  [INFO] 
 
2025-07-16 21:00:03,378  [INFO] [D][11:28:05][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:03,384  [INFO] [D][11:28:05][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:03,385  [INFO] 
 
2025-07-16 21:00:03,387  [INFO] [D][11:28:05][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:03,393  [INFO] [D][11:28:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:03,397  [INFO] [D][11:28:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:03,400  [INFO] [D][11:28:05][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:03,406  [INFO] [D][11:28:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:03,409  [INFO] [D][11:28:05][PROT]M2M Send ok [1730201285]
 
2025-07-16 21:00:08,409  [INFO] [D][11:28:10][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:08,422  [INFO] [D][11:28:10][PROT]index:0 1730201290
 
2025-07-16 21:00:08,425  [INFO] [D][11:28:10][PROT]is_send:0
 
2025-07-16 21:00:08,428  [INFO] [D][11:28:10][PROT]sequence_num:3
 
2025-07-16 21:00:08,430  [INFO] [D][11:28:10][PROT]retry_timeout:0
 
2025-07-16 21:00:08,433  [INFO] [D][11:28:10][PROT]retry_times:9
 
2025-07-16 21:00:08,435  [INFO] [D][11:28:10][PROT]send_path:0x2
 
2025-07-16 21:00:08,440  [INFO] [D][11:28:10][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:08,447  [INFO] [D][11:28:10][PROT]===========================================================
 
2025-07-16 21:00:08,452  [INFO] [D][11:28:10][HSDK]need to erase for write: is[0x0] ie[0x4E00]
 
2025-07-16 21:00:08,461  [INFO] [D][11:28:10][HSDK][0] flush to flash addr:[0xE45E00] --- write len --- [256]
 
2025-07-16 21:00:08,467  [INFO] [W][11:28:10][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201290]
 
2025-07-16 21:00:08,472  [INFO] [D][11:28:10][PROT]===========================================================
 
2025-07-16 21:00:08,478  [INFO] [D][11:28:10][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:08,480  [INFO] [D][11:28:10][PROT]Send_TO_M2M [1730201290]
 
2025-07-16 21:00:08,486  [INFO] [D][11:28:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:08,491  [INFO] [D][11:28:10][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:08,495  [INFO] [D][11:28:10][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:08,497  [INFO] [D][11:28:10][M2M ]m2m send data len[198]
 
2025-07-16 21:00:08,502  [INFO] [D][11:28:10][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:08,512  [INFO] [D][11:28:10][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:08,517  [INFO] [D][11:28:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:08,520  [INFO] [D][11:28:10][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:08,525  [INFO] [D][11:28:10][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:08,526  [INFO] 
 
2025-07-16 21:00:08,528  [INFO] [D][11:28:10][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:08,549  [INFO] 0063B981113311331133113311331B88B52D3CDCB091FE84927DB9F1E787FFABE036418D452C6FD7AB45386AC25C65DEA588EE782ACA7C54430EC5DF0E2D2F1A8562315B26247B14B897AF8F0791F830879D94A271E195A378AA86561D779299AEA98A
 
2025-07-16 21:00:08,550  [INFO] [D][11:28:10][CAT1]<<< 
 
2025-07-16 21:00:08,550  [INFO] SEND OK
 
2025-07-16 21:00:08,551  [INFO] 
 
2025-07-16 21:00:08,552  [INFO] [D][11:28:10][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:08,559  [INFO] [D][11:28:10][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:08,560  [INFO] 
 
2025-07-16 21:00:08,561  [INFO] [D][11:28:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:08,570  [INFO] [D][11:28:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:08,572  [INFO] [D][11:28:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:08,575  [INFO] [D][11:28:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:08,581  [INFO] [D][11:28:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:08,584  [INFO] [D][11:28:10][PROT]M2M Send ok [1730201290]
 
2025-07-16 21:00:10,716  [INFO] [D][11:28:12][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 2.
 
2025-07-16 21:00:13,825  [INFO] [D][11:28:16][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:13,837  [INFO] [D][11:28:16][PROT]index:0 1730201296
 
2025-07-16 21:00:13,840  [INFO] [D][11:28:16][PROT]is_send:0
 
2025-07-16 21:00:13,843  [INFO] [D][11:28:16][PROT]sequence_num:3
 
2025-07-16 21:00:13,847  [INFO] [D][11:28:16][PROT]retry_timeout:0
 
2025-07-16 21:00:13,848  [INFO] [D][11:28:16][PROT]retry_times:8
 
2025-07-16 21:00:13,851  [INFO] [D][11:28:16][PROT]send_path:0x2
 
2025-07-16 21:00:13,856  [INFO] [D][11:28:16][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:13,863  [INFO] [D][11:28:16][PROT]===========================================================
 
2025-07-16 21:00:13,869  [INFO] [W][11:28:16][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201296]
 
2025-07-16 21:00:13,877  [INFO] [D][11:28:16][PROT]===========================================================
 
2025-07-16 21:00:13,882  [INFO] [D][11:28:16][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:13,885  [INFO] [D][11:28:16][PROT]Send_TO_M2M [1730201296]
 
2025-07-16 21:00:13,890  [INFO] [D][11:28:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:13,893  [INFO] [D][11:28:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:13,898  [INFO] [D][11:28:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:13,902  [INFO] [D][11:28:16][M2M ]m2m send data len[198]
 
2025-07-16 21:00:13,905  [INFO] [D][11:28:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:13,913  [INFO] [D][11:28:16][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:13,918  [INFO] [D][11:28:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:13,921  [INFO] [D][11:28:16][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:13,922  [INFO] 
 
2025-07-16 21:00:13,927  [INFO] [D][11:28:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:13,931  [INFO] [D][11:28:16][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:13,952  [INFO] 0063B982113311331133113311331B88B52CF4F6F40211E61B938377D325EB14DE6F10B3BB272B8B29D2D66E6B24E490F216577D09743598BF8576D7807A7886E5AE2A7C44604D7BAF0EDC7BDDCE0D1B1BC0F6E7645BDEED8DB9430DD1C2754946E743
 
2025-07-16 21:00:13,952  [INFO] [D][11:28:16][CAT1]<<< 
 
2025-07-16 21:00:13,953  [INFO] SEND OK
 
2025-07-16 21:00:13,953  [INFO] 
 
2025-07-16 21:00:13,958  [INFO] [D][11:28:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:13,960  [INFO] [D][11:28:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:13,961  [INFO] 
 
2025-07-16 21:00:13,966  [INFO] [D][11:28:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:13,971  [INFO] [D][11:28:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:13,976  [INFO] [D][11:28:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:13,981  [INFO] [D][11:28:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:13,986  [INFO] [D][11:28:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:13,988  [INFO] [D][11:28:16][PROT]M2M Send ok [1730201296]
 
2025-07-16 21:00:14,784  [INFO] [D][11:28:16][COMM]msg 0226 loss. last_tick:0. cur_tick:100001. period:10000
 
2025-07-16 21:00:14,785  [INFO] 
 
2025-07-16 21:00:14,789  [INFO] [D][11:28:16][COMM]msg 0227 loss. last_tick:0. cur_tick:100002. period:10000
 
2025-07-16 21:00:14,790  [INFO] 
 
2025-07-16 21:00:14,797  [INFO] [D][11:28:16][COMM]msg 0228 loss. last_tick:0. cur_tick:100003. period:10000
 
2025-07-16 21:00:14,798  [INFO] 
 
2025-07-16 21:00:14,802  [INFO] [D][11:28:16][COMM]msg 0261 loss. last_tick:0. cur_tick:100003. period:10000
 
2025-07-16 21:00:14,803  [INFO] 
 
2025-07-16 21:00:14,811  [INFO] [D][11:28:16][COMM]msg 0262 loss. last_tick:0. cur_tick:100004. period:10000
 
2025-07-16 21:00:14,813  [INFO] 
 
2025-07-16 21:00:14,817  [INFO] [D][11:28:16][COMM]msg 0263 loss. last_tick:0. cur_tick:100004. period:10000
 
2025-07-16 21:00:14,817  [INFO] 
 
2025-07-16 21:00:14,826  [INFO] [D][11:28:16][COMM]msg 0281 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-16 21:00:14,827  [INFO] 
 
2025-07-16 21:00:14,831  [INFO] [D][11:28:16][COMM]msg 0282 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-16 21:00:14,831  [INFO] 
 
2025-07-16 21:00:14,839  [INFO] [D][11:28:16][COMM]msg 0283 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-16 21:00:14,840  [INFO] 
 
2025-07-16 21:00:14,844  [INFO] [D][11:28:16][COMM]msg 02A1 loss. last_tick:0. cur_tick:100006. period:10000
 
2025-07-16 21:00:14,845  [INFO] 
 
2025-07-16 21:00:14,852  [INFO] [D][11:28:16][COMM]msg 02A2 loss. last_tick:0. cur_tick:100006. period:10000
 
2025-07-16 21:00:14,853  [INFO] 
 
2025-07-16 21:00:14,858  [INFO] [D][11:28:16][COMM]msg 02A3 loss. last_tick:0. cur_tick:100007. period:10000
 
2025-07-16 21:00:14,858  [INFO] 
 
2025-07-16 21:00:14,866  [INFO] [D][11:28:16][COMM]msg 02C3 loss. last_tick:0. cur_tick:100007. period:10000
 
2025-07-16 21:00:14,867  [INFO] 
 
2025-07-16 21:00:14,872  [INFO] [D][11:28:16][COMM]msg 02C4 loss. last_tick:0. cur_tick:100008. period:10000
 
2025-07-16 21:00:14,874  [INFO] 
 
2025-07-16 21:00:14,881  [INFO] [D][11:28:16][COMM]msg 02C5 loss. last_tick:0. cur_tick:100008. period:10000
 
2025-07-16 21:00:14,882  [INFO] 
 
2025-07-16 21:00:14,886  [INFO] [D][11:28:16][COMM]msg 02E3 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-16 21:00:14,887  [INFO] 
 
2025-07-16 21:00:14,894  [INFO] [D][11:28:16][COMM]msg 02E4 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-16 21:00:14,895  [INFO] 
 
2025-07-16 21:00:14,899  [INFO] [D][11:28:16][COMM]msg 02E5 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-16 21:00:14,900  [INFO] 
 
2025-07-16 21:00:14,908  [INFO] [D][11:28:16][COMM]msg 0302 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-16 21:00:14,910  [INFO] 
 
2025-07-16 21:00:14,914  [INFO] [D][11:28:16][COMM]msg 0303 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-16 21:00:14,915  [INFO] 
 
2025-07-16 21:00:14,923  [INFO] [D][11:28:16][COMM]msg 0304 loss. last_tick:0. cur_tick:100011. period:10000
 
2025-07-16 21:00:14,924  [INFO] 
 
2025-07-16 21:00:14,928  [INFO] [D][11:28:16][COMM]msg 02E6 loss. last_tick:0. cur_tick:100011. period:10000
 
2025-07-16 21:00:14,928  [INFO] 
 
2025-07-16 21:00:14,936  [INFO] [D][11:28:16][COMM]msg 02E7 loss. last_tick:0. cur_tick:100012. period:10000
 
2025-07-16 21:00:14,938  [INFO] 
 
2025-07-16 21:00:14,941  [INFO] [D][11:28:16][COMM]msg 0305 loss. last_tick:0. cur_tick:100012. period:10000
 
2025-07-16 21:00:14,942  [INFO] 
 
2025-07-16 21:00:14,950  [INFO] [D][11:28:17][COMM]msg 0306 loss. last_tick:0. cur_tick:100013. period:10000
 
2025-07-16 21:00:14,951  [INFO] 
 
2025-07-16 21:00:14,955  [INFO] [D][11:28:17][COMM]msg 02A8 loss. last_tick:0. cur_tick:100013. period:10000
 
2025-07-16 21:00:14,956  [INFO] 
 
2025-07-16 21:00:14,964  [INFO] [D][11:28:17][COMM]msg 02A9 loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-16 21:00:14,965  [INFO] 
 
2025-07-16 21:00:14,970  [INFO] [D][11:28:17][COMM]msg 02AA loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-16 21:00:14,971  [INFO] 
 
2025-07-16 21:00:14,978  [INFO] [D][11:28:17][COMM]msg 02AB loss. last_tick:0. cur_tick:100015. period:10000
 
2025-07-16 21:00:14,979  [INFO] 
 
2025-07-16 21:00:14,983  [INFO] [D][11:28:17][COMM]msg 02AD loss. last_tick:0. cur_tick:100015. period:10000
 
2025-07-16 21:00:14,983  [INFO] 
 
2025-07-16 21:00:14,992  [INFO] [D][11:28:17][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100015. period:10000. j,i:0 53
 
2025-07-16 21:00:14,993  [INFO] 
 
2025-07-16 21:00:15,000  [INFO] [D][11:28:17][COMM]bat msg 02AE loss. last_tick:0. cur_tick:100016. period:10000. j,i:1 54
 
2025-07-16 21:00:15,001  [INFO] 
 
2025-07-16 21:00:15,009  [INFO] [D][11:28:17][COMM]bat msg 024A loss. last_tick:0. cur_tick:100016. period:10000. j,i:13 66
 
2025-07-16 21:00:15,010  [INFO] 
 
2025-07-16 21:00:15,017  [INFO] [D][11:28:17][COMM]bat msg 024B loss. last_tick:0. cur_tick:100017. period:10000. j,i:14 67
 
2025-07-16 21:00:15,019  [INFO] 
 
2025-07-16 21:00:15,026  [INFO] [D][11:28:17][COMM]bat msg 024C loss. last_tick:0. cur_tick:100018. period:10000. j,i:15 68
 
2025-07-16 21:00:15,026  [INFO] 
 
2025-07-16 21:00:15,034  [INFO] [D][11:28:17][COMM]bat msg 024D loss. last_tick:0. cur_tick:100018. period:10000. j,i:16 69
 
2025-07-16 21:00:15,034  [INFO] 
 
2025-07-16 21:00:15,042  [INFO] [D][11:28:17][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100018. period:10000. j,i:19 72
 
2025-07-16 21:00:15,043  [INFO] 
 
2025-07-16 21:00:15,051  [INFO] [D][11:28:17][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100019. period:10000. j,i:20 73
 
2025-07-16 21:00:15,051  [INFO] 
 
2025-07-16 21:00:15,059  [INFO] [D][11:28:17][COMM]bat msg 025A loss. last_tick:0. cur_tick:100019. period:10000. j,i:24 77
 
2025-07-16 21:00:15,060  [INFO] 
 
2025-07-16 21:00:15,067  [INFO] [D][11:28:17][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100020
 
2025-07-16 21:00:15,076  [INFO] [D][11:28:17][COMM]CAN message bat fault change: 0x06E61FFC->0x07FFFFFF 100020
 
2025-07-16 21:00:15,082  [INFO] [D][11:28:17][COMM]CAN fault change: 0x0000000300010F05->0x0000000300010F07 100021
 
2025-07-16 21:00:19,246  [INFO] [D][11:28:21][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:19,258  [INFO] [D][11:28:21][PROT]index:0 1730201301
 
2025-07-16 21:00:19,261  [INFO] [D][11:28:21][PROT]is_send:0
 
2025-07-16 21:00:19,264  [INFO] [D][11:28:21][PROT]sequence_num:3
 
2025-07-16 21:00:19,267  [INFO] [D][11:28:21][PROT]retry_timeout:0
 
2025-07-16 21:00:19,270  [INFO] [D][11:28:21][PROT]retry_times:7
 
2025-07-16 21:00:19,273  [INFO] [D][11:28:21][PROT]send_path:0x2
 
2025-07-16 21:00:19,278  [INFO] [D][11:28:21][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:19,284  [INFO] [D][11:28:21][PROT]===========================================================
 
2025-07-16 21:00:19,289  [INFO] [W][11:28:21][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201301]
 
2025-07-16 21:00:19,298  [INFO] [D][11:28:21][PROT]===========================================================
 
2025-07-16 21:00:19,304  [INFO] [D][11:28:21][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:19,307  [INFO] [D][11:28:21][PROT]Send_TO_M2M [1730201301]
 
2025-07-16 21:00:19,313  [INFO] [D][11:28:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:19,315  [INFO] [D][11:28:21][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:19,321  [INFO] [D][11:28:21][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:19,323  [INFO] [D][11:28:21][M2M ]m2m send data len[198]
 
2025-07-16 21:00:19,326  [INFO] [D][11:28:21][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:19,334  [INFO] [D][11:28:21][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:19,339  [INFO] [D][11:28:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:19,345  [INFO] [D][11:28:21][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:19,348  [INFO] [D][11:28:21][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:19,349  [INFO] 
 
2025-07-16 21:00:19,353  [INFO] [D][11:28:21][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:19,373  [INFO] 0063B98E113311331133113311331B88B56686E4A4809FDC4BC70487A7F8F373EC4FE57502BC9D70A885EB20F30B0EFC62E058BF0CE0BC11E79EF83468BB720D8A8D2596B1BC927D5A60973542A212701A8534D3442988B4301AF7C3B72702888A6647
 
2025-07-16 21:00:19,374  [INFO] [D][11:28:21][CAT1]<<< 
 
2025-07-16 21:00:19,375  [INFO] SEND OK
 
2025-07-16 21:00:19,375  [INFO] 
 
2025-07-16 21:00:19,380  [INFO] [D][11:28:21][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:19,382  [INFO] [D][11:28:21][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:19,383  [INFO] 
 
2025-07-16 21:00:19,388  [INFO] [D][11:28:21][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:19,393  [INFO] [D][11:28:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:19,398  [INFO] [D][11:28:21][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:19,401  [INFO] [D][11:28:21][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:19,407  [INFO] [D][11:28:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:19,409  [INFO] [D][11:28:21][PROT]M2M Send ok [1730201301]
 
2025-07-16 21:00:24,653  [INFO] [D][11:28:26][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:24,666  [INFO] [D][11:28:26][PROT]index:0 1730201306
 
2025-07-16 21:00:24,668  [INFO] [D][11:28:26][PROT]is_send:0
 
2025-07-16 21:00:24,671  [INFO] [D][11:28:26][PROT]sequence_num:3
 
2025-07-16 21:00:24,673  [INFO] [D][11:28:26][PROT]retry_timeout:0
 
2025-07-16 21:00:24,676  [INFO] [D][11:28:26][PROT]retry_times:6
 
2025-07-16 21:00:24,679  [INFO] [D][11:28:26][PROT]send_path:0x2
 
2025-07-16 21:00:24,684  [INFO] [D][11:28:26][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:24,691  [INFO] [D][11:28:26][PROT]===========================================================
 
2025-07-16 21:00:24,696  [INFO] [W][11:28:26][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201306]
 
2025-07-16 21:00:24,705  [INFO] [D][11:28:26][PROT]===========================================================
 
2025-07-16 21:00:24,710  [INFO] [D][11:28:26][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:24,712  [INFO] [D][11:28:26][PROT]Send_TO_M2M [1730201306]
 
2025-07-16 21:00:24,719  [INFO] [D][11:28:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:24,721  [INFO] [D][11:28:26][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:24,727  [INFO] [D][11:28:26][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:24,729  [INFO] [D][11:28:26][M2M ]m2m send data len[198]
 
2025-07-16 21:00:24,732  [INFO] [D][11:28:26][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:24,742  [INFO] [D][11:28:26][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:24,746  [INFO] [D][11:28:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:24,752  [INFO] [D][11:28:26][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:24,755  [INFO] [D][11:28:26][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:24,755  [INFO] 
 
2025-07-16 21:00:24,760  [INFO] [D][11:28:26][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:24,778  [INFO] 0063B983113311331133113311331B88B5E39339ABCB263D0AC57CD7071BE12A1E284900B1E464ADED305AB75F9849C97C863C0B1E8FA622E4415B9AF6537B106F12B0C8D2E5011FCD84BCA84AAFCEEA073602A5A923D15B1BFAD8424D6220DB5AF461
 
2025-07-16 21:00:24,779  [INFO] [D][11:28:26][CAT1]<<< 
 
2025-07-16 21:00:24,780  [INFO] SEND OK
 
2025-07-16 21:00:24,780  [INFO] 
 
2025-07-16 21:00:24,785  [INFO] [D][11:28:26][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:24,788  [INFO] [D][11:28:26][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:24,788  [INFO] 
 
2025-07-16 21:00:24,794  [INFO] [D][11:28:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:24,800  [INFO] [D][11:28:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:24,804  [INFO] [D][11:28:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:24,807  [INFO] [D][11:28:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:24,813  [INFO] [D][11:28:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:24,815  [INFO] [D][11:28:26][PROT]M2M Send ok [1730201306]
 
2025-07-16 21:00:30,056  [INFO] [D][11:28:32][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:30,068  [INFO] [D][11:28:32][PROT]index:0 1730201312
 
2025-07-16 21:00:30,072  [INFO] [D][11:28:32][PROT]is_send:0
 
2025-07-16 21:00:30,074  [INFO] [D][11:28:32][PROT]sequence_num:3
 
2025-07-16 21:00:30,077  [INFO] [D][11:28:32][PROT]retry_timeout:0
 
2025-07-16 21:00:30,079  [INFO] [D][11:28:32][PROT]retry_times:5
 
2025-07-16 21:00:30,082  [INFO] [D][11:28:32][PROT]send_path:0x2
 
2025-07-16 21:00:30,087  [INFO] [D][11:28:32][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:30,094  [INFO] [D][11:28:32][PROT]===========================================================
 
2025-07-16 21:00:30,099  [INFO] [W][11:28:32][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201312]
 
2025-07-16 21:00:30,108  [INFO] [D][11:28:32][PROT]===========================================================
 
2025-07-16 21:00:30,114  [INFO] [D][11:28:32][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:30,116  [INFO] [D][11:28:32][PROT]Send_TO_M2M [1730201312]
 
2025-07-16 21:00:30,122  [INFO] [D][11:28:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:30,124  [INFO] [D][11:28:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:30,129  [INFO] [D][11:28:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:30,132  [INFO] [D][11:28:32][M2M ]m2m send data len[198]
 
2025-07-16 21:00:30,135  [INFO] [D][11:28:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:30,144  [INFO] [D][11:28:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:30,150  [INFO] [D][11:28:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:30,155  [INFO] [D][11:28:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:30,158  [INFO] [D][11:28:32][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:30,158  [INFO] 
 
2025-07-16 21:00:30,163  [INFO] [D][11:28:32][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:30,182  [INFO] 0063B985113311331133113311331B88B58342F9F361BA6B48EF19BCDAE69ACF50BB3ABBEAEADEAD4A50DA74EDF2EF8940EEE5C7B2EE5C0BB3B085B672FB6C767D6A2D98F12194807DEC785B64B4CFA744C80675C6743101618E3FFE18FACD33112DCB
 
2025-07-16 21:00:30,183  [INFO] [D][11:28:32][CAT1]<<< 
 
2025-07-16 21:00:30,183  [INFO] SEND OK
 
2025-07-16 21:00:30,184  [INFO] 
 
2025-07-16 21:00:30,188  [INFO] [D][11:28:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:30,190  [INFO] [D][11:28:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:30,191  [INFO] 
 
2025-07-16 21:00:30,196  [INFO] [D][11:28:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:30,202  [INFO] [D][11:28:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:30,207  [INFO] [D][11:28:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:30,211  [INFO] [D][11:28:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:30,217  [INFO] [D][11:28:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:30,219  [INFO] [D][11:28:32][PROT]M2M Send ok [1730201312]
 
2025-07-16 21:00:35,462  [INFO] [D][11:28:37][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:35,474  [INFO] [D][11:28:37][PROT]index:0 1730201317
 
2025-07-16 21:00:35,476  [INFO] [D][11:28:37][PROT]is_send:0
 
2025-07-16 21:00:35,479  [INFO] [D][11:28:37][PROT]sequence_num:3
 
2025-07-16 21:00:35,482  [INFO] [D][11:28:37][PROT]retry_timeout:0
 
2025-07-16 21:00:35,485  [INFO] [D][11:28:37][PROT]retry_times:4
 
2025-07-16 21:00:35,488  [INFO] [D][11:28:37][PROT]send_path:0x2
 
2025-07-16 21:00:35,493  [INFO] [D][11:28:37][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:35,499  [INFO] [D][11:28:37][PROT]===========================================================
 
2025-07-16 21:00:35,504  [INFO] [W][11:28:37][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201317]
 
2025-07-16 21:00:35,513  [INFO] [D][11:28:37][PROT]===========================================================
 
2025-07-16 21:00:35,519  [INFO] [D][11:28:37][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:35,521  [INFO] [D][11:28:37][PROT]Send_TO_M2M [1730201317]
 
2025-07-16 21:00:35,527  [INFO] [D][11:28:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:35,530  [INFO] [D][11:28:37][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:35,535  [INFO] [D][11:28:37][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:35,537  [INFO] [D][11:28:37][M2M ]m2m send data len[198]
 
2025-07-16 21:00:35,540  [INFO] [D][11:28:37][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:35,551  [INFO] [D][11:28:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:35,555  [INFO] [D][11:28:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:35,560  [INFO] [D][11:28:37][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:35,563  [INFO] [D][11:28:37][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:35,563  [INFO] 
 
2025-07-16 21:00:35,568  [INFO] [D][11:28:37][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:35,587  [INFO] 0063B986113311331133113311331B88B5376DA48774A2D6F2E903CB54118BC228D53A02E328DE031619E662E5499810707B6E95715F1EBE3E0F0259DB7A1034C0B2C1F9FC3C63548E270382FB46E6C765BFFA358890968538A820C21F3AB19FBA1285
 
2025-07-16 21:00:35,588  [INFO] [D][11:28:37][CAT1]<<< 
 
2025-07-16 21:00:35,589  [INFO] SEND OK
 
2025-07-16 21:00:35,589  [INFO] 
 
2025-07-16 21:00:35,594  [INFO] [D][11:28:37][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:35,596  [INFO] [D][11:28:37][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:35,597  [INFO] 
 
2025-07-16 21:00:35,602  [INFO] [D][11:28:37][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:35,608  [INFO] [D][11:28:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:35,612  [INFO] [D][11:28:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:35,615  [INFO] [D][11:28:37][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:35,622  [INFO] [D][11:28:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:35,624  [INFO] [D][11:28:37][PROT]M2M Send ok [1730201317]
 
2025-07-16 21:00:40,868  [INFO] [D][11:28:43][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:40,880  [INFO] [D][11:28:43][PROT]index:0 1730201323
 
2025-07-16 21:00:40,883  [INFO] [D][11:28:43][PROT]is_send:0
 
2025-07-16 21:00:40,886  [INFO] [D][11:28:43][PROT]sequence_num:3
 
2025-07-16 21:00:40,889  [INFO] [D][11:28:43][PROT]retry_timeout:0
 
2025-07-16 21:00:40,891  [INFO] [D][11:28:43][PROT]retry_times:3
 
2025-07-16 21:00:40,894  [INFO] [D][11:28:43][PROT]send_path:0x2
 
2025-07-16 21:00:40,899  [INFO] [D][11:28:43][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:40,906  [INFO] [D][11:28:43][PROT]===========================================================
 
2025-07-16 21:00:40,912  [INFO] [W][11:28:43][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201323]
 
2025-07-16 21:00:40,921  [INFO] [D][11:28:43][PROT]===========================================================
 
2025-07-16 21:00:40,925  [INFO] [D][11:28:43][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:40,928  [INFO] [D][11:28:43][PROT]Send_TO_M2M [1730201323]
 
2025-07-16 21:00:40,933  [INFO] [D][11:28:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:40,936  [INFO] [D][11:28:43][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:40,941  [INFO] [D][11:28:43][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:40,945  [INFO] [D][11:28:43][M2M ]m2m send data len[198]
 
2025-07-16 21:00:40,948  [INFO] [D][11:28:43][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:40,956  [INFO] [D][11:28:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:40,962  [INFO] [D][11:28:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:40,967  [INFO] [D][11:28:43][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:40,970  [INFO] [D][11:28:43][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:40,971  [INFO] 
 
2025-07-16 21:00:40,975  [INFO] [D][11:28:43][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:40,994  [INFO] 0063B989113311331133113311331B88B5FF6EEFDCB323D369393AD99FDCBDE817827C1A14F19CFAF469BBAFF21F36B9C77D25AA685DC7DECDDF0D0FF7322DED2D4DBF152553D4F25699E4863C4CFE232006B46177691CBF8FB9E1B6F7BCEA17860680
 
2025-07-16 21:00:40,995  [INFO] [D][11:28:43][CAT1]<<< 
 
2025-07-16 21:00:40,996  [INFO] SEND OK
 
2025-07-16 21:00:40,996  [INFO] 
 
2025-07-16 21:00:41,001  [INFO] [D][11:28:43][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:41,003  [INFO] [D][11:28:43][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:41,004  [INFO] 
 
2025-07-16 21:00:41,009  [INFO] [D][11:28:43][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:41,014  [INFO] [D][11:28:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:41,019  [INFO] [D][11:28:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:41,023  [INFO] [D][11:28:43][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:41,029  [INFO] [D][11:28:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:41,031  [INFO] [D][11:28:43][PROT]M2M Send ok [1730201323]
 
2025-07-16 21:00:46,275  [INFO] [D][11:28:48][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:46,287  [INFO] [D][11:28:48][PROT]index:0 1730201328
 
2025-07-16 21:00:46,290  [INFO] [D][11:28:48][PROT]is_send:0
 
2025-07-16 21:00:46,292  [INFO] [D][11:28:48][PROT]sequence_num:3
 
2025-07-16 21:00:46,295  [INFO] [D][11:28:48][PROT]retry_timeout:0
 
2025-07-16 21:00:46,298  [INFO] [D][11:28:48][PROT]retry_times:2
 
2025-07-16 21:00:46,301  [INFO] [D][11:28:48][PROT]send_path:0x2
 
2025-07-16 21:00:46,307  [INFO] [D][11:28:48][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:46,313  [INFO] [D][11:28:48][PROT]===========================================================
 
2025-07-16 21:00:46,319  [INFO] [W][11:28:48][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201328]
 
2025-07-16 21:00:46,327  [INFO] [D][11:28:48][PROT]===========================================================
 
2025-07-16 21:00:46,332  [INFO] [D][11:28:48][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:46,335  [INFO] [D][11:28:48][PROT]Send_TO_M2M [1730201328]
 
2025-07-16 21:00:46,341  [INFO] [D][11:28:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:46,343  [INFO] [D][11:28:48][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:46,349  [INFO] [D][11:28:48][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:46,351  [INFO] [D][11:28:48][M2M ]m2m send data len[198]
 
2025-07-16 21:00:46,354  [INFO] [D][11:28:48][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:46,364  [INFO] [D][11:28:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:46,368  [INFO] [D][11:28:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:46,374  [INFO] [D][11:28:48][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:46,376  [INFO] [D][11:28:48][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:46,377  [INFO] 
 
2025-07-16 21:00:46,381  [INFO] [D][11:28:48][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:46,399  [INFO] 0063B987113311331133113311331B88B527905B6FECDB7FFA4F917CA16CEBBC6CB5609BBD0290B23D3B871E4BF565D9F2F440043170671DA520EE8357486F4AF703B29E1CE7A433A5020BFD5279C6D42D8164EAB7F29F55C9ED8D4EC75341DC805461
 
2025-07-16 21:00:46,401  [INFO] [D][11:28:48][CAT1]<<< 
 
2025-07-16 21:00:46,401  [INFO] SEND OK
 
2025-07-16 21:00:46,401  [INFO] 
 
2025-07-16 21:00:46,406  [INFO] [D][11:28:48][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:46,409  [INFO] [D][11:28:48][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:46,411  [INFO] 
 
2025-07-16 21:00:46,415  [INFO] [D][11:28:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:46,421  [INFO] [D][11:28:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:46,426  [INFO] [D][11:28:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:46,429  [INFO] [D][11:28:48][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:46,434  [INFO] [D][11:28:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:46,438  [INFO] [D][11:28:48][PROT]M2M Send ok [1730201328]
 
2025-07-16 21:00:51,681  [INFO] [D][11:28:53][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:51,692  [INFO] [D][11:28:53][PROT]index:0 1730201333
 
2025-07-16 21:00:51,695  [INFO] [D][11:28:53][PROT]is_send:0
 
2025-07-16 21:00:51,698  [INFO] [D][11:28:53][PROT]sequence_num:3
 
2025-07-16 21:00:51,700  [INFO] [D][11:28:53][PROT]retry_timeout:0
 
2025-07-16 21:00:51,704  [INFO] [D][11:28:53][PROT]retry_times:1
 
2025-07-16 21:00:51,706  [INFO] [D][11:28:53][PROT]send_path:0x2
 
2025-07-16 21:00:51,711  [INFO] [D][11:28:53][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:51,717  [INFO] [D][11:28:53][PROT]===========================================================
 
2025-07-16 21:00:51,724  [INFO] [W][11:28:53][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201333]
 
2025-07-16 21:00:51,732  [INFO] [D][11:28:53][PROT]===========================================================
 
2025-07-16 21:00:51,738  [INFO] [D][11:28:53][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:51,740  [INFO] [D][11:28:53][PROT]Send_TO_M2M [1730201333]
 
2025-07-16 21:00:51,746  [INFO] [D][11:28:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:51,748  [INFO] [D][11:28:53][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:51,754  [INFO] [D][11:28:53][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:51,757  [INFO] [D][11:28:53][M2M ]m2m send data len[198]
 
2025-07-16 21:00:51,759  [INFO] [D][11:28:53][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:51,769  [INFO] [D][11:28:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:51,773  [INFO] [D][11:28:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:51,778  [INFO] [D][11:28:53][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:51,781  [INFO] [D][11:28:53][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:51,783  [INFO] 
 
2025-07-16 21:00:51,787  [INFO] [D][11:28:53][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:51,806  [INFO] 0063B984113311331133113311331B88B527B76A8CE1A29CCCBF94EA796136EEE63814809E71223991AB33B6612800AA89A8B66E48A461EDF2CC73F4EBC5396E491D63F63768B463BBA68D3B2A081D392FEE1E637A68E06EF56710552D8C63D66B802C
 
2025-07-16 21:00:51,807  [INFO] [D][11:28:53][CAT1]<<< 
 
2025-07-16 21:00:51,808  [INFO] SEND OK
 
2025-07-16 21:00:51,808  [INFO] 
 
2025-07-16 21:00:51,812  [INFO] [D][11:28:53][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:51,815  [INFO] [D][11:28:53][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:51,815  [INFO] 
 
2025-07-16 21:00:51,821  [INFO] [D][11:28:53][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:51,826  [INFO] [D][11:28:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:51,831  [INFO] [D][11:28:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:51,834  [INFO] [D][11:28:53][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:51,840  [INFO] [D][11:28:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:51,843  [INFO] [D][11:28:53][PROT]M2M Send ok [1730201333]
 
2025-07-16 21:00:57,084  [INFO] [D][11:28:59][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:57,094  [INFO] [D][11:28:59][PROT]CLEAN:0
 
2025-07-16 21:00:57,106  [INFO] [D][11:28:59][PROT]index:1 1730201339
 
2025-07-16 21:00:57,109  [INFO] [D][11:28:59][PROT]is_send:0
 
2025-07-16 21:00:57,112  [INFO] [D][11:28:59][PROT]sequence_num:4
 
2025-07-16 21:00:57,115  [INFO] [D][11:28:59][PROT]retry_timeout:0
 
2025-07-16 21:00:57,117  [INFO] [D][11:28:59][PROT]retry_times:3
 
2025-07-16 21:00:57,120  [INFO] [D][11:28:59][PROT]send_path:0x2
 
2025-07-16 21:00:57,126  [INFO] [D][11:28:59][PROT]min_index:1, type:0x6A01, priority:0
 
2025-07-16 21:00:57,132  [INFO] [D][11:28:59][PROT]===========================================================
 
2025-07-16 21:00:57,138  [INFO] [W][11:28:59][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201339]
 
2025-07-16 21:00:57,147  [INFO] [D][11:28:59][PROT]===========================================================
 
2025-07-16 21:00:57,148  [INFO] [D][11:28:59][PROT]sending traceid [00]
 
2025-07-16 21:00:57,154  [INFO] [D][11:28:59][PROT]Send_TO_M2M [1730201339]
 
2025-07-16 21:00:57,157  [INFO] [D][11:28:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:57,163  [INFO] [D][11:28:59][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:57,165  [INFO] [D][11:28:59][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:57,168  [INFO] [D][11:28:59][M2M ]m2m send data len[102]
 
2025-07-16 21:00:57,174  [INFO] [D][11:28:59][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:57,183  [INFO] [D][11:28:59][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a058] format[0]
 
2025-07-16 21:00:57,188  [INFO] [D][11:28:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:57,191  [INFO] [D][11:28:59][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:57,196  [INFO] [D][11:28:59][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:00:57,197  [INFO] 
 
2025-07-16 21:00:57,201  [INFO] [D][11:28:59][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:00:57,210  [INFO] 0033B98B113311331188BB88BB88BB88BB45D6D980D71B270834A9942274282319BA01E39F0E044CDD18341A782C791BA6C595
 
2025-07-16 21:00:57,211  [INFO] [D][11:28:59][CAT1]<<< 
 
2025-07-16 21:00:57,213  [INFO] SEND OK
 
2025-07-16 21:00:57,214  [INFO] 
 
2025-07-16 21:00:57,218  [INFO] [D][11:28:59][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:57,221  [INFO] [D][11:28:59][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:57,222  [INFO] 
 
2025-07-16 21:00:57,224  [INFO] [D][11:28:59][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:57,232  [INFO] [D][11:28:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:57,234  [INFO] [D][11:28:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:57,238  [INFO] [D][11:28:59][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:57,244  [INFO] [D][11:28:59][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:57,247  [INFO] [D][11:28:59][PROT]M2M Send ok [1730201339]
 
2025-07-16 21:01:02,498  [INFO] [D][11:29:04][PROT]CLEAN,SEND:1
 
2025-07-16 21:01:02,511  [INFO] [D][11:29:04][PROT]index:1 1730201344
 
2025-07-16 21:01:02,513  [INFO] [D][11:29:04][PROT]is_send:0
 
2025-07-16 21:01:02,516  [INFO] [D][11:29:04][PROT]sequence_num:4
 
2025-07-16 21:01:02,519  [INFO] [D][11:29:04][PROT]retry_timeout:0
 
2025-07-16 21:01:02,522  [INFO] [D][11:29:04][PROT]retry_times:2
 
2025-07-16 21:01:02,525  [INFO] [D][11:29:04][PROT]send_path:0x2
 
2025-07-16 21:01:02,531  [INFO] [D][11:29:04][PROT]min_index:1, type:0x6A01, priority:0
 
2025-07-16 21:01:02,537  [INFO] [D][11:29:04][PROT]===========================================================
 
2025-07-16 21:01:02,542  [INFO] [W][11:29:04][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201344]
 
2025-07-16 21:01:02,550  [INFO] [D][11:29:04][PROT]===========================================================
 
2025-07-16 21:01:02,553  [INFO] [D][11:29:04][PROT]sending traceid [00]
 
2025-07-16 21:01:02,558  [INFO] [D][11:29:04][PROT]Send_TO_M2M [1730201344]
 
2025-07-16 21:01:02,561  [INFO] [D][11:29:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:02,566  [INFO] [D][11:29:04][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:02,569  [INFO] [D][11:29:04][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:02,572  [INFO] [D][11:29:04][M2M ]m2m send data len[102]
 
2025-07-16 21:01:02,578  [INFO] [D][11:29:04][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:02,588  [INFO] [D][11:29:04][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a058] format[0]
 
2025-07-16 21:01:02,592  [INFO] [D][11:29:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:02,594  [INFO] [D][11:29:04][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:02,601  [INFO] [D][11:29:04][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:02,602  [INFO] 
 
2025-07-16 21:01:02,605  [INFO] [D][11:29:04][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:02,614  [INFO] 0033B9A8113311331188BB88BB88BB88BB66E8E3EA8FDF426FE34BC847BD9DA5416216B85688C65C83D39E16A8A67AADFEDDAC
 
2025-07-16 21:01:02,617  [INFO] [D][11:29:04][CAT1]<<< 
 
2025-07-16 21:01:02,617  [INFO] SEND OK
 
2025-07-16 21:01:02,618  [INFO] 
 
2025-07-16 21:01:02,622  [INFO] [D][11:29:04][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:02,625  [INFO] [D][11:29:04][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:02,626  [INFO] 
 
2025-07-16 21:01:02,628  [INFO] [D][11:29:04][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:02,636  [INFO] [D][11:29:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:02,638  [INFO] [D][11:29:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:02,642  [INFO] [D][11:29:04][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:02,648  [INFO] [D][11:29:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:02,651  [INFO] [D][11:29:04][PROT]M2M Send ok [1730201344]
 
2025-07-16 21:01:03,510  [INFO] [D][11:29:05][COMM]Main Task receive event:118
 
2025-07-16 21:01:03,516  [INFO] [D][11:29:05][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:01:03,522  [INFO] [D][11:29:05][COMM]frm_mc_bat_power_on EXT_BAT_STATE_POWERON_TIMEOUT, bat is on:0
 
2025-07-16 21:01:03,529  [INFO] [D][11:29:05][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON, Ext48v = 2.
 
2025-07-16 21:01:03,531  [INFO] [D][11:29:05][COMM]open bat mos
 
2025-07-16 21:01:03,535  [INFO] [D][11:29:05][COMM]1x1 tx_id:3,7, tx_len:2
 
2025-07-16 21:01:03,538  [INFO] [D][11:29:05][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:01:03,914  [INFO] [E][11:29:06][COMM]1x1 rx timeout
 
2025-07-16 21:01:03,916  [INFO] [D][11:29:06][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:01:04,318  [INFO] [E][11:29:06][COMM]1x1 rx timeout
 
2025-07-16 21:01:04,324  [INFO] [D][11:29:06][HSDK][0] flush to flash addr:[0xE45F00] --- write len --- [256]
 
2025-07-16 21:01:04,326  [INFO] [E][11:29:06][COMM]1x1 tp timeout
 
2025-07-16 21:01:04,328  [INFO] [E][11:29:06][COMM]1x1 error -3.
 
2025-07-16 21:01:04,334  [INFO] [E][11:29:06][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:01:04,340  [INFO] [D][11:29:06][COMM]frm_peripheral_device_poweron type 12.... 
 
2025-07-16 21:01:04,343  [INFO] [D][11:29:06][COMM]get Acckey 1 and value:1
 
2025-07-16 21:01:04,345  [INFO] [D][11:29:06][COMM]get mc hw info fail
 
2025-07-16 21:01:04,349  [INFO] [D][11:29:06][COMM]get bat hw info fail
 
2025-07-16 21:01:04,354  [INFO] [D][11:29:06][COMM]get helmet hw info fail,rt:-3
 
2025-07-16 21:01:04,359  [INFO] [D][11:29:06][COMM]get helmet rope hw info fail,rt:-3
 
2025-07-16 21:01:04,360  [INFO] 
 
2025-07-16 21:01:04,362  [INFO] [D][11:29:06][COMM]get weight hw info fail,rt:-3
 
2025-07-16 21:01:04,367  [INFO] [D][11:29:06][COMM][9101][unknown camera type:0]
 
2025-07-16 21:01:04,371  [INFO] [D][11:29:06][COMM]get arm version info succ
 
2025-07-16 21:01:04,377  [INFO] [D][11:29:06][COMM][9158][unknown camera type:0]
 
2025-07-16 21:01:04,382  [INFO] [E][11:29:06][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 21:01:04,388  [INFO] [D][11:29:06][COMM]frm_rfid_get_hwinfo hw ver err,rt:0x204
 
2025-07-16 21:01:04,393  [INFO] [D][11:29:06][COMM]frm_rfid_get_hwinfo sw ver err,rt:0x204
 
2025-07-16 21:01:04,399  [INFO] [D][11:29:06][COMM]frm_rfid_get_hwinfo boot ver err,rt:0x204
 
2025-07-16 21:01:04,404  [INFO] [D][11:29:06][COMM]frm_rfid_get_hwinfo id err,rt:0x204
 
2025-07-16 21:01:04,407  [INFO] [D][11:29:06][COMM]get rfid hw info fail,rt:-1
 
2025-07-16 21:01:04,408  [INFO] 
 
2025-07-16 21:01:04,412  [INFO] [D][11:29:06][COMM]frm_peripheral_device_poweroff type 11.... 
 
2025-07-16 21:01:04,423  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:01:04,432  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[5E01],priority[3],index[0],used[1]
 
2025-07-16 21:01:04,434  [INFO] [D][11:29:06][COMM]a:5,b:7,c:255
 
2025-07-16 21:01:04,444  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:01:04,452  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 21:01:04,454  [INFO] [D][11:29:06][CAT1]gsm read msg sub id: 13
 
2025-07-16 21:01:04,460  [INFO] [D][11:29:06][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:01:04,461  [INFO] 
 
2025-07-16 21:01:04,461  [INFO] [D][11:29:06][CAT1]<<< 
 
2025-07-16 21:01:04,461  [INFO] +CSQ: 31,99
 
2025-07-16 21:01:04,462  [INFO] 
 
2025-07-16 21:01:04,462  [INFO] OK
 
2025-07-16 21:01:04,464  [INFO] 
 
2025-07-16 21:01:04,468  [INFO] [D][11:29:06][CAT1]exec over: func id: 13, ret: 21
 
2025-07-16 21:01:04,469  [INFO] [D][11:29:06][M2M ]get csq[31]
 
2025-07-16 21:01:04,625  [INFO] [D][11:29:06][COMM]lastHelmetOrderIdV2:00
 
2025-07-16 21:01:04,627  [INFO] >>>>>RESEND ALLSTATE<<<<<
 
2025-07-16 21:01:04,636  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:01:04,644  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 21:01:04,647  [INFO] [D][11:29:06][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:01:04,654  [INFO] [D][11:29:06][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:01:04,659  [INFO] [D][11:29:06][HSDK][0] flush to flash addr:[0xE46000] --- write len --- [256]
 
2025-07-16 21:01:04,661  [INFO] [W][11:29:06][COMM]get soc error
 
2025-07-16 21:01:04,664  [INFO] [W][11:29:06][GNSS]stop locating
 
2025-07-16 21:01:04,669  [INFO] [D][11:29:06][GNSS]all continue location stop
 
2025-07-16 21:01:04,673  [INFO] [W][11:29:06][GNSS]sing locating running
 
2025-07-16 21:01:04,679  [INFO] [E][11:29:06][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:01:04,684  [INFO] [D][11:29:06][COMM]report elecbike, soc 0, reason 2
 
2025-07-16 21:01:04,692  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:01:04,700  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 21:01:04,706  [INFO] [D][11:29:06][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:01:04,708  [INFO] [D][11:29:06][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:01:04,717  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:01:04,725  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 21:01:04,738  [INFO] [D][11:29:06][COMM]buried data a:10,b:2,c:255,d:0,f:16843008,k:5535,l:151,m:11,n:11,o:7,p:1676,q:2149,r:5516,z:665
 
2025-07-16 21:01:04,745  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 21:01:04,753  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 21:01:04,759  [INFO] [D][11:29:06][COMM]Main Task receive event:118 finished processing
 
2025-07-16 21:01:04,764  [INFO] [D][11:29:06][COMM]Main Task receive event:54
 
2025-07-16 21:01:04,767  [INFO] [D][11:29:06][COMM][D301]:type:1, trace id:20017150
 
2025-07-16 21:01:04,773  [INFO] [D][11:29:06][COMM]get bat basic info err
 
2025-07-16 21:01:04,779  [INFO] [D][11:29:06][HSDK][0] flush to flash addr:[0xE46100] --- write len --- [256]
 
2025-07-16 21:01:04,786  [INFO] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 21:01:04,795  [INFO] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[D302],priority[0],index[8],used[1]
 
2025-07-16 21:01:04,803  [INFO] [D][11:29:06][COMM]Main Task receive event:54 finished processing
 
2025-07-16 21:01:07,906  [INFO] [D][11:29:10][PROT]CLEAN,SEND:1
 
2025-07-16 21:01:07,919  [INFO] [D][11:29:10][PROT]index:0 1730201350
 
2025-07-16 21:01:07,922  [INFO] [D][11:29:10][PROT]is_send:0
 
2025-07-16 21:01:07,924  [INFO] [D][11:29:10][PROT]sequence_num:6
 
2025-07-16 21:01:07,928  [INFO] [D][11:29:10][PROT]retry_timeout:0
 
2025-07-16 21:01:07,930  [INFO] [D][11:29:10][PROT]retry_times:3
 
2025-07-16 21:01:07,933  [INFO] [D][11:29:10][PROT]send_path:0x2
 
2025-07-16 21:01:07,938  [INFO] [D][11:29:10][PROT]min_index:0, type:0x5E01, priority:3
 
2025-07-16 21:01:07,944  [INFO] [D][11:29:10][PROT]===========================================================
 
2025-07-16 21:01:07,949  [INFO] [W][11:29:10][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201350]
 
2025-07-16 21:01:07,959  [INFO] [D][11:29:10][PROT]===========================================================
 
2025-07-16 21:01:07,964  [INFO] [D][11:29:10][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:07,967  [INFO] [D][11:29:10][PROT]Send_TO_M2M [1730201350]
 
2025-07-16 21:01:07,972  [INFO] [D][11:29:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:07,974  [INFO] [D][11:29:10][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:07,980  [INFO] [D][11:29:10][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:07,982  [INFO] [D][11:29:10][M2M ]m2m send data len[678]
 
2025-07-16 21:01:07,986  [INFO] [D][11:29:10][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:07,995  [INFO] [D][11:29:10][SAL ]cellular SEND socket id[0] type[1], len[678], data[0x2005a058] format[0]
 
2025-07-16 21:01:08,000  [INFO] [D][11:29:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:08,005  [INFO] [D][11:29:10][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:08,008  [INFO] [D][11:29:10][CAT1]tx ret[17] >>> AT+QISEND=0,678
 
2025-07-16 21:01:08,009  [INFO] 
 
2025-07-16 21:01:08,013  [INFO] [D][11:29:10][CAT1]Send Data To Server[678][678] ... ->:
 
2025-07-16 21:01:08,079  [INFO] 0153B9AA113311331133113311331B88B5B5D0C7286200D529928FBA31FC3C3A67ED0C7957674C7E9C43D95C6C81F62C6905A43880E47256F9BE52CA5791C312BEFE0E9EAB96F0796DE38703ED8AE377E63FE22C31F831BA5A5269DE2441E760B166F71DDDAB067D20019904B15985640591B0137C2F925ED742842115C9821A0F64E113176EB3B68BE07B0C00FC927DB7DE30EE687159BBA51CE071A5916D1A5A601D12CB2489B58BFE9ECDFE008B618E778C1AC46C3B0B70067551CCA53EE684771405F0B0FB2B0AC992EEEE76F01318BBCB38B8F3A5F2E57154896C153F035C766D1D82498B0B2CD9BFD2DCB2F3780265B27B01B399F8A181F2247B4B0FF468BB96715C42AEF9A2922DB0413060DEC203E023EAD6FE3E06BAB728465F5F8FB0EA2EC244C0FCE9E7E2EA76D4FA376BE646F309F511ABF7C6EB6A02212969418A21E4DB8A488C9209703F3E2599588E49DEBC
 
2025-07-16 21:01:08,080  [INFO] [D][11:29:10][CAT1]<<< 
 
2025-07-16 21:01:08,080  [INFO] SEND OK
 
2025-07-16 21:01:08,081  [INFO] 
 
2025-07-16 21:01:08,081  [INFO] [D][11:29:10][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:08,083  [INFO] [D][11:29:10][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:08,084  [INFO] 
 
2025-07-16 21:01:08,089  [INFO] [D][11:29:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:08,094  [INFO] [D][11:29:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:08,099  [INFO] [D][11:29:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:08,102  [INFO] [D][11:29:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:08,107  [INFO] [D][11:29:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:08,111  [INFO] [D][11:29:10][PROT]M2M Send ok [1730201350]
 
2025-07-16 21:01:12,013  [INFO] [D][11:29:14][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 1.
 
2025-07-16 21:01:13,321  [INFO] [D][11:29:15][PROT]CLEAN,SEND:0
 
2025-07-16 21:01:13,334  [INFO] [D][11:29:15][PROT]index:0 1730201355
 
2025-07-16 21:01:13,337  [INFO] [D][11:29:15][PROT]is_send:0
 
2025-07-16 21:01:13,339  [INFO] [D][11:29:15][PROT]sequence_num:6
 
2025-07-16 21:01:13,342  [INFO] [D][11:29:15][PROT]retry_timeout:0
 
2025-07-16 21:01:13,344  [INFO] [D][11:29:15][PROT]retry_times:2
 
2025-07-16 21:01:13,347  [INFO] [D][11:29:15][PROT]send_path:0x2
 
2025-07-16 21:01:13,352  [INFO] [D][11:29:15][PROT]min_index:0, type:0x5E01, priority:3
 
2025-07-16 21:01:13,360  [INFO] [D][11:29:15][PROT]===========================================================
 
2025-07-16 21:01:13,365  [INFO] [W][11:29:15][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201355]
 
2025-07-16 21:01:13,374  [INFO] [D][11:29:15][PROT]===========================================================
 
2025-07-16 21:01:13,379  [INFO] [D][11:29:15][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:13,381  [INFO] [D][11:29:15][PROT]Send_TO_M2M [1730201355]
 
2025-07-16 21:01:13,387  [INFO] [D][11:29:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:13,389  [INFO] [D][11:29:15][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:13,396  [INFO] [D][11:29:15][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:13,398  [INFO] [D][11:29:15][M2M ]m2m send data len[678]
 
2025-07-16 21:01:13,401  [INFO] [D][11:29:15][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:13,410  [INFO] [D][11:29:15][SAL ]cellular SEND socket id[0] type[1], len[678], data[0x2005a058] format[0]
 
2025-07-16 21:01:13,416  [INFO] [D][11:29:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:13,420  [INFO] [D][11:29:15][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:13,423  [INFO] [D][11:29:15][CAT1]tx ret[17] >>> AT+QISEND=0,678
 
2025-07-16 21:01:13,424  [INFO] 
 
2025-07-16 21:01:13,428  [INFO] [D][11:29:15][CAT1]Send Data To Server[678][678] ... ->:
 
2025-07-16 21:01:13,494  [INFO] 0153B9AF113311331133113311331B88B5E5A60B8278178B641F23A17DA3FC17FA0049A6B99C13B7D3ECA0CE82CDD4E0DBD53A3FFE7D3820F60D259FA772C366BE13E80474758D152E15A5510B5734F8C82B97CEE1527DE5C385BEB1DEDA3CE5176EB1BFA7A92D8F990A2CA63A3A9149F68FCC9C39202A45BE4CB47CFA3B41B764D4328E4097D71EE6C233D56B4153DA93D680F270D05CF6446CEDD0AF5866F6982E4C772563D1A97CB2702D43F1E9C4CAA9494C5C7C9AA747092768576234DFDC318CC2B8378AE2AD38DA89809D0B552A1C7D9BB3A7E0340D4F834BCD7C5820C4DF23BC4B940AADD9A98E1BBF5DABA05141FA60B501F663E98F1F34DB8D29CA1A5D2E1EE18587FFFBBBF71E0AE218FBAEAD80DBDF7C1BB2EAC49785905DAA4022BFD8FD4FDEAC285A8482B74AE4C1CE5EFEAB49AA531EEFAA3A016E3FA29A8C8768FD39EF575F5FC7C9FC71828E8E4A69ABA4
 
2025-07-16 21:01:13,495  [INFO] [D][11:29:15][CAT1]<<< 
 
2025-07-16 21:01:13,495  [INFO] SEND OK
 
2025-07-16 21:01:13,496  [INFO] 
 
2025-07-16 21:01:13,496  [INFO] [D][11:29:15][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:13,498  [INFO] [D][11:29:15][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:13,499  [INFO] 
 
2025-07-16 21:01:13,504  [INFO] [D][11:29:15][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:13,510  [INFO] [D][11:29:15][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:13,514  [INFO] [D][11:29:15][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:13,517  [INFO] [D][11:29:15][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:13,523  [INFO] [D][11:29:15][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:13,526  [INFO] [D][11:29:15][PROT]M2M Send ok [1730201355]
 
2025-07-16 21:01:18,738  [INFO] [D][11:29:20][PROT]CLEAN,SEND:0
 
2025-07-16 21:01:18,751  [INFO] [D][11:29:20][PROT]index:0 1730201360
 
2025-07-16 21:01:18,753  [INFO] [D][11:29:20][PROT]is_send:0
 
2025-07-16 21:01:18,757  [INFO] [D][11:29:20][PROT]sequence_num:6
 
2025-07-16 21:01:18,759  [INFO] [D][11:29:20][PROT]retry_timeout:0
 
2025-07-16 21:01:18,762  [INFO] [D][11:29:20][PROT]retry_times:1
 
2025-07-16 21:01:18,765  [INFO] [D][11:29:20][PROT]send_path:0x2
 
2025-07-16 21:01:18,770  [INFO] [D][11:29:20][PROT]min_index:0, type:0x5E01, priority:3
 
2025-07-16 21:01:18,776  [INFO] [D][11:29:20][PROT]===========================================================
 
2025-07-16 21:01:18,782  [INFO] [W][11:29:20][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201360]
 
2025-07-16 21:01:18,790  [INFO] [D][11:29:20][PROT]===========================================================
 
2025-07-16 21:01:18,795  [INFO] [D][11:29:20][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:18,798  [INFO] [D][11:29:20][PROT]Send_TO_M2M [1730201360]
 
2025-07-16 21:01:18,804  [INFO] [D][11:29:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:18,806  [INFO] [D][11:29:20][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:18,812  [INFO] [D][11:29:20][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:18,815  [INFO] [D][11:29:20][M2M ]m2m send data len[678]
 
2025-07-16 21:01:18,817  [INFO] [D][11:29:20][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:18,826  [INFO] [D][11:29:20][SAL ]cellular SEND socket id[0] type[1], len[678], data[0x2005a058] format[0]
 
2025-07-16 21:01:18,832  [INFO] [D][11:29:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:18,837  [INFO] [D][11:29:20][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:18,840  [INFO] [D][11:29:20][CAT1]tx ret[17] >>> AT+QISEND=0,678
 
2025-07-16 21:01:18,841  [INFO] 
 
2025-07-16 21:01:18,845  [INFO] [D][11:29:20][CAT1]Send Data To Server[678][678] ... ->:
 
2025-07-16 21:01:18,911  [INFO] 0153B9A0113311331133113311331B88B5E036616840F50032278D1C208A3029F80D6EC6724D592ADCCDD454E4DEFC115238E137241635F1AE9D1CE1AF835E6990366B19317FA3B218D7D8264061EA0274A9917C53C4FA076C43802552EB21805CDDDE22565DF32983B933271E4341674F0D69520AC96803B98273BBE2386C89B576E7BEA11E94619E212F74DDD98A631D9E179137BF808D01BFC99DD36413D0E752DDED1010E5930B6AC98EAE530BBC5685F164CD3BC3319219B624C057788556F0A40479F50BE3DDD95E4A3557EE8895052D79450ACEA392EFD846BC1F678BF9411EE7F098C8E00F4BD83F657C6F3FC7DF86C9F8B1A58E5867029480047C2827CEB04A16DB7E5AD91C0175D6D47966C772C67E15CD8134E6336BC7FE084A0505E908B3E1F73F9920B995F79ACD3C0179C57954A7E5179D8B0A10640EEDF7A2A7C6EC1F5A4744C46D3722695C97C457A50F14
 
2025-07-16 21:01:18,912  [INFO] [D][11:29:21][CAT1]<<< 
 
2025-07-16 21:01:18,913  [INFO] SEND OK
 
2025-07-16 21:01:18,913  [INFO] 
 
2025-07-16 21:01:18,914  [INFO] [D][11:29:21][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:18,916  [INFO] [D][11:29:21][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:18,917  [INFO] 
 
2025-07-16 21:01:18,920  [INFO] [D][11:29:21][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:18,927  [INFO] [D][11:29:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:18,932  [INFO] [D][11:29:21][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:18,935  [INFO] [D][11:29:21][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:18,940  [INFO] [D][11:29:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:18,943  [INFO] [D][11:29:21][PROT]M2M Send ok [1730201361]
 
2025-07-16 21:01:24,153  [INFO] [D][11:29:26][PROT]CLEAN,SEND:0
 
2025-07-16 21:01:24,164  [INFO] [D][11:29:26][PROT]CLEAN:0
 
2025-07-16 21:01:24,176  [INFO] [D][11:29:26][PROT]index:5 1730201366
 
2025-07-16 21:01:24,179  [INFO] [D][11:29:26][PROT]is_send:0
 
2025-07-16 21:01:24,182  [INFO] [D][11:29:26][PROT]sequence_num:9
 
2025-07-16 21:01:24,185  [INFO] [D][11:29:26][PROT]retry_timeout:0
 
2025-07-16 21:01:24,187  [INFO] [D][11:29:26][PROT]retry_times:3
 
2025-07-16 21:01:24,190  [INFO] [D][11:29:26][PROT]send_path:0x2
 
2025-07-16 21:01:24,195  [INFO] [D][11:29:26][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:01:24,201  [INFO] [D][11:29:26][PROT]===========================================================
 
2025-07-16 21:01:24,208  [INFO] [W][11:29:26][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201366]
 
2025-07-16 21:01:24,216  [INFO] [D][11:29:26][PROT]===========================================================
 
2025-07-16 21:01:24,218  [INFO] [D][11:29:26][COMM]PB encode data:29
 
2025-07-16 21:01:24,224  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:01:24,230  [INFO] [D][11:29:26][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:24,232  [INFO] [D][11:29:26][PROT]Send_TO_M2M [1730201366]
 
2025-07-16 21:01:24,238  [INFO] [D][11:29:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:24,241  [INFO] [D][11:29:26][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:24,246  [INFO] [D][11:29:26][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:24,248  [INFO] [D][11:29:26][M2M ]m2m send data len[134]
 
2025-07-16 21:01:24,251  [INFO] [D][11:29:26][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:24,260  [INFO] [D][11:29:26][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x2005a058] format[0]
 
2025-07-16 21:01:24,265  [INFO] [D][11:29:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:24,271  [INFO] [D][11:29:26][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:24,274  [INFO] [D][11:29:26][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:01:24,275  [INFO] 
 
2025-07-16 21:01:24,279  [INFO] [D][11:29:26][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:01:24,292  [INFO] 0043B6AD113311331133113311331B88B5B8235811E3D8721922B56859C8A409BAFF5FDB1FC3A4CA1FB658B89947CB0771751C03688245677807252926E22BE09FD769
 
2025-07-16 21:01:24,293  [INFO] [D][11:29:26][CAT1]<<< 
 
2025-07-16 21:01:24,293  [INFO] SEND OK
 
2025-07-16 21:01:24,294  [INFO] 
 
2025-07-16 21:01:24,299  [INFO] [D][11:29:26][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:24,302  [INFO] [D][11:29:26][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:24,302  [INFO] 
 
2025-07-16 21:01:24,307  [INFO] [D][11:29:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:24,313  [INFO] [D][11:29:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:24,318  [INFO] [D][11:29:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:24,321  [INFO] [D][11:29:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:24,328  [INFO] [D][11:29:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:24,330  [INFO] [D][11:29:26][PROT]M2M Send ok [1730201366]
 
2025-07-16 21:01:26,192  [INFO] [D][11:29:28][CAT1]closed : 0
 
2025-07-16 21:01:26,197  [INFO] [D][11:29:28][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:01:26,199  [INFO] [D][11:29:28][SAL ]socket closed id[0]
 
2025-07-16 21:01:26,206  [INFO] [D][11:29:28][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:01:26,211  [INFO] [D][11:29:28][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:01:26,214  [INFO] [D][11:29:28][M2M ]m2m select fd[4]
 
2025-07-16 21:01:26,220  [INFO] [D][11:29:28][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:01:26,223  [INFO] [D][11:29:28][M2M ]tcpclient close[4]
 
2025-07-16 21:01:26,226  [INFO] [D][11:29:28][SAL ]socket[4] has closed
 
2025-07-16 21:01:26,230  [INFO] [D][11:29:28][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:01:26,237  [INFO] [D][11:29:28][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
 
2025-07-16 21:01:26,239  [INFO] [D][11:29:28][COMM]Main Task receive event:86
 
2025-07-16 21:01:26,247  [INFO] [W][11:29:28][PROT]remove success[1730201368],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:01:26,257  [INFO] [W][11:29:28][PROT]add success [1730201368],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:01:26,263  [INFO] [D][11:29:28][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:01:29,572  [INFO] [D][11:29:31][PROT]CLEAN,SEND:5
 
2025-07-16 21:01:29,584  [INFO] [D][11:29:31][PROT]index:5 1730201371
 
2025-07-16 21:01:29,587  [INFO] [D][11:29:31][PROT]is_send:0
 
2025-07-16 21:01:29,589  [INFO] [D][11:29:31][PROT]sequence_num:9
 
2025-07-16 21:01:29,592  [INFO] [D][11:29:31][PROT]retry_timeout:0
 
2025-07-16 21:01:29,595  [INFO] [D][11:29:31][PROT]retry_times:2
 
2025-07-16 21:01:29,598  [INFO] [D][11:29:31][PROT]send_path:0x2
 
2025-07-16 21:01:29,602  [INFO] [D][11:29:31][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:01:29,609  [INFO] [D][11:29:31][PROT]===========================================================
 
2025-07-16 21:01:29,614  [INFO] [W][11:29:31][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201371]
 
2025-07-16 21:01:29,624  [INFO] [D][11:29:31][PROT]===========================================================
 
2025-07-16 21:01:29,626  [INFO] [D][11:29:31][COMM]PB encode data:29
 
2025-07-16 21:01:29,631  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:01:29,637  [INFO] [D][11:29:31][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:29,640  [INFO] [D][11:29:31][PROT]Send_TO_M2M [1730201371]
 
2025-07-16 21:01:29,645  [INFO] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:29,651  [INFO] [E][11:29:31][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:01:29,653  [INFO] [E][11:29:31][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:01:29,656  [INFO] [D][11:29:31][M2M ]m2m send data len[-1]
 
2025-07-16 21:01:29,662  [INFO] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:29,671  [INFO] [D][11:29:31][HSDK][0] flush to flash addr:[0xE46200] --- write len --- [256]
 
2025-07-16 21:01:29,672  [INFO] [E][11:29:31][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:01:29,678  [INFO] [E][11:29:31][PROT]M2M Send Fail [1730201371]
 
2025-07-16 21:01:29,680  [INFO] [D][11:29:31][PROT]CLEAN,SEND:5
 
2025-07-16 21:01:29,687  [INFO] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:01:29,690  [INFO] [D][11:29:31][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:01:29,692  [INFO] [D][11:29:31][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:01:29,693  [INFO] 
 
2025-07-16 21:01:29,699  [INFO] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:01:29,701  [INFO] [D][11:29:31][CAT1]<<< 
 
2025-07-16 21:01:29,703  [INFO] +CGATT: 1
 
2025-07-16 21:01:29,703  [INFO] 
 
2025-07-16 21:01:29,704  [INFO] OK
 
2025-07-16 21:01:29,705  [INFO] 
 
2025-07-16 21:01:29,707  [INFO] [D][11:29:31][CAT1]tx ret[12] >>> AT+CGATT=0
 
2025-07-16 21:01:29,709  [INFO] 
 
2025-07-16 21:01:29,975  [INFO] [D][11:29:32][CAT1]<<< 
 
2025-07-16 21:01:29,975  [INFO] OK
 
2025-07-16 21:01:29,976  [INFO] 
 
2025-07-16 21:01:29,980  [INFO] [D][11:29:32][CAT1]exec over: func id: 10, ret: 6
 
2025-07-16 21:01:29,982  [INFO] [D][11:29:32][CAT1]sub id: 10, ret: 6
 
2025-07-16 21:01:29,983  [INFO] 
 
2025-07-16 21:01:29,988  [INFO] [D][11:29:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:29,994  [INFO] [D][11:29:32][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
 
2025-07-16 21:01:29,996  [INFO] [D][11:29:32][M2M ]m2m gsm shut done, ret[0]
 
2025-07-16 21:01:30,001  [INFO] [D][11:29:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:01:30,007  [INFO] [D][11:29:32][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:01:30,012  [INFO] [D][11:29:32][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:01:30,018  [INFO] [D][11:29:32][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:01:30,024  [INFO] [D][11:29:32][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:01:30,030  [INFO] [D][11:29:32][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:01:30,035  [INFO] [D][11:29:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:01:30,040  [INFO] [D][11:29:32][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:01:30,043  [INFO] [D][11:29:32][CAT1]at ops open socket[0]
 
2025-07-16 21:01:30,046  [INFO] [D][11:29:32][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:01:30,047  [INFO] 
 
2025-07-16 21:01:30,048  [INFO] [D][11:29:32][CAT1]<<< 
 
2025-07-16 21:01:30,051  [INFO] +CGATT: 0
 
2025-07-16 21:01:30,051  [INFO] 
 
2025-07-16 21:01:30,052  [INFO] OK
 
2025-07-16 21:01:30,052  [INFO] 
 
2025-07-16 21:01:30,055  [INFO] [D][11:29:32][CAT1]tx ret[12] >>> AT+CGATT=1
 
2025-07-16 21:01:30,056  [INFO] 
 
2025-07-16 21:01:30,359  [INFO] [D][11:29:32][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:01:30,411  [INFO] [D][11:29:32][CAT1]pdpdeact urc len[22]
 
2025-07-16 21:01:30,417  [INFO] [D][11:29:32][COMM]f:frm_violent_loading_over_thr_process. is_first_over pitch:[127]
 
2025-07-16 21:01:30,418  [INFO] 
 
2025-07-16 21:01:31,359  [INFO] [D][11:29:33][COMM]176590 imu init OK
 
2025-07-16 21:01:31,395  [INFO] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,395  [INFO] OK
 
2025-07-16 21:01:31,405  [INFO] 
 
2025-07-16 21:01:31,408  [INFO] [D][11:29:33][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:01:31,409  [INFO] 
 
2025-07-16 21:01:31,437  [INFO] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,438  [INFO] +CGATT: 1
 
2025-07-16 21:01:31,439  [INFO] 
 
2025-07-16 21:01:31,444  [INFO] OK
 
2025-07-16 21:01:31,448  [INFO] 
 
2025-07-16 21:01:31,451  [INFO] [D][11:29:33][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:01:31,452  [INFO] 
 
2025-07-16 21:01:31,469  [INFO] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,472  [INFO] +CSQ: 31,99
 
2025-07-16 21:01:31,472  [INFO] 
 
2025-07-16 21:01:31,473  [INFO] OK
 
2025-07-16 21:01:31,481  [INFO] 
 
2025-07-16 21:01:31,484  [INFO] [D][11:29:33][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:01:31,485  [INFO] 
 
2025-07-16 21:01:31,503  [INFO] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,504  [INFO] OK
 
2025-07-16 21:01:31,514  [INFO] 
 
2025-07-16 21:01:31,517  [INFO] [D][11:29:33][CAT1]tx ret[12] >>> AT+QIACT=1
 
2025-07-16 21:01:31,518  [INFO] 
 
2025-07-16 21:01:31,547  [INFO] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,548  [INFO] OK
 
2025-07-16 21:01:31,558  [INFO] 
 
2025-07-16 21:01:31,565  [INFO] [D][11:29:33][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:01:31,565  [INFO] 
 
2025-07-16 21:01:31,579  [INFO] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,581  [INFO] OK
 
2025-07-16 21:01:31,582  [INFO] 
 
2025-07-16 21:01:31,586  [INFO] [D][11:29:33][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:01:31,810  [INFO] [D][11:29:34][CAT1]opened : 0, 0
 
2025-07-16 21:01:31,813  [INFO] [D][11:29:34][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:31,819  [INFO] [D][11:29:34][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:01:31,824  [INFO] [D][11:29:34][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:01:31,829  [INFO] [D][11:29:34][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
 
2025-07-16 21:01:31,832  [INFO] [D][11:29:34][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:31,837  [INFO] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:31,840  [INFO] [D][11:29:34][PROT]index:5 1730201374
 
2025-07-16 21:01:31,843  [INFO] [D][11:29:34][PROT]is_send:0
 
2025-07-16 21:01:31,846  [INFO] [D][11:29:34][PROT]sequence_num:9
 
2025-07-16 21:01:31,848  [INFO] [D][11:29:34][PROT]retry_timeout:0
 
2025-07-16 21:01:31,851  [INFO] [D][11:29:34][PROT]retry_times:1
 
2025-07-16 21:01:31,854  [INFO] [D][11:29:34][PROT]send_path:0x2
 
2025-07-16 21:01:31,860  [INFO] [D][11:29:34][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:01:31,868  [INFO] [D][11:29:34][PROT]===========================================================
 
2025-07-16 21:01:31,874  [INFO] [W][11:29:34][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201374]
 
2025-07-16 21:01:31,880  [INFO] [D][11:29:34][PROT]===========================================================
 
2025-07-16 21:01:31,884  [INFO] [D][11:29:34][COMM]PB encode data:29
 
2025-07-16 21:01:31,887  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:01:31,892  [INFO] [D][11:29:34][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:31,898  [INFO] [D][11:29:34][PROT]Send_TO_M2M [1730201374]
 
2025-07-16 21:01:31,901  [INFO] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:31,907  [INFO] [D][11:29:34][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:31,910  [INFO] [D][11:29:34][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:31,912  [INFO] [D][11:29:34][M2M ]m2m send data len[134]
 
2025-07-16 21:01:31,919  [INFO] [D][11:29:34][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:31,927  [INFO] [D][11:29:34][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:01:31,929  [INFO] [D][11:29:34][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:31,934  [INFO] [D][11:29:34][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:01:31,935  [INFO] 
 
2025-07-16 21:01:31,940  [INFO] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:31,946  [INFO] [D][11:29:34][CAT1]Send Data To Server[134][134] ... ->:
 
2025-07-16 21:01:31,958  [INFO] 0043B6A1113311331133113311331B88B56BAE37795C88C027274E8E79401037039DA9D0B83BE4D11AD57A17CF645D906C3FCA1D48EE9248AD8A1E44A16AB0EEB81DCE
 
2025-07-16 21:01:31,960  [INFO] [D][11:29:34][CAT1]<<< 
 
2025-07-16 21:01:31,961  [INFO] SEND OK
 
2025-07-16 21:01:31,961  [INFO] 
 
2025-07-16 21:01:31,965  [INFO] [D][11:29:34][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:31,968  [INFO] [D][11:29:34][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:31,969  [INFO] 
 
2025-07-16 21:01:31,971  [INFO] [D][11:29:34][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:31,979  [INFO] [D][11:29:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:31,981  [INFO] [D][11:29:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:31,984  [INFO] [D][11:29:34][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:31,990  [INFO] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:31,994  [INFO] [D][11:29:34][PROT]M2M Send ok [1730201374]
 
2025-07-16 21:01:37,223  [INFO] [D][11:29:39][PROT]CLEAN,SEND:5
 
2025-07-16 21:01:37,234  [INFO] [D][11:29:39][PROT]CLEAN:5
 
2025-07-16 21:01:37,247  [INFO] [D][11:29:39][PROT]index:4 1730201379
 
2025-07-16 21:01:37,249  [INFO] [D][11:29:39][PROT]is_send:0
 
2025-07-16 21:01:37,252  [INFO] [D][11:29:39][PROT]sequence_num:8
 
2025-07-16 21:01:37,255  [INFO] [D][11:29:39][PROT]retry_timeout:0
 
2025-07-16 21:01:37,257  [INFO] [D][11:29:39][PROT]retry_times:1
 
2025-07-16 21:01:37,260  [INFO] [D][11:29:39][PROT]send_path:0x2
 
2025-07-16 21:01:37,266  [INFO] [D][11:29:39][PROT]min_index:4, type:0x5006, priority:2
 
2025-07-16 21:01:37,272  [INFO] [D][11:29:39][PROT]===========================================================
 
2025-07-16 21:01:37,278  [INFO] [W][11:29:39][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730201379]
 
2025-07-16 21:01:37,286  [INFO] [D][11:29:39][PROT]===========================================================
 
2025-07-16 21:01:37,288  [INFO] [D][11:29:39][COMM]PB encode data:39
 
2025-07-16 21:01:37,297  [INFO] 0A25080210841E18C8012000280030003800401F4818600070018001B0228A0106000000000000
 
2025-07-16 21:01:37,299  [INFO] [D][11:29:39][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:37,305  [INFO] [D][11:29:39][PROT]Send_TO_M2M [1730201379]
 
2025-07-16 21:01:37,308  [INFO] [D][11:29:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:37,314  [INFO] [D][11:29:39][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:37,316  [INFO] [D][11:29:39][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:37,322  [INFO] [D][11:29:39][M2M ]m2m send data len[134]
 
2025-07-16 21:01:37,324  [INFO] [D][11:29:39][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:37,333  [INFO] [D][11:29:39][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:01:37,339  [INFO] [D][11:29:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:37,341  [INFO] [D][11:29:39][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:37,347  [INFO] [D][11:29:39][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:01:37,348  [INFO] 
 
2025-07-16 21:01:37,352  [INFO] [D][11:29:39][CAT1]Send Data To Server[134][134] ... ->:
 
2025-07-16 21:01:37,364  [INFO] 0043B6A2113311331133113311331B88B52AC52146B2A001AAF8CC7DB585F95405AC5F42EA4B8B8CFCDE79A8482A36E2CED0B37FB9324CF2FA48EF05495E9EF01AA645
 
2025-07-16 21:01:37,365  [INFO] [D][11:29:39][CAT1]<<< 
 
2025-07-16 21:01:37,367  [INFO] SEND OK
 
2025-07-16 21:01:37,368  [INFO] 
 
2025-07-16 21:01:37,373  [INFO] [D][11:29:39][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:37,375  [INFO] [D][11:29:39][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:37,375  [INFO] 
 
2025-07-16 21:01:37,380  [INFO] [D][11:29:39][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:37,386  [INFO] [D][11:29:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:37,389  [INFO] [D][11:29:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:37,395  [INFO] [D][11:29:39][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:37,397  [INFO] [D][11:29:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:37,402  [INFO] [D][11:29:39][PROT]M2M Send ok [1730201379]
 
2025-07-16 21:01:41,550  [INFO] [D][11:29:43][COMM]Main Task receive event:99
 
2025-07-16 21:01:41,555  [INFO] [D][11:29:43][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:01:41,564  [INFO] [D][11:29:43][COMM]handlerPeriodWakeup, g_elecBatMissedCount = 1, time = 1730201383, allstateRepSeconds = 1730199600
 
2025-07-16 21:01:41,569  [INFO] [D][11:29:43][COMM]Main Task receive event:99 finished processing
 
2025-07-16 21:01:41,576  [INFO] [D][11:29:43][COMM]Main Task receive event:146
 
2025-07-16 21:01:41,578  [INFO] [D][11:29:43][COMM]a:13,b:0,c:0,d:0,e:0
 
2025-07-16 21:01:41,586  [INFO] [W][11:29:43][PROT]remove success[1730201383],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:01:41,594  [INFO] [W][11:29:43][PROT]add success [1730201383],send_path[2],type[C001],priority[0],index[5],used[1]
 
2025-07-16 21:01:41,602  [INFO] [D][11:29:43][COMM]Main Task receive event:146 finished processing
 
2025-07-16 21:01:42,643  [INFO] [D][11:29:44][PROT]CLEAN,SEND:4
 
2025-07-16 21:01:42,653  [INFO] [D][11:29:44][PROT]CLEAN:4
 
2025-07-16 21:01:42,662  [INFO] [D][11:29:44][COMM]Main Task receive event:79
 
2025-07-16 21:01:42,667  [INFO] [D][11:29:44][COMM]Receive protocol ACK TIMEOUT event
 
2025-07-16 21:01:42,672  [INFO] [D][11:29:44][COMM]Main Task receive event:79 finished processing
 
2025-07-16 21:01:42,674  [INFO] [D][11:29:44][PROT]index:1 1730201384
 
2025-07-16 21:01:42,677  [INFO] [D][11:29:44][PROT]is_send:0
 
2025-07-16 21:01:42,680  [INFO] [D][11:29:44][PROT]sequence_num:4
 
2025-07-16 21:01:42,683  [INFO] [D][11:29:44][PROT]retry_timeout:0
 
2025-07-16 21:01:42,686  [INFO] [D][11:29:44][PROT]retry_times:1
 
2025-07-16 21:01:42,688  [INFO] [D][11:29:44][PROT]send_path:0x2
 
2025-07-16 21:01:42,694  [INFO] [D][11:29:44][PROT]min_index:1, type:0x6A01, priority:0
 
2025-07-16 21:01:42,703  [INFO] [D][11:29:44][PROT]===========================================================
 
2025-07-16 21:01:42,708  [INFO] [W][11:29:44][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201384]
 
2025-07-16 21:01:42,714  [INFO] [D][11:29:44][PROT]===========================================================
 
2025-07-16 21:01:42,716  [INFO] [D][11:29:44][PROT]sending traceid [00]
 
2025-07-16 21:01:42,722  [INFO] [D][11:29:44][PROT]Send_TO_M2M [1730201384]
 
2025-07-16 21:01:42,727  [INFO] [D][11:29:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:42,730  [INFO] [D][11:29:44][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:42,733  [INFO] [D][11:29:44][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:42,739  [INFO] [D][11:29:44][M2M ]m2m send data len[102]
 
2025-07-16 21:01:42,742  [INFO] [D][11:29:44][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:42,750  [INFO] [D][11:29:44][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:01:42,756  [INFO] [D][11:29:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:42,758  [INFO] [D][11:29:44][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:42,765  [INFO] [D][11:29:44][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:42,765  [INFO] 
 
2025-07-16 21:01:42,769  [INFO] [D][11:29:44][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:42,778  [INFO] 0033B9AE113311331188BB88BB88BB88BBF06C04C5E4555FC197BB038B7E9658D300C431315FE496FC4B8956E9C629C932A150
 
2025-07-16 21:01:42,781  [INFO] [D][11:29:44][CAT1]<<< 
 
2025-07-16 21:01:42,782  [INFO] SEND OK
 
2025-07-16 21:01:42,783  [INFO] 
 
2025-07-16 21:01:42,787  [INFO] [D][11:29:44][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:42,789  [INFO] [D][11:29:44][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:42,790  [INFO] 
 
2025-07-16 21:01:42,795  [INFO] [D][11:29:44][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:42,800  [INFO] [D][11:29:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:42,802  [INFO] [D][11:29:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:42,808  [INFO] [D][11:29:44][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:42,811  [INFO] [D][11:29:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:42,816  [INFO] [D][11:29:44][PROT]M2M Send ok [1730201384]
 
2025-07-16 21:01:48,058  [INFO] [D][11:29:50][PROT]CLEAN,SEND:1
 
2025-07-16 21:01:48,069  [INFO] [D][11:29:50][PROT]CLEAN:1
 
2025-07-16 21:01:48,081  [INFO] [D][11:29:50][PROT]index:2 1730201390
 
2025-07-16 21:01:48,084  [INFO] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,087  [INFO] [D][11:29:50][PROT]sequence_num:5
 
2025-07-16 21:01:48,090  [INFO] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,093  [INFO] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,096  [INFO] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,101  [INFO] [D][11:29:50][PROT]min_index:2, type:0x5A07, priority:0
 
2025-07-16 21:01:48,107  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,113  [INFO] [W][11:29:50][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,121  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,124  [INFO] [D][11:29:50][COMM]PB encode data:176
 
2025-07-16 21:01:48,157  [INFO] 0AAD010AAA01180020FBFF0128D2B883B906300340076812A801ED8C83B906F801008A0209082110221835209A018A020908171021182D208C028A0209080D101F183320DD018A02090826101F182520BF018A0209082110171835209A018A020908171015182D208C028A020908261014182520BF018A020808001000180020008A020808001000180020008A020808001000180020008A020808001000180020008A02080800100018002000C00200
 
2025-07-16 21:01:48,160  [INFO] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,162  [INFO] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,168  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,170  [INFO] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,176  [INFO] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,179  [INFO] [D][11:29:50][M2M ]m2m send data len[422]
 
2025-07-16 21:01:48,185  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,194  [INFO] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[422], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,196  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,202  [INFO] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,204  [INFO] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,422
 
2025-07-16 21:01:48,205  [INFO] 
 
2025-07-16 21:01:48,210  [INFO] [D][11:29:50][CAT1]Send Data To Server[422][425] ... ->:
 
2025-07-16 21:01:48,253  [INFO] 00D3B6A3113311331133113311331B88B5B35115295C0DAAAD066FF44A0CE591A4E5ED42787E94FC6B77C235C930CD080F517D4AF65DF7101F935F75A1618022403DB36AB50EACAD8A9986875571596B0FBB4B4312A7897C335DD468C41EF2E93E244D324B6FA7AD42C7E086785DFC1E6D62FE7D36AA5298C5D4C92378AB4C41BE4F21B1EE14287DD0178EDDF979C92BADBA325589519570882F226128E552E135E307DD466583E0AF7E1CF774F0EEB5DC45EFD40F3073BE536FDFFE3CAED74F2ED27859512CCB0CF43F10FC88F4CA49BCBE3D
 
2025-07-16 21:01:48,254  [INFO] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,255  [INFO] SEND OK
 
2025-07-16 21:01:48,255  [INFO] 
 
2025-07-16 21:01:48,256  [INFO] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,261  [INFO] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,262  [INFO] 
 
2025-07-16 21:01:48,262  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,268  [INFO] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,273  [INFO] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,277  [INFO] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,283  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,285  [INFO] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,288  [INFO] [D][11:29:50][PROT]CLEAN:2
 
2025-07-16 21:01:48,290  [INFO] [D][11:29:50][PROT]index:3 1730201390
 
2025-07-16 21:01:48,293  [INFO] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,296  [INFO] [D][11:29:50][PROT]sequence_num:7
 
2025-07-16 21:01:48,301  [INFO] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,304  [INFO] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,307  [INFO] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,309  [INFO] [D][11:29:50][PROT]min_index:3, type:0xC001, priority:0
 
2025-07-16 21:01:48,319  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,324  [INFO] [W][11:29:50][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,332  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,335  [INFO] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,341  [INFO] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,344  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,349  [INFO] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,352  [INFO] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,357  [INFO] [D][11:29:50][M2M ]m2m send data len[102]
 
2025-07-16 21:01:48,360  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,369  [INFO] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,371  [INFO] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,377  [INFO] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:48,378  [INFO] 
 
2025-07-16 21:01:48,382  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,387  [INFO] [D][11:29:50][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:48,397  [INFO] 0033B9A5113311331133113311331B88B5FF233ED25B3956931B2067A77C2CB15ADA431A6E6C4A36F9E83E53F660D7D04F5143
 
2025-07-16 21:01:48,398  [INFO] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,399  [INFO] SEND OK
 
2025-07-16 21:01:48,399  [INFO] 
 
2025-07-16 21:01:48,405  [INFO] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,407  [INFO] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,408  [INFO] 
 
2025-07-16 21:01:48,410  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,420  [INFO] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,421  [INFO] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,427  [INFO] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,430  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,436  [INFO] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,438  [INFO] [D][11:29:50][PROT]CLEAN:3
 
2025-07-16 21:01:48,440  [INFO] [D][11:29:50][PROT]index:6 1730201390
 
2025-07-16 21:01:48,443  [INFO] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,446  [INFO] [D][11:29:50][PROT]sequence_num:10
 
2025-07-16 21:01:48,449  [INFO] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,451  [INFO] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,454  [INFO] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,460  [INFO] [D][11:29:50][PROT]min_index:6, type:0xFF0E, priority:0
 
2025-07-16 21:01:48,467  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,471  [INFO] [W][11:29:50][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,481  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,485  [INFO] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,488  [INFO] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,493  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,496  [INFO] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,502  [INFO] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,504  [INFO] [D][11:29:50][M2M ]m2m send data len[166]
 
2025-07-16 21:01:48,507  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,517  [INFO] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,521  [INFO] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,525  [INFO] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,166
 
2025-07-16 21:01:48,526  [INFO] 
 
2025-07-16 21:01:48,530  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,535  [INFO] [D][11:29:50][CAT1]Send Data To Server[166][169] ... ->:
 
2025-07-16 21:01:48,551  [INFO] 0053B9A6113311331133113311331B88B5675E855DF7BE53CD724DFC32697E88A9E2234602B03E99440BA317A3A33881970F7A9E22C6E8DF4A05B8A22D9BA1F351AE84B68873C9D65381D36EED33CD48DBA4A9
 
2025-07-16 21:01:48,552  [INFO] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,553  [INFO] SEND OK
 
2025-07-16 21:01:48,553  [INFO] 
 
2025-07-16 21:01:48,558  [INFO] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,560  [INFO] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,561  [INFO] 
 
2025-07-16 21:01:48,566  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,572  [INFO] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,577  [INFO] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,580  [INFO] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,587  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,588  [INFO] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,591  [INFO] [D][11:29:50][PROT]CLEAN:6
 
2025-07-16 21:01:48,594  [INFO] [D][11:29:50][PROT]index:7 1730201390
 
2025-07-16 21:01:48,597  [INFO] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,599  [INFO] [D][11:29:50][PROT]sequence_num:11
 
2025-07-16 21:01:48,602  [INFO] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,605  [INFO] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,608  [INFO] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,613  [INFO] [D][11:29:50][PROT]min_index:7, type:0xC001, priority:0
 
2025-07-16 21:01:48,623  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,629  [INFO] [W][11:29:50][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,633  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,639  [INFO] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,641  [INFO] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,647  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,652  [INFO] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,655  [INFO] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,658  [INFO] [D][11:29:50][M2M ]m2m send data len[230]
 
2025-07-16 21:01:48,663  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,673  [INFO] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,674  [INFO] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,680  [INFO] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:01:48,681  [INFO] 
 
2025-07-16 21:01:48,686  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,689  [INFO] [D][11:29:50][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:01:48,712  [INFO] 0073B9A9113311331133113311331B88B582D2A4803F76DE5A81BF7E29494687B66290C1F0977189197DC85445B9C4E8446657994FA2454DDD84A5CBBA06F6E07501FFDA23B2D8B36ACA05EB19DCD1201E166A43885E5520CBBE0F43D6EB5226A97CB4F05B0A844B1CB802D49C50D5CD3DDC0B
 
2025-07-16 21:01:48,713  [INFO] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,714  [INFO] SEND OK
 
2025-07-16 21:01:48,714  [INFO] 
 
2025-07-16 21:01:48,716  [INFO] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,723  [INFO] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,724  [INFO] 
 
2025-07-16 21:01:48,725  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,734  [INFO] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,735  [INFO] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,739  [INFO] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,744  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,747  [INFO] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,749  [INFO] [D][11:29:50][PROT]CLEAN:7
 
2025-07-16 21:01:48,755  [INFO] [D][11:29:50][PROT]index:8 1730201390
 
2025-07-16 21:01:48,756  [INFO] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,762  [INFO] [D][11:29:50][PROT]sequence_num:12
 
2025-07-16 21:01:48,764  [INFO] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,767  [INFO] [D][11:29:50][PROT]retry_times:3
 
2025-07-16 21:01:48,770  [INFO] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,775  [INFO] [D][11:29:50][PROT]min_index:8, type:0xD302, priority:0
 
2025-07-16 21:01:48,781  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,789  [INFO] [D][11:29:50][HSDK][0] flush to flash addr:[0xE46300] --- write len --- [256]
 
2025-07-16 21:01:48,795  [INFO] [W][11:29:50][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,800  [INFO] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,802  [INFO] [D][11:29:50][COMM]PB encode data:11
 
2025-07-16 21:01:48,805  [INFO] 0A0908011A013030013A00
 
2025-07-16 21:01:48,811  [INFO] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,813  [INFO] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,819  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,822  [INFO] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,828  [INFO] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,831  [INFO] [D][11:29:50][M2M ]m2m send data len[102]
 
2025-07-16 21:01:48,836  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,845  [INFO] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,847  [INFO] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,853  [INFO] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:48,855  [INFO] 
 
2025-07-16 21:01:48,856  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,861  [INFO] [D][11:29:50][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:48,873  [INFO] 0033B6A7113311331133113311331B88B57300823AA2FB17BE3A0C7398E9119E48250809815A8EA75237B0789DCDB554A84213
 
2025-07-16 21:01:48,874  [INFO] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,875  [INFO] SEND OK
 
2025-07-16 21:01:48,875  [INFO] 
 
2025-07-16 21:01:48,878  [INFO] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,884  [INFO] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,884  [INFO] 
 
2025-07-16 21:01:48,887  [INFO] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,893  [INFO] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,896  [INFO] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,901  [INFO] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,906  [INFO] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,910  [INFO] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:53,545  [INFO] [D][11:29:55][COMM]Main Task receive event:7
 
2025-07-16 21:01:53,551  [INFO] [D][11:29:55][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:01:53,560  [INFO] [D][11:29:55][COMM]handlerPeriodRep, g_elecBatMissedCount = 1, time = 1730201395, allstateRepSeconds = 1730199600
 
2025-07-16 21:01:53,562  [INFO] [D][11:29:55][COMM]Open GPS Module...
 
2025-07-16 21:01:53,564  [INFO] [D][11:29:55][GNSS]start event:3
 
2025-07-16 21:01:53,570  [INFO] [D][11:29:55][GNSS]GPS start. ret=0
 
2025-07-16 21:01:53,572  [INFO] [W][11:29:55][GNSS]start sing locating
 
2025-07-16 21:01:53,579  [INFO] [D][11:29:55][GNSS]gps single mode only, do wifi scan.
 
2025-07-16 21:01:53,581  [INFO] [D][11:29:55][COMM]index:0,power_mode:0xFF
 
2025-07-16 21:01:53,584  [INFO] [D][11:29:55][COMM]index:1,sound_mode:0xFF
 
2025-07-16 21:01:53,590  [INFO] [D][11:29:55][COMM]index:2,gsensor_mode:0xFF
 
2025-07-16 21:01:53,592  [INFO] [D][11:29:55][COMM]index:3,report_freq_mode:0xFF
 
2025-07-16 21:01:53,597  [INFO] [D][11:29:55][COMM]index:4,report_period:0xFF
 
2025-07-16 21:01:53,600  [INFO] [D][11:29:55][COMM]index:5,normal_reset_mode:0xFF
 
2025-07-16 21:01:53,607  [INFO] [D][11:29:55][COMM]index:6,normal_reset_period:0xFF
 
2025-07-16 21:01:53,612  [INFO] [D][11:29:55][COMM]index:7,spock_over_speed:0xFF
 
2025-07-16 21:01:53,615  [INFO] [D][11:29:55][COMM]index:8,spock_limit_speed:0xFF
 
2025-07-16 21:01:53,620  [INFO] [D][11:29:55][COMM]index:9,spock_report_period_unlock:0xFF
 
2025-07-16 21:01:53,626  [INFO] [D][11:29:55][COMM]index:10,spock_report_period_unlock_unit:0xFF
 
2025-07-16 21:01:53,631  [INFO] [D][11:29:55][COMM]index:11,ble_scan_mode:0xFF
 
2025-07-16 21:01:53,633  [INFO] [D][11:29:55][COMM]index:12,ble_adv_mode:0xFF
 
2025-07-16 21:01:53,639  [INFO] [D][11:29:55][COMM]index:13,spock_audio_volumn:0xFF
 
2025-07-16 21:01:53,644  [INFO] [D][11:29:55][COMM]index:14,spock_low_bat_alarm_soc:0xFF
 
2025-07-16 21:01:53,648  [INFO] [D][11:29:55][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:01:53,650  [INFO] [D][11:29:55][COMM]index:15,bat_auth_mode:0xFF
 
2025-07-16 21:01:53,656  [INFO] [D][11:29:55][COMM]index:16,imu_config_params:0xFF
 
2025-07-16 21:01:53,662  [INFO] [D][11:29:55][COMM]index:17,long_connect_params:0xFF
 
2025-07-16 21:01:53,665  [INFO] [D][11:29:55][COMM]index:18,detain_mark:0xFF
 
2025-07-16 21:01:53,670  [INFO] [D][11:29:55][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:01:53,671  [INFO] 
 
2025-07-16 21:01:53,676  [INFO] [D][11:29:55][COMM]index:19,lock_pos_report_count:0xFF
 
2025-07-16 21:01:53,681  [INFO] [D][11:29:55][COMM]index:20,lock_pos_report_interval:0xFF
 
2025-07-16 21:01:53,684  [INFO] [D][11:29:55][COMM]index:21,mc_mode:0xFF
 
2025-07-16 21:01:53,687  [INFO] [D][11:29:55][COMM]index:22,S_mode:0xFF
 
2025-07-16 21:01:53,692  [INFO] [D][11:29:55][COMM]index:23,overweight:0xFF
 
2025-07-16 21:01:53,696  [INFO] [D][11:29:55][COMM]index:24,standstill_mode:0xFF
 
2025-07-16 21:01:53,701  [INFO] [D][11:29:55][COMM]index:25,night_mode:0xFF
 
2025-07-16 21:01:53,704  [INFO] [D][11:29:55][COMM]index:26,experiment1:0xFF
 
2025-07-16 21:01:53,707  [INFO] [D][11:29:55][COMM]index:27,experiment2:0xFF
 
2025-07-16 21:01:53,712  [INFO] [D][11:29:55][COMM]index:28,experiment3:0xFF
 
2025-07-16 21:01:53,715  [INFO] [D][11:29:55][COMM]index:29,experiment4:0xFF
 
2025-07-16 21:01:53,720  [INFO] [D][11:29:55][COMM]index:30,night_mode_start:0xFF
 
2025-07-16 21:01:53,723  [INFO] [D][11:29:55][COMM]index:31,night_mode_end:0xFF
 
2025-07-16 21:01:53,728  [INFO] [D][11:29:55][COMM]index:33,park_report_minutes:0xFF
 
2025-07-16 21:01:53,734  [INFO] [D][11:29:55][COMM]index:34,park_report_mode:0xFF
 
2025-07-16 21:01:53,740  [INFO] [D][11:29:55][COMM]index:35,mc_undervoltage_protection:0xFF
 
2025-07-16 21:01:53,743  [INFO] [D][11:29:55][COMM]index:38,charge_battery_para: FF
 
2025-07-16 21:01:53,748  [INFO] [D][11:29:55][COMM]index:39,multirider_mode:0xFF
 
2025-07-16 21:01:53,751  [INFO] [D][11:29:55][COMM]index:40,mc_launch_mode:0xFF
 
2025-07-16 21:01:53,757  [INFO] [D][11:29:55][COMM]index:41,head_light_enable_mode:0xFF
 
2025-07-16 21:01:53,762  [INFO] [D][11:29:55][COMM]index:42,set_time_ble_mode_begin_min:0xFF
 
2025-07-16 21:01:53,767  [INFO] [D][11:29:55][COMM]index:43,set_time_ble_mode_end_min:0xFF
 
2025-07-16 21:01:53,773  [INFO] [D][11:29:55][COMM]index:44,riding_duration_config:0xFF
 
2025-07-16 21:01:53,776  [INFO] [D][11:29:55][COMM]index:45,camera_park_angle_cfg:0xFF
 
2025-07-16 21:01:53,782  [INFO] [D][11:29:55][COMM]index:46,camera_park_type_cfg:0xFF
 
2025-07-16 21:01:53,787  [INFO] [D][11:29:55][COMM]index:47,bat_info_rep_cfg:0xFF
 
2025-07-16 21:01:53,790  [INFO] [D][11:29:55][COMM]index:48,shlmt_sensor_en:0xFF
 
2025-07-16 21:01:53,795  [INFO] [D][11:29:55][COMM]index:49,mc_load_startup:0xFF
 
2025-07-16 21:01:53,798  [INFO] [D][11:29:55][COMM]index:50,mc_tcs_mode:0xFF
 
2025-07-16 21:01:53,803  [INFO] [D][11:29:55][COMM]index:51,traffic_audio_play:0xFF
 
2025-07-16 21:01:53,806  [INFO] [D][11:29:55][COMM]index:52,traffic_mode:0xFF
 
2025-07-16 21:01:53,812  [INFO] [D][11:29:55][COMM]index:53,traffic_info_collect_freq:0xFF
 
2025-07-16 21:01:53,818  [INFO] [D][11:29:55][COMM]index:54,traffic_security_model_cycle:0xFF
 
2025-07-16 21:01:53,823  [INFO] [D][11:29:55][COMM]index:55,wheel_alarm_play_switch:255
 
2025-07-16 21:01:53,829  [INFO] [D][11:29:55][COMM]index:57,traffic_sens_cycle:0xFF
 
2025-07-16 21:01:53,834  [INFO] [D][11:29:55][COMM]index:58,traffic_light_threshold:0xFF
 
2025-07-16 21:01:53,840  [INFO] [D][11:29:55][COMM]index:59,traffic_retrograde_threshold:0xFF
 
2025-07-16 21:01:53,843  [INFO] [D][11:29:55][COMM]index:60,traffic_road_threshold:0xFF
 
2025-07-16 21:01:53,849  [INFO] [D][11:29:55][COMM]index:61,traffic_sens_threshold:0xFF
 
2025-07-16 21:01:53,854  [INFO] [D][11:29:55][COMM]index:63,experiment5:0xFF
 
2025-07-16 21:01:53,856  [INFO] [D][11:29:55][COMM]index:64,camera_park_markline_cfg:0xFF
 
2025-07-16 21:01:53,859  [INFO] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:53,872  [INFO] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"
 
2025-07-16 21:01:53,873  [INFO] 
 
2025-07-16 21:01:53,873  [INFO] OK
 
2025-07-16 21:01:53,874  [INFO] 
 
2025-07-16 21:01:53,876  [INFO] [D][11:29:55][COMM]index:65,camera_park_fenceline_cfg:0xFF
 
2025-07-16 21:01:53,882  [INFO] [D][11:29:55][COMM]index:66,camera_park_distance_cfg:0xFF
 
2025-07-16 21:01:53,888  [INFO] [D][11:29:55][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
 
2025-07-16 21:01:53,893  [INFO] [D][11:29:55][COMM]index:68,camera_park_ps_cfg:0xFFFF
 
2025-07-16 21:01:53,895  [INFO] [D][11:29:55][COMM]index:70,camera_park_light_cfg:0xFF
 
2025-07-16 21:01:53,902  [INFO] [D][11:29:55][COMM]index:71,camera_park_self_check_cfg:0xFF
 
2025-07-16 21:01:53,907  [INFO] [D][11:29:55][COMM]index:72,experiment6:0xFF
 
2025-07-16 21:01:53,910  [INFO] [D][11:29:55][COMM]index:73,experiment7:0xFF
 
2025-07-16 21:01:53,915  [INFO] [D][11:29:55][COMM]index:74,load_messurement_cfg:0xff
 
2025-07-16 21:01:53,920  [INFO] [D][11:29:55][COMM]index:75,zero_value_from_server:-1
 
2025-07-16 21:01:53,923  [INFO] [D][11:29:55][COMM]index:76,multirider_threshold:255
 
2025-07-16 21:01:53,930  [INFO] [D][11:29:55][COMM]index:77,experiment8:255
 
2025-07-16 21:01:53,935  [INFO] [D][11:29:55][COMM]index:78,temp_park_audio_play_duration:255
 
2025-07-16 21:01:53,941  [INFO] [D][11:29:55][COMM]index:79,temp_park_tail_light_twinkle_duration:255
 
2025-07-16 21:01:53,946  [INFO] [D][11:29:55][COMM]index:80,temp_park_reminder_timeout_duration:255
 
2025-07-16 21:01:53,951  [INFO] [D][11:29:55][COMM]index:82,loc_report_low_speed_thr:255
 
2025-07-16 21:01:53,954  [INFO] [D][11:29:55][COMM]index:83,loc_report_interval:255
 
2025-07-16 21:01:53,959  [INFO] [D][11:29:55][COMM]index:84,multirider_threshold_p2:255
 
2025-07-16 21:01:53,965  [INFO] [D][11:29:55][COMM]index:85,multirider_strategy:255
 
2025-07-16 21:01:53,971  [INFO] [D][11:29:55][COMM]index:81,camera_park_similar_thr_cfg:0xFF
 
2025-07-16 21:01:53,976  [INFO] [D][11:29:55][COMM]index:86,camera_park_self_check_period_cfg:0xFF
 
2025-07-16 21:01:53,979  [INFO] [D][11:29:55][COMM]index:96,rope_sensor_cfg:0xFF
 
2025-07-16 21:01:53,985  [INFO] [D][11:29:55][COMM]index:90,weight_param:0xFF
 
2025-07-16 21:01:53,990  [INFO] [D][11:29:55][COMM]index:93,lock_anti_theft_mode:0xFF
 
2025-07-16 21:01:53,994  [INFO] [D][11:29:55][CAT1]tx ret[12] >>> AT+GPSCFG?
 
2025-07-16 21:01:53,995  [INFO] 
 
2025-07-16 21:01:53,998  [INFO] [D][11:29:55][COMM]index:94,high_temp_alarm_count:0xFF
 
2025-07-16 21:01:54,001  [INFO] [D][11:29:55][COMM]index:95,current_limit:0xFF
 
2025-07-16 21:01:54,010  [INFO] [D][11:29:55][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
 
2025-07-16 21:01:54,016  [INFO] [D][11:29:55][COMM]index:104,brake_release_protect:0xFF
 
2025-07-16 21:01:54,017  [INFO] 
 
2025-07-16 21:01:54,018  [INFO] [D][11:29:55][COMM]index:100,location_mode:0xFF
 
2025-07-16 21:01:54,024  [INFO] [D][11:29:55][COMM]index:110,display_speed_limit:255
 
2025-07-16 21:01:54,029  [INFO] [D][11:29:55][COMM]index:105,vo_unload_thr:0xFFFF
 
2025-07-16 21:01:54,032  [INFO] [D][11:29:55][COMM]index:107,vo_loading_angle_thr:0xFF
 
2025-07-16 21:01:54,037  [INFO] [D][11:29:55][COMM]index:108,vo_func_switch:0xFF
 
2025-07-16 21:01:54,046  [INFO] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:01:54,055  [INFO] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4205],priority[0],index[1],used[1]
 
2025-07-16 21:01:54,063  [INFO] [D][11:29:55][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:01:54,066  [INFO] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:01:54,071  [INFO] [D][11:29:55][COMM]report park limit config
 
2025-07-16 21:01:54,080  [INFO] [D][11:29:55][COMM]report park limit config:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:01:54,082  [INFO] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:01:54,085  [INFO] [D][11:29:55][COMM]
 
2025-07-16 21:01:54,094  [INFO] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:01:54,102  [INFO] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4701],priority[0],index[2],used[1]
 
2025-07-16 21:01:54,110  [INFO] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:01:54,121  [INFO] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4705],priority[0],index[3],used[1]
 
2025-07-16 21:01:54,124  [INFO] [D][11:29:55][COMM]bledev scan is invalid, will return
 
2025-07-16 21:01:54,130  [INFO] [D][11:29:55][COMM]------>period, report file manifest
 
2025-07-16 21:01:54,135  [INFO] [D][11:29:55][COMM]Main Task receive event:7 finished processing
 
2025-07-16 21:01:54,138  [INFO] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,141  [INFO] +GPSCFG:0,0,115200,1,0,65472,0,1,1
 
2025-07-16 21:01:54,141  [INFO] 
 
2025-07-16 21:01:54,142  [INFO] OK
 
2025-07-16 21:01:54,144  [INFO] 
 
2025-07-16 21:01:54,146  [INFO] [D][11:29:55][CAT1]tx ret[14] >>> AT+GPSPORT=1
 
2025-07-16 21:01:54,148  [INFO] 
 
2025-07-16 21:01:54,149  [INFO] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,150  [INFO] OK
 
2025-07-16 21:01:54,150  [INFO] 
 
2025-07-16 21:01:54,152  [INFO] [D][11:29:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1
 
2025-07-16 21:01:54,153  [INFO] 
 
2025-07-16 21:01:54,155  [INFO] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,155  [INFO] OK
 
2025-07-16 21:01:54,156  [INFO] 
 
2025-07-16 21:01:54,160  [INFO] [D][11:29:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0
 
2025-07-16 21:01:54,161  [INFO] 
 
2025-07-16 21:01:54,163  [INFO] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,163  [INFO] OK
 
2025-07-16 21:01:54,164  [INFO] 
 
2025-07-16 21:01:54,165  [INFO] [D][11:29:55][CAT1]tx ret[13] >>> AT+GPSPWR=1
 
2025-07-16 21:01:54,168  [INFO] 
 
2025-07-16 21:01:54,171  [INFO] [D][11:29:56][PROT]CLEAN,SEND:8
 
2025-07-16 21:01:54,174  [INFO] [D][11:29:56][PROT]index:8 1730201396
 
2025-07-16 21:01:54,177  [INFO] [D][11:29:56][PROT]is_send:0
 
2025-07-16 21:01:54,179  [INFO] [D][11:29:56][PROT]sequence_num:12
 
2025-07-16 21:01:54,182  [INFO] [D][11:29:56][PROT]retry_timeout:0
 
2025-07-16 21:01:54,185  [INFO] [D][11:29:56][PROT]retry_times:2
 
2025-07-16 21:01:54,188  [INFO] [D][11:29:56][PROT]send_path:0x2
 
2025-07-16 21:01:54,194  [INFO] [D][11:29:56][PROT]min_index:8, type:0xD302, priority:0
 
2025-07-16 21:01:54,201  [INFO] [D][11:29:56][PROT]===========================================================
 
2025-07-16 21:01:54,205  [INFO] [W][11:29:56][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201396]
 
2025-07-16 21:01:54,215  [INFO] [D][11:29:56][PROT]===========================================================
 
2025-07-16 21:01:54,216  [INFO] [D][11:29:56][COMM]PB encode data:11
 
2025-07-16 21:01:54,220  [INFO] 0A0908011A013030013A00
 
2025-07-16 21:01:54,225  [INFO] [D][11:29:56][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:54,227  [INFO] [D][11:29:56][PROT]Send_TO_M2M [1730201396]
 
2025-07-16 21:01:54,233  [INFO] [D][11:29:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:54,236  [INFO] [D][11:29:56][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:54,242  [INFO] [D][11:29:56][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:54,244  [INFO] [D][11:29:56][M2M ]m2m send data len[102]
 
2025-07-16 21:01:54,247  [INFO] [D][11:29:56][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:54,257  [INFO] [D][11:29:56][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a2a0] format[0]
 
2025-07-16 21:01:54,263  [INFO] [D][11:29:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:55,148  [INFO] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,150  [INFO] OK
 
2025-07-16 21:01:55,159  [INFO] 
 
2025-07-16 21:01:55,162  [INFO] [D][11:29:57][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F
 
2025-07-16 21:01:55,163  [INFO] 
 
2025-07-16 21:01:55,234  [INFO] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,235  [INFO] OK
 
2025-07-16 21:01:55,244  [INFO] 
 
2025-07-16 21:01:55,248  [INFO] [D][11:29:57][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:01:55,248  [INFO] 
 
2025-07-16 21:01:55,267  [INFO] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,278  [INFO] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"
 
2025-07-16 21:01:55,278  [INFO] 
 
2025-07-16 21:01:55,278  [INFO] OK
 
2025-07-16 21:01:55,279  [INFO] 
 
2025-07-16 21:01:55,282  [INFO] [D][11:29:57][CAT1]tx ret[14] >>> AT+GPSMODE=1
 
2025-07-16 21:01:55,283  [INFO] 
 
2025-07-16 21:01:55,300  [INFO] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,301  [INFO] OK
 
2025-07-16 21:01:55,302  [INFO] 
 
2025-07-16 21:01:55,305  [INFO] [D][11:29:57][CAT1]exec over: func id: 23, ret: 6
 
2025-07-16 21:01:55,309  [INFO] [D][11:29:57][CAT1]sub id: 23, ret: 6
 
2025-07-16 21:01:55,310  [INFO] 
 
2025-07-16 21:01:55,314  [INFO] [D][11:29:57][CAT1]gsm read msg sub id: 26
 
2025-07-16 21:01:55,318  [INFO] [D][11:29:57][CAT1]tx ret[18] >>> AT+WIFISCAN=8,45
 
2025-07-16 21:01:55,320  [INFO] 
 
2025-07-16 21:01:55,334  [INFO] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,335  [INFO] OK
 
2025-07-16 21:01:55,336  [INFO] 
 
2025-07-16 21:01:55,340  [INFO] [D][11:29:57][CAT1]exec over: func id: 26, ret: 6
 
2025-07-16 21:01:55,347  [INFO] [D][11:29:57][CAT1]gsm read msg sub id: 21
 
2025-07-16 21:01:55,351  [INFO] [D][11:29:57][CAT1]tx ret[15] >>> AT+QCELLINFO?
 
2025-07-16 21:01:55,353  [INFO] 
 
2025-07-16 21:01:55,791  [INFO] [D][11:29:58][GNSS]recv submsg id[1]
 
2025-07-16 21:01:55,795  [INFO] [D][11:29:58][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
 
2025-07-16 21:01:55,837  [INFO] [D][11:29:58][CAT1]<<< 
 
2025-07-16 21:01:55,838  [INFO] OK
 
2025-07-16 21:01:55,839  [INFO] 
 
2025-07-16 21:01:55,842  [INFO] [D][11:29:58][CAT1]cell info report total[0]
 
2025-07-16 21:01:55,845  [INFO] [D][11:29:58][CAT1]exec over: func id: 21, ret: 6
 
2025-07-16 21:01:55,851  [INFO] [D][11:29:58][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:55,853  [INFO] [D][11:29:58][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:55,854  [INFO] 
 
2025-07-16 21:01:55,862  [INFO] [D][11:29:58][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:55,873  [INFO] 0033B6A4113311331133113311331B88B53C8D75E4B84C5C2F9AFEFD8A216470BC230BC0993499F6A57EEA5402282B81DC4D03
 
2025-07-16 21:01:55,893  [INFO] [D][11:29:58][CAT1]<<< 
 
2025-07-16 21:01:55,896  [INFO] SEND OK
 
2025-07-16 21:01:55,896  [INFO] 
 
2025-07-16 21:01:55,899  [INFO] [D][11:29:58][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:55,901  [INFO] [D][11:29:58][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:55,904  [INFO] 
 
2025-07-16 21:01:55,908  [INFO] [D][11:29:58][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:55,913  [INFO] [D][11:29:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:55,917  [INFO] [D][11:29:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:55,921  [INFO] [D][11:29:58][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:55,927  [INFO] [D][11:29:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:55,930  [INFO] [D][11:29:58][PROT]M2M Send ok [1730201398]
 
2025-07-16 21:02:01,232  [INFO] [D][11:30:03][PROT]CLEAN,SEND:8
 
2025-07-16 21:02:01,244  [INFO] [D][11:30:03][PROT]index:8 1730201403
 
2025-07-16 21:02:01,247  [INFO] [D][11:30:03][PROT]is_send:0
 
2025-07-16 21:02:01,250  [INFO] [D][11:30:03][PROT]sequence_num:12
 
2025-07-16 21:02:01,253  [INFO] [D][11:30:03][PROT]retry_timeout:0
 
2025-07-16 21:02:01,255  [INFO] [D][11:30:03][PROT]retry_times:1
 
2025-07-16 21:02:01,258  [INFO] [D][11:30:03][PROT]send_path:0x2
 
2025-07-16 21:02:01,264  [INFO] [D][11:30:03][PROT]min_index:8, type:0xD302, priority:0
 
2025-07-16 21:02:01,271  [INFO] [D][11:30:03][PROT]===========================================================
 
2025-07-16 21:02:01,278  [INFO] [D][11:30:03][HSDK][0] flush to flash addr:[0xE46400] --- write len --- [256]
 
2025-07-16 21:02:01,284  [INFO] [W][11:30:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201403]
 
2025-07-16 21:02:01,290  [INFO] [D][11:30:03][PROT]===========================================================
 
2025-07-16 21:02:01,295  [INFO] [D][11:30:03][COMM]PB encode data:11
 
2025-07-16 21:02:01,296  [INFO] 0A0908011A013030013A00
 
2025-07-16 21:02:01,300  [INFO] [D][11:30:03][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:02:01,302  [INFO] [D][11:30:03][PROT]Send_TO_M2M [1730201403]
 
2025-07-16 21:02:01,309  [INFO] [D][11:30:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:01,314  [INFO] [D][11:30:03][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:01,317  [INFO] [D][11:30:03][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:01,319  [INFO] [D][11:30:03][M2M ]m2m send data len[102]
 
2025-07-16 21:02:01,325  [INFO] [D][11:30:03][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:01,334  [INFO] [D][11:30:03][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a010] format[0]
 
2025-07-16 21:02:01,339  [INFO] [D][11:30:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:01,342  [INFO] [D][11:30:03][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:01,347  [INFO] [D][11:30:03][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:02:01,348  [INFO] 
 
2025-07-16 21:02:01,349  [INFO] [D][11:30:03][CAT1]Send Data To Server[102][102] ... ->:
 
2025-07-16 21:02:01,362  [INFO] 0033B6AB113311331133113311331B88B562B7957B38C3C6FCA84C14F72F2518C3DCD271B669702EB09E85CEFFE835E8C2D168
 
2025-07-16 21:02:01,363  [INFO] [D][11:30:03][CAT1]<<< 
 
2025-07-16 21:02:01,363  [INFO] SEND OK
 
2025-07-16 21:02:01,364  [INFO] 
 
2025-07-16 21:02:01,367  [INFO] [D][11:30:03][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:01,372  [INFO] [D][11:30:03][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:01,373  [INFO] 
 
2025-07-16 21:02:01,375  [INFO] [D][11:30:03][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:01,381  [INFO] [D][11:30:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:01,386  [INFO] [D][11:30:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:01,389  [INFO] [D][11:30:03][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:01,395  [INFO] [D][11:30:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:01,398  [INFO] [D][11:30:03][PROT]M2M Send ok [1730201403]
 
2025-07-16 21:02:05,690  [INFO] +WIFISCAN:8,0,FCD733633E16,-34
 
2025-07-16 21:02:05,692  [INFO] +WIFISCAN:8,1,E22E0BF5E7EF,-61
 
2025-07-16 21:02:05,695  [INFO] +WIFISCAN:8,2,A400E2F462C0,-61
 
2025-07-16 21:02:05,697  [INFO] +WIFISCAN:8,3,A400E2F468E1,-77
 
2025-07-16 21:02:05,700  [INFO] +WIFISCAN:8,4,A400E2F468E2,-77
 
2025-07-16 21:02:05,703  [INFO] +WIFISCAN:8,5,A400E2F464E2,-79
 
2025-07-16 21:02:05,706  [INFO] +WIFISCAN:8,6,980D51F3B459,-79
 
2025-07-16 21:02:05,709  [INFO] +WIFISCAN:8,7,A400E2F464E0,-80
 
2025-07-16 21:02:05,711  [INFO] 
 
2025-07-16 21:02:05,715  [INFO] [D][11:30:07][CAT1]wifi scan report total[8]
 
2025-07-16 21:02:05,720  [INFO] [D][11:30:07][CAT1]wifi scan result rpt len[256], retval[256]
 
2025-07-16 21:02:05,832  [INFO] [D][11:30:08][GNSS]recv submsg id[3]
 
2025-07-16 21:02:05,835  [INFO] [D][11:30:08][GNSS]handlerWifiScanDone
 
2025-07-16 21:02:05,841  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[0]mac:FCD733633E16 ssid: rssi:-34
 
2025-07-16 21:02:05,848  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[1]mac:E22E0BF5E7EF ssid: rssi:-61
 
2025-07-16 21:02:05,854  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[2]mac:A400E2F462C0 ssid: rssi:-61
 
2025-07-16 21:02:05,862  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[3]mac:A400E2F468E1 ssid: rssi:-77
 
2025-07-16 21:02:05,868  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[4]mac:A400E2F468E2 ssid: rssi:-77
 
2025-07-16 21:02:05,876  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[5]mac:A400E2F464E2 ssid: rssi:-79
 
2025-07-16 21:02:05,883  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[6]mac:980D51F3B459 ssid: rssi:-79
 
2025-07-16 21:02:05,888  [INFO] [D][11:30:08][GNSS]frm_wifi_scan_callback:[7]mac:A400E2F464E0 ssid: rssi:-80
 
2025-07-16 21:02:05,898  [INFO] [W][11:30:08][PROT]remove success[1730201408],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:02:05,906  [INFO] [W][11:30:08][PROT]add success [1730201408],send_path[2],type[5103],priority[0],index[4],used[1]
 
2025-07-16 21:02:06,079  [INFO] [D][11:30:08][CAT1]closed : 0
 
2025-07-16 21:02:06,084  [INFO] [D][11:30:08][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:02:06,088  [INFO] [D][11:30:08][SAL ]socket closed id[0]
 
2025-07-16 21:02:06,091  [INFO] [D][11:30:08][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:02:06,097  [INFO] [D][11:30:08][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:02:06,100  [INFO] [D][11:30:08][M2M ]m2m select fd[4]
 
2025-07-16 21:02:06,106  [INFO] [D][11:30:08][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:02:06,108  [INFO] [D][11:30:08][M2M ]tcpclient close[4]
 
2025-07-16 21:02:06,111  [INFO] [D][11:30:08][SAL ]socket[4] has closed
 
2025-07-16 21:02:06,116  [INFO] [D][11:30:08][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:02:06,122  [INFO] [D][11:30:08][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 2
 
2025-07-16 21:02:06,187  [INFO] [D][11:30:08][COMM]Main Task receive event:86
 
2025-07-16 21:02:06,193  [INFO] [D][11:30:08][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:02:06,629  [INFO] [D][11:30:08][PROT]CLEAN,SEND:8
 
2025-07-16 21:02:06,641  [INFO] [D][11:30:08][PROT]CLEAN:8
 
2025-07-16 21:02:06,653  [INFO] [D][11:30:08][PROT]index:0 1730201408
 
2025-07-16 21:02:06,656  [INFO] [D][11:30:08][PROT]is_send:0
 
2025-07-16 21:02:06,659  [INFO] [D][11:30:08][PROT]sequence_num:13
 
2025-07-16 21:02:06,662  [INFO] [D][11:30:08][PROT]retry_timeout:0
 
2025-07-16 21:02:06,664  [INFO] [D][11:30:08][PROT]retry_times:1
 
2025-07-16 21:02:06,667  [INFO] [D][11:30:08][PROT]send_path:0x2
 
2025-07-16 21:02:06,672  [INFO] [D][11:30:08][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:02:06,679  [INFO] [D][11:30:08][PROT]===========================================================
 
2025-07-16 21:02:06,684  [INFO] [W][11:30:08][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201408]
 
2025-07-16 21:02:06,693  [INFO] [D][11:30:08][PROT]===========================================================
 
2025-07-16 21:02:06,698  [INFO] [D][11:30:08][PROT]sending traceid [9999999999900006]
 
2025-07-16 21:02:06,701  [INFO] [D][11:30:08][PROT]Send_TO_M2M [1730201408]
 
2025-07-16 21:02:06,706  [INFO] [D][11:30:08][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:06,711  [INFO] [D][11:30:08][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:06,715  [INFO] [D][11:30:08][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:02:06,724  [INFO] [D][11:30:08][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:02:06,726  [INFO] [D][11:30:08][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:02:06,735  [INFO] [D][11:30:08][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:02:06,739  [INFO] [D][11:30:08][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:02:06,742  [INFO] [D][11:30:08][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:02:06,745  [INFO] [D][11:30:08][CAT1]at ops open socket[0]
 
2025-07-16 21:02:06,750  [INFO] [D][11:30:08][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:02:06,751  [INFO] 
 
2025-07-16 21:02:06,756  [INFO] [D][11:30:08][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:02:06,759  [INFO] [D][11:30:08][CAT1]<<< 
 
2025-07-16 21:02:06,760  [INFO] +CGATT: 1
 
2025-07-16 21:02:06,760  [INFO] 
 
2025-07-16 21:02:06,760  [INFO] OK
 
2025-07-16 21:02:06,761  [INFO] 
 
2025-07-16 21:02:06,761  [INFO] [D][11:30:08][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:02:06,763  [INFO] 
 
2025-07-16 21:02:06,765  [INFO] [D][11:30:08][CAT1]<<< 
 
2025-07-16 21:02:06,767  [INFO] +CSQ: 31,99
 
2025-07-16 21:02:06,769  [INFO] 
 
2025-07-16 21:02:06,769  [INFO] OK
 
2025-07-16 21:02:06,769  [INFO] 
 
2025-07-16 21:02:06,771  [INFO] [D][11:30:08][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:02:06,772  [INFO] 
 
2025-07-16 21:02:06,773  [INFO] [D][11:30:08][CAT1]<<< 
 
2025-07-16 21:02:06,777  [INFO] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:02:06,777  [INFO] 
 
2025-07-16 21:02:06,778  [INFO] OK
 
2025-07-16 21:02:06,778  [INFO] 
 
2025-07-16 21:02:06,786  [INFO] [D][11:30:08][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:02:06,787  [INFO] 
 
2025-07-16 21:02:06,787  [INFO] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:06,788  [INFO] OK
 
2025-07-16 21:02:06,789  [INFO] 
 
2025-07-16 21:02:06,793  [INFO] [D][11:30:09][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:02:07,014  [INFO] [D][11:30:09][CAT1]opened : 0, 0
 
2025-07-16 21:02:07,017  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,023  [INFO] [D][11:30:09][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:02:07,028  [INFO] [D][11:30:09][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:02:07,034  [INFO] [D][11:30:09][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:02:07,039  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:07,042  [INFO] [D][11:30:09][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:07,044  [INFO] [D][11:30:09][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:07,051  [INFO] [D][11:30:09][M2M ]m2m send data len[70]
 
2025-07-16 21:02:07,053  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:07,062  [INFO] [D][11:30:09][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:02:07,068  [INFO] [D][11:30:09][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:07,070  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:07,075  [INFO] [D][11:30:09][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:02:07,076  [INFO] 
 
2025-07-16 21:02:07,080  [INFO] [D][11:30:09][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:02:07,087  [INFO] 0023B9F8113311331133113311331B88B397A3E22296667BCF0E7809D8DE4ABBDD5290
 
2025-07-16 21:02:07,089  [INFO] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:07,090  [INFO] SEND OK
 
2025-07-16 21:02:07,090  [INFO] 
 
2025-07-16 21:02:07,095  [INFO] [D][11:30:09][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:07,098  [INFO] [D][11:30:09][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:07,099  [INFO] 
 
2025-07-16 21:02:07,103  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,110  [INFO] [D][11:30:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:07,112  [INFO] [D][11:30:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:07,117  [INFO] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,120  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,126  [INFO] [D][11:30:09][PROT]M2M Send ok [1730201409]
 
2025-07-16 21:02:07,128  [INFO] [D][11:30:09][PROT]CLEAN:0
 
2025-07-16 21:02:07,131  [INFO] [D][11:30:09][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:07,138  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:07,143  [INFO] [D][11:30:09][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:02:07,145  [INFO] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,151  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,153  [INFO] [D][11:30:09][PROT]index:5 1730201409
 
2025-07-16 21:02:07,156  [INFO] [D][11:30:09][PROT]is_send:0
 
2025-07-16 21:02:07,160  [INFO] [D][11:30:09][PROT]sequence_num:14
 
2025-07-16 21:02:07,162  [INFO] [D][11:30:09][PROT]retry_timeout:0
 
2025-07-16 21:02:07,165  [INFO] [D][11:30:09][PROT]retry_times:1
 
2025-07-16 21:02:07,170  [INFO] [D][11:30:09][PROT]send_path:0x2
 
2025-07-16 21:02:07,173  [INFO] [D][11:30:09][PROT]min_index:5, type:0xC001, priority:0
 
2025-07-16 21:02:07,182  [INFO] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,187  [INFO] [W][11:30:09][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201409]
 
2025-07-16 21:02:07,194  [INFO] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,199  [INFO] [D][11:30:09][PROT]sending traceid [9999999999900008]
 
2025-07-16 21:02:07,205  [INFO] [D][11:30:09][PROT]Send_TO_M2M [1730201409]
 
2025-07-16 21:02:07,207  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:07,213  [INFO] [D][11:30:09][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:07,215  [INFO] [D][11:30:09][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:07,218  [INFO] [D][11:30:09][M2M ]m2m send data len[102]
 
2025-07-16 21:02:07,224  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:07,232  [INFO] [D][11:30:09][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a010] format[0]
 
2025-07-16 21:02:07,238  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:07,240  [INFO] [D][11:30:09][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:07,246  [INFO] [D][11:30:09][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:02:07,247  [INFO] 
 
2025-07-16 21:02:07,251  [INFO] [D][11:30:09][CAT1]Send Data To Server[102][102] ... ->:
 
2025-07-16 21:02:07,261  [INFO] 0033B9FA113311331133113311331B88B222E772B83E7A622E479422819013E546AC238D81A241D7EA2752C2E98B58CBFB86D9
 
2025-07-16 21:02:07,262  [INFO] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:07,263  [INFO] SEND OK
 
2025-07-16 21:02:07,263  [INFO] 
 
2025-07-16 21:02:07,268  [INFO] [D][11:30:09][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:07,270  [INFO] [D][11:30:09][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:07,271  [INFO] 
 
2025-07-16 21:02:07,273  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,282  [INFO] [D][11:30:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:07,284  [INFO] [D][11:30:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:07,287  [INFO] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,293  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,299  [INFO] [D][11:30:09][PROT]M2M Send ok [1730201409]
 
2025-07-16 21:02:07,299  [INFO] [D][11:30:09][PROT]CLEAN:5
 
2025-07-16 21:02:07,303  [INFO] [D][11:30:09][PROT]index:1 1730201409
 
2025-07-16 21:02:07,307  [INFO] [D][11:30:09][PROT]is_send:0
 
2025-07-16 21:02:07,310  [INFO] [D][11:30:09][PROT]sequence_num:15
 
2025-07-16 21:02:07,313  [INFO] [D][11:30:09][PROT]retry_timeout:0
 
2025-07-16 21:02:07,316  [INFO] [D][11:30:09][PROT]retry_times:1
 
2025-07-16 21:02:07,318  [INFO] [D][11:30:09][PROT]send_path:0x2
 
2025-07-16 21:02:07,323  [INFO] [D][11:30:09][PROT]min_index:1, type:0x4205, priority:0
 
2025-07-16 21:02:07,330  [INFO] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,335  [INFO] [W][11:30:09][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201409]
 
2025-07-16 21:02:07,345  [INFO] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,350  [INFO] [D][11:30:09][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:02:07,352  [INFO] [D][11:30:09][PROT]Send_TO_M2M [1730201409]
 
2025-07-16 21:02:07,358  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:07,361  [INFO] [D][11:30:09][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:07,366  [INFO] [D][11:30:09][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:07,368  [INFO] [D][11:30:09][M2M ]m2m send data len[294]
 
2025-07-16 21:02:07,371  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:07,381  [INFO] [D][11:30:09][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x2005a010] format[0]
 
2025-07-16 21:02:07,386  [INFO] [D][11:30:09][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:07,388  [INFO] [D][11:30:09][CAT1]tx ret[17] >>> AT+QISEND=0,294
 
2025-07-16 21:02:07,389  [INFO] 
 
2025-07-16 21:02:07,394  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:07,399  [INFO] [D][11:30:09][CAT1]Send Data To Server[294][297] ... ->:
 
2025-07-16 21:02:07,427  [INFO] 0093B9FF113311331133113311331B88B15ED3C2A04C77B56FC8B55B15BF56DAE9B208F930F297BF7943547DF85F550FD509A11D770048BAFBA7B44A9EFCE9ED517FA37BDCA18B3EFE0D1AC0F64D0F5C9F3B7380433E4DF9A8844CCE10EE119FFCA007CF0A1201EE9FA86342D7DDE0A8E23904F51B46F8A7183C67D67707C62C3883E021ACB9FF4211FF6C56302CDC081575D8
 
2025-07-16 21:02:07,428  [INFO] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:07,429  [INFO] SEND OK
 
2025-07-16 21:02:07,429  [INFO] 
 
2025-07-16 21:02:07,433  [INFO] [D][11:30:09][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:07,435  [INFO] [D][11:30:09][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:07,435  [INFO] 
 
2025-07-16 21:02:07,440  [INFO] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,447  [INFO] [D][11:30:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:07,449  [INFO] [D][11:30:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:07,456  [INFO] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,462  [INFO] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,464  [INFO] [D][11:30:09][PROT]M2M Send ok [1730201409]
 
2025-07-16 21:02:12,617  [INFO] [D][11:30:14][PROT]CLEAN,SEND:1
 
2025-07-16 21:02:12,628  [INFO] [D][11:30:14][PROT]CLEAN:1
 
2025-07-16 21:02:12,641  [INFO] [D][11:30:14][PROT]index:2 1730201414
 
2025-07-16 21:02:12,644  [INFO] [D][11:30:14][PROT]is_send:0
 
2025-07-16 21:02:12,646  [INFO] [D][11:30:14][PROT]sequence_num:16
 
2025-07-16 21:02:12,649  [INFO] [D][11:30:14][PROT]retry_timeout:0
 
2025-07-16 21:02:12,652  [INFO] [D][11:30:14][PROT]retry_times:1
 
2025-07-16 21:02:12,654  [INFO] [D][11:30:14][PROT]send_path:0x2
 
2025-07-16 21:02:12,660  [INFO] [D][11:30:14][PROT]min_index:2, type:0x4701, priority:0
 
2025-07-16 21:02:12,667  [INFO] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,672  [INFO] [W][11:30:14][PROT]SEND DATA TYPE:4701, SENDPATH:0x2 [1730201414]
 
2025-07-16 21:02:12,680  [INFO] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,685  [INFO] [D][11:30:14][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:02:12,688  [INFO] [D][11:30:14][PROT]Send_TO_M2M [1730201414]
 
2025-07-16 21:02:12,694  [INFO] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:12,696  [INFO] [D][11:30:14][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:12,702  [INFO] [D][11:30:14][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:12,704  [INFO] [D][11:30:14][M2M ]m2m send data len[198]
 
2025-07-16 21:02:12,707  [INFO] [D][11:30:14][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:12,716  [INFO] [D][11:30:14][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a010] format[0]
 
2025-07-16 21:02:12,722  [INFO] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:12,727  [INFO] [D][11:30:14][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:12,730  [INFO] [D][11:30:14][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:02:12,731  [INFO] 
 
2025-07-16 21:02:12,734  [INFO] [D][11:30:14][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:02:12,753  [INFO] 0063B9F0113311331133113311331B88B1E3C692AE645C5E2128BCCB41926302A175EC9CB7ADD146BC5D850E3D638F33DA89119784A3678ED3237BD471C85592F740891E80D1903380B509D3B50B4C5C3E384B410F43EFEA087A71E9AD337228A3CF60
 
2025-07-16 21:02:12,754  [INFO] [D][11:30:14][CAT1]<<< 
 
2025-07-16 21:02:12,755  [INFO] SEND OK
 
2025-07-16 21:02:12,756  [INFO] 
 
2025-07-16 21:02:12,761  [INFO] [D][11:30:14][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:12,762  [INFO] [D][11:30:14][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:12,764  [INFO] 
 
2025-07-16 21:02:12,770  [INFO] [D][11:30:14][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:12,774  [INFO] [D][11:30:14][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:12,779  [INFO] [D][11:30:14][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:12,782  [INFO] [D][11:30:14][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:12,788  [INFO] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:12,790  [INFO] [D][11:30:14][PROT]M2M Send ok [1730201414]
 
2025-07-16 21:02:12,793  [INFO] [D][11:30:14][PROT]CLEAN:2
 
2025-07-16 21:02:12,796  [INFO] [D][11:30:14][PROT]index:3 1730201414
 
2025-07-16 21:02:12,799  [INFO] [D][11:30:14][PROT]is_send:0
 
2025-07-16 21:02:12,802  [INFO] [D][11:30:14][PROT]sequence_num:17
 
2025-07-16 21:02:12,804  [INFO] [D][11:30:14][PROT]retry_timeout:0
 
2025-07-16 21:02:12,808  [INFO] [D][11:30:14][PROT]retry_times:1
 
2025-07-16 21:02:12,811  [INFO] [D][11:30:14][PROT]send_path:0x2
 
2025-07-16 21:02:12,816  [INFO] [D][11:30:14][PROT]min_index:3, type:0x4705, priority:0
 
2025-07-16 21:02:12,825  [INFO] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,830  [INFO] [W][11:30:14][PROT]SEND DATA TYPE:4705, SENDPATH:0x2 [1730201414]
 
2025-07-16 21:02:12,836  [INFO] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,841  [INFO] [D][11:30:14][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:02:12,843  [INFO] [D][11:30:14][PROT]Send_TO_M2M [1730201414]
 
2025-07-16 21:02:12,850  [INFO] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:12,856  [INFO] [D][11:30:14][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:12,858  [INFO] [D][11:30:14][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:12,861  [INFO] [D][11:30:14][M2M ]m2m send data len[390]
 
2025-07-16 21:02:12,866  [INFO] [D][11:30:14][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:12,875  [INFO] [D][11:30:14][SAL ]cellular SEND socket id[0] type[1], len[390], data[0x2005a010] format[0]
 
2025-07-16 21:02:12,877  [INFO] [D][11:30:14][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:12,883  [INFO] [D][11:30:14][CAT1]tx ret[17] >>> AT+QISEND=0,390
 
2025-07-16 21:02:12,884  [INFO] 
 
2025-07-16 21:02:12,888  [INFO] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:12,890  [INFO] [D][11:30:14][CAT1]Send Data To Server[390][390] ... ->:
 
2025-07-16 21:02:12,931  [INFO] 00C3B9FD113311331133113311331B88B16129A7D98F2BA09DDC043A25D96B36112CC426E8C9D5AF384D6A5F923ACD50026DF54112FA509C2F4E33D14EE4C400A477848748AFBA1981F7880C73A37C2E985975CC699AC7214364A1A4C4023D3EB4031176237A840A52AA100E37AB19F96342D6BA2A645ABC51B4C35E2BCA20A88A1BBCAD04E5C0B341A5260A35FE838156A73B84656223498B5A995232E498312DABE67451223F9674FB2202AF4FCF5FA1A290438E0C781EBC6418B69A442E2EF0F6D1
 
2025-07-16 21:02:12,932  [INFO] [D][11:30:15][CAT1]<<< 
 
2025-07-16 21:02:12,934  [INFO] SEND OK
 
2025-07-16 21:02:12,934  [INFO] 
 
2025-07-16 21:02:12,935  [INFO] [D][11:30:15][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:12,939  [INFO] [D][11:30:15][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:12,940  [INFO] 
 
2025-07-16 21:02:12,941  [INFO] [D][11:30:15][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:12,947  [INFO] [D][11:30:15][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:12,952  [INFO] [D][11:30:15][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:12,955  [INFO] [D][11:30:15][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:12,961  [INFO] [D][11:30:15][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:12,965  [INFO] [D][11:30:15][PROT]M2M Send ok [1730201415]
 
2025-07-16 21:02:13,871  [INFO] [D][11:30:16][GNSS]handler GSMGet Base timeout
 
2025-07-16 21:02:18,114  [INFO] [D][11:30:20][PROT]CLEAN,SEND:3
 
2025-07-16 21:02:18,125  [INFO] [D][11:30:20][PROT]CLEAN:3
 
2025-07-16 21:02:18,137  [INFO] [D][11:30:20][PROT]index:4 1730201420
 
2025-07-16 21:02:18,140  [INFO] [D][11:30:20][PROT]is_send:0
 
2025-07-16 21:02:18,143  [INFO] [D][11:30:20][PROT]sequence_num:18
 
2025-07-16 21:02:18,146  [INFO] [D][11:30:20][PROT]retry_timeout:0
 
2025-07-16 21:02:18,148  [INFO] [D][11:30:20][PROT]retry_times:1
 
2025-07-16 21:02:18,151  [INFO] [D][11:30:20][PROT]send_path:0x2
 
2025-07-16 21:02:18,156  [INFO] [D][11:30:20][PROT]min_index:4, type:0x5103, priority:0
 
2025-07-16 21:02:18,162  [INFO] [D][11:30:20][PROT]===========================================================
 
2025-07-16 21:02:18,169  [INFO] [W][11:30:20][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730201420]
 
2025-07-16 21:02:18,177  [INFO] [D][11:30:20][PROT]===========================================================
 
2025-07-16 21:02:18,179  [INFO] [D][11:30:20][COMM]PB encode data:87
 
2025-07-16 21:02:18,198  [INFO] 0A55080110001A120A0C464344373333363333453136120020431A120A0C453232453042463545374546120020791A120A0C413430304532463436324330120020791A130A0C4134303045324634363845311200209901
 
2025-07-16 21:02:18,199  [INFO] [D][11:30:20][PROT]sending traceid [999999999990000A]
 
2025-07-16 21:02:18,204  [INFO] [D][11:30:20][PROT]Send_TO_M2M [1730201420]
 
2025-07-16 21:02:18,207  [INFO] [D][11:30:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:18,213  [INFO] [D][11:30:20][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:18,216  [INFO] [D][11:30:20][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:18,221  [INFO] [D][11:30:20][M2M ]m2m send data len[230]
 
2025-07-16 21:02:18,224  [INFO] [D][11:30:20][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:18,232  [INFO] [D][11:30:20][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x2005a010] format[0]
 
2025-07-16 21:02:18,236  [INFO] [D][11:30:20][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:18,241  [INFO] [D][11:30:20][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:02:18,242  [INFO] 
 
2025-07-16 21:02:18,247  [INFO] [D][11:30:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:18,251  [INFO] [D][11:30:20][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:02:18,274  [INFO] 0073B6FC113311331133113311331B88BCA6E385F425614FFB9D58B45AEB2BAC6BD4A2DC5CE6C2ED8A7FC372BC48A62F7DC13BA27D8B42AD15D78C2942A2983DE9CCB2AA3884BF05B0E01058D6A55F6C279A85BF062025EF6A0B2ECED231BCD63FAADB143AC1FC236BAE5C9CCDFD56248CE0E9
 
2025-07-16 21:02:18,274  [INFO] [D][11:30:20][CAT1]<<< 
 
2025-07-16 21:02:18,275  [INFO] SEND OK
 
2025-07-16 21:02:18,275  [INFO] 
 
2025-07-16 21:02:18,279  [INFO] [D][11:30:20][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:18,282  [INFO] [D][11:30:20][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:18,283  [INFO] 
 
2025-07-16 21:02:18,288  [INFO] [D][11:30:20][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:18,294  [INFO] [D][11:30:20][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:18,295  [INFO] [D][11:30:20][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:18,301  [INFO] [D][11:30:20][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:18,305  [INFO] [D][11:30:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:18,309  [INFO] [D][11:30:20][PROT]M2M Send ok [1730201420]
 
2025-07-16 21:02:23,531  [INFO] [D][11:30:25][PROT]CLEAN,SEND:4
 
2025-07-16 21:02:23,542  [INFO] [D][11:30:25][PROT]CLEAN:4
 
2025-07-16 21:02:27,866  [INFO] [D][11:30:30][CAT1]closed : 0
 
2025-07-16 21:02:27,872  [INFO] [D][11:30:30][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:02:27,873  [INFO] [D][11:30:30][SAL ]socket closed id[0]
 
2025-07-16 21:02:27,879  [INFO] [D][11:30:30][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:02:27,884  [INFO] [D][11:30:30][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:02:27,887  [INFO] [D][11:30:30][M2M ]m2m select fd[4]
 
2025-07-16 21:02:27,893  [INFO] [D][11:30:30][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:02:27,895  [INFO] [D][11:30:30][M2M ]tcpclient close[4]
 
2025-07-16 21:02:27,898  [INFO] [D][11:30:30][SAL ]socket[4] has closed
 
2025-07-16 21:02:27,904  [INFO] [D][11:30:30][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:02:27,909  [INFO] [D][11:30:30][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 3
 
2025-07-16 21:02:27,954  [INFO] [D][11:30:30][COMM]Main Task receive event:86
 
2025-07-16 21:02:27,963  [INFO] [W][11:30:30][PROT]remove success[1730201430],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:02:27,971  [INFO] [D][11:30:30][HSDK][0] flush to flash addr:[0xE46500] --- write len --- [256]
 
2025-07-16 21:02:27,980  [INFO] [W][11:30:30][PROT]add success [1730201430],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:02:27,986  [INFO] [D][11:30:30][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:02:27,988  [INFO] [D][11:30:30][PROT]index:0 1730201430
 
2025-07-16 21:02:27,991  [INFO] [D][11:30:30][PROT]is_send:0
 
2025-07-16 21:02:27,994  [INFO] [D][11:30:30][PROT]sequence_num:19
 
2025-07-16 21:02:27,996  [INFO] [D][11:30:30][PROT]retry_timeout:0
 
2025-07-16 21:02:27,999  [INFO] [D][11:30:30][PROT]retry_times:1
 
2025-07-16 21:02:28,002  [INFO] [D][11:30:30][PROT]send_path:0x2
 
2025-07-16 21:02:28,007  [INFO] [D][11:30:30][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:02:28,016  [INFO] [D][11:30:30][PROT]===========================================================
 
2025-07-16 21:02:28,022  [INFO] [W][11:30:30][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201430]
 
2025-07-16 21:02:28,028  [INFO] [D][11:30:30][PROT]===========================================================
 
2025-07-16 21:02:28,032  [INFO] [D][11:30:30][PROT]sending traceid [999999999990000B]
 
2025-07-16 21:02:28,035  [INFO] [D][11:30:30][PROT]Send_TO_M2M [1730201430]
 
2025-07-16 21:02:28,041  [INFO] [D][11:30:30][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:28,046  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:28,049  [INFO] [D][11:30:30][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:02:28,059  [INFO] [D][11:30:30][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:02:28,060  [INFO] [D][11:30:30][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:02:28,069  [INFO] [D][11:30:30][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:02:28,074  [INFO] [D][11:30:30][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:02:28,077  [INFO] [D][11:30:30][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:02:28,083  [INFO] [D][11:30:30][CAT1]at ops open socket[0]
 
2025-07-16 21:02:28,086  [INFO] [D][11:30:30][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:02:28,086  [INFO] 
 
2025-07-16 21:02:28,091  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:02:28,093  [INFO] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,094  [INFO] +CGATT: 1
 
2025-07-16 21:02:28,094  [INFO] 
 
2025-07-16 21:02:28,095  [INFO] OK
 
2025-07-16 21:02:28,095  [INFO] 
 
2025-07-16 21:02:28,099  [INFO] [D][11:30:30][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:02:28,100  [INFO] 
 
2025-07-16 21:02:28,100  [INFO] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,102  [INFO] +CSQ: 31,99
 
2025-07-16 21:02:28,103  [INFO] 
 
2025-07-16 21:02:28,103  [INFO] OK
 
2025-07-16 21:02:28,103  [INFO] 
 
2025-07-16 21:02:28,107  [INFO] [D][11:30:30][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:02:28,108  [INFO] 
 
2025-07-16 21:02:28,109  [INFO] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,110  [INFO] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:02:28,111  [INFO] 
 
2025-07-16 21:02:28,112  [INFO] OK
 
2025-07-16 21:02:28,113  [INFO] 
 
2025-07-16 21:02:28,119  [INFO] [D][11:30:30][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:02:28,120  [INFO] 
 
2025-07-16 21:02:28,121  [INFO] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,122  [INFO] OK
 
2025-07-16 21:02:28,122  [INFO] 
 
2025-07-16 21:02:28,126  [INFO] [D][11:30:30][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:02:28,216  [INFO] [D][11:30:30][CAT1]opened : 0, 0
 
2025-07-16 21:02:28,219  [INFO] [D][11:30:30][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:28,226  [INFO] [D][11:30:30][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:02:28,230  [INFO] [D][11:30:30][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:02:28,235  [INFO] [D][11:30:30][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:02:28,241  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:28,244  [INFO] [D][11:30:30][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:28,247  [INFO] [D][11:30:30][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:28,252  [INFO] [D][11:30:30][M2M ]m2m send data len[70]
 
2025-07-16 21:02:28,255  [INFO] [D][11:30:30][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:28,264  [INFO] [D][11:30:30][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:02:28,269  [INFO] [D][11:30:30][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:28,272  [INFO] [D][11:30:30][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:02:28,273  [INFO] 
 
2025-07-16 21:02:28,278  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:28,282  [INFO] [D][11:30:30][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:02:28,289  [INFO] 0023B9F1113311331133113311331B88BDA2F86023307D811A4F3516BD21EA985A350C
 
2025-07-16 21:02:28,291  [INFO] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,292  [INFO] SEND OK
 
2025-07-16 21:02:28,292  [INFO] 
 
2025-07-16 21:02:28,297  [INFO] [D][11:30:30][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:28,299  [INFO] [D][11:30:30][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:28,300  [INFO] 
 
2025-07-16 21:02:28,305  [INFO] [D][11:30:30][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:28,310  [INFO] [D][11:30:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:28,313  [INFO] [D][11:30:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:28,318  [INFO] [D][11:30:30][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:28,323  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:28,328  [INFO] [D][11:30:30][PROT]M2M Send ok [1730201430]
 
2025-07-16 21:02:28,330  [INFO] [D][11:30:30][PROT]CLEAN:0
 
2025-07-16 21:02:28,333  [INFO] [D][11:30:30][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:28,338  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:28,344  [INFO] [D][11:30:30][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:02:28,347  [INFO] [D][11:30:30][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:28,353  [INFO] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:38,286  [INFO] [D][11:30:40][COMM]S->M yaw:INVALID
 
2025-07-16 21:02:39,576  [INFO] [D][11:30:41][COMM]M->S yaw:INVALID
 
2025-07-16 21:03:08,506  [INFO] [D][11:31:10][COMM]IMU: 273745 MEMS ERROR when cali 0
 
2025-07-16 21:03:08,827  [INFO] [D][11:31:11][CAT1]closed : 0
 
2025-07-16 21:03:08,832  [INFO] [D][11:31:11][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:03:08,835  [INFO] [D][11:31:11][SAL ]socket closed id[0]
 
2025-07-16 21:03:08,840  [INFO] [D][11:31:11][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:03:08,845  [INFO] [D][11:31:11][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:03:08,848  [INFO] [D][11:31:11][M2M ]m2m select fd[4]
 
2025-07-16 21:03:08,854  [INFO] [D][11:31:11][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:03:08,857  [INFO] [D][11:31:11][M2M ]tcpclient close[4]
 
2025-07-16 21:03:08,860  [INFO] [D][11:31:11][SAL ]socket[4] has closed
 
2025-07-16 21:03:08,866  [INFO] [D][11:31:11][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:03:08,870  [INFO] [D][11:31:11][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 4
 
2025-07-16 21:03:08,895  [INFO] [D][11:31:11][COMM]Main Task receive event:86
 
2025-07-16 21:03:08,902  [INFO] [W][11:31:11][PROT]remove success[1730201471],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:03:08,910  [INFO] [W][11:31:11][PROT]add success [1730201471],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:03:08,916  [INFO] [D][11:31:11][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:03:08,918  [INFO] [D][11:31:11][PROT]index:0 1730201471
 
2025-07-16 21:03:08,922  [INFO] [D][11:31:11][PROT]is_send:0
 
2025-07-16 21:03:08,928  [INFO] [D][11:31:11][PROT]sequence_num:20
 
2025-07-16 21:03:08,931  [INFO] [D][11:31:11][PROT]retry_timeout:0
 
2025-07-16 21:03:08,933  [INFO] [D][11:31:11][PROT]retry_times:1
 
2025-07-16 21:03:08,936  [INFO] [D][11:31:11][PROT]send_path:0x2
 
2025-07-16 21:03:08,941  [INFO] [D][11:31:11][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:03:08,948  [INFO] [D][11:31:11][PROT]===========================================================
 
2025-07-16 21:03:08,953  [INFO] [W][11:31:11][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201471]
 
2025-07-16 21:03:08,961  [INFO] [D][11:31:11][PROT]===========================================================
 
2025-07-16 21:03:08,963  [INFO] [D][11:31:11][PROT]sending traceid [999999999990000C]
 
2025-07-16 21:03:08,969  [INFO] [D][11:31:11][PROT]Send_TO_M2M [1730201471]
 
2025-07-16 21:03:08,975  [INFO] [D][11:31:11][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:08,977  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:08,983  [INFO] [D][11:31:11][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:03:08,990  [INFO] [D][11:31:11][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:03:08,994  [INFO] [D][11:31:11][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:03:09,001  [INFO] [D][11:31:11][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:03:09,009  [INFO] [D][11:31:11][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:03:09,011  [INFO] [D][11:31:11][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:03:09,014  [INFO] [D][11:31:11][CAT1]at ops open socket[0]
 
2025-07-16 21:03:09,019  [INFO] [D][11:31:11][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:03:09,020  [INFO] 
 
2025-07-16 21:03:09,025  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:03:09,027  [INFO] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,027  [INFO] +CGATT: 1
 
2025-07-16 21:03:09,028  [INFO] 
 
2025-07-16 21:03:09,028  [INFO] OK
 
2025-07-16 21:03:09,029  [INFO] 
 
2025-07-16 21:03:09,030  [INFO] [D][11:31:11][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:03:09,031  [INFO] 
 
2025-07-16 21:03:09,033  [INFO] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,034  [INFO] +CSQ: 31,99
 
2025-07-16 21:03:09,035  [INFO] 
 
2025-07-16 21:03:09,035  [INFO] OK
 
2025-07-16 21:03:09,036  [INFO] 
 
2025-07-16 21:03:09,039  [INFO] [D][11:31:11][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:03:09,039  [INFO] 
 
2025-07-16 21:03:09,042  [INFO] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,044  [INFO] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:03:09,045  [INFO] 
 
2025-07-16 21:03:09,045  [INFO] OK
 
2025-07-16 21:03:09,046  [INFO] 
 
2025-07-16 21:03:09,053  [INFO] [D][11:31:11][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:03:09,054  [INFO] 
 
2025-07-16 21:03:09,055  [INFO] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,056  [INFO] OK
 
2025-07-16 21:03:09,056  [INFO] 
 
2025-07-16 21:03:09,060  [INFO] [D][11:31:11][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:03:09,165  [INFO] [D][11:31:11][CAT1]opened : 0, 0
 
2025-07-16 21:03:09,168  [INFO] [D][11:31:11][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:09,173  [INFO] [D][11:31:11][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:03:09,179  [INFO] [D][11:31:11][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:03:09,185  [INFO] [D][11:31:11][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:03:09,190  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:03:09,192  [INFO] [D][11:31:11][SAL ]sock send credit cnt[6]
 
2025-07-16 21:03:09,195  [INFO] [D][11:31:11][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:03:09,202  [INFO] [D][11:31:11][M2M ]m2m send data len[70]
 
2025-07-16 21:03:09,204  [INFO] [D][11:31:11][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:03:09,213  [INFO] [D][11:31:11][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:03:09,217  [INFO] [D][11:31:11][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:03:09,221  [INFO] [D][11:31:11][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:03:09,221  [INFO] 
 
2025-07-16 21:03:09,226  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:03:09,231  [INFO] [D][11:31:11][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:03:09,238  [INFO] 0023B9F2113311331133113311331B88B011E819F7C6E94D9730959F41121DA84AE768
 
2025-07-16 21:03:09,240  [INFO] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,241  [INFO] SEND OK
 
2025-07-16 21:03:09,241  [INFO] 
 
2025-07-16 21:03:09,246  [INFO] [D][11:31:11][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:03:09,248  [INFO] [D][11:31:11][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:03:09,249  [INFO] 
 
2025-07-16 21:03:09,254  [INFO] [D][11:31:11][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:09,259  [INFO] [D][11:31:11][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:03:09,262  [INFO] [D][11:31:11][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:03:09,268  [INFO] [D][11:31:11][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:09,271  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:03:09,277  [INFO] [D][11:31:11][PROT]M2M Send ok [1730201471]
 
2025-07-16 21:03:09,279  [INFO] [D][11:31:11][PROT]CLEAN:0
 
2025-07-16 21:03:09,282  [INFO] [D][11:31:11][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:09,287  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:09,293  [INFO] [D][11:31:11][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:03:09,296  [INFO] [D][11:31:11][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:09,302  [INFO] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:03:49,786  [INFO] [D][11:31:52][CAT1]closed : 0
 
2025-07-16 21:03:49,791  [INFO] [D][11:31:52][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:03:49,794  [INFO] [D][11:31:52][SAL ]socket closed id[0]
 
2025-07-16 21:03:49,799  [INFO] [D][11:31:52][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:03:49,805  [INFO] [D][11:31:52][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:03:49,808  [INFO] [D][11:31:52][M2M ]m2m select fd[4]
 
2025-07-16 21:03:49,813  [INFO] [D][11:31:52][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:03:49,815  [INFO] [D][11:31:52][M2M ]tcpclient close[4]
 
2025-07-16 21:03:49,818  [INFO] [D][11:31:52][SAL ]socket[4] has closed
 
2025-07-16 21:03:49,824  [INFO] [D][11:31:52][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:03:49,830  [INFO] [D][11:31:52][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 5
 
2025-07-16 21:03:49,833  [INFO] [D][11:31:52][COMM]Main Task receive event:86
 
2025-07-16 21:03:49,841  [INFO] [W][11:31:52][PROT]remove success[1730201512],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:03:49,846  [INFO] [D][11:31:52][PROT]index:0 1730201512
 
2025-07-16 21:03:49,849  [INFO] [D][11:31:52][PROT]is_send:0
 
2025-07-16 21:03:49,852  [INFO] [D][11:31:52][PROT]sequence_num:21
 
2025-07-16 21:03:49,855  [INFO] [D][11:31:52][PROT]retry_timeout:0
 
2025-07-16 21:03:49,857  [INFO] [D][11:31:52][PROT]retry_times:1
 
2025-07-16 21:03:49,860  [INFO] [D][11:31:52][PROT]send_path:0x2
 
2025-07-16 21:03:49,865  [INFO] [D][11:31:52][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:03:49,871  [INFO] [D][11:31:52][PROT]===========================================================
 
2025-07-16 21:03:49,878  [INFO] [D][11:31:52][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:49,883  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:49,886  [INFO] [D][11:31:52][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:03:49,894  [INFO] [D][11:31:52][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:03:49,897  [INFO] [D][11:31:52][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:03:49,906  [INFO] [D][11:31:52][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:03:49,911  [INFO] [D][11:31:52][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:03:49,916  [INFO] [W][11:31:52][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201512]
 
2025-07-16 21:03:49,925  [INFO] [D][11:31:52][PROT]===========================================================
 
2025-07-16 21:03:49,927  [INFO] [D][11:31:52][PROT]sending traceid [999999999990000D]
 
2025-07-16 21:03:49,933  [INFO] [D][11:31:52][PROT]Send_TO_M2M [1730201512]
 
2025-07-16 21:03:49,941  [INFO] [W][11:31:52][PROT]add success [1730201512],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:03:49,947  [INFO] [D][11:31:52][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:03:49,949  [INFO] [D][11:31:52][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:03:49,955  [INFO] [D][11:31:52][CAT1]at ops open socket[0]
 
2025-07-16 21:03:49,957  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:03:49,963  [INFO] [D][11:31:52][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:03:49,964  [INFO] 
 
2025-07-16 21:03:49,966  [INFO] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,966  [INFO] +CGATT: 1
 
2025-07-16 21:03:49,966  [INFO] 
 
2025-07-16 21:03:49,966  [INFO] OK
 
2025-07-16 21:03:49,968  [INFO] 
 
2025-07-16 21:03:49,972  [INFO] [D][11:31:52][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:03:49,972  [INFO] 
 
2025-07-16 21:03:49,973  [INFO] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,974  [INFO] +CSQ: 31,99
 
2025-07-16 21:03:49,975  [INFO] 
 
2025-07-16 21:03:49,975  [INFO] OK
 
2025-07-16 21:03:49,975  [INFO] 
 
2025-07-16 21:03:49,977  [INFO] [D][11:31:52][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:03:49,979  [INFO] 
 
2025-07-16 21:03:49,980  [INFO] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,983  [INFO] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:03:49,984  [INFO] 
 
2025-07-16 21:03:49,985  [INFO] OK
 
2025-07-16 21:03:49,985  [INFO] 
 
2025-07-16 21:03:49,992  [INFO] [D][11:31:52][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:03:49,993  [INFO] 
 
2025-07-16 21:03:49,993  [INFO] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,994  [INFO] OK
 
2025-07-16 21:03:49,994  [INFO] 
 
2025-07-16 21:03:49,998  [INFO] [D][11:31:52][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:03:50,074  [INFO] [D][11:31:52][CAT1]opened : 0, 0
 
2025-07-16 21:03:50,077  [INFO] [D][11:31:52][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:50,082  [INFO] [D][11:31:52][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:03:50,088  [INFO] [D][11:31:52][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:03:50,093  [INFO] [D][11:31:52][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:03:50,099  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:03:50,101  [INFO] [D][11:31:52][SAL ]sock send credit cnt[6]
 
2025-07-16 21:03:50,104  [INFO] [D][11:31:52][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:03:50,111  [INFO] [D][11:31:52][M2M ]m2m send data len[70]
 
2025-07-16 21:03:50,113  [INFO] [D][11:31:52][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:03:50,121  [INFO] [D][11:31:52][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:03:50,127  [INFO] [D][11:31:52][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:03:50,129  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:03:50,135  [INFO] [D][11:31:52][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:03:50,135  [INFO] 
 
2025-07-16 21:03:50,140  [INFO] [D][11:31:52][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:03:50,147  [INFO] 0023B9FE113311331133113311331B88BF0B481DA392E4B7C005C641C444A6CE7BB173
 
2025-07-16 21:03:50,149  [INFO] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:50,150  [INFO] SEND OK
 
2025-07-16 21:03:50,150  [INFO] 
 
2025-07-16 21:03:50,155  [INFO] [D][11:31:52][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:03:50,157  [INFO] [D][11:31:52][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:03:50,158  [INFO] 
 
2025-07-16 21:03:50,163  [INFO] [D][11:31:52][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:50,168  [INFO] [D][11:31:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:03:50,171  [INFO] [D][11:31:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:03:50,177  [INFO] [D][11:31:52][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:50,180  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:03:50,185  [INFO] [D][11:31:52][PROT]M2M Send ok [1730201512]
 
2025-07-16 21:03:50,187  [INFO] [D][11:31:52][PROT]CLEAN:0
 
2025-07-16 21:03:50,191  [INFO] [D][11:31:52][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:50,197  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:50,203  [INFO] [D][11:31:52][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:03:50,205  [INFO] [D][11:31:52][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:50,211  [INFO] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:30,787  [INFO] [D][11:32:33][CAT1]closed : 0
 
2025-07-16 21:04:30,793  [INFO] [D][11:32:33][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:04:30,796  [INFO] [D][11:32:33][SAL ]socket closed id[0]
 
2025-07-16 21:04:30,801  [INFO] [D][11:32:33][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:04:30,806  [INFO] [D][11:32:33][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:04:30,809  [INFO] [D][11:32:33][M2M ]m2m select fd[4]
 
2025-07-16 21:04:30,815  [INFO] [D][11:32:33][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:04:30,818  [INFO] [D][11:32:33][M2M ]tcpclient close[4]
 
2025-07-16 21:04:30,820  [INFO] [D][11:32:33][SAL ]socket[4] has closed
 
2025-07-16 21:04:30,826  [INFO] [D][11:32:33][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:04:30,831  [INFO] [D][11:32:33][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 6
 
2025-07-16 21:04:30,869  [INFO] [D][11:32:33][COMM]Main Task receive event:86
 
2025-07-16 21:04:30,875  [INFO] [D][11:32:33][HSDK][0] flush to flash addr:[0xE46600] --- write len --- [256]
 
2025-07-16 21:04:30,884  [INFO] [W][11:32:33][PROT]remove success[1730201553],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:04:30,892  [INFO] [W][11:32:33][PROT]add success [1730201553],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:04:30,899  [INFO] [D][11:32:33][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:04:30,901  [INFO] [D][11:32:33][PROT]index:0 1730201553
 
2025-07-16 21:04:30,904  [INFO] [D][11:32:33][PROT]is_send:0
 
2025-07-16 21:04:30,907  [INFO] [D][11:32:33][PROT]sequence_num:22
 
2025-07-16 21:04:30,909  [INFO] [D][11:32:33][PROT]retry_timeout:0
 
2025-07-16 21:04:30,912  [INFO] [D][11:32:33][PROT]retry_times:1
 
2025-07-16 21:04:30,915  [INFO] [D][11:32:33][PROT]send_path:0x2
 
2025-07-16 21:04:30,921  [INFO] [D][11:32:33][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:04:30,930  [INFO] [D][11:32:33][PROT]===========================================================
 
2025-07-16 21:04:30,935  [INFO] [W][11:32:33][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201553]
 
2025-07-16 21:04:30,940  [INFO] [D][11:32:33][PROT]===========================================================
 
2025-07-16 21:04:30,946  [INFO] [D][11:32:33][PROT]sending traceid [999999999990000E]
 
2025-07-16 21:04:30,949  [INFO] [D][11:32:33][PROT]Send_TO_M2M [1730201553]
 
2025-07-16 21:04:30,955  [INFO] [D][11:32:33][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:04:30,959  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:04:30,962  [INFO] [D][11:32:33][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:04:30,971  [INFO] [D][11:32:33][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:04:30,974  [INFO] [D][11:32:33][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:04:30,983  [INFO] [D][11:32:33][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:04:30,988  [INFO] [D][11:32:33][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:04:30,991  [INFO] [D][11:32:33][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:04:30,997  [INFO] [D][11:32:33][CAT1]at ops open socket[0]
 
2025-07-16 21:04:30,999  [INFO] [D][11:32:33][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:04:31,000  [INFO] 
 
2025-07-16 21:04:31,004  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:04:31,007  [INFO] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,007  [INFO] +CGATT: 1
 
2025-07-16 21:04:31,008  [INFO] 
 
2025-07-16 21:04:31,008  [INFO] OK
 
2025-07-16 21:04:31,009  [INFO] 
 
2025-07-16 21:04:31,012  [INFO] [D][11:32:33][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:04:31,013  [INFO] 
 
2025-07-16 21:04:31,013  [INFO] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,015  [INFO] +CSQ: 31,99
 
2025-07-16 21:04:31,016  [INFO] 
 
2025-07-16 21:04:31,017  [INFO] OK
 
2025-07-16 21:04:31,017  [INFO] 
 
2025-07-16 21:04:31,021  [INFO] [D][11:32:33][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:04:31,022  [INFO] 
 
2025-07-16 21:04:31,022  [INFO] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,024  [INFO] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:04:31,024  [INFO] 
 
2025-07-16 21:04:31,026  [INFO] OK
 
2025-07-16 21:04:31,027  [INFO] 
 
2025-07-16 21:04:31,033  [INFO] [D][11:32:33][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:04:31,034  [INFO] 
 
2025-07-16 21:04:31,035  [INFO] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,038  [INFO] OK
 
2025-07-16 21:04:31,039  [INFO] 
 
2025-07-16 21:04:31,040  [INFO] [D][11:32:33][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:04:31,127  [INFO] [D][11:32:33][CAT1]opened : 0, 0
 
2025-07-16 21:04:31,129  [INFO] [D][11:32:33][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:31,136  [INFO] [D][11:32:33][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:04:31,141  [INFO] [D][11:32:33][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:04:31,146  [INFO] [D][11:32:33][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:04:31,153  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:04:31,155  [INFO] [D][11:32:33][SAL ]sock send credit cnt[6]
 
2025-07-16 21:04:31,158  [INFO] [D][11:32:33][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:04:31,167  [INFO] [D][11:32:33][M2M ]m2m send data len[70]
 
2025-07-16 21:04:31,168  [INFO] [D][11:32:33][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:04:31,174  [INFO] [D][11:32:33][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:04:31,180  [INFO] [D][11:32:33][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:04:31,183  [INFO] [D][11:32:33][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:04:31,183  [INFO] 
 
2025-07-16 21:04:31,189  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:04:31,193  [INFO] [D][11:32:33][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:04:31,200  [INFO] 0023B9F3113311331133113311331B88BA4AF2E0ACD634682DB91A782CAEDFEF29DDB3
 
2025-07-16 21:04:31,202  [INFO] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,203  [INFO] SEND OK
 
2025-07-16 21:04:31,203  [INFO] 
 
2025-07-16 21:04:31,207  [INFO] [D][11:32:33][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:04:31,210  [INFO] [D][11:32:33][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:04:31,210  [INFO] 
 
2025-07-16 21:04:31,216  [INFO] [D][11:32:33][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:31,221  [INFO] [D][11:32:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:04:31,224  [INFO] [D][11:32:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:04:31,230  [INFO] [D][11:32:33][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:31,233  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:31,238  [INFO] [D][11:32:33][PROT]M2M Send ok [1730201553]
 
2025-07-16 21:04:31,240  [INFO] [D][11:32:33][PROT]CLEAN:0
 
2025-07-16 21:04:31,244  [INFO] [D][11:32:33][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:04:31,250  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:04:31,255  [INFO] [D][11:32:33][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:04:31,258  [INFO] [D][11:32:33][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:31,263  [INFO] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:53,864  [INFO] [D][11:32:56][GNSS]location_callback:event=3
 
2025-07-16 21:04:54,572  [INFO] [D][11:32:56][COMM]Main Task receive event:14
 
2025-07-16 21:04:54,578  [INFO] [D][11:32:56][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:04:54,580  [INFO] [D][11:32:56][GNSS]stop event:3
 
2025-07-16 21:04:54,583  [INFO] [D][11:32:56][GNSS]GPS stop. ret=0
 
2025-07-16 21:04:54,586  [INFO] [D][11:32:56][COMM]Period location failed
 
2025-07-16 21:04:54,589  [INFO] [D][11:32:56][COMM]getLocatInfoStep2:1
 
2025-07-16 21:04:54,589  [INFO] 
 
2025-07-16 21:04:54,600  [INFO] [D][11:32:56][COMM]f:frm_park_cam_get_info. done reason:[1],type:[0],result:[0x04],err:[0xFF],angle:[0xFFFF],dist:[255]!!!
 
2025-07-16 21:04:54,605  [INFO] [D][11:32:56][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:04:54,608  [INFO] [D][11:32:56][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:04:54,611  [INFO] [W][11:32:56][COMM]get bat state1 error
 
2025-07-16 21:04:54,613  [INFO] [D][11:32:56][GNSS]5F01 soc:255
 
2025-07-16 21:04:54,620  [INFO] [W][11:32:56][COMM]get mc state information fail
 
2025-07-16 21:04:54,625  [INFO] [W][11:32:56][COMM]get mc speed information fail
 
2025-07-16 21:04:54,628  [INFO] [W][11:32:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 21:04:54,633  [INFO] [D][11:32:56][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:04:54,636  [INFO] [D][11:32:56][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:04:54,638  [INFO] [D][11:32:56][GNSS]nSatsInUse 0
 
2025-07-16 21:04:54,644  [INFO] [D][11:32:56][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:04:54,653  [INFO] [D][11:32:56][COMM]realtime_info.pitch_angle=25,realtime_info.roll_angle=0,realtime_info.nav_angle=32763
 
2025-07-16 21:04:54,664  [INFO] [W][11:32:56][COMM]5F04 LocFail:reason:0x01;diff:41576;LocUsedTime:0;LocStatus|Type:3|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:04:54,666  [INFO] [D][11:32:56][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:04:54,668  [INFO] 
 
2025-07-16 21:04:54,675  [INFO] [W][11:32:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:04:54,680  [INFO] [W][11:32:56][COMM]get mc power mode information fail
 
2025-07-16 21:04:54,682  [INFO] [D][11:32:56][COMM]can to 485 :254
 
2025-07-16 21:04:54,683  [INFO] 
 
2025-07-16 21:04:54,691  [INFO] [W][11:32:56][PROT]remove success[1730201576],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:04:54,699  [INFO] [W][11:32:56][PROT]add success [1730201576],send_path[2],type[5F04],priority[0],index[0],used[1]
 
2025-07-16 21:04:54,702  [INFO] [D][11:32:56][PROT]index:0 1730201576
 
2025-07-16 21:04:54,705  [INFO] [D][11:32:56][PROT]is_send:0
 
2025-07-16 21:04:54,708  [INFO] [D][11:32:56][PROT]sequence_num:23
 
2025-07-16 21:04:54,714  [INFO] [D][11:32:56][PROT]retry_timeout:0
 
2025-07-16 21:04:54,716  [INFO] [D][11:32:56][PROT]retry_times:1
 
2025-07-16 21:04:54,719  [INFO] [D][11:32:56][PROT]send_path:0x2
 
2025-07-16 21:04:54,721  [INFO] [D][11:32:56][PROT]min_index:0, type:0x5F04, priority:0
 
2025-07-16 21:04:54,732  [INFO] [D][11:32:56][PROT]===========================================================
 
2025-07-16 21:04:54,737  [INFO] [D][11:32:56][HSDK][0] flush to flash addr:[0xE46700] --- write len --- [256]
 
2025-07-16 21:04:54,742  [INFO] [W][11:32:56][PROT]SEND DATA TYPE:5F04, SENDPATH:0x2 [1730201576]
 
2025-07-16 21:04:54,751  [INFO] [D][11:32:56][PROT]===========================================================
 
2025-07-16 21:04:54,753  [INFO] [D][11:32:56][COMM]PB encode data:94
 
2025-07-16 21:04:54,770  [INFO] 0A5C0A1018FBFF01201928006080CB80B9066803200228003000380160FF0168FF0198010EA0010EA8010DB80107C00109C801FFFF01D80105E00100E80100F00101F80100800201880200900204AA02021800D802FE01E80200F002FE01
 
2025-07-16 21:04:54,775  [INFO] [D][11:32:56][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:04:54,778  [INFO] [D][11:32:56][PROT]Send_TO_M2M [1730201576]
 
2025-07-16 21:04:54,783  [INFO] [D][11:32:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:04:54,786  [INFO] [D][11:32:56][SAL ]sock send credit cnt[6]
 
2025-07-16 21:04:54,792  [INFO] [D][11:32:56][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:04:54,794  [INFO] [D][11:32:56][M2M ]m2m send data len[262]
 
2025-07-16 21:04:54,800  [INFO] [D][11:32:56][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:04:54,810  [INFO] [D][11:32:56][SAL ]cellular SEND socket id[0] type[1], len[262], data[0x2005a018] format[0]
 
2025-07-16 21:04:54,811  [INFO] [D][11:32:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:04:54,814  [INFO] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,815  [INFO] OK
 
2025-07-16 21:04:54,815  [INFO] 
 
2025-07-16 21:04:54,820  [INFO] [D][11:32:56][CAT1]tx ret[13] >>> AT+GPSRTK=0
 
2025-07-16 21:04:54,820  [INFO] 
 
2025-07-16 21:04:54,822  [INFO] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,823  [INFO] OK
 
2025-07-16 21:04:54,823  [INFO] 
 
2025-07-16 21:04:54,825  [INFO] [D][11:32:56][CAT1]tx ret[12] >>> AT+GPSDR=0
 
2025-07-16 21:04:54,826  [INFO] 
 
2025-07-16 21:04:54,828  [INFO] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,829  [INFO] OK
 
2025-07-16 21:04:54,829  [INFO] 
 
2025-07-16 21:04:54,833  [INFO] [D][11:32:56][CAT1]exec over: func id: 24, ret: 6
 
2025-07-16 21:04:54,836  [INFO] [D][11:32:56][CAT1]sub id: 24, ret: 6
 
2025-07-16 21:04:54,837  [INFO] 
 
2025-07-16 21:04:54,842  [INFO] [D][11:32:56][CAT1]gsm read msg sub id: 26
 
2025-07-16 21:04:54,844  [INFO] [D][11:32:56][CAT1]tx ret[18] >>> AT+WIFISCAN=8,45
 
2025-07-16 21:04:54,845  [INFO] 
 
2025-07-16 21:04:54,847  [INFO] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,847  [INFO] OK
 
2025-07-16 21:04:54,848  [INFO] 
 
2025-07-16 21:04:54,852  [INFO] [D][11:32:56][CAT1]exec over: func id: 26, ret: 6
 
2025-07-16 21:04:54,855  [INFO] [D][11:32:56][CAT1]gsm read msg sub id: 13
 
2025-07-16 21:04:54,860  [INFO] [D][11:32:56][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:04:54,861  [INFO] 
 
2025-07-16 21:04:54,872  [INFO] [D][11:32:57][GNSS]recv submsg id[1]
 
2025-07-16 21:04:54,875  [INFO] [D][11:32:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
 
2025-07-16 21:04:54,879  [INFO] [D][11:32:57][GNSS]location stop evt done evt
 
2025-07-16 21:04:55,245  [INFO] [D][11:32:57][CAT1]<<< 
 
2025-07-16 21:04:55,247  [INFO] +CSQ: 31,99
 
2025-07-16 21:04:55,248  [INFO] 
 
2025-07-16 21:04:55,249  [INFO] OK
 
2025-07-16 21:04:55,250  [INFO] 
 
2025-07-16 21:04:55,254  [INFO] [D][11:32:57][CAT1]exec over: func id: 13, ret: 21
 
2025-07-16 21:04:55,256  [INFO] [D][11:32:57][M2M ]get csq[31]
 
2025-07-16 21:04:55,260  [INFO] [D][11:32:57][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:04:55,264  [INFO] [D][11:32:57][CAT1]tx ret[17] >>> AT+QISEND=0,262
 
2025-07-16 21:04:55,264  [INFO] 
 
2025-07-16 21:04:55,302  [INFO] [D][11:32:57][CAT1]Send Data To Server[262][265] ... ->:
 
2025-07-16 21:04:55,327  [INFO] 0083B6F5113311331133113311331B88B11278D909AC919600DB01534FEA0E8A89CC5525B4C45B6B9A3F2711A3D86767B4FCE2ABE4DFCBCE6C6F942F33C4EBEBDA80CF5797A88A350E513D636974EB7FD7267E6D03B08D509B9C3EC8C856CB137950C105460735717B98364905BB9344B7CCA92C529444AB476F33055D936A2BBAA660
 
2025-07-16 21:04:55,334  [INFO] [D][11:32:57][CAT1]<<< 
 
2025-07-16 21:04:55,336  [INFO] SEND OK
 
2025-07-16 21:04:55,337  [INFO] 
 
2025-07-16 21:04:55,340  [INFO] [D][11:32:57][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:04:55,342  [INFO] [D][11:32:57][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:04:55,345  [INFO] 
 
2025-07-16 21:04:55,349  [INFO] [D][11:32:57][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:55,354  [INFO] [D][11:32:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:04:55,359  [INFO] [D][11:32:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:04:55,361  [INFO] [D][11:32:57][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:55,367  [INFO] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:55,370  [INFO] [D][11:32:57][PROT]M2M Send ok [1730201577]
 
2025-07-16 21:04:55,373  [INFO] [D][11:32:57][PROT]CLEAN:0
 
2025-07-16 21:04:55,512  [INFO] [D][11:32:57][COMM]lastHelmetOrderIdV2:00
 
2025-07-16 21:04:55,515  [INFO] >>>>>RESEND ALLSTATE<<<<<
 
2025-07-16 21:04:55,524  [INFO] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:04:55,532  [INFO] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5006],priority[2],index[0],used[1]
 
2025-07-16 21:04:55,535  [INFO] [D][11:32:57][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:04:55,540  [INFO] [D][11:32:57][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:04:55,543  [INFO] [W][11:32:57][COMM]get soc error
 
2025-07-16 21:04:55,546  [INFO] [W][11:32:57][GNSS]stop locating
 
2025-07-16 21:04:55,549  [INFO] [D][11:32:57][GNSS]all continue location stop
 
2025-07-16 21:04:55,554  [INFO] [W][11:32:57][GNSS]sing locating running
 
2025-07-16 21:04:55,560  [INFO] [E][11:32:57][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:04:55,562  [INFO] [D][11:32:57][COMM]report elecbike, soc 0, reason 2
 
2025-07-16 21:04:55,573  [INFO] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:04:55,581  [INFO] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5D05],priority[3],index[1],used[1]
 
2025-07-16 21:04:55,584  [INFO] [D][11:32:57][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:04:55,590  [INFO] [D][11:32:57][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:04:55,592  [INFO] [D][11:32:57][PROT]index:1 1730201577
 
2025-07-16 21:04:55,595  [INFO] [D][11:32:57][PROT]is_send:0
 
2025-07-16 21:04:55,598  [INFO] [D][11:32:57][PROT]sequence_num:25
 
2025-07-16 21:04:55,601  [INFO] [D][11:32:57][PROT]retry_timeout:0
 
2025-07-16 21:04:55,604  [INFO] [D][11:32:57][PROT]retry_times:3
 
2025-07-16 21:04:55,607  [INFO] [D][11:32:57][PROT]send_path:0x2
 
2025-07-16 21:04:55,612  [INFO] [D][11:32:57][PROT]min_index:1, type:0x5D05, priority:3
 
2025-07-16 21:04:55,618  [INFO] [D][11:32:57][PROT]===========================================================
 
2025-07-16 21:04:55,624  [INFO] [W][11:32:57][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201577]
 
2025-07-16 21:04:55,633  [INFO] [D][11:32:57][PROT]===========================================================
 
2025-07-16 21:04:55,636  [INFO] [D][11:32:57][COMM]PB encode data:29
 
2025-07-16 21:04:55,642  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:04:55,646  [INFO] [D][11:32:57][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:04:55,649  [INFO] [D][11:32:57][PROT]Send_TO_M2M [1730201577]
 
2025-07-16 21:04:55,657  [INFO] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:04:55,666  [INFO] [D][11:32:57][HSDK][0] flush to flash addr:[0xE46800] --- write len --- [256]
 
2025-07-16 21:04:55,668  [INFO] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:04:55,674  [INFO] [D][11:32:57][SAL ]sock send credit cnt[6]
 
2025-07-16 21:04:55,677  [INFO] [D][11:32:57][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:04:55,683  [INFO] [D][11:32:57][M2M ]m2m send data len[134]
 
2025-07-16 21:04:55,685  [INFO] [D][11:32:57][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:04:55,694  [INFO] [D][11:32:57][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:04:55,696  [INFO] [D][11:32:57][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:04:55,707  [INFO] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[FF0E],priority[0],index[2],used[1]
 
2025-07-16 21:04:55,717  [INFO] [D][11:32:57][COMM]buried data a:10,b:2,c:255,d:0,f:16843008,k:5519,l:303,m:11,n:15,o:7,p:1673,q:2301,r:5519,z:665
 
2025-07-16 21:04:55,722  [INFO] [D][11:32:57][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:04:55,723  [INFO] 
 
2025-07-16 21:04:55,730  [INFO] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:04:55,737  [INFO] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 21:04:55,743  [INFO] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:04:55,746  [INFO] [D][11:32:57][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:04:55,753  [INFO] [D][11:32:57][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:04:55,755  [INFO] [D][11:32:57][GNSS]nSatsInUse 0
 
2025-07-16 21:04:55,767  [INFO] [W][11:32:57][COMM]5A07 LocFail:reason:0x01;diff:41577;LocUsedTime:0;LocStatus|Type:3|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:04:55,771  [INFO] [W][11:32:57][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:04:55,780  [INFO] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:04:55,788  [INFO] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5A07],priority[0],index[4],used[1]
 
2025-07-16 21:04:55,793  [INFO] [D][11:32:57][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:04:55,806  [INFO] 0043B6F6113311331133113311331B88B1B5151B374FAF1DD2611DFAECF23750E699619ECCB6BA59F2C4E791E7093D104876A053428D4C2BB72CD4F2A82C47333186E5
 
2025-07-16 21:04:55,811  [INFO] [D][11:32:57][COMM]Main Task receive event:14 finished processing
 
2025-07-16 21:04:55,816  [INFO] [D][11:32:57][COMM]Main Task receive event:54
 
2025-07-16 21:04:55,819  [INFO] [D][11:32:57][COMM][D301]:type:1, trace id:0
 
2025-07-16 21:04:55,821  [INFO] [D][11:32:57][COMM]get bat basic info err
 
2025-07-16 21:04:55,830  [INFO] [D][11:32:57][HSDK][0] flush to flash addr:[0xE46900] --- write len --- [256]
 
2025-07-16 21:04:55,838  [INFO] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:04:55,846  [INFO] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[D302],priority[0],index[5],used[1]
 
2025-07-16 21:04:55,852  [INFO] [D][11:32:57][COMM]Main Task receive event:54 finished processing
 
2025-07-16 21:04:55,854  [INFO] [D][11:32:57][CAT1]<<< 
 
2025-07-16 21:04:55,855  [INFO] SEND OK
 
2025-07-16 21:04:55,855  [INFO] 
 
2025-07-16 21:04:55,860  [INFO] [D][11:32:57][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:04:55,863  [INFO] [D][11:32:57][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:04:55,864  [INFO] 
 
2025-07-16 21:04:55,870  [INFO] [D][11:32:57][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:55,875  [INFO] [D][11:32:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:04:55,877  [INFO] [D][11:32:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:04:55,884  [INFO] [D][11:32:57][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:55,889  [INFO] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:55,891  [INFO] [D][11:32:57][PROT]M2M Send ok [1730201577]
 
2025-07-16 21:05:00,917  [INFO] [D][11:33:03][PROT]CLEAN,SEND:1
 
2025-07-16 21:05:00,930  [INFO] [D][11:33:03][PROT]index:1 1730201583
 
2025-07-16 21:05:00,933  [INFO] [D][11:33:03][PROT]is_send:0
 
2025-07-16 21:05:00,935  [INFO] [D][11:33:03][PROT]sequence_num:25
 
2025-07-16 21:05:00,938  [INFO] [D][11:33:03][PROT]retry_timeout:0
 
2025-07-16 21:05:00,941  [INFO] [D][11:33:03][PROT]retry_times:2
 
2025-07-16 21:05:00,944  [INFO] [D][11:33:03][PROT]send_path:0x2
 
2025-07-16 21:05:00,949  [INFO] [D][11:33:03][PROT]min_index:1, type:0x5D05, priority:3
 
2025-07-16 21:05:00,955  [INFO] [D][11:33:03][PROT]===========================================================
 
2025-07-16 21:05:00,961  [INFO] [W][11:33:03][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201583]
 
2025-07-16 21:05:00,970  [INFO] [D][11:33:03][PROT]===========================================================
 
2025-07-16 21:05:00,972  [INFO] [D][11:33:03][COMM]PB encode data:29
 
2025-07-16 21:05:00,978  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:05:00,983  [INFO] [D][11:33:03][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:00,986  [INFO] [D][11:33:03][PROT]Send_TO_M2M [1730201583]
 
2025-07-16 21:05:00,991  [INFO] [D][11:33:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:00,994  [INFO] [D][11:33:03][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:00,999  [INFO] [D][11:33:03][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:01,002  [INFO] [D][11:33:03][M2M ]m2m send data len[134]
 
2025-07-16 21:05:01,007  [INFO] [D][11:33:03][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:01,014  [INFO] [D][11:33:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:05:01,019  [INFO] [D][11:33:03][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:01,021  [INFO] [D][11:33:03][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:05:01,023  [INFO] 
 
2025-07-16 21:05:01,028  [INFO] [D][11:33:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:01,033  [INFO] [D][11:33:03][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:05:01,045  [INFO] 0043B6F9113311331133113311331B88B1F5CF014126E6864C9DF772B21C1E00947763C833BA91D2CC2CA3EBD426C4E3629B50F48365DC642D6AF1A949A31C3043713C
 
2025-07-16 21:05:01,047  [INFO] [D][11:33:03][CAT1]<<< 
 
2025-07-16 21:05:01,048  [INFO] SEND OK
 
2025-07-16 21:05:01,048  [INFO] 
 
2025-07-16 21:05:01,053  [INFO] [D][11:33:03][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:01,056  [INFO] [D][11:33:03][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:01,057  [INFO] 
 
2025-07-16 21:05:01,061  [INFO] [D][11:33:03][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:01,067  [INFO] [D][11:33:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:01,071  [INFO] [D][11:33:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:01,074  [INFO] [D][11:33:03][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:01,080  [INFO] [D][11:33:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:01,082  [INFO] [D][11:33:03][PROT]M2M Send ok [1730201583]
 
2025-07-16 21:05:06,324  [INFO] [D][11:33:08][PROT]CLEAN,SEND:1
 
2025-07-16 21:05:06,335  [INFO] [D][11:33:08][PROT]index:1 1730201588
 
2025-07-16 21:05:06,338  [INFO] [D][11:33:08][PROT]is_send:0
 
2025-07-16 21:05:06,341  [INFO] [D][11:33:08][PROT]sequence_num:25
 
2025-07-16 21:05:06,344  [INFO] [D][11:33:08][PROT]retry_timeout:0
 
2025-07-16 21:05:06,346  [INFO] [D][11:33:08][PROT]retry_times:1
 
2025-07-16 21:05:06,350  [INFO] [D][11:33:08][PROT]send_path:0x2
 
2025-07-16 21:05:06,355  [INFO] [D][11:33:08][PROT]min_index:1, type:0x5D05, priority:3
 
2025-07-16 21:05:06,361  [INFO] [D][11:33:08][PROT]===========================================================
 
2025-07-16 21:05:06,367  [INFO] [W][11:33:08][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201588]
 
2025-07-16 21:05:06,375  [INFO] [D][11:33:08][PROT]===========================================================
 
2025-07-16 21:05:06,377  [INFO] [D][11:33:08][COMM]PB encode data:29
 
2025-07-16 21:05:06,383  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:05:06,389  [INFO] [D][11:33:08][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:06,392  [INFO] [D][11:33:08][PROT]Send_TO_M2M [1730201588]
 
2025-07-16 21:05:06,398  [INFO] [D][11:33:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:06,400  [INFO] [D][11:33:08][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:06,406  [INFO] [D][11:33:08][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:06,408  [INFO] [D][11:33:08][M2M ]m2m send data len[134]
 
2025-07-16 21:05:06,414  [INFO] [D][11:33:08][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:06,420  [INFO] [D][11:33:08][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:05:06,425  [INFO] [D][11:33:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:06,430  [INFO] [D][11:33:08][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:06,433  [INFO] [D][11:33:08][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:05:06,433  [INFO] 
 
2025-07-16 21:05:06,439  [INFO] [D][11:33:08][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:05:06,451  [INFO] 0043B6F7113311331133113311331B88B1B4035DFA2366F921D82D176C32CB1116AED5229C56CA0DD0A70F00B8C5179CA5DAA0250085FD670EA5EEB6632E2EB4525AC8
 
2025-07-16 21:05:06,453  [INFO] [D][11:33:08][CAT1]<<< 
 
2025-07-16 21:05:06,454  [INFO] SEND OK
 
2025-07-16 21:05:06,454  [INFO] 
 
2025-07-16 21:05:06,459  [INFO] [D][11:33:08][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:06,462  [INFO] [D][11:33:08][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:06,463  [INFO] 
 
2025-07-16 21:05:06,467  [INFO] [D][11:33:08][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:06,473  [INFO] [D][11:33:08][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:06,477  [INFO] [D][11:33:08][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:06,481  [INFO] [D][11:33:08][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:06,486  [INFO] [D][11:33:08][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:06,489  [INFO] [D][11:33:08][PROT]M2M Send ok [1730201588]
 
2025-07-16 21:05:06,673  [INFO] [D][11:33:08][CAT1]closed : 0
 
2025-07-16 21:05:06,678  [INFO] [D][11:33:08][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:05:06,680  [INFO] [D][11:33:08][SAL ]socket closed id[0]
 
2025-07-16 21:05:06,685  [INFO] [D][11:33:08][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:05:06,692  [INFO] [D][11:33:08][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:05:06,694  [INFO] [D][11:33:08][M2M ]m2m select fd[4]
 
2025-07-16 21:05:06,699  [INFO] [D][11:33:08][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:05:06,702  [INFO] [D][11:33:08][M2M ]tcpclient close[4]
 
2025-07-16 21:05:06,704  [INFO] [D][11:33:08][SAL ]socket[4] has closed
 
2025-07-16 21:05:06,710  [INFO] [D][11:33:08][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:05:06,716  [INFO] [D][11:33:08][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 7
 
2025-07-16 21:05:06,734  [INFO] [D][11:33:08][COMM]Main Task receive event:86
 
2025-07-16 21:05:06,743  [INFO] [W][11:33:08][PROT]remove success[1730201588],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:05:06,751  [INFO] [W][11:33:08][PROT]add success [1730201588],send_path[2],type[8301],priority[0],index[6],used[1]
 
2025-07-16 21:05:06,757  [INFO] [D][11:33:08][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:05:11,729  [INFO] [D][11:33:13][PROT]CLEAN,SEND:1
 
2025-07-16 21:05:11,740  [INFO] [D][11:33:13][PROT]CLEAN:1
 
2025-07-16 21:05:11,752  [INFO] [D][11:33:13][PROT]index:0 1730201593
 
2025-07-16 21:05:11,755  [INFO] [D][11:33:13][PROT]is_send:0
 
2025-07-16 21:05:11,758  [INFO] [D][11:33:13][PROT]sequence_num:24
 
2025-07-16 21:05:11,761  [INFO] [D][11:33:13][PROT]retry_timeout:0
 
2025-07-16 21:05:11,763  [INFO] [D][11:33:13][PROT]retry_times:1
 
2025-07-16 21:05:11,766  [INFO] [D][11:33:13][PROT]send_path:0x2
 
2025-07-16 21:05:11,771  [INFO] [D][11:33:13][PROT]min_index:0, type:0x5006, priority:2
 
2025-07-16 21:05:11,777  [INFO] [D][11:33:13][PROT]===========================================================
 
2025-07-16 21:05:11,782  [INFO] [W][11:33:13][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730201593]
 
2025-07-16 21:05:11,790  [INFO] [D][11:33:13][PROT]===========================================================
 
2025-07-16 21:05:11,794  [INFO] [D][11:33:13][COMM]PB encode data:38
 
2025-07-16 21:05:11,803  [INFO] 0A24080210841E18322000280030003800401F481860007001800194238A0106000000000000
 
2025-07-16 21:05:11,805  [INFO] [D][11:33:13][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:11,810  [INFO] [D][11:33:13][PROT]Send_TO_M2M [1730201593]
 
2025-07-16 21:05:11,813  [INFO] [D][11:33:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:11,818  [INFO] [E][11:33:13][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:05:11,825  [INFO] [E][11:33:13][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:05:11,827  [INFO] [D][11:33:13][M2M ]m2m send data len[-1]
 
2025-07-16 21:05:11,833  [INFO] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:11,838  [INFO] [E][11:33:14][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:05:11,841  [INFO] [E][11:33:14][PROT]M2M Send Fail [1730201594]
 
2025-07-16 21:05:11,843  [INFO] [D][11:33:14][PROT]CLEAN,SEND:0
 
2025-07-16 21:05:11,850  [INFO] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:05:11,852  [INFO] [D][11:33:14][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:05:11,858  [INFO] [D][11:33:14][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:11,859  [INFO] 
 
2025-07-16 21:05:11,859  [INFO] [D][11:33:14][PROT]CLEAN:0
 
2025-07-16 21:05:11,864  [INFO] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:05:11,866  [INFO] [D][11:33:14][CAT1]<<< 
 
2025-07-16 21:05:11,867  [INFO] +CGATT: 1
 
2025-07-16 21:05:11,869  [INFO] 
 
2025-07-16 21:05:11,869  [INFO] OK
 
2025-07-16 21:05:11,870  [INFO] 
 
2025-07-16 21:05:11,872  [INFO] [D][11:33:14][CAT1]tx ret[12] >>> AT+CGATT=0
 
2025-07-16 21:05:11,874  [INFO] 
 
2025-07-16 21:05:11,878  [INFO] [D][11:33:14][COMM]Main Task receive event:79
 
2025-07-16 21:05:11,881  [INFO] [D][11:33:14][COMM]Receive protocol ACK TIMEOUT event
 
2025-07-16 21:05:11,886  [INFO] [D][11:33:14][COMM]Main Task receive event:79 finished processing
 
2025-07-16 21:05:11,889  [INFO] +WIFISCAN:8,0,FCD733633E16,-31
 
2025-07-16 21:05:11,891  [INFO] +WIFISCAN:8,1,8ED733633E16,-32
 
2025-07-16 21:05:11,894  [INFO] +WIFISCAN:8,2,A400E2F462C0,-59
 
2025-07-16 21:05:11,896  [INFO] +WIFISCAN:8,3,E22E0BF5E7EF,-61
 
2025-07-16 21:05:11,900  [INFO] +WIFISCAN:8,4,A400E2F462C1,-61
 
2025-07-16 21:05:11,903  [INFO] +WIFISCAN:8,5,909838971A70,-66
 
2025-07-16 21:05:11,905  [INFO] +WIFISCAN:8,6,A400E2F468E2,-76
 
2025-07-16 21:05:11,908  [INFO] +WIFISCAN:8,7,A400E2F464E1,-77
 
2025-07-16 21:05:11,910  [INFO] 
 
2025-07-16 21:05:11,915  [INFO] [D][11:33:14][CAT1]wifi scan report total[8]
 
2025-07-16 21:05:11,919  [INFO] [D][11:33:14][CAT1]wifi scan result rpt len[256], retval[256]
 
2025-07-16 21:05:11,976  [INFO] [D][11:33:14][GNSS]recv submsg id[3]
 
2025-07-16 21:05:11,978  [INFO] [D][11:33:14][GNSS]handlerWifiScanDone
 
2025-07-16 21:05:11,985  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[0]mac:FCD733633E16 ssid: rssi:-31
 
2025-07-16 21:05:11,992  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[1]mac:8ED733633E16 ssid: rssi:-32
 
2025-07-16 21:05:11,999  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[2]mac:A400E2F462C0 ssid: rssi:-59
 
2025-07-16 21:05:12,007  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[3]mac:E22E0BF5E7EF ssid: rssi:-61
 
2025-07-16 21:05:12,012  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[4]mac:A400E2F462C1 ssid: rssi:-61
 
2025-07-16 21:05:12,021  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[5]mac:909838971A70 ssid: rssi:-66
 
2025-07-16 21:05:12,027  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[6]mac:A400E2F468E2 ssid: rssi:-76
 
2025-07-16 21:05:12,032  [INFO] [D][11:33:14][GNSS]frm_wifi_scan_callback:[7]mac:A400E2F464E1 ssid: rssi:-77
 
2025-07-16 21:05:12,042  [INFO] [D][11:33:14][HSDK][0] flush to flash addr:[0xE46A00] --- write len --- [256]
 
2025-07-16 21:05:12,048  [INFO] [W][11:33:14][PROT]remove success[1730201594],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:05:12,057  [INFO] [W][11:33:14][PROT]add success [1730201594],send_path[2],type[5103],priority[0],index[0],used[1]
 
2025-07-16 21:05:12,120  [INFO] [D][11:33:14][CAT1]<<< 
 
2025-07-16 21:05:12,121  [INFO] OK
 
2025-07-16 21:05:12,122  [INFO] 
 
2025-07-16 21:05:12,125  [INFO] [D][11:33:14][CAT1]exec over: func id: 10, ret: 6
 
2025-07-16 21:05:12,127  [INFO] [D][11:33:14][CAT1]sub id: 10, ret: 6
 
2025-07-16 21:05:12,128  [INFO] 
 
2025-07-16 21:05:12,134  [INFO] [D][11:33:14][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:12,140  [INFO] [D][11:33:14][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
 
2025-07-16 21:05:12,142  [INFO] [D][11:33:14][M2M ]m2m gsm shut done, ret[0]
 
2025-07-16 21:05:12,147  [INFO] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:12,153  [INFO] [D][11:33:14][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:05:12,159  [INFO] [D][11:33:14][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:05:12,163  [INFO] [D][11:33:14][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:05:12,170  [INFO] [D][11:33:14][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:05:12,176  [INFO] [D][11:33:14][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:05:12,181  [INFO] [D][11:33:14][CAT1]pdpdeact urc len[22]
 
2025-07-16 21:05:12,183  [INFO] [D][11:33:14][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:05:12,186  [INFO] [D][11:33:14][CAT1]at ops open socket[0]
 
2025-07-16 21:05:12,192  [INFO] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:05:12,197  [INFO] [D][11:33:14][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:12,198  [INFO] 
 
2025-07-16 21:05:12,198  [INFO] [D][11:33:14][CAT1]<<< 
 
2025-07-16 21:05:12,200  [INFO] +CGATT: 0
 
2025-07-16 21:05:12,200  [INFO] 
 
2025-07-16 21:05:12,200  [INFO] OK
 
2025-07-16 21:05:12,200  [INFO] 
 
2025-07-16 21:05:12,203  [INFO] [D][11:33:14][CAT1]tx ret[12] >>> AT+CGATT=1
 
2025-07-16 21:05:12,204  [INFO] 
 
2025-07-16 21:05:12,506  [INFO] [D][11:33:14][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:05:12,557  [INFO] [D][11:33:14][COMM]f:frm_violent_loading_over_thr_process. is_first_over pitch:[127]
 
2025-07-16 21:05:12,558  [INFO] 
 
2025-07-16 21:05:13,505  [INFO] [D][11:33:15][COMM]398758 imu init OK
 
2025-07-16 21:05:13,596  [INFO] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,597  [INFO] OK
 
2025-07-16 21:05:13,605  [INFO] 
 
2025-07-16 21:05:13,608  [INFO] [D][11:33:15][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:13,609  [INFO] 
 
2025-07-16 21:05:13,637  [INFO] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,639  [INFO] +CGATT: 1
 
2025-07-16 21:05:13,639  [INFO] 
 
2025-07-16 21:05:13,639  [INFO] OK
 
2025-07-16 21:05:13,648  [INFO] 
 
2025-07-16 21:05:13,650  [INFO] [D][11:33:15][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:05:13,651  [INFO] 
 
2025-07-16 21:05:13,670  [INFO] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,672  [INFO] +CSQ: 31,99
 
2025-07-16 21:05:13,673  [INFO] 
 
2025-07-16 21:05:13,673  [INFO] OK
 
2025-07-16 21:05:13,681  [INFO] 
 
2025-07-16 21:05:13,684  [INFO] [D][11:33:15][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:05:13,685  [INFO] 
 
2025-07-16 21:05:13,704  [INFO] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,706  [INFO] OK
 
2025-07-16 21:05:13,714  [INFO] 
 
2025-07-16 21:05:13,718  [INFO] [D][11:33:15][CAT1]tx ret[12] >>> AT+QIACT=1
 
2025-07-16 21:05:13,719  [INFO] 
 
2025-07-16 21:05:13,747  [INFO] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,747  [INFO] OK
 
2025-07-16 21:05:13,757  [INFO] 
 
2025-07-16 21:05:13,764  [INFO] [D][11:33:16][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:05:13,765  [INFO] 
 
2025-07-16 21:05:13,778  [INFO] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:13,779  [INFO] OK
 
2025-07-16 21:05:13,780  [INFO] 
 
2025-07-16 21:05:13,784  [INFO] [D][11:33:16][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:05:13,988  [INFO] [D][11:33:16][CAT1]opened : 0, 0
 
2025-07-16 21:05:13,991  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:13,997  [INFO] [D][11:33:16][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:05:14,003  [INFO] [D][11:33:16][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:05:14,008  [INFO] [D][11:33:16][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
 
2025-07-16 21:05:14,011  [INFO] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,016  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,019  [INFO] [D][11:33:16][PROT]index:2 1730201596
 
2025-07-16 21:05:14,021  [INFO] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,024  [INFO] [D][11:33:16][PROT]sequence_num:26
 
2025-07-16 21:05:14,027  [INFO] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,030  [INFO] [D][11:33:16][PROT]retry_times:1
 
2025-07-16 21:05:14,033  [INFO] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,039  [INFO] [D][11:33:16][PROT]min_index:2, type:0xFF0E, priority:0
 
2025-07-16 21:05:14,047  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,053  [INFO] [W][11:33:16][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,058  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,063  [INFO] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,067  [INFO] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,073  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,078  [INFO] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,081  [INFO] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,083  [INFO] [D][11:33:16][M2M ]m2m send data len[166]
 
2025-07-16 21:05:14,089  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,098  [INFO] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,100  [INFO] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,105  [INFO] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,166
 
2025-07-16 21:05:14,106  [INFO] 
 
2025-07-16 21:05:14,111  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,115  [INFO] [D][11:33:16][CAT1]Send Data To Server[166][169] ... ->:
 
2025-07-16 21:05:14,131  [INFO] 0053B9FB113311331133113311331B88B1EBFB1DB095A64A303BD12DB57B97520410891BEB5216DF71CD8452069F62517082FD3469F3FFCA648A25D070D883F8891824A076BB15CE66C55C6B802B8EEEB7D4C8
 
2025-07-16 21:05:14,132  [INFO] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,134  [INFO] SEND OK
 
2025-07-16 21:05:14,135  [INFO] 
 
2025-07-16 21:05:14,139  [INFO] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,142  [INFO] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,143  [INFO] 
 
2025-07-16 21:05:14,145  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,153  [INFO] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,155  [INFO] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,158  [INFO] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,163  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,166  [INFO] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:14,168  [INFO] [D][11:33:16][PROT]CLEAN:2
 
2025-07-16 21:05:14,174  [INFO] [D][11:33:16][PROT]index:3 1730201596
 
2025-07-16 21:05:14,178  [INFO] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,180  [INFO] [D][11:33:16][PROT]sequence_num:27
 
2025-07-16 21:05:14,183  [INFO] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,186  [INFO] [D][11:33:16][PROT]retry_times:1
 
2025-07-16 21:05:14,188  [INFO] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,194  [INFO] [D][11:33:16][PROT]min_index:3, type:0xC001, priority:0
 
2025-07-16 21:05:14,200  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,206  [INFO] [W][11:33:16][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,214  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,217  [INFO] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,222  [INFO] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,228  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,231  [INFO] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,234  [INFO] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,239  [INFO] [D][11:33:16][M2M ]m2m send data len[230]
 
2025-07-16 21:05:14,242  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,251  [INFO] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,256  [INFO] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,259  [INFO] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:05:14,259  [INFO] 
 
2025-07-16 21:05:14,264  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,269  [INFO] [D][11:33:16][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:05:14,291  [INFO] 0073B908113311331133113311331B88B10CCBEA11E5A9F42133A2D60D54907E4B7A8B794ED7712624C24CCA118F8D5BE51DD6EE662C342D16514DE55768D03164B3AF2C82F9DA2669FCB50941A9E9CAE97F41300ABB1240E2DD1CABAAA2E1E5F08E75C2386BD29E5F38355A4ACDF504C1582A
 
2025-07-16 21:05:14,291  [INFO] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,292  [INFO] SEND OK
 
2025-07-16 21:05:14,292  [INFO] 
 
2025-07-16 21:05:14,297  [INFO] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,301  [INFO] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,302  [INFO] 
 
2025-07-16 21:05:14,306  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,312  [INFO] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,314  [INFO] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,320  [INFO] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,323  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,328  [INFO] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:14,330  [INFO] [D][11:33:16][PROT]CLEAN:3
 
2025-07-16 21:05:14,333  [INFO] [D][11:33:16][PROT]index:4 1730201596
 
2025-07-16 21:05:14,336  [INFO] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,339  [INFO] [D][11:33:16][PROT]sequence_num:28
 
2025-07-16 21:05:14,343  [INFO] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,345  [INFO] [D][11:33:16][PROT]retry_times:1
 
2025-07-16 21:05:14,348  [INFO] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,352  [INFO] [D][11:33:16][PROT]min_index:4, type:0x5A07, priority:0
 
2025-07-16 21:05:14,362  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,367  [INFO] [W][11:33:16][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,372  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,375  [INFO] [D][11:33:16][COMM]PB encode data:35
 
2025-07-16 21:05:14,384  [INFO] 0A210A1F180020FBFF012880CB80B906300340016800A801B38E83B906F80100C00200
 
2025-07-16 21:05:14,386  [INFO] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,392  [INFO] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,395  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,400  [INFO] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,403  [INFO] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,409  [INFO] [D][11:33:16][M2M ]m2m send data len[134]
 
2025-07-16 21:05:14,412  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,420  [INFO] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,422  [INFO] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,428  [INFO] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:05:14,429  [INFO] 
 
2025-07-16 21:05:14,434  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,439  [INFO] [D][11:33:16][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:05:14,452  [INFO] 0043B60A113311331133113311331B88B1BB07D1896932C9D95E259C7101F42D1B2A99D2642DAA383694E29DEDCAAE651DA8732A525C3E10557A664CCBACBD8511CDC1
 
2025-07-16 21:05:14,453  [INFO] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,454  [INFO] SEND OK
 
2025-07-16 21:05:14,454  [INFO] 
 
2025-07-16 21:05:14,459  [INFO] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,461  [INFO] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,462  [INFO] 
 
2025-07-16 21:05:14,467  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,472  [INFO] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,476  [INFO] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,482  [INFO] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,484  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,490  [INFO] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:14,492  [INFO] [D][11:33:16][PROT]CLEAN:4
 
2025-07-16 21:05:14,495  [INFO] [D][11:33:16][PROT]index:5 1730201596
 
2025-07-16 21:05:14,498  [INFO] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,501  [INFO] [D][11:33:16][PROT]sequence_num:29
 
2025-07-16 21:05:14,503  [INFO] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,506  [INFO] [D][11:33:16][PROT]retry_times:3
 
2025-07-16 21:05:14,509  [INFO] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,514  [INFO] [D][11:33:16][PROT]min_index:5, type:0xD302, priority:0
 
2025-07-16 21:05:14,520  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,529  [INFO] [W][11:33:16][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,535  [INFO] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,537  [INFO] [D][11:33:16][COMM]PB encode data:11
 
2025-07-16 21:05:14,539  [INFO] 0A0908011A013030013A00
 
2025-07-16 21:05:14,545  [INFO] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,548  [INFO] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,554  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,556  [INFO] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,562  [INFO] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,564  [INFO] [D][11:33:16][M2M ]m2m send data len[102]
 
2025-07-16 21:05:14,570  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,577  [INFO] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,582  [INFO] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,585  [INFO] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:05:14,586  [INFO] 
 
2025-07-16 21:05:14,591  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,615  [INFO] [D][11:33:16][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:05:14,616  [INFO] 0033B60F113311331133113311331B88B127D41B2485247DDCA796E9C7D2A829E9BEA4B34646BC2935B9A01B24D8B126F00D29
 
2025-07-16 21:05:14,617  [INFO] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,617  [INFO] SEND OK
 
2025-07-16 21:05:14,617  [INFO] 
 
2025-07-16 21:05:14,618  [INFO] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,618  [INFO] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,619  [INFO] 
 
2025-07-16 21:05:14,620  [INFO] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,627  [INFO] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,630  [INFO] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,634  [INFO] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,640  [INFO] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,642  [INFO] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:19,677  [INFO] [D][11:33:21][PROT]CLEAN,SEND:5
 
2025-07-16 21:05:19,690  [INFO] [D][11:33:21][PROT]index:5 1730201601
 
2025-07-16 21:05:19,693  [INFO] [D][11:33:21][PROT]is_send:0
 
2025-07-16 21:05:19,696  [INFO] [D][11:33:21][PROT]sequence_num:29
 
2025-07-16 21:05:19,699  [INFO] [D][11:33:21][PROT]retry_timeout:0
 
2025-07-16 21:05:19,702  [INFO] [D][11:33:21][PROT]retry_times:2
 
2025-07-16 21:05:19,704  [INFO] [D][11:33:21][PROT]send_path:0x2
 
2025-07-16 21:05:19,710  [INFO] [D][11:33:21][PROT]min_index:5, type:0xD302, priority:0
 
2025-07-16 21:05:19,716  [INFO] [D][11:33:21][PROT]===========================================================
 
2025-07-16 21:05:19,721  [INFO] [W][11:33:21][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201601]
 
2025-07-16 21:05:19,729  [INFO] [D][11:33:21][PROT]===========================================================
 
2025-07-16 21:05:19,732  [INFO] [D][11:33:21][COMM]PB encode data:11
 
2025-07-16 21:05:19,735  [INFO] 0A0908011A013030013A00
 
2025-07-16 21:05:19,741  [INFO] [D][11:33:21][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:19,743  [INFO] [D][11:33:21][PROT]Send_TO_M2M [1730201601]
 
2025-07-16 21:05:19,749  [INFO] [D][11:33:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:19,751  [INFO] [D][11:33:21][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:19,757  [INFO] [D][11:33:21][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:19,759  [INFO] [D][11:33:21][M2M ]m2m send data len[102]
 
2025-07-16 21:05:19,762  [INFO] [D][11:33:21][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:19,772  [INFO] [D][11:33:21][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:05:19,777  [INFO] [D][11:33:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:19,781  [INFO] [D][11:33:21][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:19,784  [INFO] [D][11:33:21][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:05:19,787  [INFO] 
 
2025-07-16 21:05:19,790  [INFO] [D][11:33:21][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:05:19,800  [INFO] 0033B600113311331133113311331B88B154091CAD635DECD3DEBDF7B1B9FF12883BC02FDC9D8F96DB892FD5A0F9F0E554A07A
 
2025-07-16 21:05:19,802  [INFO] [D][11:33:21][CAT1]<<< 
 
2025-07-16 21:05:19,803  [INFO] SEND OK
 
2025-07-16 21:05:19,803  [INFO] 
 
2025-07-16 21:05:19,807  [INFO] [D][11:33:21][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:19,810  [INFO] [D][11:33:21][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:19,811  [INFO] 
 
2025-07-16 21:05:19,815  [INFO] [D][11:33:21][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:19,821  [INFO] [D][11:33:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:19,823  [INFO] [D][11:33:22][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:19,829  [INFO] [D][11:33:22][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:19,835  [INFO] [D][11:33:22][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:19,838  [INFO] [D][11:33:22][PROT]M2M Send ok [1730201602]
 
2025-07-16 21:05:25,082  [INFO] [D][11:33:27][PROT]CLEAN,SEND:5
 
2025-07-16 21:05:25,093  [INFO] [D][11:33:27][PROT]index:5 1730201607
 
2025-07-16 21:05:25,097  [INFO] [D][11:33:27][PROT]is_send:0
 
2025-07-16 21:05:25,100  [INFO] [D][11:33:27][PROT]sequence_num:29
 
2025-07-16 21:05:25,103  [INFO] [D][11:33:27][PROT]retry_timeout:0
 
2025-07-16 21:05:25,105  [INFO] [D][11:33:27][PROT]retry_times:1
 
2025-07-16 21:05:25,108  [INFO] [D][11:33:27][PROT]send_path:0x2
 
2025-07-16 21:05:25,113  [INFO] [D][11:33:27][PROT]min_index:5, type:0xD302, priority:0
 
2025-07-16 21:05:25,120  [INFO] [D][11:33:27][PROT]===========================================================
 
2025-07-16 21:05:25,126  [INFO] [W][11:33:27][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201607]
 
2025-07-16 21:05:25,133  [INFO] [D][11:33:27][PROT]===========================================================
 
2025-07-16 21:05:25,136  [INFO] [D][11:33:27][COMM]PB encode data:11
 
2025-07-16 21:05:25,139  [INFO] 0A0908011A013030013A00
 
2025-07-16 21:05:25,145  [INFO] [D][11:33:27][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:25,147  [INFO] [D][11:33:27][PROT]Send_TO_M2M [1730201607]
 
2025-07-16 21:05:25,153  [INFO] [D][11:33:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:25,155  [INFO] [D][11:33:27][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:25,161  [INFO] [D][11:33:27][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:25,163  [INFO] [D][11:33:27][M2M ]m2m send data len[102]
 
2025-07-16 21:05:25,167  [INFO] [D][11:33:27][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:25,175  [INFO] [D][11:33:27][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:05:25,181  [INFO] [D][11:33:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:25,187  [INFO] [D][11:33:27][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:25,189  [INFO] [D][11:33:27][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:05:25,190  [INFO] 
 
2025-07-16 21:05:25,194  [INFO] [D][11:33:27][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:05:25,204  [INFO] 0033B60D113311331133113311331B88B1E8FA2FD3469ECA1643B8F85A7F674BAEABB12FF0B385A79B10B529DD84401911E1E3
 
2025-07-16 21:05:25,205  [INFO] [D][11:33:27][CAT1]<<< 
 
2025-07-16 21:05:25,206  [INFO] SEND OK
 
2025-07-16 21:05:25,206  [INFO] 
 
2025-07-16 21:05:25,211  [INFO] [D][11:33:27][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:25,214  [INFO] [D][11:33:27][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:25,215  [INFO] 
 
2025-07-16 21:05:25,220  [INFO] [D][11:33:27][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:25,226  [INFO] [D][11:33:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:25,227  [INFO] [D][11:33:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:25,235  [INFO] [D][11:33:27][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:25,239  [INFO] [D][11:33:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:25,241  [INFO] [D][11:33:27][PROT]M2M Send ok [1730201607]
 
2025-07-16 21:05:30,487  [INFO] [D][11:33:32][PROT]CLEAN,SEND:5
 
2025-07-16 21:05:30,499  [INFO] [D][11:33:32][PROT]CLEAN:5
 
2025-07-16 21:05:30,510  [INFO] [D][11:33:32][PROT]index:6 1730201612
 
2025-07-16 21:05:30,513  [INFO] [D][11:33:32][PROT]is_send:0
 
2025-07-16 21:05:30,516  [INFO] [D][11:33:32][PROT]sequence_num:30
 
2025-07-16 21:05:30,518  [INFO] [D][11:33:32][PROT]retry_timeout:0
 
2025-07-16 21:05:30,522  [INFO] [D][11:33:32][PROT]retry_times:1
 
2025-07-16 21:05:30,524  [INFO] [D][11:33:32][PROT]send_path:0x2
 
2025-07-16 21:05:30,530  [INFO] [D][11:33:32][PROT]min_index:6, type:0x8301, priority:0
 
2025-07-16 21:05:30,536  [INFO] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,542  [INFO] [W][11:33:32][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201612]
 
2025-07-16 21:05:30,550  [INFO] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,555  [INFO] [D][11:33:32][PROT]sending traceid [999999999990000F]
 
2025-07-16 21:05:30,558  [INFO] [D][11:33:32][PROT]Send_TO_M2M [1730201612]
 
2025-07-16 21:05:30,565  [INFO] [D][11:33:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:30,570  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:30,573  [INFO] [D][11:33:32][M2M ]socket has connect, gsm_send_status:1
 
2025-07-16 21:05:30,578  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:30,581  [INFO] [D][11:33:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:30,586  [INFO] [D][11:33:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:30,588  [INFO] [D][11:33:32][M2M ]m2m send data len[70]
 
2025-07-16 21:05:30,594  [INFO] [D][11:33:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:30,603  [INFO] [D][11:33:32][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:05:30,605  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:30,610  [INFO] [D][11:33:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:30,613  [INFO] [D][11:33:32][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:05:30,614  [INFO] 
 
2025-07-16 21:05:30,619  [INFO] [D][11:33:32][CAT1]Send Data To Server[70][73] ... ->:
 
2025-07-16 21:05:30,625  [INFO] 0023B90C113311331133113311331B88B86A998A58B2AEE933DD02A37C347E41B818F4
 
2025-07-16 21:05:30,627  [INFO] [D][11:33:32][CAT1]<<< 
 
2025-07-16 21:05:30,630  [INFO] SEND OK
 
2025-07-16 21:05:30,631  [INFO] 
 
2025-07-16 21:05:30,633  [INFO] [D][11:33:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:30,636  [INFO] [D][11:33:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:30,638  [INFO] 
 
2025-07-16 21:05:30,642  [INFO] [D][11:33:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:30,647  [INFO] [D][11:33:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:30,652  [INFO] [D][11:33:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:30,656  [INFO] [D][11:33:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:30,661  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:30,664  [INFO] [D][11:33:32][PROT]M2M Send ok [1730201612]
 
2025-07-16 21:05:30,667  [INFO] [D][11:33:32][PROT]CLEAN:6
 
2025-07-16 21:05:30,672  [INFO] [D][11:33:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:30,678  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:30,681  [INFO] [D][11:33:32][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:05:30,686  [INFO] [D][11:33:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:30,689  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:30,694  [INFO] [D][11:33:32][PROT]index:0 1730201612
 
2025-07-16 21:05:30,697  [INFO] [D][11:33:32][PROT]is_send:0
 
2025-07-16 21:05:30,702  [INFO] [D][11:33:32][PROT]sequence_num:31
 
2025-07-16 21:05:30,703  [INFO] [D][11:33:32][PROT]retry_timeout:0
 
2025-07-16 21:05:30,705  [INFO] [D][11:33:32][PROT]retry_times:1
 
2025-07-16 21:05:30,708  [INFO] [D][11:33:32][PROT]send_path:0x2
 
2025-07-16 21:05:30,714  [INFO] [D][11:33:32][PROT]min_index:0, type:0x5103, priority:0
 
2025-07-16 21:05:30,720  [INFO] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,726  [INFO] [W][11:33:32][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730201612]
 
2025-07-16 21:05:30,734  [INFO] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,736  [INFO] [D][11:33:32][COMM]PB encode data:86
 
2025-07-16 21:05:30,754  [INFO] 0A54080110001A120A0C4643443733333633334531361200203D1A120A0C3845443733333633334531361200203F1A120A0C413430304532463436324330120020751A120A0C45323245304246354537454612002079
 
2025-07-16 21:05:30,756  [INFO] [D][11:33:32][PROT]sending traceid [9999999999900010]
 
2025-07-16 21:05:30,761  [INFO] [D][11:33:32][PROT]Send_TO_M2M [1730201612]
 
2025-07-16 21:05:30,764  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:30,770  [INFO] [D][11:33:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:30,772  [INFO] [D][11:33:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:30,778  [INFO] [D][11:33:32][M2M ]m2m send data len[230]
 
2025-07-16 21:05:30,781  [INFO] [D][11:33:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:30,791  [INFO] [D][11:33:32][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x20059fd0] format[0]
 
2025-07-16 21:05:30,796  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:30,798  [INFO] [D][11:33:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:30,804  [INFO] [D][11:33:32][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:05:30,804  [INFO] 
 
2025-07-16 21:05:30,808  [INFO] [D][11:33:32][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:05:30,831  [INFO] 0073B601113311331133113311331B884B48D418FBD78D8DE94899A3FA1B2BC648EE551D8989117C2843EE23F66BC4C32544F20D9A4DC98A55C2D004BDD08A64EF22789535F8DE38CF475AC36CA89B3503D25E19DC5452B6159DA9A87FBBC69DDE199BF03DAC759932E7F71DD42EE6E76A2AB6
 
2025-07-16 21:05:30,831  [INFO] [D][11:33:32][CAT1]<<< 
 
2025-07-16 21:05:30,832  [INFO] SEND OK
 
2025-07-16 21:05:30,832  [INFO] 
 
2025-07-16 21:05:30,837  [INFO] [D][11:33:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:30,839  [INFO] [D][11:33:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:30,840  [INFO] 
 
2025-07-16 21:05:30,842  [INFO] [D][11:33:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:30,851  [INFO] [D][11:33:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:30,853  [INFO] [D][11:33:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:30,858  [INFO] [D][11:33:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:30,861  [INFO] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:30,865  [INFO] [D][11:33:32][PROT]M2M Send ok [1730201612]
 
2025-07-16 21:05:36,020  [INFO] [D][11:33:38][PROT]CLEAN,SEND:0
 
2025-07-16 21:05:36,031  [INFO] [D][11:33:38][PROT]CLEAN:0
 
2025-07-16 21:05:37,314  [INFO] [D][11:33:39][CAT1]closed : 0
 
2025-07-16 21:05:37,320  [INFO] [D][11:33:39][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:05:37,323  [INFO] [D][11:33:39][SAL ]socket closed id[0]
 
2025-07-16 21:05:37,329  [INFO] [D][11:33:39][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:05:37,334  [INFO] [D][11:33:39][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:05:37,337  [INFO] [D][11:33:39][M2M ]m2m select fd[4]
 
2025-07-16 21:05:37,342  [INFO] [D][11:33:39][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:05:37,345  [INFO] [D][11:33:39][M2M ]tcpclient close[4]
 
2025-07-16 21:05:37,348  [INFO] [D][11:33:39][SAL ]socket[4] has closed
 
2025-07-16 21:05:37,354  [INFO] [D][11:33:39][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:05:37,359  [INFO] [D][11:33:39][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 8
 
2025-07-16 21:05:37,362  [INFO] [D][11:33:39][COMM]Main Task receive event:86
 
2025-07-16 21:05:37,370  [INFO] [W][11:33:39][PROT]remove success[1730201619],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:05:37,379  [INFO] [D][11:33:39][HSDK][0] flush to flash addr:[0xE46B00] --- write len --- [256]
 
2025-07-16 21:05:37,381  [INFO] [D][11:33:39][PROT]index:0 1730201619
 
2025-07-16 21:05:37,384  [INFO] [D][11:33:39][PROT]is_send:0
 
2025-07-16 21:05:37,386  [INFO] [D][11:33:39][PROT]sequence_num:32
 
2025-07-16 21:05:37,389  [INFO] [D][11:33:39][PROT]retry_timeout:0
 
2025-07-16 21:05:37,392  [INFO] [D][11:33:39][PROT]retry_times:1
 
2025-07-16 21:05:37,395  [INFO] [D][11:33:39][PROT]send_path:0x2
 
2025-07-16 21:05:37,401  [INFO] [D][11:33:39][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:05:37,410  [INFO] [D][11:33:39][PROT]===========================================================
 
2025-07-16 21:05:37,415  [INFO] [D][11:33:39][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:37,417  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:37,422  [INFO] [D][11:33:39][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:05:37,428  [INFO] [D][11:33:39][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:05:37,434  [INFO] [D][11:33:39][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:05:37,440  [INFO] [D][11:33:39][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:05:37,449  [INFO] [D][11:33:39][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:05:37,454  [INFO] [W][11:33:39][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201619]
 
2025-07-16 21:05:37,460  [INFO] [D][11:33:39][PROT]===========================================================
 
2025-07-16 21:05:37,465  [INFO] [D][11:33:39][PROT]sending traceid [9999999999900011]
 
2025-07-16 21:05:37,467  [INFO] [D][11:33:39][PROT]Send_TO_M2M [1730201619]
 
2025-07-16 21:05:37,472  [INFO] [D][11:33:39][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:05:37,476  [INFO] [D][11:33:39][CAT1]at ops open socket[0]
 
2025-07-16 21:05:37,484  [INFO] [W][11:33:39][PROT]add success [1730201619],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:05:37,487  [INFO] [D][11:33:39][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:37,488  [INFO] 
 
2025-07-16 21:05:37,493  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:05:37,499  [INFO] [D][11:33:39][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:05:37,501  [INFO] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,502  [INFO] +CGATT: 1
 
2025-07-16 21:05:37,504  [INFO] 
 
2025-07-16 21:05:37,504  [INFO] OK
 
2025-07-16 21:05:37,505  [INFO] 
 
2025-07-16 21:05:37,508  [INFO] [D][11:33:39][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:05:37,509  [INFO] 
 
2025-07-16 21:05:37,510  [INFO] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,511  [INFO] +CSQ: 31,99
 
2025-07-16 21:05:37,512  [INFO] 
 
2025-07-16 21:05:37,512  [INFO] OK
 
2025-07-16 21:05:37,512  [INFO] 
 
2025-07-16 21:05:37,516  [INFO] [D][11:33:39][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:05:37,517  [INFO] 
 
2025-07-16 21:05:37,519  [INFO] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,521  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:05:37,522  [INFO] 
 
2025-07-16 21:05:37,522  [INFO] OK
 
2025-07-16 21:05:37,522  [INFO] 
 
2025-07-16 21:05:37,530  [INFO] [D][11:33:39][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:05:37,531  [INFO] 
 
2025-07-16 21:05:37,532  [INFO] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,533  [INFO] OK
 
2025-07-16 21:05:37,533  [INFO] 
 
2025-07-16 21:05:37,535  [INFO] [D][11:33:39][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:05:37,586  [INFO] [D][11:33:39][CAT1]opened : 0, 0
 
2025-07-16 21:05:37,589  [INFO] [D][11:33:39][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:37,595  [INFO] [D][11:33:39][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:05:37,601  [INFO] [D][11:33:39][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:05:37,606  [INFO] [D][11:33:39][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:05:37,611  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:37,614  [INFO] [D][11:33:39][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:37,617  [INFO] [D][11:33:39][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:37,626  [INFO] [D][11:33:39][M2M ]m2m send data len[70]
 
2025-07-16 21:05:37,627  [INFO] [D][11:33:39][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:37,633  [INFO] [D][11:33:39][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:05:37,639  [INFO] [D][11:33:39][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:37,642  [INFO] [D][11:33:39][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:05:37,643  [INFO] 
 
2025-07-16 21:05:37,648  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:37,653  [INFO] [D][11:33:39][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:05:37,659  [INFO] 0023B902113311331133113311331B88447E531D77A18C1F90D3091A14BA0659C3C1E0
 
2025-07-16 21:05:37,661  [INFO] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,661  [INFO] SEND OK
 
2025-07-16 21:05:37,662  [INFO] 
 
2025-07-16 21:05:37,668  [INFO] [D][11:33:39][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:37,670  [INFO] [D][11:33:39][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:37,671  [INFO] 
 
2025-07-16 21:05:37,675  [INFO] [D][11:33:39][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:37,681  [INFO] [D][11:33:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:37,684  [INFO] [D][11:33:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:37,689  [INFO] [D][11:33:39][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:37,693  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:37,698  [INFO] [D][11:33:39][PROT]M2M Send ok [1730201619]
 
2025-07-16 21:05:37,700  [INFO] [D][11:33:39][PROT]CLEAN:0
 
2025-07-16 21:05:37,704  [INFO] [D][11:33:39][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:37,709  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:37,714  [INFO] [D][11:33:39][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:05:37,717  [INFO] [D][11:33:39][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:37,723  [INFO] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:45,633  [INFO] [D][11:33:47][COMM]IMU: 430887 MEMS ERROR when cali 0
 
2025-07-16 21:05:56,835  [INFO] [D][11:33:59][COMM]IMU: 442091 MEMS ERROR when cali 0
 
2025-07-16 21:06:18,270  [INFO] [D][11:34:20][CAT1]closed : 0
 
2025-07-16 21:06:18,275  [INFO] [D][11:34:20][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:06:18,277  [INFO] [D][11:34:20][SAL ]socket closed id[0]
 
2025-07-16 21:06:18,283  [INFO] [D][11:34:20][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:06:18,289  [INFO] [D][11:34:20][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:06:18,292  [INFO] [D][11:34:20][M2M ]m2m select fd[4]
 
2025-07-16 21:06:18,297  [INFO] [D][11:34:20][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:06:18,301  [INFO] [D][11:34:20][M2M ]tcpclient close[4]
 
2025-07-16 21:06:18,303  [INFO] [D][11:34:20][SAL ]socket[4] has closed
 
2025-07-16 21:06:18,308  [INFO] [D][11:34:20][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:06:18,314  [INFO] [D][11:34:20][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 9
 
2025-07-16 21:06:18,320  [INFO] [D][11:34:20][COMM]Main Task receive event:86
 
2025-07-16 21:06:18,328  [INFO] [W][11:34:20][PROT]remove success[1730201660],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:06:18,337  [INFO] [W][11:34:20][PROT]add success [1730201660],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:06:18,342  [INFO] [D][11:34:20][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:06:18,345  [INFO] [D][11:34:20][PROT]index:0 1730201660
 
2025-07-16 21:06:18,348  [INFO] [D][11:34:20][PROT]is_send:0
 
2025-07-16 21:06:18,353  [INFO] [D][11:34:20][PROT]sequence_num:33
 
2025-07-16 21:06:18,356  [INFO] [D][11:34:20][PROT]retry_timeout:0
 
2025-07-16 21:06:18,359  [INFO] [D][11:34:20][PROT]retry_times:1
 
2025-07-16 21:06:18,362  [INFO] [D][11:34:20][PROT]send_path:0x2
 
2025-07-16 21:06:18,367  [INFO] [D][11:34:20][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:06:18,374  [INFO] [D][11:34:20][PROT]===========================================================
 
2025-07-16 21:06:18,379  [INFO] [W][11:34:20][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201660]
 
2025-07-16 21:06:18,387  [INFO] [D][11:34:20][PROT]===========================================================
 
2025-07-16 21:06:18,390  [INFO] [D][11:34:20][PROT]sending traceid [9999999999900012]
 
2025-07-16 21:06:18,396  [INFO] [D][11:34:20][PROT]Send_TO_M2M [1730201660]
 
2025-07-16 21:06:18,402  [INFO] [D][11:34:20][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:18,404  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:18,410  [INFO] [D][11:34:20][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:06:18,415  [INFO] [D][11:34:20][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:06:18,420  [INFO] [D][11:34:20][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:06:18,427  [INFO] [D][11:34:20][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:06:18,434  [INFO] [D][11:34:20][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:06:18,436  [INFO] [D][11:34:20][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:06:18,439  [INFO] [D][11:34:20][CAT1]at ops open socket[0]
 
2025-07-16 21:06:18,446  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:06:18,448  [INFO] [D][11:34:20][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:06:18,451  [INFO] 
 
2025-07-16 21:06:18,451  [INFO] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,453  [INFO] +CGATT: 1
 
2025-07-16 21:06:18,454  [INFO] 
 
2025-07-16 21:06:18,455  [INFO] OK
 
2025-07-16 21:06:18,455  [INFO] 
 
2025-07-16 21:06:18,456  [INFO] [D][11:34:20][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:06:18,457  [INFO] 
 
2025-07-16 21:06:18,459  [INFO] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,460  [INFO] +CSQ: 31,99
 
2025-07-16 21:06:18,461  [INFO] 
 
2025-07-16 21:06:18,462  [INFO] OK
 
2025-07-16 21:06:18,462  [INFO] 
 
2025-07-16 21:06:18,465  [INFO] [D][11:34:20][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:06:18,466  [INFO] 
 
2025-07-16 21:06:18,468  [INFO] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,471  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:06:18,473  [INFO] 
 
2025-07-16 21:06:18,473  [INFO] OK
 
2025-07-16 21:06:18,474  [INFO] 
 
2025-07-16 21:06:18,479  [INFO] [D][11:34:20][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:06:18,481  [INFO] 
 
2025-07-16 21:06:18,482  [INFO] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,483  [INFO] OK
 
2025-07-16 21:06:18,484  [INFO] 
 
2025-07-16 21:06:18,487  [INFO] [D][11:34:20][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:06:18,590  [INFO] [D][11:34:20][CAT1]opened : 0, 0
 
2025-07-16 21:06:18,593  [INFO] [D][11:34:20][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:18,599  [INFO] [D][11:34:20][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:06:18,604  [INFO] [D][11:34:20][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:06:18,609  [INFO] [D][11:34:20][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:06:18,615  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:06:18,618  [INFO] [D][11:34:20][SAL ]sock send credit cnt[6]
 
2025-07-16 21:06:18,620  [INFO] [D][11:34:20][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:06:18,627  [INFO] [D][11:34:20][M2M ]m2m send data len[70]
 
2025-07-16 21:06:18,629  [INFO] [D][11:34:20][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:06:18,638  [INFO] [D][11:34:20][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:06:18,643  [INFO] [D][11:34:20][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:06:18,646  [INFO] [D][11:34:20][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:06:18,647  [INFO] 
 
2025-07-16 21:06:18,651  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:06:18,656  [INFO] [D][11:34:20][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:06:18,662  [INFO] 0023B90E113311331133113311331B8847FC44142F18BB4EBE2C6FA772E52F1749450D
 
2025-07-16 21:06:18,665  [INFO] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,665  [INFO] SEND OK
 
2025-07-16 21:06:18,665  [INFO] 
 
2025-07-16 21:06:18,671  [INFO] [D][11:34:20][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:06:18,674  [INFO] [D][11:34:20][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:06:18,674  [INFO] 
 
2025-07-16 21:06:18,679  [INFO] [D][11:34:20][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:18,685  [INFO] [D][11:34:20][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:06:18,687  [INFO] [D][11:34:20][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:06:18,693  [INFO] [D][11:34:20][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:18,695  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:06:18,702  [INFO] [D][11:34:20][PROT]M2M Send ok [1730201660]
 
2025-07-16 21:06:18,704  [INFO] [D][11:34:20][PROT]CLEAN:0
 
2025-07-16 21:06:18,707  [INFO] [D][11:34:20][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:18,713  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:18,718  [INFO] [D][11:34:20][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:06:18,721  [INFO] [D][11:34:20][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:18,726  [INFO] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:06:51,644  [INFO] [D][11:34:53][COMM]IMU: 496907 MEMS ERROR when cali 0
 
2025-07-16 21:06:59,232  [INFO] [D][11:35:01][CAT1]closed : 0
 
2025-07-16 21:06:59,237  [INFO] [D][11:35:01][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:06:59,239  [INFO] [D][11:35:01][SAL ]socket closed id[0]
 
2025-07-16 21:06:59,245  [INFO] [D][11:35:01][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:06:59,251  [INFO] [D][11:35:01][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:06:59,254  [INFO] [D][11:35:01][M2M ]m2m select fd[4]
 
2025-07-16 21:06:59,259  [INFO] [D][11:35:01][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:06:59,262  [INFO] [D][11:35:01][M2M ]tcpclient close[4]
 
2025-07-16 21:06:59,265  [INFO] [D][11:35:01][SAL ]socket[4] has closed
 
2025-07-16 21:06:59,270  [INFO] [D][11:35:01][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:06:59,276  [INFO] [D][11:35:01][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 10
 
2025-07-16 21:06:59,278  [INFO] [D][11:35:01][COMM]Main Task receive event:86
 
2025-07-16 21:06:59,287  [INFO] [W][11:35:01][PROT]remove success[1730201701],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:06:59,292  [INFO] [D][11:35:01][PROT]index:0 1730201701
 
2025-07-16 21:06:59,295  [INFO] [D][11:35:01][PROT]is_send:0
 
2025-07-16 21:06:59,298  [INFO] [D][11:35:01][PROT]sequence_num:34
 
2025-07-16 21:06:59,301  [INFO] [D][11:35:01][PROT]retry_timeout:0
 
2025-07-16 21:06:59,303  [INFO] [D][11:35:01][PROT]retry_times:1
 
2025-07-16 21:06:59,306  [INFO] [D][11:35:01][PROT]send_path:0x2
 
2025-07-16 21:06:59,312  [INFO] [D][11:35:01][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:06:59,318  [INFO] [D][11:35:01][PROT]===========================================================
 
2025-07-16 21:06:59,324  [INFO] [W][11:35:01][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201701]
 
2025-07-16 21:06:59,332  [INFO] [D][11:35:01][PROT]===========================================================
 
2025-07-16 21:06:59,337  [INFO] [D][11:35:01][PROT]sending traceid [9999999999900013]
 
2025-07-16 21:06:59,339  [INFO] [D][11:35:01][PROT]Send_TO_M2M [1730201701]
 
2025-07-16 21:06:59,345  [INFO] [D][11:35:01][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:59,350  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:59,353  [INFO] [D][11:35:01][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:06:59,362  [INFO] [D][11:35:01][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:06:59,364  [INFO] [D][11:35:01][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:06:59,374  [INFO] [D][11:35:01][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:06:59,379  [INFO] [D][11:35:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:06:59,387  [INFO] [W][11:35:01][PROT]add success [1730201701],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:06:59,393  [INFO] [D][11:35:01][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:06:59,395  [INFO] [D][11:35:01][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:06:59,401  [INFO] [D][11:35:01][CAT1]at ops open socket[0]
 
2025-07-16 21:06:59,404  [INFO] [D][11:35:01][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:06:59,405  [INFO] 
 
2025-07-16 21:06:59,410  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:06:59,412  [INFO] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,412  [INFO] +CGATT: 1
 
2025-07-16 21:06:59,413  [INFO] 
 
2025-07-16 21:06:59,413  [INFO] OK
 
2025-07-16 21:06:59,413  [INFO] 
 
2025-07-16 21:06:59,417  [INFO] [D][11:35:01][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:06:59,419  [INFO] 
 
2025-07-16 21:06:59,419  [INFO] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,420  [INFO] +CSQ: 31,99
 
2025-07-16 21:06:59,421  [INFO] 
 
2025-07-16 21:06:59,421  [INFO] OK
 
2025-07-16 21:06:59,422  [INFO] 
 
2025-07-16 21:06:59,424  [INFO] [D][11:35:01][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:06:59,426  [INFO] 
 
2025-07-16 21:06:59,427  [INFO] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,429  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:06:59,430  [INFO] 
 
2025-07-16 21:06:59,430  [INFO] OK
 
2025-07-16 21:06:59,432  [INFO] 
 
2025-07-16 21:06:59,438  [INFO] [D][11:35:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:06:59,440  [INFO] 
 
2025-07-16 21:06:59,440  [INFO] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,441  [INFO] OK
 
2025-07-16 21:06:59,442  [INFO] 
 
2025-07-16 21:06:59,446  [INFO] [D][11:35:01][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:06:59,506  [INFO] [D][11:35:01][CAT1]opened : 0, 0
 
2025-07-16 21:06:59,509  [INFO] [D][11:35:01][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:59,515  [INFO] [D][11:35:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:06:59,520  [INFO] [D][11:35:01][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:06:59,525  [INFO] [D][11:35:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:06:59,532  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:06:59,533  [INFO] [D][11:35:01][SAL ]sock send credit cnt[6]
 
2025-07-16 21:06:59,537  [INFO] [D][11:35:01][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:06:59,543  [INFO] [D][11:35:01][M2M ]m2m send data len[70]
 
2025-07-16 21:06:59,545  [INFO] [D][11:35:01][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:06:59,554  [INFO] [D][11:35:01][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:06:59,558  [INFO] [D][11:35:01][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:06:59,562  [INFO] [D][11:35:01][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:06:59,563  [INFO] 
 
2025-07-16 21:06:59,569  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:06:59,573  [INFO] [D][11:35:01][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:06:59,579  [INFO] 0023B903113311331133113311331B88494B04E413E7D37CA1067EBEE67F1A9CC40EE3
 
2025-07-16 21:06:59,581  [INFO] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,582  [INFO] SEND OK
 
2025-07-16 21:06:59,582  [INFO] 
 
2025-07-16 21:06:59,587  [INFO] [D][11:35:01][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:06:59,590  [INFO] [D][11:35:01][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:06:59,591  [INFO] 
 
2025-07-16 21:06:59,596  [INFO] [D][11:35:01][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:59,602  [INFO] [D][11:35:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:06:59,603  [INFO] [D][11:35:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:06:59,609  [INFO] [D][11:35:01][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:59,612  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:06:59,618  [INFO] [D][11:35:01][PROT]M2M Send ok [1730201701]
 
2025-07-16 21:06:59,621  [INFO] [D][11:35:01][PROT]CLEAN:0
 
2025-07-16 21:06:59,624  [INFO] [D][11:35:01][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:59,630  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:59,635  [INFO] [D][11:35:01][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:06:59,637  [INFO] [D][11:35:01][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:59,643  [INFO] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:07:40,170  [INFO] [D][11:35:42][CAT1]closed : 0
 
2025-07-16 21:07:40,175  [INFO] [D][11:35:42][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:07:40,179  [INFO] [D][11:35:42][SAL ]socket closed id[0]
 
2025-07-16 21:07:40,184  [INFO] [D][11:35:42][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:07:40,190  [INFO] [D][11:35:42][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:07:40,192  [INFO] [D][11:35:42][M2M ]m2m select fd[4]
 
2025-07-16 21:07:40,198  [INFO] [D][11:35:42][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:07:40,200  [INFO] [D][11:35:42][M2M ]tcpclient close[4]
 
2025-07-16 21:07:40,203  [INFO] [D][11:35:42][SAL ]socket[4] has closed
 
2025-07-16 21:07:40,208  [INFO] [D][11:35:42][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:07:40,215  [INFO] [D][11:35:42][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 11
 
2025-07-16 21:07:40,218  [INFO] [D][11:35:42][COMM]Main Task receive event:86
 
2025-07-16 21:07:40,226  [INFO] [D][11:35:42][HSDK][0] flush to flash addr:[0xE46C00] --- write len --- [256]
 
2025-07-16 21:07:40,233  [INFO] [W][11:35:42][PROT]remove success[1730201742],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:07:40,237  [INFO] [D][11:35:42][PROT]index:0 1730201742
 
2025-07-16 21:07:40,240  [INFO] [D][11:35:42][PROT]is_send:0
 
2025-07-16 21:07:40,243  [INFO] [D][11:35:42][PROT]sequence_num:35
 
2025-07-16 21:07:40,245  [INFO] [D][11:35:42][PROT]retry_timeout:0
 
2025-07-16 21:07:40,248  [INFO] [D][11:35:42][PROT]retry_times:1
 
2025-07-16 21:07:40,251  [INFO] [D][11:35:42][PROT]send_path:0x2
 
2025-07-16 21:07:40,256  [INFO] [D][11:35:42][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:07:40,266  [INFO] [D][11:35:42][PROT]===========================================================
 
2025-07-16 21:07:40,270  [INFO] [W][11:35:42][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201742]
 
2025-07-16 21:07:40,276  [INFO] [D][11:35:42][PROT]===========================================================
 
2025-07-16 21:07:40,281  [INFO] [D][11:35:42][PROT]sending traceid [9999999999900014]
 
2025-07-16 21:07:40,284  [INFO] [D][11:35:42][PROT]Send_TO_M2M [1730201742]
 
2025-07-16 21:07:40,290  [INFO] [D][11:35:42][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:07:40,296  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:07:40,301  [INFO] [D][11:35:42][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:07:40,307  [INFO] [D][11:35:42][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:07:40,312  [INFO] [D][11:35:42][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:07:40,319  [INFO] [D][11:35:42][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:07:40,323  [INFO] [D][11:35:42][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:07:40,329  [INFO] [D][11:35:42][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:07:40,331  [INFO] [D][11:35:42][CAT1]at ops open socket[0]
 
2025-07-16 21:07:40,334  [INFO] [D][11:35:42][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:07:40,335  [INFO] 
 
2025-07-16 21:07:40,342  [INFO] [W][11:35:42][PROT]add success [1730201742],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:07:40,351  [INFO] [D][11:35:42][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:07:40,354  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:07:40,356  [INFO] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,358  [INFO] +CGATT: 1
 
2025-07-16 21:07:40,359  [INFO] 
 
2025-07-16 21:07:40,359  [INFO] OK
 
2025-07-16 21:07:40,360  [INFO] 
 
2025-07-16 21:07:40,361  [INFO] [D][11:35:42][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:07:40,363  [INFO] 
 
2025-07-16 21:07:40,365  [INFO] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,366  [INFO] +CSQ: 31,99
 
2025-07-16 21:07:40,366  [INFO] 
 
2025-07-16 21:07:40,366  [INFO] OK
 
2025-07-16 21:07:40,367  [INFO] 
 
2025-07-16 21:07:40,373  [INFO] [D][11:35:42][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:07:40,374  [INFO] 
 
2025-07-16 21:07:40,374  [INFO] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,375  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:07:40,376  [INFO] 
 
2025-07-16 21:07:40,376  [INFO] OK
 
2025-07-16 21:07:40,378  [INFO] 
 
2025-07-16 21:07:40,385  [INFO] [D][11:35:42][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:07:40,386  [INFO] 
 
2025-07-16 21:07:40,386  [INFO] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,387  [INFO] OK
 
2025-07-16 21:07:40,387  [INFO] 
 
2025-07-16 21:07:40,390  [INFO] [D][11:35:42][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:07:40,424  [INFO] [D][11:35:42][CAT1]opened : 0, 0
 
2025-07-16 21:07:40,428  [INFO] [D][11:35:42][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:07:40,434  [INFO] [D][11:35:42][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:07:40,439  [INFO] [D][11:35:42][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:07:40,444  [INFO] [D][11:35:42][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:07:40,450  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:07:40,453  [INFO] [D][11:35:42][SAL ]sock send credit cnt[6]
 
2025-07-16 21:07:40,456  [INFO] [D][11:35:42][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:07:40,461  [INFO] [D][11:35:42][M2M ]m2m send data len[70]
 
2025-07-16 21:07:40,464  [INFO] [D][11:35:42][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:07:40,472  [INFO] [D][11:35:42][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:07:40,477  [INFO] [D][11:35:42][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:07:40,481  [INFO] [D][11:35:42][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:07:40,482  [INFO] 
 
2025-07-16 21:07:40,486  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:07:40,491  [INFO] [D][11:35:42][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:07:40,497  [INFO] 0023B905113311331133113311331B8846C26D84433FEAC2A266FEFB27AC9BD9F4697B
 
2025-07-16 21:07:40,500  [INFO] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,501  [INFO] SEND OK
 
2025-07-16 21:07:40,501  [INFO] 
 
2025-07-16 21:07:40,505  [INFO] [D][11:35:42][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:07:40,508  [INFO] [D][11:35:42][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:07:40,510  [INFO] 
 
2025-07-16 21:07:40,514  [INFO] [D][11:35:42][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:07:40,520  [INFO] [D][11:35:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:07:40,522  [INFO] [D][11:35:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:07:40,528  [INFO] [D][11:35:42][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:07:40,530  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:07:40,536  [INFO] [D][11:35:42][PROT]M2M Send ok [1730201742]
 
2025-07-16 21:07:40,538  [INFO] [D][11:35:42][PROT]CLEAN:0
 
2025-07-16 21:07:40,541  [INFO] [D][11:35:42][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:07:40,548  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:07:40,553  [INFO] [D][11:35:42][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:07:40,556  [INFO] [D][11:35:42][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:07:40,562  [INFO] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:07:40,824  [INFO] [D][11:35:43][COMM]S->M yaw:INVALID
 
2025-07-16 21:07:41,881  [INFO] [D][11:35:44][COMM]M->S yaw:INVALID
 
2025-07-16 21:08:04,881  [INFO] [D][11:36:07][COMM]S->M yaw:INVALID
 
2025-07-16 21:08:06,158  [INFO] [D][11:36:08][COMM]M->S yaw:INVALID
 
2025-07-16 21:08:08,719  [INFO] [D][11:36:10][COMM]S->M yaw:INVALID
 
2025-07-16 21:08:10,115  [INFO] [D][11:36:12][COMM]M->S yaw:INVALID
 
2025-07-16 21:08:21,122  [INFO] [D][11:36:23][CAT1]closed : 0
 
2025-07-16 21:08:21,127  [INFO] [D][11:36:23][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:08:21,129  [INFO] [D][11:36:23][SAL ]socket closed id[0]
 
2025-07-16 21:08:21,135  [INFO] [D][11:36:23][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:08:21,141  [INFO] [D][11:36:23][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:08:21,144  [INFO] [D][11:36:23][M2M ]m2m select fd[4]
 
2025-07-16 21:08:21,149  [INFO] [D][11:36:23][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:08:21,152  [INFO] [D][11:36:23][M2M ]tcpclient close[4]
 
2025-07-16 21:08:21,155  [INFO] [D][11:36:23][SAL ]socket[4] has closed
 
2025-07-16 21:08:21,160  [INFO] [D][11:36:23][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:08:21,166  [INFO] [D][11:36:23][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 12
 
2025-07-16 21:08:21,198  [INFO] [D][11:36:23][COMM]Main Task receive event:86
 
2025-07-16 21:08:21,208  [INFO] [W][11:36:23][PROT]remove success[1730201783],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:08:21,216  [INFO] [W][11:36:23][PROT]add success [1730201783],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:08:21,221  [INFO] [D][11:36:23][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:08:21,224  [INFO] [D][11:36:23][PROT]index:0 1730201783
 
2025-07-16 21:08:21,227  [INFO] [D][11:36:23][PROT]is_send:0
 
2025-07-16 21:08:21,232  [INFO] [D][11:36:23][PROT]sequence_num:36
 
2025-07-16 21:08:21,234  [INFO] [D][11:36:23][PROT]retry_timeout:0
 
2025-07-16 21:08:21,238  [INFO] [D][11:36:23][PROT]retry_times:1
 
2025-07-16 21:08:21,241  [INFO] [D][11:36:23][PROT]send_path:0x2
 
2025-07-16 21:08:21,246  [INFO] [D][11:36:23][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:08:21,252  [INFO] [D][11:36:23][PROT]===========================================================
 
2025-07-16 21:08:21,257  [INFO] [W][11:36:23][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201783]
 
2025-07-16 21:08:21,266  [INFO] [D][11:36:23][PROT]===========================================================
 
2025-07-16 21:08:21,269  [INFO] [D][11:36:23][PROT]sending traceid [9999999999900015]
 
2025-07-16 21:08:21,275  [INFO] [D][11:36:23][PROT]Send_TO_M2M [1730201783]
 
2025-07-16 21:08:21,280  [INFO] [D][11:36:23][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:08:21,283  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:08:21,288  [INFO] [D][11:36:23][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:08:21,295  [INFO] [D][11:36:23][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:08:21,299  [INFO] [D][11:36:23][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:08:21,306  [INFO] [D][11:36:23][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:08:21,313  [INFO] [D][11:36:23][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:08:21,316  [INFO] [D][11:36:23][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:08:21,319  [INFO] [D][11:36:23][CAT1]at ops open socket[0]
 
2025-07-16 21:08:21,324  [INFO] [D][11:36:23][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:08:21,324  [INFO] 
 
2025-07-16 21:08:21,330  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:08:21,332  [INFO] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,333  [INFO] +CGATT: 1
 
2025-07-16 21:08:21,333  [INFO] 
 
2025-07-16 21:08:21,334  [INFO] OK
 
2025-07-16 21:08:21,334  [INFO] 
 
2025-07-16 21:08:21,335  [INFO] [D][11:36:23][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:08:21,336  [INFO] 
 
2025-07-16 21:08:21,338  [INFO] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,339  [INFO] +CSQ: 31,99
 
2025-07-16 21:08:21,340  [INFO] 
 
2025-07-16 21:08:21,341  [INFO] OK
 
2025-07-16 21:08:21,342  [INFO] 
 
2025-07-16 21:08:21,345  [INFO] [D][11:36:23][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:08:21,346  [INFO] 
 
2025-07-16 21:08:21,348  [INFO] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,350  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:08:21,350  [INFO] 
 
2025-07-16 21:08:21,350  [INFO] OK
 
2025-07-16 21:08:21,351  [INFO] 
 
2025-07-16 21:08:21,359  [INFO] [D][11:36:23][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:08:21,360  [INFO] 
 
2025-07-16 21:08:21,361  [INFO] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,362  [INFO] OK
 
2025-07-16 21:08:21,363  [INFO] 
 
2025-07-16 21:08:21,365  [INFO] [D][11:36:23][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:08:21,442  [INFO] [D][11:36:23][CAT1]opened : 0, 0
 
2025-07-16 21:08:21,444  [INFO] [D][11:36:23][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:08:21,452  [INFO] [D][11:36:23][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:08:21,457  [INFO] [D][11:36:23][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:08:21,462  [INFO] [D][11:36:23][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:08:21,467  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:08:21,470  [INFO] [D][11:36:23][SAL ]sock send credit cnt[6]
 
2025-07-16 21:08:21,473  [INFO] [D][11:36:23][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:08:21,478  [INFO] [D][11:36:23][M2M ]m2m send data len[70]
 
2025-07-16 21:08:21,481  [INFO] [D][11:36:23][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:08:21,490  [INFO] [D][11:36:23][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:08:21,495  [INFO] [D][11:36:23][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:08:21,498  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:08:21,504  [INFO] [D][11:36:23][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:08:21,504  [INFO] 
 
2025-07-16 21:08:21,508  [INFO] [D][11:36:23][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:08:21,516  [INFO] 0023B906113311331133113311331B884541A058DC1BB1DDE0EA468B42D2014749EB27
 
2025-07-16 21:08:21,518  [INFO] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,518  [INFO] SEND OK
 
2025-07-16 21:08:21,519  [INFO] 
 
2025-07-16 21:08:21,523  [INFO] [D][11:36:23][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:08:21,526  [INFO] [D][11:36:23][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:08:21,526  [INFO] 
 
2025-07-16 21:08:21,531  [INFO] [D][11:36:23][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:08:21,537  [INFO] [D][11:36:23][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:08:21,539  [INFO] [D][11:36:23][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:08:21,546  [INFO] [D][11:36:23][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:08:21,549  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:08:21,554  [INFO] [D][11:36:23][PROT]M2M Send ok [1730201783]
 
2025-07-16 21:08:21,556  [INFO] [D][11:36:23][PROT]CLEAN:0
 
2025-07-16 21:08:21,560  [INFO] [D][11:36:23][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:08:21,565  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:08:21,572  [INFO] [D][11:36:23][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:08:21,574  [INFO] [D][11:36:23][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:08:21,580  [INFO] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:02,098  [INFO] [D][11:37:04][CAT1]closed : 0
 
2025-07-16 21:09:02,103  [INFO] [D][11:37:04][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:09:02,105  [INFO] [D][11:37:04][SAL ]socket closed id[0]
 
2025-07-16 21:09:02,111  [INFO] [D][11:37:04][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:09:02,117  [INFO] [D][11:37:04][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:09:02,119  [INFO] [D][11:37:04][M2M ]m2m select fd[4]
 
2025-07-16 21:09:02,125  [INFO] [D][11:37:04][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:09:02,128  [INFO] [D][11:37:04][M2M ]tcpclient close[4]
 
2025-07-16 21:09:02,131  [INFO] [D][11:37:04][SAL ]socket[4] has closed
 
2025-07-16 21:09:02,136  [INFO] [D][11:37:04][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:09:02,142  [INFO] [D][11:37:04][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 13
 
2025-07-16 21:09:02,144  [INFO] [D][11:37:04][COMM]Main Task receive event:86
 
2025-07-16 21:09:02,153  [INFO] [W][11:37:04][PROT]remove success[1730201824],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:09:02,158  [INFO] [D][11:37:04][PROT]index:0 1730201824
 
2025-07-16 21:09:02,161  [INFO] [D][11:37:04][PROT]is_send:0
 
2025-07-16 21:09:02,164  [INFO] [D][11:37:04][PROT]sequence_num:37
 
2025-07-16 21:09:02,167  [INFO] [D][11:37:04][PROT]retry_timeout:0
 
2025-07-16 21:09:02,169  [INFO] [D][11:37:04][PROT]retry_times:1
 
2025-07-16 21:09:02,172  [INFO] [D][11:37:04][PROT]send_path:0x2
 
2025-07-16 21:09:02,177  [INFO] [D][11:37:04][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:09:02,184  [INFO] [D][11:37:04][PROT]===========================================================
 
2025-07-16 21:09:02,189  [INFO] [W][11:37:04][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201824]
 
2025-07-16 21:09:02,198  [INFO] [D][11:37:04][PROT]===========================================================
 
2025-07-16 21:09:02,203  [INFO] [D][11:37:04][PROT]sending traceid [9999999999900016]
 
2025-07-16 21:09:02,205  [INFO] [D][11:37:04][PROT]Send_TO_M2M [1730201824]
 
2025-07-16 21:09:02,212  [INFO] [D][11:37:04][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:02,217  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:02,219  [INFO] [D][11:37:04][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:09:02,229  [INFO] [D][11:37:04][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:09:02,231  [INFO] [D][11:37:04][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:09:02,240  [INFO] [D][11:37:04][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:09:02,245  [INFO] [D][11:37:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:09:02,248  [INFO] [D][11:37:04][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:09:02,250  [INFO] [D][11:37:04][CAT1]at ops open socket[0]
 
2025-07-16 21:09:02,257  [INFO] [D][11:37:04][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:09:02,257  [INFO] 
 
2025-07-16 21:09:02,264  [INFO] [W][11:37:04][PROT]add success [1730201824],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:09:02,270  [INFO] [D][11:37:04][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:09:02,275  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:09:02,278  [INFO] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,279  [INFO] +CGATT: 1
 
2025-07-16 21:09:02,279  [INFO] 
 
2025-07-16 21:09:02,279  [INFO] OK
 
2025-07-16 21:09:02,280  [INFO] 
 
2025-07-16 21:09:02,284  [INFO] [D][11:37:04][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:09:02,285  [INFO] 
 
2025-07-16 21:09:02,285  [INFO] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,286  [INFO] +CSQ: 31,99
 
2025-07-16 21:09:02,287  [INFO] 
 
2025-07-16 21:09:02,287  [INFO] OK
 
2025-07-16 21:09:02,287  [INFO] 
 
2025-07-16 21:09:02,289  [INFO] [D][11:37:04][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:09:02,292  [INFO] 
 
2025-07-16 21:09:02,292  [INFO] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,295  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:09:02,295  [INFO] 
 
2025-07-16 21:09:02,295  [INFO] OK
 
2025-07-16 21:09:02,297  [INFO] 
 
2025-07-16 21:09:02,303  [INFO] [D][11:37:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:09:02,304  [INFO] 
 
2025-07-16 21:09:02,305  [INFO] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,306  [INFO] OK
 
2025-07-16 21:09:02,306  [INFO] 
 
2025-07-16 21:09:02,310  [INFO] [D][11:37:04][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:09:02,356  [INFO] [D][11:37:04][CAT1]opened : 0, 0
 
2025-07-16 21:09:02,359  [INFO] [D][11:37:04][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:02,365  [INFO] [D][11:37:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:09:02,370  [INFO] [D][11:37:04][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:09:02,376  [INFO] [D][11:37:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:09:02,381  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:09:02,384  [INFO] [D][11:37:04][SAL ]sock send credit cnt[6]
 
2025-07-16 21:09:02,387  [INFO] [D][11:37:04][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:09:02,392  [INFO] [D][11:37:04][M2M ]m2m send data len[70]
 
2025-07-16 21:09:02,395  [INFO] [D][11:37:04][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:09:02,404  [INFO] [D][11:37:04][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:09:02,409  [INFO] [D][11:37:04][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:09:02,412  [INFO] [D][11:37:04][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:09:02,413  [INFO] 
 
2025-07-16 21:09:02,418  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:09:02,423  [INFO] [D][11:37:04][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:09:02,429  [INFO] 0023B909113311331133113311331B884349476A4DED3FF21DD3BB8BB1C99DCB8A93C7
 
2025-07-16 21:09:02,431  [INFO] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,432  [INFO] SEND OK
 
2025-07-16 21:09:02,432  [INFO] 
 
2025-07-16 21:09:02,437  [INFO] [D][11:37:04][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:09:02,440  [INFO] [D][11:37:04][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:09:02,441  [INFO] 
 
2025-07-16 21:09:02,446  [INFO] [D][11:37:04][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:02,451  [INFO] [D][11:37:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:09:02,454  [INFO] [D][11:37:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:09:02,459  [INFO] [D][11:37:04][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:02,462  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:02,467  [INFO] [D][11:37:04][PROT]M2M Send ok [1730201824]
 
2025-07-16 21:09:02,470  [INFO] [D][11:37:04][PROT]CLEAN:0
 
2025-07-16 21:09:02,473  [INFO] [D][11:37:04][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:02,478  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:02,485  [INFO] [D][11:37:04][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:09:02,487  [INFO] [D][11:37:04][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:02,493  [INFO] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:43,049  [INFO] [D][11:37:45][CAT1]closed : 0
 
2025-07-16 21:09:43,055  [INFO] [D][11:37:45][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:09:43,057  [INFO] [D][11:37:45][SAL ]socket closed id[0]
 
2025-07-16 21:09:43,062  [INFO] [D][11:37:45][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:09:43,069  [INFO] [D][11:37:45][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:09:43,071  [INFO] [D][11:37:45][M2M ]m2m select fd[4]
 
2025-07-16 21:09:43,076  [INFO] [D][11:37:45][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:09:43,079  [INFO] [D][11:37:45][M2M ]tcpclient close[4]
 
2025-07-16 21:09:43,082  [INFO] [D][11:37:45][SAL ]socket[4] has closed
 
2025-07-16 21:09:43,087  [INFO] [D][11:37:45][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:09:43,092  [INFO] [D][11:37:45][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 14
 
2025-07-16 21:09:43,119  [INFO] [D][11:37:45][COMM]Main Task receive event:86
 
2025-07-16 21:09:43,126  [INFO] [D][11:37:45][HSDK][0] flush to flash addr:[0xE46D00] --- write len --- [256]
 
2025-07-16 21:09:43,135  [INFO] [W][11:37:45][PROT]remove success[1730201865],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:09:43,144  [INFO] [W][11:37:45][PROT]add success [1730201865],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:09:43,151  [INFO] [D][11:37:45][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:09:43,152  [INFO] [D][11:37:45][PROT]index:0 1730201865
 
2025-07-16 21:09:43,156  [INFO] [D][11:37:45][PROT]is_send:0
 
2025-07-16 21:09:43,159  [INFO] [D][11:37:45][PROT]sequence_num:38
 
2025-07-16 21:09:43,161  [INFO] [D][11:37:45][PROT]retry_timeout:0
 
2025-07-16 21:09:43,164  [INFO] [D][11:37:45][PROT]retry_times:1
 
2025-07-16 21:09:43,166  [INFO] [D][11:37:45][PROT]send_path:0x2
 
2025-07-16 21:09:43,172  [INFO] [D][11:37:45][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:09:43,181  [INFO] [D][11:37:45][PROT]===========================================================
 
2025-07-16 21:09:43,186  [INFO] [W][11:37:45][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201865]
 
2025-07-16 21:09:43,191  [INFO] [D][11:37:45][PROT]===========================================================
 
2025-07-16 21:09:43,196  [INFO] [D][11:37:45][PROT]sending traceid [9999999999900017]
 
2025-07-16 21:09:43,199  [INFO] [D][11:37:45][PROT]Send_TO_M2M [1730201865]
 
2025-07-16 21:09:43,205  [INFO] [D][11:37:45][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:43,210  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:43,214  [INFO] [D][11:37:45][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:09:43,222  [INFO] [D][11:37:45][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:09:43,225  [INFO] [D][11:37:45][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:09:43,233  [INFO] [D][11:37:45][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:09:43,239  [INFO] [D][11:37:45][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:09:43,241  [INFO] [D][11:37:45][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:09:43,247  [INFO] [D][11:37:45][CAT1]at ops open socket[0]
 
2025-07-16 21:09:43,249  [INFO] [D][11:37:45][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:09:43,250  [INFO] 
 
2025-07-16 21:09:43,255  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:09:43,258  [INFO] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,259  [INFO] +CGATT: 1
 
2025-07-16 21:09:43,259  [INFO] 
 
2025-07-16 21:09:43,260  [INFO] OK
 
2025-07-16 21:09:43,260  [INFO] 
 
2025-07-16 21:09:43,264  [INFO] [D][11:37:45][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:09:43,265  [INFO] 
 
2025-07-16 21:09:43,265  [INFO] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,266  [INFO] +CSQ: 31,99
 
2025-07-16 21:09:43,267  [INFO] 
 
2025-07-16 21:09:43,268  [INFO] OK
 
2025-07-16 21:09:43,268  [INFO] 
 
2025-07-16 21:09:43,273  [INFO] [D][11:37:45][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:09:43,278  [INFO] 
 
2025-07-16 21:09:43,278  [INFO] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,279  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:09:43,280  [INFO] 
 
2025-07-16 21:09:43,280  [INFO] OK
 
2025-07-16 21:09:43,280  [INFO] 
 
2025-07-16 21:09:43,283  [INFO] [D][11:37:45][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:09:43,284  [INFO] 
 
2025-07-16 21:09:43,285  [INFO] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,286  [INFO] OK
 
2025-07-16 21:09:43,287  [INFO] 
 
2025-07-16 21:09:43,291  [INFO] [D][11:37:45][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:09:43,370  [INFO] [D][11:37:45][CAT1]opened : 0, 0
 
2025-07-16 21:09:43,373  [INFO] [D][11:37:45][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:43,379  [INFO] [D][11:37:45][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:09:43,384  [INFO] [D][11:37:45][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:09:43,390  [INFO] [D][11:37:45][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:09:43,395  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:09:43,398  [INFO] [D][11:37:45][SAL ]sock send credit cnt[6]
 
2025-07-16 21:09:43,401  [INFO] [D][11:37:45][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:09:43,406  [INFO] [D][11:37:45][M2M ]m2m send data len[70]
 
2025-07-16 21:09:43,409  [INFO] [D][11:37:45][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:09:43,418  [INFO] [D][11:37:45][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:09:43,422  [INFO] [D][11:37:45][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:09:43,426  [INFO] [D][11:37:45][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:09:43,427  [INFO] 
 
2025-07-16 21:09:43,431  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:09:43,436  [INFO] [D][11:37:45][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:09:43,442  [INFO] 0023B907113311331133113311331B884EED2F8F53045FC58B9E7C8535F30601FC2133
 
2025-07-16 21:09:43,445  [INFO] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,446  [INFO] SEND OK
 
2025-07-16 21:09:43,446  [INFO] 
 
2025-07-16 21:09:43,450  [INFO] [D][11:37:45][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:09:43,454  [INFO] [D][11:37:45][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:09:43,454  [INFO] 
 
2025-07-16 21:09:43,459  [INFO] [D][11:37:45][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:43,465  [INFO] [D][11:37:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:09:43,467  [INFO] [D][11:37:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:09:43,473  [INFO] [D][11:37:45][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:43,475  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:43,481  [INFO] [D][11:37:45][PROT]M2M Send ok [1730201865]
 
2025-07-16 21:09:43,484  [INFO] [D][11:37:45][PROT]CLEAN:0
 
2025-07-16 21:09:43,487  [INFO] [D][11:37:45][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:43,492  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:43,498  [INFO] [D][11:37:45][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:09:43,501  [INFO] [D][11:37:45][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:43,507  [INFO] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:10:24,000  [INFO] [D][11:38:26][CAT1]closed : 0
 
2025-07-16 21:10:24,005  [INFO] [D][11:38:26][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:10:24,008  [INFO] [D][11:38:26][SAL ]socket closed id[0]
 
2025-07-16 21:10:24,014  [INFO] [D][11:38:26][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:10:24,019  [INFO] [D][11:38:26][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:10:24,021  [INFO] [D][11:38:26][M2M ]m2m select fd[4]
 
2025-07-16 21:10:24,027  [INFO] [D][11:38:26][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:10:24,030  [INFO] [D][11:38:26][M2M ]tcpclient close[4]
 
2025-07-16 21:10:24,033  [INFO] [D][11:38:26][SAL ]socket[4] has closed
 
2025-07-16 21:10:24,038  [INFO] [D][11:38:26][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:10:24,043  [INFO] [D][11:38:26][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 15
 
2025-07-16 21:10:24,068  [INFO] [D][11:38:26][COMM]Main Task receive event:86
 
2025-07-16 21:10:24,076  [INFO] [W][11:38:26][PROT]remove success[1730201906],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:10:24,085  [INFO] [W][11:38:26][PROT]add success [1730201906],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:10:24,091  [INFO] [D][11:38:26][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:10:24,093  [INFO] [D][11:38:26][PROT]index:0 1730201906
 
2025-07-16 21:10:24,096  [INFO] [D][11:38:26][PROT]is_send:0
 
2025-07-16 21:10:24,102  [INFO] [D][11:38:26][PROT]sequence_num:39
 
2025-07-16 21:10:24,104  [INFO] [D][11:38:26][PROT]retry_timeout:0
 
2025-07-16 21:10:24,106  [INFO] [D][11:38:26][PROT]retry_times:1
 
2025-07-16 21:10:24,109  [INFO] [D][11:38:26][PROT]send_path:0x2
 
2025-07-16 21:10:24,115  [INFO] [D][11:38:26][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:10:24,121  [INFO] [D][11:38:26][PROT]===========================================================
 
2025-07-16 21:10:24,126  [INFO] [W][11:38:26][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201906]
 
2025-07-16 21:10:24,135  [INFO] [D][11:38:26][PROT]===========================================================
 
2025-07-16 21:10:24,138  [INFO] [D][11:38:26][PROT]sending traceid [9999999999900018]
 
2025-07-16 21:10:24,143  [INFO] [D][11:38:26][PROT]Send_TO_M2M [1730201906]
 
2025-07-16 21:10:24,149  [INFO] [D][11:38:26][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:10:24,152  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:10:24,157  [INFO] [D][11:38:26][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:10:24,163  [INFO] [D][11:38:26][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:10:24,168  [INFO] [D][11:38:26][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:10:24,175  [INFO] [D][11:38:26][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:10:24,183  [INFO] [D][11:38:26][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:10:24,185  [INFO] [D][11:38:26][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:10:24,188  [INFO] [D][11:38:26][CAT1]at ops open socket[0]
 
2025-07-16 21:10:24,193  [INFO] [D][11:38:26][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:10:24,194  [INFO] 
 
2025-07-16 21:10:24,199  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:10:24,200  [INFO] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,201  [INFO] +CGATT: 1
 
2025-07-16 21:10:24,203  [INFO] 
 
2025-07-16 21:10:24,203  [INFO] OK
 
2025-07-16 21:10:24,203  [INFO] 
 
2025-07-16 21:10:24,204  [INFO] [D][11:38:26][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:10:24,205  [INFO] 
 
2025-07-16 21:10:24,207  [INFO] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,208  [INFO] +CSQ: 31,99
 
2025-07-16 21:10:24,208  [INFO] 
 
2025-07-16 21:10:24,209  [INFO] OK
 
2025-07-16 21:10:24,210  [INFO] 
 
2025-07-16 21:10:24,212  [INFO] [D][11:38:26][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:10:24,213  [INFO] 
 
2025-07-16 21:10:24,215  [INFO] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,218  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:10:24,218  [INFO] 
 
2025-07-16 21:10:24,219  [INFO] OK
 
2025-07-16 21:10:24,219  [INFO] 
 
2025-07-16 21:10:24,227  [INFO] [D][11:38:26][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:10:24,228  [INFO] 
 
2025-07-16 21:10:24,229  [INFO] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,230  [INFO] OK
 
2025-07-16 21:10:24,230  [INFO] 
 
2025-07-16 21:10:24,233  [INFO] [D][11:38:26][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:10:24,302  [INFO] [D][11:38:26][CAT1]opened : 0, 0
 
2025-07-16 21:10:24,305  [INFO] [D][11:38:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:10:24,310  [INFO] [D][11:38:26][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:10:24,315  [INFO] [D][11:38:26][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:10:24,321  [INFO] [D][11:38:26][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:10:24,327  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:10:24,330  [INFO] [D][11:38:26][SAL ]sock send credit cnt[6]
 
2025-07-16 21:10:24,332  [INFO] [D][11:38:26][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:10:24,338  [INFO] [D][11:38:26][M2M ]m2m send data len[70]
 
2025-07-16 21:10:24,341  [INFO] [D][11:38:26][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:10:24,349  [INFO] [D][11:38:26][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:10:24,354  [INFO] [D][11:38:26][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:10:24,357  [INFO] [D][11:38:26][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:10:24,358  [INFO] 
 
2025-07-16 21:10:24,363  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:10:24,368  [INFO] [D][11:38:26][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:10:24,375  [INFO] 0023B904113311331133113311331B88428F71A963829F327E6CF30F6AB0A145FA9E2A
 
2025-07-16 21:10:24,376  [INFO] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,377  [INFO] SEND OK
 
2025-07-16 21:10:24,378  [INFO] 
 
2025-07-16 21:10:24,382  [INFO] [D][11:38:26][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:10:24,384  [INFO] [D][11:38:26][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:10:24,385  [INFO] 
 
2025-07-16 21:10:24,391  [INFO] [D][11:38:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:10:24,397  [INFO] [D][11:38:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:10:24,399  [INFO] [D][11:38:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:10:24,405  [INFO] [D][11:38:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:10:24,408  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:10:24,413  [INFO] [D][11:38:26][PROT]M2M Send ok [1730201906]
 
2025-07-16 21:10:24,415  [INFO] [D][11:38:26][PROT]CLEAN:0
 
2025-07-16 21:10:24,418  [INFO] [D][11:38:26][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:10:24,425  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:10:24,430  [INFO] [D][11:38:26][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:10:24,433  [INFO] [D][11:38:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:10:24,438  [INFO] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:04,942  [INFO] [D][11:39:07][CAT1]closed : 0
 
2025-07-16 21:11:04,948  [INFO] [D][11:39:07][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:11:04,951  [INFO] [D][11:39:07][SAL ]socket closed id[0]
 
2025-07-16 21:11:04,956  [INFO] [D][11:39:07][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:11:04,962  [INFO] [D][11:39:07][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:11:04,964  [INFO] [D][11:39:07][M2M ]m2m select fd[4]
 
2025-07-16 21:11:04,969  [INFO] [D][11:39:07][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:11:04,972  [INFO] [D][11:39:07][M2M ]tcpclient close[4]
 
2025-07-16 21:11:04,975  [INFO] [D][11:39:07][SAL ]socket[4] has closed
 
2025-07-16 21:11:04,981  [INFO] [D][11:39:07][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:11:04,986  [INFO] [D][11:39:07][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 16
 
2025-07-16 21:11:04,998  [INFO] [D][11:39:07][COMM]Main Task receive event:86
 
2025-07-16 21:11:05,006  [INFO] [W][11:39:07][PROT]remove success[1730201947],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:11:05,015  [INFO] [W][11:39:07][PROT]add success [1730201947],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:11:05,021  [INFO] [D][11:39:07][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:11:05,023  [INFO] [D][11:39:07][PROT]index:0 1730201947
 
2025-07-16 21:11:05,026  [INFO] [D][11:39:07][PROT]is_send:0
 
2025-07-16 21:11:05,031  [INFO] [D][11:39:07][PROT]sequence_num:40
 
2025-07-16 21:11:05,034  [INFO] [D][11:39:07][PROT]retry_timeout:0
 
2025-07-16 21:11:05,037  [INFO] [D][11:39:07][PROT]retry_times:1
 
2025-07-16 21:11:05,040  [INFO] [D][11:39:07][PROT]send_path:0x2
 
2025-07-16 21:11:05,045  [INFO] [D][11:39:07][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:11:05,051  [INFO] [D][11:39:07][PROT]===========================================================
 
2025-07-16 21:11:05,058  [INFO] [W][11:39:07][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201947]
 
2025-07-16 21:11:05,065  [INFO] [D][11:39:07][PROT]===========================================================
 
2025-07-16 21:11:05,068  [INFO] [D][11:39:07][PROT]sending traceid [9999999999900019]
 
2025-07-16 21:11:05,073  [INFO] [D][11:39:07][PROT]Send_TO_M2M [1730201947]
 
2025-07-16 21:11:05,079  [INFO] [D][11:39:07][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:05,082  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:05,087  [INFO] [D][11:39:07][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:11:05,092  [INFO] [D][11:39:07][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:11:05,099  [INFO] [D][11:39:07][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:11:05,105  [INFO] [D][11:39:07][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:11:05,113  [INFO] [D][11:39:07][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:11:05,116  [INFO] [D][11:39:07][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:11:05,118  [INFO] [D][11:39:07][CAT1]at ops open socket[0]
 
2025-07-16 21:11:05,123  [INFO] [D][11:39:07][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:11:05,124  [INFO] 
 
2025-07-16 21:11:05,129  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:11:05,129  [INFO] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,131  [INFO] +CGATT: 1
 
2025-07-16 21:11:05,132  [INFO] 
 
2025-07-16 21:11:05,132  [INFO] OK
 
2025-07-16 21:11:05,132  [INFO] 
 
2025-07-16 21:11:05,135  [INFO] [D][11:39:07][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:11:05,136  [INFO] 
 
2025-07-16 21:11:05,137  [INFO] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,138  [INFO] +CSQ: 31,99
 
2025-07-16 21:11:05,139  [INFO] 
 
2025-07-16 21:11:05,140  [INFO] OK
 
2025-07-16 21:11:05,141  [INFO] 
 
2025-07-16 21:11:05,144  [INFO] [D][11:39:07][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:11:05,145  [INFO] 
 
2025-07-16 21:11:05,146  [INFO] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,149  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:11:05,150  [INFO] 
 
2025-07-16 21:11:05,150  [INFO] OK
 
2025-07-16 21:11:05,150  [INFO] 
 
2025-07-16 21:11:05,158  [INFO] [D][11:39:07][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:11:05,159  [INFO] 
 
2025-07-16 21:11:05,160  [INFO] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,161  [INFO] OK
 
2025-07-16 21:11:05,161  [INFO] 
 
2025-07-16 21:11:05,164  [INFO] [D][11:39:07][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:11:05,241  [INFO] [D][11:39:07][CAT1]opened : 0, 0
 
2025-07-16 21:11:05,245  [INFO] [D][11:39:07][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:05,251  [INFO] [D][11:39:07][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:11:05,256  [INFO] [D][11:39:07][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:11:05,261  [INFO] [D][11:39:07][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:11:05,266  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:11:05,269  [INFO] [D][11:39:07][SAL ]sock send credit cnt[6]
 
2025-07-16 21:11:05,272  [INFO] [D][11:39:07][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:11:05,277  [INFO] [D][11:39:07][M2M ]m2m send data len[70]
 
2025-07-16 21:11:05,281  [INFO] [D][11:39:07][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:11:05,290  [INFO] [D][11:39:07][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:11:05,295  [INFO] [D][11:39:07][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:11:05,297  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:11:05,302  [INFO] [D][11:39:07][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:11:05,303  [INFO] 
 
2025-07-16 21:11:05,308  [INFO] [D][11:39:07][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:11:05,315  [INFO] 0023B90B113311331133113311331B88417E7438FB91B91E8B606791CFB8E31776D14E
 
2025-07-16 21:11:05,317  [INFO] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,317  [INFO] SEND OK
 
2025-07-16 21:11:05,318  [INFO] 
 
2025-07-16 21:11:05,323  [INFO] [D][11:39:07][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:11:05,325  [INFO] [D][11:39:07][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:11:05,326  [INFO] 
 
2025-07-16 21:11:05,330  [INFO] [D][11:39:07][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:05,337  [INFO] [D][11:39:07][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:11:05,338  [INFO] [D][11:39:07][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:11:05,345  [INFO] [D][11:39:07][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:05,347  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:05,353  [INFO] [D][11:39:07][PROT]M2M Send ok [1730201947]
 
2025-07-16 21:11:05,356  [INFO] [D][11:39:07][PROT]CLEAN:0
 
2025-07-16 21:11:05,359  [INFO] [D][11:39:07][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:05,365  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:05,370  [INFO] [D][11:39:07][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:11:05,373  [INFO] [D][11:39:07][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:05,378  [INFO] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:45,972  [INFO] [D][11:39:48][CAT1]closed : 0
 
2025-07-16 21:11:45,976  [INFO] [D][11:39:48][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:11:45,979  [INFO] [D][11:39:48][SAL ]socket closed id[0]
 
2025-07-16 21:11:45,985  [INFO] [D][11:39:48][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:11:45,991  [INFO] [D][11:39:48][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:11:45,993  [INFO] [D][11:39:48][M2M ]m2m select fd[4]
 
2025-07-16 21:11:45,998  [INFO] [D][11:39:48][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:11:46,001  [INFO] [D][11:39:48][M2M ]tcpclient close[4]
 
2025-07-16 21:11:46,004  [INFO] [D][11:39:48][SAL ]socket[4] has closed
 
2025-07-16 21:11:46,010  [INFO] [D][11:39:48][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:11:46,016  [INFO] [D][11:39:48][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 17
 
2025-07-16 21:11:46,027  [INFO] [D][11:39:48][COMM]Main Task receive event:86
 
2025-07-16 21:11:46,031  [INFO] [D][11:39:48][HSDK]need to erase for write: is[0x0] ie[0x5E00]
 
2025-07-16 21:11:46,044  [INFO] [D][11:39:48][HSDK][0] flush to flash addr:[0xE46E00] --- write len --- [256]
 
2025-07-16 21:11:46,052  [INFO] [W][11:39:48][PROT]remove success[1730201988],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:11:46,054  [INFO] [D][11:39:48][PROT]index:0 1730201988
 
2025-07-16 21:11:46,057  [INFO] [D][11:39:48][PROT]is_send:0
 
2025-07-16 21:11:46,060  [INFO] [D][11:39:48][PROT]sequence_num:41
 
2025-07-16 21:11:46,063  [INFO] [D][11:39:48][PROT]retry_timeout:0
 
2025-07-16 21:11:46,069  [INFO] [D][11:39:48][PROT]retry_times:1
 
2025-07-16 21:11:46,071  [INFO] [D][11:39:48][PROT]send_path:0x2
 
2025-07-16 21:11:46,073  [INFO] [D][11:39:48][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:11:46,082  [INFO] [D][11:39:48][PROT]===========================================================
 
2025-07-16 21:11:46,088  [INFO] [W][11:39:48][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201988]
 
2025-07-16 21:11:46,094  [INFO] [D][11:39:48][PROT]===========================================================
 
2025-07-16 21:11:46,099  [INFO] [D][11:39:48][PROT]sending traceid [999999999990001A]
 
2025-07-16 21:11:46,105  [INFO] [D][11:39:48][PROT]Send_TO_M2M [1730201988]
 
2025-07-16 21:11:46,107  [INFO] [D][11:39:48][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:46,113  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:46,119  [INFO] [D][11:39:48][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:11:46,124  [INFO] [D][11:39:48][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:11:46,130  [INFO] [D][11:39:48][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:11:46,136  [INFO] [D][11:39:48][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:11:46,141  [INFO] [D][11:39:48][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:11:46,149  [INFO] [W][11:39:48][PROT]add success [1730201988],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:11:46,157  [INFO] [D][11:39:48][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:11:46,160  [INFO] [D][11:39:48][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:11:46,163  [INFO] [D][11:39:48][CAT1]at ops open socket[0]
 
2025-07-16 21:11:46,168  [INFO] [D][11:39:48][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:11:46,169  [INFO] 
 
2025-07-16 21:11:46,173  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:11:46,175  [INFO] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,176  [INFO] +CGATT: 1
 
2025-07-16 21:11:46,177  [INFO] 
 
2025-07-16 21:11:46,177  [INFO] OK
 
2025-07-16 21:11:46,177  [INFO] 
 
2025-07-16 21:11:46,179  [INFO] [D][11:39:48][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:11:46,180  [INFO] 
 
2025-07-16 21:11:46,183  [INFO] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,184  [INFO] +CSQ: 31,99
 
2025-07-16 21:11:46,184  [INFO] 
 
2025-07-16 21:11:46,185  [INFO] OK
 
2025-07-16 21:11:46,185  [INFO] 
 
2025-07-16 21:11:46,188  [INFO] [D][11:39:48][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:11:46,189  [INFO] 
 
2025-07-16 21:11:46,191  [INFO] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,194  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:11:46,194  [INFO] 
 
2025-07-16 21:11:46,195  [INFO] OK
 
2025-07-16 21:11:46,195  [INFO] 
 
2025-07-16 21:11:46,203  [INFO] [D][11:39:48][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:11:46,203  [INFO] 
 
2025-07-16 21:11:46,204  [INFO] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,205  [INFO] OK
 
2025-07-16 21:11:46,205  [INFO] 
 
2025-07-16 21:11:46,209  [INFO] [D][11:39:48][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:11:46,293  [INFO] [D][11:39:48][CAT1]opened : 0, 0
 
2025-07-16 21:11:46,297  [INFO] [D][11:39:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:46,302  [INFO] [D][11:39:48][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:11:46,307  [INFO] [D][11:39:48][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:11:46,313  [INFO] [D][11:39:48][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:11:46,318  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:11:46,321  [INFO] [D][11:39:48][SAL ]sock send credit cnt[6]
 
2025-07-16 21:11:46,324  [INFO] [D][11:39:48][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:11:46,330  [INFO] [D][11:39:48][M2M ]m2m send data len[70]
 
2025-07-16 21:11:46,333  [INFO] [D][11:39:48][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:11:46,341  [INFO] [D][11:39:48][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:11:46,345  [INFO] [D][11:39:48][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:11:46,349  [INFO] [D][11:39:48][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:11:46,350  [INFO] 
 
2025-07-16 21:11:46,355  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:11:46,359  [INFO] [D][11:39:48][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:11:46,366  [INFO] 0023B9D8113311331133113311331B884C85DA0101AB78FC51631E30F3C6AE279EF4E2
 
2025-07-16 21:11:46,369  [INFO] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,370  [INFO] SEND OK
 
2025-07-16 21:11:46,370  [INFO] 
 
2025-07-16 21:11:46,374  [INFO] [D][11:39:48][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:11:46,377  [INFO] [D][11:39:48][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:11:46,377  [INFO] 
 
2025-07-16 21:11:46,382  [INFO] [D][11:39:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:46,389  [INFO] [D][11:39:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:11:46,390  [INFO] [D][11:39:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:11:46,397  [INFO] [D][11:39:48][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:46,400  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:46,404  [INFO] [D][11:39:48][PROT]M2M Send ok [1730201988]
 
2025-07-16 21:11:46,407  [INFO] [D][11:39:48][PROT]CLEAN:0
 
2025-07-16 21:11:46,410  [INFO] [D][11:39:48][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:46,416  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:46,422  [INFO] [D][11:39:48][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:11:46,424  [INFO] [D][11:39:48][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:46,430  [INFO] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:12:26,900  [INFO] [D][11:40:29][CAT1]closed : 0
 
2025-07-16 21:12:26,905  [INFO] [D][11:40:29][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:12:26,908  [INFO] [D][11:40:29][SAL ]socket closed id[0]
 
2025-07-16 21:12:26,913  [INFO] [D][11:40:29][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:12:26,919  [INFO] [D][11:40:29][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:12:26,922  [INFO] [D][11:40:29][M2M ]m2m select fd[4]
 
2025-07-16 21:12:26,928  [INFO] [D][11:40:29][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:12:26,930  [INFO] [D][11:40:29][M2M ]tcpclient close[4]
 
2025-07-16 21:12:26,933  [INFO] [D][11:40:29][SAL ]socket[4] has closed
 
2025-07-16 21:12:26,937  [INFO] [D][11:40:29][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:12:26,945  [INFO] [D][11:40:29][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 18
 
2025-07-16 21:12:26,948  [INFO] [D][11:40:29][COMM]Main Task receive event:86
 
2025-07-16 21:12:26,955  [INFO] [W][11:40:29][PROT]remove success[1730202029],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:12:26,966  [INFO] [W][11:40:29][PROT]add success [1730202029],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:12:26,973  [INFO] [D][11:40:29][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:12:26,975  [INFO] [D][11:40:29][PROT]index:0 1730202029
 
2025-07-16 21:12:26,977  [INFO] [D][11:40:29][PROT]is_send:0
 
2025-07-16 21:12:26,980  [INFO] [D][11:40:29][PROT]sequence_num:42
 
2025-07-16 21:12:26,983  [INFO] [D][11:40:29][PROT]retry_timeout:0
 
2025-07-16 21:12:26,986  [INFO] [D][11:40:29][PROT]retry_times:1
 
2025-07-16 21:12:26,989  [INFO] [D][11:40:29][PROT]send_path:0x2
 
2025-07-16 21:12:26,994  [INFO] [D][11:40:29][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:12:27,000  [INFO] [D][11:40:29][PROT]===========================================================
 
2025-07-16 21:12:27,009  [INFO] [W][11:40:29][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202029]
 
2025-07-16 21:12:27,014  [INFO] [D][11:40:29][PROT]===========================================================
 
2025-07-16 21:12:27,020  [INFO] [D][11:40:29][PROT]sending traceid [999999999990001B]
 
2025-07-16 21:12:27,022  [INFO] [D][11:40:29][PROT]Send_TO_M2M [1730202029]
 
2025-07-16 21:12:27,028  [INFO] [D][11:40:29][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:12:27,033  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:12:27,036  [INFO] [D][11:40:29][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:12:27,044  [INFO] [D][11:40:29][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:12:27,047  [INFO] [D][11:40:29][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:12:27,056  [INFO] [D][11:40:29][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:12:27,062  [INFO] [D][11:40:29][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:12:27,064  [INFO] [D][11:40:29][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:12:27,069  [INFO] [D][11:40:29][CAT1]at ops open socket[0]
 
2025-07-16 21:12:27,071  [INFO] [D][11:40:29][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:12:27,073  [INFO] 
 
2025-07-16 21:12:27,078  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:12:27,080  [INFO] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,081  [INFO] +CGATT: 1
 
2025-07-16 21:12:27,082  [INFO] 
 
2025-07-16 21:12:27,082  [INFO] OK
 
2025-07-16 21:12:27,082  [INFO] 
 
2025-07-16 21:12:27,086  [INFO] [D][11:40:29][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:12:27,087  [INFO] 
 
2025-07-16 21:12:27,087  [INFO] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,088  [INFO] +CSQ: 31,99
 
2025-07-16 21:12:27,089  [INFO] 
 
2025-07-16 21:12:27,090  [INFO] OK
 
2025-07-16 21:12:27,090  [INFO] 
 
2025-07-16 21:12:27,091  [INFO] [D][11:40:29][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:12:27,094  [INFO] 
 
2025-07-16 21:12:27,095  [INFO] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,097  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:12:27,098  [INFO] 
 
2025-07-16 21:12:27,098  [INFO] OK
 
2025-07-16 21:12:27,099  [INFO] 
 
2025-07-16 21:12:27,106  [INFO] [D][11:40:29][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:12:27,107  [INFO] 
 
2025-07-16 21:12:27,108  [INFO] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,109  [INFO] OK
 
2025-07-16 21:12:27,109  [INFO] 
 
2025-07-16 21:12:27,113  [INFO] [D][11:40:29][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:12:27,220  [INFO] [D][11:40:29][CAT1]opened : 0, 0
 
2025-07-16 21:12:27,224  [INFO] [D][11:40:29][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:12:27,230  [INFO] [D][11:40:29][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:12:27,235  [INFO] [D][11:40:29][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:12:27,241  [INFO] [D][11:40:29][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:12:27,246  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:12:27,249  [INFO] [D][11:40:29][SAL ]sock send credit cnt[6]
 
2025-07-16 21:12:27,252  [INFO] [D][11:40:29][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:12:27,257  [INFO] [D][11:40:29][M2M ]m2m send data len[70]
 
2025-07-16 21:12:27,260  [INFO] [D][11:40:29][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:12:27,269  [INFO] [D][11:40:29][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:12:27,274  [INFO] [D][11:40:29][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:12:27,277  [INFO] [D][11:40:29][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:12:27,277  [INFO] 
 
2025-07-16 21:12:27,282  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:12:27,287  [INFO] [D][11:40:29][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:12:27,294  [INFO] 0023B9DA113311331133113311331B884D5367E36637DC2F86FF5738DC94525F182801
 
2025-07-16 21:12:27,296  [INFO] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,297  [INFO] SEND OK
 
2025-07-16 21:12:27,297  [INFO] 
 
2025-07-16 21:12:27,302  [INFO] [D][11:40:29][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:12:27,304  [INFO] [D][11:40:29][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:12:27,305  [INFO] 
 
2025-07-16 21:12:27,310  [INFO] [D][11:40:29][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:12:27,316  [INFO] [D][11:40:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:12:27,318  [INFO] [D][11:40:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:12:27,324  [INFO] [D][11:40:29][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:12:27,327  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:12:27,333  [INFO] [D][11:40:29][PROT]M2M Send ok [1730202029]
 
2025-07-16 21:12:27,335  [INFO] [D][11:40:29][PROT]CLEAN:0
 
2025-07-16 21:12:27,339  [INFO] [D][11:40:29][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:12:27,344  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:12:27,349  [INFO] [D][11:40:29][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:12:27,352  [INFO] [D][11:40:29][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:12:27,359  [INFO] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:12:34,449  [INFO] [D][11:40:36][COMM]IMU: 839745 MEMS ERROR when cali 0
 
2025-07-16 21:13:07,829  [INFO] [D][11:41:10][CAT1]closed : 0
 
2025-07-16 21:13:07,835  [INFO] [D][11:41:10][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:13:07,836  [INFO] [D][11:41:10][SAL ]socket closed id[0]
 
2025-07-16 21:13:07,842  [INFO] [D][11:41:10][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:13:07,847  [INFO] [D][11:41:10][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:13:07,850  [INFO] [D][11:41:10][M2M ]m2m select fd[4]
 
2025-07-16 21:13:07,856  [INFO] [D][11:41:10][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:13:07,859  [INFO] [D][11:41:10][M2M ]tcpclient close[4]
 
2025-07-16 21:13:07,861  [INFO] [D][11:41:10][SAL ]socket[4] has closed
 
2025-07-16 21:13:07,867  [INFO] [D][11:41:10][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:13:07,873  [INFO] [D][11:41:10][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 19
 
2025-07-16 21:13:07,875  [INFO] [D][11:41:10][COMM]Main Task receive event:86
 
2025-07-16 21:13:07,883  [INFO] [W][11:41:10][PROT]remove success[1730202070],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:13:07,894  [INFO] [W][11:41:10][PROT]add success [1730202070],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:13:07,900  [INFO] [D][11:41:10][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:13:07,902  [INFO] [D][11:41:10][PROT]index:0 1730202070
 
2025-07-16 21:13:07,905  [INFO] [D][11:41:10][PROT]is_send:0
 
2025-07-16 21:13:07,908  [INFO] [D][11:41:10][PROT]sequence_num:43
 
2025-07-16 21:13:07,910  [INFO] [D][11:41:10][PROT]retry_timeout:0
 
2025-07-16 21:13:07,914  [INFO] [D][11:41:10][PROT]retry_times:1
 
2025-07-16 21:13:07,917  [INFO] [D][11:41:10][PROT]send_path:0x2
 
2025-07-16 21:13:07,923  [INFO] [D][11:41:10][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:13:07,929  [INFO] [D][11:41:10][PROT]===========================================================
 
2025-07-16 21:13:07,937  [INFO] [D][11:41:10][HSDK][0] flush to flash addr:[0xE46F00] --- write len --- [256]
 
2025-07-16 21:13:07,942  [INFO] [W][11:41:10][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202070]
 
2025-07-16 21:13:07,950  [INFO] [D][11:41:10][PROT]===========================================================
 
2025-07-16 21:13:07,953  [INFO] [D][11:41:10][PROT]sending traceid [999999999990001C]
 
2025-07-16 21:13:07,958  [INFO] [D][11:41:10][PROT]Send_TO_M2M [1730202070]
 
2025-07-16 21:13:07,961  [INFO] [D][11:41:10][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:07,967  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:07,972  [INFO] [D][11:41:10][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:13:07,978  [INFO] [D][11:41:10][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:13:07,983  [INFO] [D][11:41:10][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:13:07,990  [INFO] [D][11:41:10][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:13:07,995  [INFO] [D][11:41:10][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:13:08,000  [INFO] [D][11:41:10][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:13:08,003  [INFO] [D][11:41:10][CAT1]at ops open socket[0]
 
2025-07-16 21:13:08,006  [INFO] [D][11:41:10][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:13:08,008  [INFO] 
 
2025-07-16 21:13:08,012  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:13:08,014  [INFO] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,017  [INFO] +CGATT: 1
 
2025-07-16 21:13:08,017  [INFO] 
 
2025-07-16 21:13:08,017  [INFO] OK
 
2025-07-16 21:13:08,018  [INFO] 
 
2025-07-16 21:13:08,020  [INFO] [D][11:41:10][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:13:08,021  [INFO] 
 
2025-07-16 21:13:08,022  [INFO] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,023  [INFO] +CSQ: 31,99
 
2025-07-16 21:13:08,024  [INFO] 
 
2025-07-16 21:13:08,024  [INFO] OK
 
2025-07-16 21:13:08,025  [INFO] 
 
2025-07-16 21:13:08,028  [INFO] [D][11:41:10][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:13:08,029  [INFO] 
 
2025-07-16 21:13:08,031  [INFO] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,034  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:13:08,034  [INFO] 
 
2025-07-16 21:13:08,035  [INFO] OK
 
2025-07-16 21:13:08,035  [INFO] 
 
2025-07-16 21:13:08,043  [INFO] [D][11:41:10][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:13:08,043  [INFO] 
 
2025-07-16 21:13:08,044  [INFO] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,046  [INFO] OK
 
2025-07-16 21:13:08,047  [INFO] 
 
2025-07-16 21:13:08,048  [INFO] [D][11:41:10][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:13:08,112  [INFO] [D][11:41:10][CAT1]opened : 0, 0
 
2025-07-16 21:13:08,114  [INFO] [D][11:41:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:08,120  [INFO] [D][11:41:10][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:13:08,125  [INFO] [D][11:41:10][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:13:08,131  [INFO] [D][11:41:10][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:13:08,137  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:13:08,140  [INFO] [D][11:41:10][SAL ]sock send credit cnt[6]
 
2025-07-16 21:13:08,143  [INFO] [D][11:41:10][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:13:08,148  [INFO] [D][11:41:10][M2M ]m2m send data len[70]
 
2025-07-16 21:13:08,151  [INFO] [D][11:41:10][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:13:08,160  [INFO] [D][11:41:10][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:13:08,165  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:13:08,168  [INFO] [D][11:41:10][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:13:08,172  [INFO] [D][11:41:10][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:13:08,173  [INFO] 
 
2025-07-16 21:13:08,178  [INFO] [D][11:41:10][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:13:08,185  [INFO] 0023B9DF113311331133113311331B8840D741505F3D0BB0831ADDFF9F2D601BCA62E6
 
2025-07-16 21:13:08,187  [INFO] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,190  [INFO] SEND OK
 
2025-07-16 21:13:08,190  [INFO] 
 
2025-07-16 21:13:08,193  [INFO] [D][11:41:10][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:13:08,196  [INFO] [D][11:41:10][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:13:08,198  [INFO] 
 
2025-07-16 21:13:08,201  [INFO] [D][11:41:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:08,206  [INFO] [D][11:41:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:13:08,211  [INFO] [D][11:41:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:13:08,215  [INFO] [D][11:41:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:08,221  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:13:08,224  [INFO] [D][11:41:10][PROT]M2M Send ok [1730202070]
 
2025-07-16 21:13:08,226  [INFO] [D][11:41:10][PROT]CLEAN:0
 
2025-07-16 21:13:08,232  [INFO] [D][11:41:10][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:08,237  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:08,241  [INFO] [D][11:41:10][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:13:08,246  [INFO] [D][11:41:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:08,249  [INFO] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:13:48,866  [INFO] [D][11:41:51][CAT1]closed : 0
 
2025-07-16 21:13:48,870  [INFO] [D][11:41:51][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:13:48,873  [INFO] [D][11:41:51][SAL ]socket closed id[0]
 
2025-07-16 21:13:48,879  [INFO] [D][11:41:51][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:13:48,884  [INFO] [D][11:41:51][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:13:48,887  [INFO] [D][11:41:51][M2M ]m2m select fd[4]
 
2025-07-16 21:13:48,892  [INFO] [D][11:41:51][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:13:48,895  [INFO] [D][11:41:51][M2M ]tcpclient close[4]
 
2025-07-16 21:13:48,897  [INFO] [D][11:41:51][SAL ]socket[4] has closed
 
2025-07-16 21:13:48,904  [INFO] [D][11:41:51][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:13:48,909  [INFO] [D][11:41:51][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 20
 
2025-07-16 21:13:48,976  [INFO] [D][11:41:51][COMM]Main Task receive event:86
 
2025-07-16 21:13:48,984  [INFO] [W][11:41:51][PROT]remove success[1730202111],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:13:48,992  [INFO] [W][11:41:51][PROT]add success [1730202111],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:13:48,998  [INFO] [D][11:41:51][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:13:49,000  [INFO] [D][11:41:51][PROT]index:0 1730202111
 
2025-07-16 21:13:49,003  [INFO] [D][11:41:51][PROT]is_send:0
 
2025-07-16 21:13:49,008  [INFO] [D][11:41:51][PROT]sequence_num:44
 
2025-07-16 21:13:49,011  [INFO] [D][11:41:51][PROT]retry_timeout:0
 
2025-07-16 21:13:49,015  [INFO] [D][11:41:51][PROT]retry_times:1
 
2025-07-16 21:13:49,018  [INFO] [D][11:41:51][PROT]send_path:0x2
 
2025-07-16 21:13:49,023  [INFO] [D][11:41:51][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:13:49,029  [INFO] [D][11:41:51][PROT]===========================================================
 
2025-07-16 21:13:49,037  [INFO] [W][11:41:51][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202111]
 
2025-07-16 21:13:49,044  [INFO] [D][11:41:51][PROT]===========================================================
 
2025-07-16 21:13:49,046  [INFO] [D][11:41:51][PROT]sending traceid [999999999990001D]
 
2025-07-16 21:13:49,051  [INFO] [D][11:41:51][PROT]Send_TO_M2M [1730202111]
 
2025-07-16 21:13:49,056  [INFO] [D][11:41:51][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:49,059  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:49,065  [INFO] [D][11:41:51][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:13:49,071  [INFO] [D][11:41:51][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:13:49,076  [INFO] [D][11:41:51][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:13:49,082  [INFO] [D][11:41:51][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:13:49,090  [INFO] [D][11:41:51][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:13:49,093  [INFO] [D][11:41:51][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:13:49,096  [INFO] [D][11:41:51][CAT1]at ops open socket[0]
 
2025-07-16 21:13:49,101  [INFO] [D][11:41:51][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:13:49,102  [INFO] 
 
2025-07-16 21:13:49,107  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:13:49,108  [INFO] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,109  [INFO] +CGATT: 1
 
2025-07-16 21:13:49,109  [INFO] 
 
2025-07-16 21:13:49,110  [INFO] OK
 
2025-07-16 21:13:49,110  [INFO] 
 
2025-07-16 21:13:49,112  [INFO] [D][11:41:51][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:13:49,113  [INFO] 
 
2025-07-16 21:13:49,115  [INFO] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,116  [INFO] +CSQ: 31,99
 
2025-07-16 21:13:49,116  [INFO] 
 
2025-07-16 21:13:49,117  [INFO] OK
 
2025-07-16 21:13:49,117  [INFO] 
 
2025-07-16 21:13:49,120  [INFO] [D][11:41:51][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:13:49,121  [INFO] 
 
2025-07-16 21:13:49,123  [INFO] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,125  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:13:49,126  [INFO] 
 
2025-07-16 21:13:49,127  [INFO] OK
 
2025-07-16 21:13:49,129  [INFO] 
 
2025-07-16 21:13:49,136  [INFO] [D][11:41:51][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:13:49,137  [INFO] 
 
2025-07-16 21:13:49,137  [INFO] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,138  [INFO] OK
 
2025-07-16 21:13:49,138  [INFO] 
 
2025-07-16 21:13:49,141  [INFO] [D][11:41:51][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:13:49,216  [INFO] [D][11:41:51][CAT1]opened : 0, 0
 
2025-07-16 21:13:49,219  [INFO] [D][11:41:51][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:49,225  [INFO] [D][11:41:51][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:13:49,230  [INFO] [D][11:41:51][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:13:49,236  [INFO] [D][11:41:51][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:13:49,241  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:13:49,244  [INFO] [D][11:41:51][SAL ]sock send credit cnt[6]
 
2025-07-16 21:13:49,247  [INFO] [D][11:41:51][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:13:49,252  [INFO] [D][11:41:51][M2M ]m2m send data len[70]
 
2025-07-16 21:13:49,255  [INFO] [D][11:41:51][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:13:49,264  [INFO] [D][11:41:51][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:13:49,269  [INFO] [D][11:41:51][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:13:49,272  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:13:49,277  [INFO] [D][11:41:51][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:13:49,278  [INFO] 
 
2025-07-16 21:13:49,282  [INFO] [D][11:41:51][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:13:49,289  [INFO] 0023B9D0113311331133113311331B884F682EE6C640A6192331486DB1E8C73BC0A40C
 
2025-07-16 21:13:49,292  [INFO] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,292  [INFO] SEND OK
 
2025-07-16 21:13:49,293  [INFO] 
 
2025-07-16 21:13:49,297  [INFO] [D][11:41:51][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:13:49,299  [INFO] [D][11:41:51][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:13:49,300  [INFO] 
 
2025-07-16 21:13:49,306  [INFO] [D][11:41:51][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:49,312  [INFO] [D][11:41:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:13:49,313  [INFO] [D][11:41:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:13:49,319  [INFO] [D][11:41:51][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:49,323  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:13:49,328  [INFO] [D][11:41:51][PROT]M2M Send ok [1730202111]
 
2025-07-16 21:13:49,330  [INFO] [D][11:41:51][PROT]CLEAN:0
 
2025-07-16 21:13:49,334  [INFO] [D][11:41:51][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:49,339  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:49,345  [INFO] [D][11:41:51][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:13:49,347  [INFO] [D][11:41:51][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:49,354  [INFO] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:14:29,752  [INFO] [D][11:42:32][CAT1]closed : 0
 
2025-07-16 21:14:29,757  [INFO] [D][11:42:32][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:14:29,759  [INFO] [D][11:42:32][SAL ]socket closed id[0]
 
2025-07-16 21:14:29,765  [INFO] [D][11:42:32][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:14:29,771  [INFO] [D][11:42:32][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:14:29,774  [INFO] [D][11:42:32][M2M ]m2m select fd[4]
 
2025-07-16 21:14:29,779  [INFO] [D][11:42:32][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:14:29,782  [INFO] [D][11:42:32][M2M ]tcpclient close[4]
 
2025-07-16 21:14:29,785  [INFO] [D][11:42:32][SAL ]socket[4] has closed
 
2025-07-16 21:14:29,791  [INFO] [D][11:42:32][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:14:29,796  [INFO] [D][11:42:32][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 21
 
2025-07-16 21:14:29,810  [INFO] [D][11:42:32][COMM]Main Task receive event:86
 
2025-07-16 21:14:29,819  [INFO] [W][11:42:32][PROT]remove success[1730202152],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:14:29,827  [INFO] [W][11:42:32][PROT]add success [1730202152],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:14:29,833  [INFO] [D][11:42:32][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:14:29,834  [INFO] [D][11:42:32][PROT]index:0 1730202152
 
2025-07-16 21:14:29,839  [INFO] [D][11:42:32][PROT]is_send:0
 
2025-07-16 21:14:29,844  [INFO] [D][11:42:32][PROT]sequence_num:45
 
2025-07-16 21:14:29,847  [INFO] [D][11:42:32][PROT]retry_timeout:0
 
2025-07-16 21:14:29,849  [INFO] [D][11:42:32][PROT]retry_times:1
 
2025-07-16 21:14:29,852  [INFO] [D][11:42:32][PROT]send_path:0x2
 
2025-07-16 21:14:29,857  [INFO] [D][11:42:32][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:14:29,864  [INFO] [D][11:42:32][PROT]===========================================================
 
2025-07-16 21:14:29,870  [INFO] [W][11:42:32][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202152]
 
2025-07-16 21:14:29,878  [INFO] [D][11:42:32][PROT]===========================================================
 
2025-07-16 21:14:29,881  [INFO] [D][11:42:32][PROT]sending traceid [999999999990001E]
 
2025-07-16 21:14:29,886  [INFO] [D][11:42:32][PROT]Send_TO_M2M [1730202152]
 
2025-07-16 21:14:29,891  [INFO] [D][11:42:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:14:29,894  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:14:29,899  [INFO] [D][11:42:32][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:14:29,906  [INFO] [D][11:42:32][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:14:29,911  [INFO] [D][11:42:32][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:14:29,917  [INFO] [D][11:42:32][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:14:29,925  [INFO] [D][11:42:32][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:14:29,927  [INFO] [D][11:42:32][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:14:29,930  [INFO] [D][11:42:32][CAT1]at ops open socket[0]
 
2025-07-16 21:14:29,935  [INFO] [D][11:42:32][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:14:29,936  [INFO] 
 
2025-07-16 21:14:29,941  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:14:29,942  [INFO] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,943  [INFO] +CGATT: 1
 
2025-07-16 21:14:29,944  [INFO] 
 
2025-07-16 21:14:29,944  [INFO] OK
 
2025-07-16 21:14:29,945  [INFO] 
 
2025-07-16 21:14:29,946  [INFO] [D][11:42:32][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:14:29,948  [INFO] 
 
2025-07-16 21:14:29,949  [INFO] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,951  [INFO] +CSQ: 31,99
 
2025-07-16 21:14:29,951  [INFO] 
 
2025-07-16 21:14:29,952  [INFO] OK
 
2025-07-16 21:14:29,952  [INFO] 
 
2025-07-16 21:14:29,955  [INFO] [D][11:42:32][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:14:29,956  [INFO] 
 
2025-07-16 21:14:29,957  [INFO] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,961  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:14:29,961  [INFO] 
 
2025-07-16 21:14:29,962  [INFO] OK
 
2025-07-16 21:14:29,963  [INFO] 
 
2025-07-16 21:14:29,970  [INFO] [D][11:42:32][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:14:29,970  [INFO] 
 
2025-07-16 21:14:29,972  [INFO] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,972  [INFO] OK
 
2025-07-16 21:14:29,972  [INFO] 
 
2025-07-16 21:14:29,976  [INFO] [D][11:42:32][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:14:30,044  [INFO] [D][11:42:32][CAT1]opened : 0, 0
 
2025-07-16 21:14:30,047  [INFO] [D][11:42:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:14:30,053  [INFO] [D][11:42:32][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:14:30,058  [INFO] [D][11:42:32][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:14:30,063  [INFO] [D][11:42:32][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:14:30,069  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:14:30,072  [INFO] [D][11:42:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:14:30,074  [INFO] [D][11:42:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:14:30,080  [INFO] [D][11:42:32][M2M ]m2m send data len[70]
 
2025-07-16 21:14:30,083  [INFO] [D][11:42:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:14:30,092  [INFO] [D][11:42:32][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:14:30,097  [INFO] [D][11:42:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:14:30,100  [INFO] [D][11:42:32][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:14:30,100  [INFO] 
 
2025-07-16 21:14:30,106  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:14:30,110  [INFO] [D][11:42:32][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:14:30,117  [INFO] 0023B9DD113311331133113311331B884AB0493A00D6EB7C0D641AB04A925367576D89
 
2025-07-16 21:14:30,118  [INFO] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:30,119  [INFO] SEND OK
 
2025-07-16 21:14:30,120  [INFO] 
 
2025-07-16 21:14:30,126  [INFO] [D][11:42:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:14:30,127  [INFO] [D][11:42:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:14:30,127  [INFO] 
 
2025-07-16 21:14:30,133  [INFO] [D][11:42:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:14:30,139  [INFO] [D][11:42:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:14:30,141  [INFO] [D][11:42:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:14:30,147  [INFO] [D][11:42:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:14:30,151  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:14:30,156  [INFO] [D][11:42:32][PROT]M2M Send ok [1730202152]
 
2025-07-16 21:14:30,158  [INFO] [D][11:42:32][PROT]CLEAN:0
 
2025-07-16 21:14:30,162  [INFO] [D][11:42:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:14:30,167  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:14:30,172  [INFO] [D][11:42:32][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:14:30,175  [INFO] [D][11:42:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:14:30,180  [INFO] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:10,701  [INFO] [D][11:43:13][CAT1]closed : 0
 
2025-07-16 21:15:10,706  [INFO] [D][11:43:13][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:15:10,708  [INFO] [D][11:43:13][SAL ]socket closed id[0]
 
2025-07-16 21:15:10,714  [INFO] [D][11:43:13][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:15:10,719  [INFO] [D][11:43:13][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:15:10,722  [INFO] [D][11:43:13][M2M ]m2m select fd[4]
 
2025-07-16 21:15:10,727  [INFO] [D][11:43:13][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:15:10,730  [INFO] [D][11:43:13][M2M ]tcpclient close[4]
 
2025-07-16 21:15:10,733  [INFO] [D][11:43:13][SAL ]socket[4] has closed
 
2025-07-16 21:15:10,739  [INFO] [D][11:43:13][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:15:10,745  [INFO] [D][11:43:13][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 22
 
2025-07-16 21:15:10,747  [INFO] [D][11:43:13][COMM]Main Task receive event:86
 
2025-07-16 21:15:10,755  [INFO] [W][11:43:13][PROT]remove success[1730202193],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:15:10,764  [INFO] [D][11:43:13][HSDK][0] flush to flash addr:[0xE47000] --- write len --- [256]
 
2025-07-16 21:15:10,766  [INFO] [D][11:43:13][PROT]index:0 1730202193
 
2025-07-16 21:15:10,769  [INFO] [D][11:43:13][PROT]is_send:0
 
2025-07-16 21:15:10,772  [INFO] [D][11:43:13][PROT]sequence_num:46
 
2025-07-16 21:15:10,775  [INFO] [D][11:43:13][PROT]retry_timeout:0
 
2025-07-16 21:15:10,777  [INFO] [D][11:43:13][PROT]retry_times:1
 
2025-07-16 21:15:10,781  [INFO] [D][11:43:13][PROT]send_path:0x2
 
2025-07-16 21:15:10,786  [INFO] [D][11:43:13][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:15:10,795  [INFO] [D][11:43:13][PROT]===========================================================
 
2025-07-16 21:15:10,800  [INFO] [D][11:43:13][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:10,803  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:10,808  [INFO] [D][11:43:13][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:15:10,814  [INFO] [D][11:43:13][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:15:10,820  [INFO] [D][11:43:13][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:15:10,826  [INFO] [D][11:43:13][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:15:10,835  [INFO] [D][11:43:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:15:10,839  [INFO] [W][11:43:13][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202193]
 
2025-07-16 21:15:10,845  [INFO] [D][11:43:13][PROT]===========================================================
 
2025-07-16 21:15:10,850  [INFO] [D][11:43:13][PROT]sending traceid [999999999990001F]
 
2025-07-16 21:15:10,852  [INFO] [D][11:43:13][PROT]Send_TO_M2M [1730202193]
 
2025-07-16 21:15:10,859  [INFO] [D][11:43:13][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:15:10,861  [INFO] [D][11:43:13][CAT1]at ops open socket[0]
 
2025-07-16 21:15:10,870  [INFO] [W][11:43:13][PROT]add success [1730202193],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:15:10,875  [INFO] [D][11:43:13][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:15:10,878  [INFO] [D][11:43:13][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:15:10,880  [INFO] 
 
2025-07-16 21:15:10,884  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:15:10,886  [INFO] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,888  [INFO] +CGATT: 1
 
2025-07-16 21:15:10,889  [INFO] 
 
2025-07-16 21:15:10,889  [INFO] OK
 
2025-07-16 21:15:10,889  [INFO] 
 
2025-07-16 21:15:10,891  [INFO] [D][11:43:13][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:15:10,892  [INFO] 
 
2025-07-16 21:15:10,894  [INFO] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,896  [INFO] +CSQ: 31,99
 
2025-07-16 21:15:10,897  [INFO] 
 
2025-07-16 21:15:10,898  [INFO] OK
 
2025-07-16 21:15:10,899  [INFO] 
 
2025-07-16 21:15:10,901  [INFO] [D][11:43:13][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:15:10,902  [INFO] 
 
2025-07-16 21:15:10,903  [INFO] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,906  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:15:10,907  [INFO] 
 
2025-07-16 21:15:10,907  [INFO] OK
 
2025-07-16 21:15:10,909  [INFO] 
 
2025-07-16 21:15:10,914  [INFO] [D][11:43:13][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:15:10,915  [INFO] 
 
2025-07-16 21:15:10,917  [INFO] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,918  [INFO] OK
 
2025-07-16 21:15:10,918  [INFO] 
 
2025-07-16 21:15:10,921  [INFO] [D][11:43:13][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:15:10,970  [INFO] [D][11:43:13][CAT1]opened : 0, 0
 
2025-07-16 21:15:10,972  [INFO] [D][11:43:13][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:10,979  [INFO] [D][11:43:13][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:15:10,984  [INFO] [D][11:43:13][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:15:10,990  [INFO] [D][11:43:13][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:15:10,995  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:15:10,998  [INFO] [D][11:43:13][SAL ]sock send credit cnt[6]
 
2025-07-16 21:15:11,001  [INFO] [D][11:43:13][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:15:11,007  [INFO] [D][11:43:13][M2M ]m2m send data len[70]
 
2025-07-16 21:15:11,009  [INFO] [D][11:43:13][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:15:11,018  [INFO] [D][11:43:13][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:15:11,023  [INFO] [D][11:43:13][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:15:11,025  [INFO] [D][11:43:13][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:15:11,026  [INFO] 
 
2025-07-16 21:15:11,032  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:15:11,036  [INFO] [D][11:43:13][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:15:11,043  [INFO] 0023B9DC113311331133113311331B88485570E4CD69CA006BFF6365E0C9861E2CE57D
 
2025-07-16 21:15:11,045  [INFO] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:11,046  [INFO] SEND OK
 
2025-07-16 21:15:11,046  [INFO] 
 
2025-07-16 21:15:11,051  [INFO] [D][11:43:13][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:15:11,053  [INFO] [D][11:43:13][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:15:11,054  [INFO] 
 
2025-07-16 21:15:11,059  [INFO] [D][11:43:13][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:11,065  [INFO] [D][11:43:13][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:15:11,067  [INFO] [D][11:43:13][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:15:11,074  [INFO] [D][11:43:13][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:11,076  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:11,082  [INFO] [D][11:43:13][PROT]M2M Send ok [1730202193]
 
2025-07-16 21:15:11,084  [INFO] [D][11:43:13][PROT]CLEAN:0
 
2025-07-16 21:15:11,088  [INFO] [D][11:43:13][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:11,094  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:11,099  [INFO] [D][11:43:13][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:15:11,101  [INFO] [D][11:43:13][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:11,107  [INFO] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:48,511  [INFO] [D][11:43:50][COMM]IMU: 1033828 MEMS ERROR when cali 0
 
2025-07-16 21:15:51,653  [INFO] [D][11:43:53][CAT1]closed : 0
 
2025-07-16 21:15:51,657  [INFO] [D][11:43:53][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:15:51,661  [INFO] [D][11:43:53][SAL ]socket closed id[0]
 
2025-07-16 21:15:51,666  [INFO] [D][11:43:53][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:15:51,671  [INFO] [D][11:43:53][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:15:51,674  [INFO] [D][11:43:53][M2M ]m2m select fd[4]
 
2025-07-16 21:15:51,680  [INFO] [D][11:43:53][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:15:51,682  [INFO] [D][11:43:53][M2M ]tcpclient close[4]
 
2025-07-16 21:15:51,685  [INFO] [D][11:43:53][SAL ]socket[4] has closed
 
2025-07-16 21:15:51,690  [INFO] [D][11:43:53][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:15:51,697  [INFO] [D][11:43:53][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 23
 
2025-07-16 21:15:51,699  [INFO] [D][11:43:53][COMM]Main Task receive event:86
 
2025-07-16 21:15:51,707  [INFO] [W][11:43:53][PROT]remove success[1730202233],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:15:51,713  [INFO] [D][11:43:53][PROT]index:0 1730202233
 
2025-07-16 21:15:51,715  [INFO] [D][11:43:53][PROT]is_send:0
 
2025-07-16 21:15:51,719  [INFO] [D][11:43:53][PROT]sequence_num:47
 
2025-07-16 21:15:51,721  [INFO] [D][11:43:53][PROT]retry_timeout:0
 
2025-07-16 21:15:51,725  [INFO] [D][11:43:53][PROT]retry_times:1
 
2025-07-16 21:15:51,727  [INFO] [D][11:43:53][PROT]send_path:0x2
 
2025-07-16 21:15:51,732  [INFO] [D][11:43:53][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:15:51,738  [INFO] [D][11:43:53][PROT]===========================================================
 
2025-07-16 21:15:51,743  [INFO] [W][11:43:53][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202233]
 
2025-07-16 21:15:51,752  [INFO] [D][11:43:53][PROT]===========================================================
 
2025-07-16 21:15:51,757  [INFO] [D][11:43:53][PROT]sending traceid [9999999999900020]
 
2025-07-16 21:15:51,760  [INFO] [D][11:43:53][PROT]Send_TO_M2M [1730202233]
 
2025-07-16 21:15:51,766  [INFO] [D][11:43:53][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:51,772  [INFO] [D][11:43:53][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:51,774  [INFO] [D][11:43:53][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:15:51,783  [INFO] [D][11:43:53][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:15:51,785  [INFO] [D][11:43:53][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:15:51,794  [INFO] [D][11:43:53][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:15:51,800  [INFO] [D][11:43:54][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:15:51,802  [INFO] [D][11:43:54][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:15:51,805  [INFO] [D][11:43:54][CAT1]at ops open socket[0]
 
2025-07-16 21:15:51,816  [INFO] [W][11:43:53][PROT]add success [1730202233],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:15:51,822  [INFO] [D][11:43:54][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:15:51,824  [INFO] [D][11:43:54][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:15:51,825  [INFO] 
 
2025-07-16 21:15:51,831  [INFO] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:15:51,833  [INFO] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,833  [INFO] +CGATT: 1
 
2025-07-16 21:15:51,834  [INFO] 
 
2025-07-16 21:15:51,834  [INFO] OK
 
2025-07-16 21:15:51,834  [INFO] 
 
2025-07-16 21:15:51,838  [INFO] [D][11:43:54][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:15:51,839  [INFO] 
 
2025-07-16 21:15:51,839  [INFO] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,840  [INFO] +CSQ: 31,99
 
2025-07-16 21:15:51,841  [INFO] 
 
2025-07-16 21:15:51,842  [INFO] OK
 
2025-07-16 21:15:51,842  [INFO] 
 
2025-07-16 21:15:51,844  [INFO] [D][11:43:54][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:15:51,846  [INFO] 
 
2025-07-16 21:15:51,846  [INFO] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,849  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:15:51,849  [INFO] 
 
2025-07-16 21:15:51,851  [INFO] OK
 
2025-07-16 21:15:51,851  [INFO] 
 
2025-07-16 21:15:51,860  [INFO] [D][11:43:54][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:15:51,860  [INFO] 
 
2025-07-16 21:15:51,861  [INFO] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,861  [INFO] OK
 
2025-07-16 21:15:51,862  [INFO] 
 
2025-07-16 21:15:51,865  [INFO] [D][11:43:54][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:15:51,914  [INFO] [D][11:43:54][CAT1]opened : 0, 0
 
2025-07-16 21:15:51,917  [INFO] [D][11:43:54][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:51,922  [INFO] [D][11:43:54][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:15:51,927  [INFO] [D][11:43:54][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:15:51,932  [INFO] [D][11:43:54][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:15:51,937  [INFO] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:15:51,941  [INFO] [D][11:43:54][SAL ]sock send credit cnt[6]
 
2025-07-16 21:15:51,943  [INFO] [D][11:43:54][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:15:51,949  [INFO] [D][11:43:54][M2M ]m2m send data len[70]
 
2025-07-16 21:15:51,952  [INFO] [D][11:43:54][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:15:51,961  [INFO] [D][11:43:54][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:15:51,966  [INFO] [D][11:43:54][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:15:51,968  [INFO] [D][11:43:54][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:15:51,969  [INFO] 
 
2025-07-16 21:15:51,974  [INFO] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:15:51,979  [INFO] [D][11:43:54][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:15:51,986  [INFO] 0023B9D1113311331133113311331B887BFC7772C024EA25C5D92CC180F405746DD570
 
2025-07-16 21:15:51,988  [INFO] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,988  [INFO] SEND OK
 
2025-07-16 21:15:51,989  [INFO] 
 
2025-07-16 21:15:51,994  [INFO] [D][11:43:54][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:15:51,997  [INFO] [D][11:43:54][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:15:51,997  [INFO] 
 
2025-07-16 21:15:52,002  [INFO] [D][11:43:54][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:52,008  [INFO] [D][11:43:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:15:52,010  [INFO] [D][11:43:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:15:52,016  [INFO] [D][11:43:54][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:52,018  [INFO] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:52,024  [INFO] [D][11:43:54][PROT]M2M Send ok [1730202234]
 
2025-07-16 21:15:52,026  [INFO] [D][11:43:54][PROT]CLEAN:0
 
2025-07-16 21:15:52,029  [INFO] [D][11:43:54][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:52,036  [INFO] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:52,041  [INFO] [D][11:43:54][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:15:52,043  [INFO] [D][11:43:54][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:52,049  [INFO] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:16:32,619  [INFO] [D][11:44:34][CAT1]closed : 0
 
2025-07-16 21:16:32,625  [INFO] [D][11:44:34][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:16:32,627  [INFO] [D][11:44:34][SAL ]socket closed id[0]
 
2025-07-16 21:16:32,634  [INFO] [D][11:44:34][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:16:32,640  [INFO] [D][11:44:34][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:16:32,642  [INFO] [D][11:44:34][M2M ]m2m select fd[4]
 
2025-07-16 21:16:32,647  [INFO] [D][11:44:34][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:16:32,649  [INFO] [D][11:44:34][M2M ]tcpclient close[4]
 
2025-07-16 21:16:32,652  [INFO] [D][11:44:34][SAL ]socket[4] has closed
 
2025-07-16 21:16:32,658  [INFO] [D][11:44:34][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:16:32,664  [INFO] [D][11:44:34][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 24
 
2025-07-16 21:16:32,704  [INFO] [D][11:44:35][COMM]Main Task receive event:86
 
2025-07-16 21:16:32,712  [INFO] [W][11:44:35][PROT]remove success[1730202275],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:16:32,715  [INFO] [D][11:44:35][PROT]index:0 1730202275
 
2025-07-16 21:16:32,717  [INFO] [D][11:44:35][PROT]is_send:0
 
2025-07-16 21:16:32,720  [INFO] [D][11:44:35][PROT]sequence_num:48
 
2025-07-16 21:16:32,723  [INFO] [D][11:44:35][PROT]retry_timeout:0
 
2025-07-16 21:16:32,729  [INFO] [D][11:44:35][PROT]retry_times:1
 
2025-07-16 21:16:32,732  [INFO] [D][11:44:35][PROT]send_path:0x2
 
2025-07-16 21:16:32,734  [INFO] [D][11:44:35][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:16:32,743  [INFO] [D][11:44:35][PROT]===========================================================
 
2025-07-16 21:16:32,749  [INFO] [W][11:44:35][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202275]
 
2025-07-16 21:16:32,755  [INFO] [D][11:44:35][PROT]===========================================================
 
2025-07-16 21:16:32,759  [INFO] [D][11:44:35][PROT]sending traceid [9999999999900021]
 
2025-07-16 21:16:32,766  [INFO] [D][11:44:35][PROT]Send_TO_M2M [1730202275]
 
2025-07-16 21:16:32,768  [INFO] [D][11:44:35][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:16:32,773  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:16:32,779  [INFO] [D][11:44:35][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:16:32,784  [INFO] [D][11:44:35][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:16:32,790  [INFO] [D][11:44:35][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:16:32,796  [INFO] [D][11:44:35][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:16:32,802  [INFO] [D][11:44:35][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:16:32,807  [INFO] [D][11:44:35][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:16:32,810  [INFO] [D][11:44:35][CAT1]at ops open socket[0]
 
2025-07-16 21:16:32,812  [INFO] [D][11:44:35][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:16:32,814  [INFO] 
 
2025-07-16 21:16:32,823  [INFO] [W][11:44:35][PROT]add success [1730202275],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:16:32,830  [INFO] [D][11:44:35][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:16:32,834  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:16:32,835  [INFO] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,836  [INFO] +CGATT: 1
 
2025-07-16 21:16:32,838  [INFO] 
 
2025-07-16 21:16:32,838  [INFO] OK
 
2025-07-16 21:16:32,838  [INFO] 
 
2025-07-16 21:16:32,840  [INFO] [D][11:44:35][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:16:32,841  [INFO] 
 
2025-07-16 21:16:32,843  [INFO] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,844  [INFO] +CSQ: 31,99
 
2025-07-16 21:16:32,845  [INFO] 
 
2025-07-16 21:16:32,846  [INFO] OK
 
2025-07-16 21:16:32,846  [INFO] 
 
2025-07-16 21:16:32,849  [INFO] [D][11:44:35][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:16:32,850  [INFO] 
 
2025-07-16 21:16:32,851  [INFO] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,854  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:16:32,854  [INFO] 
 
2025-07-16 21:16:32,855  [INFO] OK
 
2025-07-16 21:16:32,855  [INFO] 
 
2025-07-16 21:16:32,862  [INFO] [D][11:44:35][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:16:32,863  [INFO] 
 
2025-07-16 21:16:32,864  [INFO] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,865  [INFO] OK
 
2025-07-16 21:16:32,865  [INFO] 
 
2025-07-16 21:16:32,869  [INFO] [D][11:44:35][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:16:32,931  [INFO] [D][11:44:35][CAT1]opened : 0, 0
 
2025-07-16 21:16:32,934  [INFO] [D][11:44:35][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:16:32,939  [INFO] [D][11:44:35][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:16:32,945  [INFO] [D][11:44:35][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:16:32,950  [INFO] [D][11:44:35][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:16:32,956  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:16:32,959  [INFO] [D][11:44:35][SAL ]sock send credit cnt[6]
 
2025-07-16 21:16:32,962  [INFO] [D][11:44:35][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:16:32,968  [INFO] [D][11:44:35][M2M ]m2m send data len[70]
 
2025-07-16 21:16:32,970  [INFO] [D][11:44:35][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:16:32,979  [INFO] [D][11:44:35][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:16:32,984  [INFO] [D][11:44:35][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:16:32,987  [INFO] [D][11:44:35][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:16:32,988  [INFO] 
 
2025-07-16 21:16:32,993  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:16:32,998  [INFO] [D][11:44:35][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:16:33,004  [INFO] 0023B9D2113311331133113311331B8874F0AF8392C29442FF78461E21A6DCB6F2D770
 
2025-07-16 21:16:33,006  [INFO] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:33,007  [INFO] SEND OK
 
2025-07-16 21:16:33,007  [INFO] 
 
2025-07-16 21:16:33,012  [INFO] [D][11:44:35][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:16:33,015  [INFO] [D][11:44:35][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:16:33,015  [INFO] 
 
2025-07-16 21:16:33,020  [INFO] [D][11:44:35][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:16:33,026  [INFO] [D][11:44:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:16:33,028  [INFO] [D][11:44:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:16:33,034  [INFO] [D][11:44:35][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:16:33,037  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:16:33,042  [INFO] [D][11:44:35][PROT]M2M Send ok [1730202275]
 
2025-07-16 21:16:33,045  [INFO] [D][11:44:35][PROT]CLEAN:0
 
2025-07-16 21:16:33,048  [INFO] [D][11:44:35][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:16:33,054  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:16:33,060  [INFO] [D][11:44:35][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:16:33,062  [INFO] [D][11:44:35][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:16:33,067  [INFO] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:17:13,658  [INFO] [D][11:45:15][CAT1]closed : 0
 
2025-07-16 21:17:13,662  [INFO] [D][11:45:15][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:17:13,666  [INFO] [D][11:45:15][SAL ]socket closed id[0]
 
2025-07-16 21:17:13,671  [INFO] [D][11:45:15][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:17:13,677  [INFO] [D][11:45:15][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:17:13,679  [INFO] [D][11:45:15][M2M ]m2m select fd[4]
 
2025-07-16 21:17:13,685  [INFO] [D][11:45:15][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:17:13,688  [INFO] [D][11:45:15][M2M ]tcpclient close[4]
 
2025-07-16 21:17:13,691  [INFO] [D][11:45:15][SAL ]socket[4] has closed
 
2025-07-16 21:17:13,696  [INFO] [D][11:45:15][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:17:13,702  [INFO] [D][11:45:15][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 25
 
2025-07-16 21:17:13,720  [INFO] [D][11:45:16][COMM]Main Task receive event:86
 
2025-07-16 21:17:13,728  [INFO] [W][11:45:16][PROT]remove success[1730202316],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:17:13,737  [INFO] [D][11:45:16][HSDK][0] flush to flash addr:[0xE47100] --- write len --- [256]
 
2025-07-16 21:17:13,738  [INFO] [D][11:45:16][PROT]index:0 1730202316
 
2025-07-16 21:17:13,741  [INFO] [D][11:45:16][PROT]is_send:0
 
2025-07-16 21:17:13,744  [INFO] [D][11:45:16][PROT]sequence_num:49
 
2025-07-16 21:17:13,747  [INFO] [D][11:45:16][PROT]retry_timeout:0
 
2025-07-16 21:17:13,749  [INFO] [D][11:45:16][PROT]retry_times:1
 
2025-07-16 21:17:13,752  [INFO] [D][11:45:16][PROT]send_path:0x2
 
2025-07-16 21:17:13,758  [INFO] [D][11:45:16][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:17:13,764  [INFO] [D][11:45:16][PROT]===========================================================
 
2025-07-16 21:17:13,769  [INFO] [D][11:45:16][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:17:13,774  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:17:13,778  [INFO] [D][11:45:16][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:17:13,786  [INFO] [D][11:45:16][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:17:13,788  [INFO] [D][11:45:16][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:17:13,799  [INFO] [D][11:45:16][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:17:13,804  [INFO] [D][11:45:16][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:17:13,808  [INFO] [W][11:45:16][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202316]
 
2025-07-16 21:17:13,816  [INFO] [D][11:45:16][PROT]===========================================================
 
2025-07-16 21:17:13,820  [INFO] [D][11:45:16][PROT]sending traceid [9999999999900022]
 
2025-07-16 21:17:13,826  [INFO] [D][11:45:16][PROT]Send_TO_M2M [1730202316]
 
2025-07-16 21:17:13,828  [INFO] [D][11:45:16][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:17:13,833  [INFO] [D][11:45:16][CAT1]at ops open socket[0]
 
2025-07-16 21:17:13,836  [INFO] [D][11:45:16][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:17:13,837  [INFO] 
 
2025-07-16 21:17:13,841  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:17:13,849  [INFO] [W][11:45:16][PROT]add success [1730202316],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:17:13,857  [INFO] [D][11:45:16][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:17:13,858  [INFO] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,859  [INFO] +CGATT: 1
 
2025-07-16 21:17:13,859  [INFO] 
 
2025-07-16 21:17:13,860  [INFO] OK
 
2025-07-16 21:17:13,860  [INFO] 
 
2025-07-16 21:17:13,864  [INFO] [D][11:45:16][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:17:13,865  [INFO] 
 
2025-07-16 21:17:13,865  [INFO] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,867  [INFO] +CSQ: 31,99
 
2025-07-16 21:17:13,868  [INFO] 
 
2025-07-16 21:17:13,868  [INFO] OK
 
2025-07-16 21:17:13,869  [INFO] 
 
2025-07-16 21:17:13,872  [INFO] [D][11:45:16][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:17:13,873  [INFO] 
 
2025-07-16 21:17:13,873  [INFO] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,875  [INFO] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:17:13,875  [INFO] 
 
2025-07-16 21:17:13,877  [INFO] OK
 
2025-07-16 21:17:13,878  [INFO] 
 
2025-07-16 21:17:13,884  [INFO] [D][11:45:16][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:17:13,885  [INFO] 
 
2025-07-16 21:17:13,886  [INFO] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,887  [INFO] OK
 
2025-07-16 21:17:13,887  [INFO] 
 
2025-07-16 21:17:13,891  [INFO] [D][11:45:16][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:17:13,959  [INFO] [D][11:45:16][CAT1]opened : 0, 0
 
2025-07-16 21:17:13,962  [INFO] [D][11:45:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:17:13,968  [INFO] [D][11:45:16][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:17:13,972  [INFO] [D][11:45:16][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:17:13,978  [INFO] [D][11:45:16][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:17:13,984  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:17:13,987  [INFO] [D][11:45:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:17:13,989  [INFO] [D][11:45:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:17:13,995  [INFO] [D][11:45:16][M2M ]m2m send data len[70]
 
2025-07-16 21:17:13,998  [INFO] [D][11:45:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:17:14,007  [INFO] [D][11:45:16][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:17:14,012  [INFO] [D][11:45:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:17:14,015  [INFO] [D][11:45:16][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:17:14,016  [INFO] 
 
2025-07-16 21:17:14,020  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:17:14,025  [INFO] [D][11:45:16][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:17:14,031  [INFO] 0023B9DE113311331133113311331B8877096CCFBF55FFDBA63D3419FDFA87F1E216EB
 
2025-07-16 21:17:14,034  [INFO] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:14,035  [INFO] SEND OK
 
2025-07-16 21:17:14,035  [INFO] 
 
2025-07-16 21:17:14,040  [INFO] [D][11:45:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:17:14,042  [INFO] [D][11:45:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:17:14,043  [INFO] 
 
2025-07-16 21:17:14,048  [INFO] [D][11:45:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:17:14,054  [INFO] [D][11:45:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:17:14,056  [INFO] [D][11:45:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:17:14,062  [INFO] [D][11:45:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:17:14,065  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:17:14,071  [INFO] [D][11:45:16][PROT]M2M Send ok [1730202316]
 
2025-07-16 21:17:14,073  [INFO] [D][11:45:16][PROT]CLEAN:0
 
2025-07-16 21:17:14,076  [INFO] [D][11:45:16][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:17:14,081  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:17:14,087  [INFO] [D][11:45:16][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:17:14,089  [INFO] [D][11:45:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:17:14,096  [INFO] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:17:54,531  [INFO] [D][11:45:56][CAT1]closed : 0
 
2025-07-16 21:17:54,535  [INFO] [D][11:45:56][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:17:54,538  [INFO] [D][11:45:56][SAL ]socket closed id[0]
 
2025-07-16 21:17:54,544  [INFO] [D][11:45:56][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:17:54,549  [INFO] [D][11:45:56][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:17:54,551  [INFO] [D][11:45:56][M2M ]m2m select fd[4]
 
2025-07-16 21:17:54,558  [INFO] [D][11:45:56][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:17:54,560  [INFO] [D][11:45:56][M2M ]tcpclient close[4]
 
2025-07-16 21:17:54,563  [INFO] [D][11:45:56][SAL ]socket[4] has closed
 
2025-07-16 21:17:54,568  [INFO] [D][11:45:56][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:18:14,506  [INFO] [D][11:46:16][COMM]Main Task receive event:21
 
2025-07-16 21:18:14,596  [INFO] [D][11:46:16][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:18:14,601  [INFO] [D][11:46:16][HSDK]hexlog index save 0 25088 39 @ 1536 : 0
 
2025-07-16 21:18:14,605  [INFO] [D][11:46:16][HSDK]write save hexlog index [0]
 
2025-07-16 21:18:15,267  [INFO] [W][11:46:17][COMM]Power Off
 
2025-07-16 21:18:15,274  [INFO] [D][11:46:17][COMM]Ready to power off, please pull out the cable
 
2025-07-16 21:18:15,281  [INFO] [D][11:46:17][COMM]Main Task receive event:21 finished processing
 
2025-07-16 21:18:27,966  [INFO] [D][11:46:30][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:28,175  [INFO] [D][11:46:30][COMM]appComBuriedPack:module=9,type=2,localtime=1730202390, cnt=2
 
2025-07-16 21:18:28,179  [INFO] [D][11:46:30][COMM]Main Task receive event:102
 
2025-07-16 21:18:28,185  [INFO] [D][11:46:30][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:18:28,964  [INFO] [D][11:46:31][COMM]1194299 imu init OK
 
2025-07-16 21:18:28,975  [INFO] [D][11:46:31][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:29,975  [INFO] [D][11:46:32][COMM]1195310 imu init OK
 
2025-07-16 21:18:29,986  [INFO] [D][11:46:32][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:30,986  [INFO] [D][11:46:33][COMM]1196321 imu init OK
 
2025-07-16 21:18:30,998  [INFO] [D][11:46:33][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:32,000  [INFO] [D][11:46:34][COMM]1197333 imu init OK
 
2025-07-16 21:18:32,010  [INFO] [D][11:46:34][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:33,010  [INFO] [D][11:46:35][COMM]1198344 imu init OK
 
2025-07-16 21:18:33,021  [INFO] [D][11:46:35][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:34,020  [INFO] [D][11:46:36][COMM]1199355 imu init OK
 
2025-07-16 21:18:34,032  [INFO] [D][11:46:36][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:35,032  [INFO] [D][11:46:37][COMM]1200367 imu init OK
 
2025-07-16 21:18:35,043  [INFO] [D][11:46:37][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:36,043  [INFO] [D][11:46:38][COMM]1201378 imu init OK
 
2025-07-16 21:18:36,055  [INFO] [D][11:46:38][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:37,054  [INFO] [D][11:46:39][COMM]1202389 imu init OK
 
2025-07-16 21:18:37,066  [INFO] [D][11:46:39][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:38,065  [INFO] [D][11:46:40][COMM]1203401 imu init OK
 
2025-07-16 21:18:38,076  [INFO] [D][11:46:40][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:39,076  [INFO] [D][11:46:41][COMM]imu error,enter wait
 
2025-07-16 21:19:28,971  [INFO] [D][11:47:31][COMM]appComBuriedPack:module=9,type=2,localtime=1730202451, cnt=3
 
2025-07-16 21:19:29,015  [INFO] [D][11:47:31][COMM]Main Task receive event:102
 
2025-07-16 21:19:29,022  [INFO] [D][11:47:31][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:31:41,410  [INFO] [D][11:59:43][COMM]Main Task receive event:99
 
2025-07-16 21:31:41,415  [INFO] [D][11:59:43][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:31:41,424  [INFO] [D][11:59:43][COMM]handlerPeriodWakeup, g_elecBatMissedCount = 2, time = 1730203183, allstateRepSeconds = 1730201400
 
2025-07-16 21:31:41,428  [INFO] [D][11:59:43][COMM]Main Task receive event:99 finished processing
 
2025-07-16 21:31:41,434  [INFO] [D][11:59:43][COMM]Main Task receive event:146
 
2025-07-16 21:31:41,436  [INFO] [D][11:59:43][COMM]a:13,b:0,c:0,d:0,e:0
 
2025-07-16 21:31:41,446  [INFO] [W][11:59:43][PROT]remove success[1730203183],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:31:41,453  [INFO] [W][11:59:43][PROT]add success [1730203183],send_path[2],type[C001],priority[0],index[0],used[1]
 
2025-07-16 21:31:41,459  [INFO] [D][11:59:43][COMM]Main Task receive event:146 finished processing
 
2025-07-16 21:31:41,465  [INFO] [D][11:59:43][PROT]index:0 1730203183
 
2025-07-16 21:31:41,468  [INFO] [D][11:59:43][PROT]is_send:0
 
2025-07-16 21:31:41,471  [INFO] [D][11:59:43][PROT]sequence_num:50
 
2025-07-16 21:31:41,473  [INFO] [D][11:59:43][PROT]retry_timeout:0
 
2025-07-16 21:31:41,476  [INFO] [D][11:59:43][PROT]retry_times:1
 
2025-07-16 21:31:41,479  [INFO] [D][11:59:43][PROT]send_path:0x2
 
2025-07-16 21:31:41,484  [INFO] [D][11:59:43][PROT]min_index:0, type:0xC001, priority:0
 
2025-07-16 21:31:41,490  [INFO] [D][11:59:43][PROT]===========================================================
 
2025-07-16 21:31:41,496  [INFO] [W][11:59:43][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730203183]
 
2025-07-16 21:31:41,504  [INFO] [D][11:59:43][PROT]===========================================================
 
2025-07-16 21:31:41,509  [INFO] [D][11:59:43][PROT]sending traceid [9999999999900026]
 
2025-07-16 21:31:41,512  [INFO] [D][11:59:43][PROT]Send_TO_M2M [1730203183]
 
2025-07-16 21:31:41,518  [INFO] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:31:41,521  [INFO] [E][11:59:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:31:41,527  [INFO] [E][11:59:43][M2M ]m2m send data len err[-1,102]
 
2025-07-16 21:31:41,529  [INFO] [D][11:59:43][M2M ]m2m send data len[-1]
 
2025-07-16 21:31:41,534  [INFO] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:31:41,540  [INFO] [E][11:59:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:31:41,542  [INFO] [E][11:59:43][PROT]M2M Send Fail [1730203183]
 
2025-07-16 21:31:41,546  [INFO] [D][11:59:43][PROT]CLEAN,SEND:0
 
2025-07-16 21:31:41,552  [INFO] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:31:41,554  [INFO] [D][11:59:43][PROT]CLEAN:0
 
2025-07-16 21:31:41,557  [INFO] [D][11:59:43][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:31:41,562  [INFO] [D][11:59:43][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:31:41,563  [INFO] 
 
2025-07-16 21:31:41,567  [INFO] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:31:49,472  [INFO] [D][11:59:51][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:31:49,473  [INFO] 
 
2025-07-16 21:31:53,454  [INFO] [D][11:59:55][COMM]Main Task receive event:7
 
2025-07-16 21:31:53,459  [INFO] [D][11:59:55][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:31:53,468  [INFO] [D][11:59:55][COMM]handlerPeriodRep, g_elecBatMissedCount = 2, time = 1730203195, allstateRepSeconds = 1730201400
 
2025-07-16 21:31:53,470  [INFO] [D][11:59:55][COMM]Open GPS Module...
 
2025-07-16 21:31:53,473  [INFO] [D][11:59:55][GNSS]start event:3
 
2025-07-16 21:31:53,479  [INFO] [D][11:59:55][GNSS]GPS start. ret=0
 
2025-07-16 21:31:53,481  [INFO] [W][11:59:55][GNSS]start sing locating
 
2025-07-16 21:31:53,487  [INFO] [D][11:59:55][GNSS]gps single mode only, do wifi scan.
 
2025-07-16 21:31:53,490  [INFO] [D][11:59:55][COMM]index:0,power_mode:0xFF
 
2025-07-16 21:31:53,492  [INFO] [D][11:59:55][COMM]index:1,sound_mode:0xFF
 
2025-07-16 21:31:53,498  [INFO] [D][11:59:55][COMM]index:2,gsensor_mode:0xFF
 
2025-07-16 21:31:53,500  [INFO] [D][11:59:55][COMM]index:3,report_freq_mode:0xFF
 
2025-07-16 21:31:53,506  [INFO] [D][11:59:55][COMM]index:4,report_period:0xFF
 
2025-07-16 21:31:53,509  [INFO] [D][11:59:55][COMM]index:5,normal_reset_mode:0xFF
 
2025-07-16 21:31:53,514  [INFO] [D][11:59:55][COMM]index:6,normal_reset_period:0xFF
 
2025-07-16 21:31:53,520  [INFO] [D][11:59:55][COMM]index:7,spock_over_speed:0xFF
 
2025-07-16 21:31:53,523  [INFO] [D][11:59:55][COMM]index:8,spock_limit_speed:0xFF
 
2025-07-16 21:31:53,528  [INFO] [D][11:59:55][COMM]index:9,spock_report_period_unlock:0xFF
 
2025-07-16 21:31:53,534  [INFO] [D][11:59:55][COMM]index:10,spock_report_period_unlock_unit:0xFF
 
2025-07-16 21:31:53,540  [INFO] [D][11:59:55][COMM]index:11,ble_scan_mode:0xFF
 
2025-07-16 21:31:53,543  [INFO] [D][11:59:55][COMM]index:12,ble_adv_mode:0xFF
 
2025-07-16 21:31:53,548  [INFO] [D][11:59:55][COMM]index:13,spock_audio_volumn:0xFF
 
2025-07-16 21:31:53,554  [INFO] [D][11:59:55][COMM]index:14,spock_low_bat_alarm_soc:0xFF
 
2025-07-16 21:31:53,556  [INFO] [D][11:59:55][COMM]index:15,bat_auth_mode:0xFF
 
2025-07-16 21:31:53,562  [INFO] [D][11:59:55][COMM]index:16,imu_config_params:0xFF
 
2025-07-16 21:31:53,564  [INFO] [D][11:59:55][COMM]index:17,long_connect_params:0xFF
 
2025-07-16 21:31:53,570  [INFO] [D][11:59:55][COMM]index:18,detain_mark:0xFF
 
2025-07-16 21:31:53,575  [INFO] [D][11:59:55][COMM]index:19,lock_pos_report_count:0xFF
 
2025-07-16 21:31:53,578  [INFO] [D][11:59:55][COMM]index:20,lock_pos_report_interval:0xFF
 
2025-07-16 21:31:53,584  [INFO] [D][11:59:55][COMM]index:21,mc_mode:0xFF
 
2025-07-16 21:31:53,587  [INFO] [D][11:59:55][COMM]index:22,S_mode:0xFF
 
2025-07-16 21:31:53,590  [INFO] [D][11:59:55][COMM]index:23,overweight:0xFF
 
2025-07-16 21:31:53,595  [INFO] [D][11:59:55][COMM]index:24,standstill_mode:0xFF
 
2025-07-16 21:31:53,598  [INFO] [D][11:59:55][COMM]index:25,night_mode:0xFF
 
2025-07-16 21:31:53,603  [INFO] [D][11:59:55][COMM]index:26,experiment1:0xFF
 
2025-07-16 21:31:53,607  [INFO] [D][11:59:55][COMM]index:27,experiment2:0xFF
 
2025-07-16 21:31:53,612  [INFO] [D][11:59:55][COMM]index:28,experiment3:0xFF
 
2025-07-16 21:31:53,615  [INFO] [D][11:59:55][COMM]index:29,experiment4:0xFF
 
2025-07-16 21:31:53,621  [INFO] [D][11:59:55][COMM]index:30,night_mode_start:0xFF
 
2025-07-16 21:31:53,623  [INFO] [D][11:59:55][COMM]index:31,night_mode_end:0xFF
 
2025-07-16 21:31:53,629  [INFO] [D][11:59:55][COMM]index:33,park_report_minutes:0xFF
 
2025-07-16 21:31:53,634  [INFO] [D][11:59:55][COMM]index:34,park_report_mode:0xFF
 
2025-07-16 21:31:53,637  [INFO] [D][11:59:55][COMM]index:35,mc_undervoltage_protection:0xFF
 
2025-07-16 21:31:53,642  [INFO] [D][11:59:55][COMM]index:38,charge_battery_para: FF
 
2025-07-16 21:31:53,649  [INFO] [D][11:59:55][COMM]index:39,multirider_mode:0xFF
 
2025-07-16 21:31:53,651  [INFO] [D][11:59:55][COMM]index:40,mc_launch_mode:0xFF
 
2025-07-16 21:31:53,657  [INFO] [D][11:59:55][COMM]index:41,head_light_enable_mode:0xFF
 
2025-07-16 21:31:53,662  [INFO] [D][11:59:55][COMM]index:42,set_time_ble_mode_begin_min:0xFF
 
2025-07-16 21:31:53,668  [INFO] [D][11:59:55][COMM]index:43,set_time_ble_mode_end_min:0xFF
 
2025-07-16 21:31:53,673  [INFO] [D][11:59:55][COMM]index:44,riding_duration_config:0xFF
 
2025-07-16 21:31:53,676  [INFO] [D][11:59:55][COMM]index:45,camera_park_angle_cfg:0xFF
 
2025-07-16 21:31:53,682  [INFO] [D][11:59:55][COMM]index:46,camera_park_type_cfg:0xFF
 
2025-07-16 21:31:53,687  [INFO] [D][11:59:55][COMM]index:47,bat_info_rep_cfg:0xFF
 
2025-07-16 21:31:53,690  [INFO] [D][11:59:55][COMM]index:48,shlmt_sensor_en:0xFF
 
2025-07-16 21:31:53,696  [INFO] [D][11:59:55][COMM]index:49,mc_load_startup:0xFF
 
2025-07-16 21:31:53,699  [INFO] [D][11:59:55][COMM]index:50,mc_tcs_mode:0xFF
 
2025-07-16 21:31:53,704  [INFO] [D][11:59:55][COMM]index:51,traffic_audio_play:0xFF
 
2025-07-16 21:31:53,707  [INFO] [D][11:59:55][COMM]index:52,traffic_mode:0xFF
 
2025-07-16 21:31:53,712  [INFO] [D][11:59:55][COMM]index:53,traffic_info_collect_freq:0xFF
 
2025-07-16 21:31:53,718  [INFO] [D][11:59:55][COMM]index:54,traffic_security_model_cycle:0xFF
 
2025-07-16 21:31:53,723  [INFO] [D][11:59:55][COMM]index:55,wheel_alarm_play_switch:255
 
2025-07-16 21:31:53,729  [INFO] [D][11:59:55][COMM]index:57,traffic_sens_cycle:0xFF
 
2025-07-16 21:31:53,732  [INFO] [D][11:59:55][COMM]index:58,traffic_light_threshold:0xFF
 
2025-07-16 21:31:53,737  [INFO] [D][11:59:55][COMM]index:59,traffic_retrograde_threshold:0xFF
 
2025-07-16 21:31:53,742  [INFO] [D][11:59:55][COMM]index:60,traffic_road_threshold:0xFF
 
2025-07-16 21:31:53,749  [INFO] [D][11:59:55][COMM]index:61,traffic_sens_threshold:0xFF
 
2025-07-16 21:31:53,752  [INFO] [D][11:59:55][COMM]index:63,experiment5:0xFF
 
2025-07-16 21:31:53,757  [INFO] [D][11:59:55][COMM]index:64,camera_park_markline_cfg:0xFF
 
2025-07-16 21:31:53,762  [INFO] [D][11:59:55][COMM]index:65,camera_park_fenceline_cfg:0xFF
 
2025-07-16 21:31:53,768  [INFO] [D][11:59:55][COMM]index:66,camera_park_distance_cfg:0xFF
 
2025-07-16 21:31:53,773  [INFO] [D][11:59:55][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
 
2025-07-16 21:31:53,776  [INFO] [D][11:59:55][COMM]index:68,camera_park_ps_cfg:0xFFFF
 
2025-07-16 21:31:53,782  [INFO] [D][11:59:55][COMM]index:70,camera_park_light_cfg:0xFF
 
2025-07-16 21:31:53,787  [INFO] [D][11:59:55][COMM]index:71,camera_park_self_check_cfg:0xFF
 
2025-07-16 21:31:53,793  [INFO] [D][11:59:55][COMM]index:72,experiment6:0xFF
 
2025-07-16 21:31:53,796  [INFO] [D][11:59:55][COMM]index:73,experiment7:0xFF
 
2025-07-16 21:31:53,801  [INFO] [D][11:59:55][COMM]index:74,load_messurement_cfg:0xff
 
2025-07-16 21:31:53,804  [INFO] [D][11:59:55][COMM]index:75,zero_value_from_server:-1
 
2025-07-16 21:31:53,809  [INFO] [D][11:59:55][COMM]index:76,multirider_threshold:255
 
2025-07-16 21:31:53,815  [INFO] [D][11:59:55][COMM]index:77,experiment8:255
 
2025-07-16 21:31:53,821  [INFO] [D][11:59:55][COMM]index:78,temp_park_audio_play_duration:255
 
2025-07-16 21:31:53,826  [INFO] [D][11:59:55][COMM]index:79,temp_park_tail_light_twinkle_duration:255
 
2025-07-16 21:31:53,831  [INFO] [D][11:59:55][COMM]index:80,temp_park_reminder_timeout_duration:255
 
2025-07-16 21:31:53,837  [INFO] [D][11:59:55][COMM]index:82,loc_report_low_speed_thr:255
 
2025-07-16 21:31:53,840  [INFO] [D][11:59:55][COMM]index:83,loc_report_interval:255
 
2025-07-16 21:31:53,845  [INFO] [D][11:59:55][COMM]index:84,multirider_threshold_p2:255
 
2025-07-16 21:31:53,851  [INFO] [D][11:59:55][COMM]index:85,multirider_strategy:255
 
2025-07-16 21:31:53,857  [INFO] [D][11:59:55][COMM]index:81,camera_park_similar_thr_cfg:0xFF
 
2025-07-16 21:31:53,862  [INFO] [D][11:59:55][COMM]index:86,camera_park_self_check_period_cfg:0xFF
 
2025-07-16 21:31:53,865  [INFO] [D][11:59:55][COMM]index:96,rope_sensor_cfg:0xFF
 
2025-07-16 21:31:53,871  [INFO] [D][11:59:55][COMM]index:90,weight_param:0xFF
 
2025-07-16 21:31:53,876  [INFO] [D][11:59:55][COMM]index:93,lock_anti_theft_mode:0xFF
 
2025-07-16 21:31:53,879  [INFO] [D][11:59:55][COMM]index:94,high_temp_alarm_count:0xFF
 
2025-07-16 21:31:53,885  [INFO] [D][11:59:55][COMM]index:95,current_limit:0xFF
 
2025-07-16 21:31:53,891  [INFO] [D][11:59:55][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
 
2025-07-16 21:31:53,896  [INFO] [D][11:59:55][COMM]index:104,brake_release_protect:0xFF
 
2025-07-16 21:31:53,897  [INFO] 
 
2025-07-16 21:31:53,902  [INFO] [D][11:59:55][COMM]index:100,location_mode:0xFF
 
2025-07-16 21:31:53,904  [INFO] [D][11:59:55][COMM]index:110,display_speed_limit:255
 
2025-07-16 21:31:53,910  [INFO] [D][11:59:55][COMM]index:105,vo_unload_thr:0xFFFF
 
2025-07-16 21:31:53,915  [INFO] [D][11:59:55][COMM]index:107,vo_loading_angle_thr:0xFF
 
2025-07-16 21:31:53,918  [INFO] [D][11:59:55][COMM]index:108,vo_func_switch:0xFF
 
2025-07-16 21:31:53,927  [INFO] [W][11:59:55][PROT]remove success[1730203195],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:31:53,935  [INFO] [W][11:59:55][PROT]add success [1730203195],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 21:31:53,947  [INFO] [D][11:59:55][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:31:53,949  [INFO] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:31:53,952  [INFO] [D][11:59:55][COMM]report park limit config
 
2025-07-16 21:31:53,962  [INFO] [D][11:59:55][COMM]report park limit config:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:31:53,967  [INFO] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:31:53,969  [INFO] [D][11:59:55][COMM]
 
2025-07-16 21:31:53,977  [INFO] [W][11:59:55][PROT]remove success[1730203195],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:31:53,986  [INFO] [W][11:59:55][PROT]add success [1730203195],send_path[2],type[4701],priority[0],index[1],used[1]
 
2025-07-16 21:31:53,993  [INFO] [W][11:59:55][PROT]remove success[1730203195],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:31:54,003  [INFO] [W][11:59:55][PROT]add success [1730203195],send_path[2],type[4705],priority[0],index[2],used[1]
 
2025-07-16 21:31:54,008  [INFO] [D][11:59:55][COMM]bledev scan is invalid, will return
 
2025-07-16 21:31:54,017  [INFO] [D][11:59:55][COMM]------>period, report file manifest, waiting for Verify or count 1 less
 
2025-07-16 21:31:54,021  [INFO] [D][11:59:55][COMM]Main Task receive event:7 finished processing
 
2025-07-16 21:31:57,493  [INFO] [D][11:59:59][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:31:57,494  [INFO] 
 
2025-07-16 21:32:04,004  [INFO] [E][12:00:06][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:04,007  [INFO] [D][12:00:06][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:04,011  [INFO] [D][12:00:06][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:05,510  [INFO] [D][12:00:07][CAT1]exec over: func id: 10, ret: -43
 
2025-07-16 21:32:05,512  [INFO] [D][12:00:07][CAT1]sub id: 10, ret: -43
 
2025-07-16 21:32:05,513  [INFO] 
 
2025-07-16 21:32:05,518  [INFO] [D][12:00:07][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:32:05,524  [INFO] [D][12:00:07][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
 
2025-07-16 21:32:05,529  [INFO] [D][12:00:07][M2M ]m2m gsm shut done, ret[1]
 
2025-07-16 21:32:05,532  [INFO] [D][12:00:07][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:32:05,537  [INFO] [D][12:00:07][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:05,537  [INFO] 
 
2025-07-16 21:32:05,542  [INFO] [D][12:00:07][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
 
2025-07-16 21:32:05,546  [INFO] [D][12:00:07][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET_ACK
 
2025-07-16 21:32:05,622  [INFO] [D][12:00:08][COMM]Main Task receive event:98
 
2025-07-16 21:32:05,624  [INFO] [D][12:00:08][COMM]Err Event 2 Cnt
 
2025-07-16 21:32:05,630  [INFO] [D][12:00:08][COMM]Main Task receive event:98 finished processing
 
2025-07-16 21:32:07,539  [INFO] [D][12:00:09][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:07,540  [INFO] 
 
2025-07-16 21:32:09,554  [INFO] [D][12:00:11][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:09,555  [INFO] 
 
2025-07-16 21:32:11,570  [INFO] [D][12:00:13][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:11,570  [INFO] 
 
2025-07-16 21:32:13,590  [INFO] [D][12:00:15][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:13,591  [INFO] 
 
2025-07-16 21:32:14,041  [INFO] [E][12:00:16][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:14,044  [INFO] [D][12:00:16][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:14,049  [INFO] [D][12:00:16][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:14,052  [INFO] [D][12:00:16][GNSS]handler GSMGet Base timeout
 
2025-07-16 21:32:15,599  [INFO] [D][12:00:18][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:32:15,602  [INFO] [D][12:00:18][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:32:15,604  [INFO] 
 
2025-07-16 21:32:15,610  [INFO] [D][12:00:18][CAT1]gsm read msg sub id: 4
 
2025-07-16 21:32:15,612  [INFO] [W][12:00:18][CAT1]gsm_module_reboot
 
2025-07-16 21:32:15,613  [INFO] 
 
2025-07-16 21:32:16,048  [INFO] [D][12:00:18][GNSS]recv submsg id[1]
 
2025-07-16 21:32:16,051  [INFO] [D][12:00:18][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:32:16,055  [INFO] [D][12:00:18][GNSS]start gps fail
 
2025-07-16 21:32:23,970  [INFO] [D][12:00:26][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:23,971  [INFO] 
 
2025-07-16 21:32:24,079  [INFO] [E][12:00:26][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:24,081  [INFO] [D][12:00:26][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:24,085  [INFO] [D][12:00:26][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:24,982  [INFO] [D][12:00:27][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:24,983  [INFO] 
 
2025-07-16 21:32:25,999  [INFO] [D][12:00:28][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:26,000  [INFO] 
 
2025-07-16 21:32:27,015  [INFO] [D][12:00:29][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:27,016  [INFO] 
 
2025-07-16 21:32:28,033  [INFO] [D][12:00:30][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:28,034  [INFO] 
 
2025-07-16 21:32:29,046  [INFO] [D][12:00:31][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:29,047  [INFO] 
 
2025-07-16 21:32:30,067  [INFO] [D][12:00:32][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:30,068  [INFO] 
 
2025-07-16 21:32:31,087  [INFO] [D][12:00:33][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:31,088  [INFO] 
 
2025-07-16 21:32:32,106  [INFO] [D][12:00:34][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:32,107  [INFO] 
 
2025-07-16 21:32:33,121  [INFO] [D][12:00:35][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:33,122  [INFO] 
 
2025-07-16 21:32:34,116  [INFO] [E][12:00:36][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:34,118  [INFO] [D][12:00:36][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:34,122  [INFO] [D][12:00:36][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:34,135  [INFO] [D][12:00:36][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:34,136  [INFO] 
 
2025-07-16 21:32:35,149  [INFO] [D][12:00:37][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:35,150  [INFO] 
 
2025-07-16 21:32:36,164  [INFO] [D][12:00:38][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:36,165  [INFO] 
 
2025-07-16 21:32:37,180  [INFO] [D][12:00:39][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:37,181  [INFO] 
 
2025-07-16 21:32:38,197  [INFO] [D][12:00:40][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:38,197  [INFO] 
 
2025-07-16 21:32:39,209  [INFO] [D][12:00:41][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:39,210  [INFO] 
 
2025-07-16 21:32:40,224  [INFO] [D][12:00:42][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:40,225  [INFO] 
 
2025-07-16 21:32:41,238  [INFO] [D][12:00:43][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:41,240  [INFO] 
 
2025-07-16 21:32:42,253  [INFO] [D][12:00:44][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:42,254  [INFO] 
 
2025-07-16 21:32:42,436  [INFO] [D][12:00:44][COMM]Main Task receive event:21
 
2025-07-16 21:32:42,526  [INFO] [D][12:00:44][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:32:42,531  [INFO] [D][12:00:44][HSDK]hexlog index save 0 25088 39 @ 1536 : 0
 
2025-07-16 21:32:42,534  [INFO] [D][12:00:44][HSDK]write save hexlog index [0]
 
2025-07-16 21:32:43,143  [INFO] [W][12:00:45][COMM]Power Off
 
2025-07-16 21:32:43,150  [INFO] [D][12:00:45][COMM]Ready to power off, please pull out the cable
 
2025-07-16 21:32:43,156  [INFO] [D][12:00:45][COMM]Main Task receive event:21 finished processing
 
2025-07-16 21:32:43,273  [INFO] [D][12:00:45][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:43,274  [INFO] 
 
2025-07-16 21:32:44,162  [INFO] [E][12:00:46][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:44,164  [INFO] [D][12:00:46][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:44,169  [INFO] [D][12:00:46][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:44,290  [INFO] [D][12:00:46][CAT1]exec over: func id: 4, ret: -32
 
2025-07-16 21:32:44,292  [INFO] [D][12:00:46][CAT1]sub id: 4, ret: -32
 
2025-07-16 21:32:44,293  [INFO] 
 
2025-07-16 21:32:44,298  [INFO] [D][12:00:46][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:32:44,303  [INFO] [D][12:00:46][SAL ]handle subcmd ack sub_id[4], socket[0], result[-32]
 
2025-07-16 21:32:44,309  [INFO] [D][12:00:46][M2M ]m2m gsm reset done, ret[255]
 
2025-07-16 21:32:44,312  [INFO] [D][12:00:46][M2M ]reset OK, ret err
 
2025-07-16 21:32:44,314  [INFO] [D][12:00:46][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:32:44,319  [INFO] [D][12:00:46][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:32:44,320  [INFO] 
 
2025-07-16 21:32:44,322  [INFO] [D][12:00:46][M2M ]m2m switch to: M2M_GSM_PWR_ON
 
2025-07-16 21:32:44,328  [INFO] [D][12:00:46][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
 
2025-07-16 21:32:49,320  [INFO] [D][12:00:51][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:32:49,321  [INFO] 
 
2025-07-16 21:32:54,216  [INFO] [E][12:00:56][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:54,219  [INFO] [D][12:00:56][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:54,224  [INFO] [D][12:00:56][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:54,228  [INFO] [D][12:00:56][GNSS]frm_wifi_scan_callback: scan timeout
 
2025-07-16 21:32:54,330  [INFO] [D][12:00:56][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:32:54,333  [INFO] [D][12:00:56][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:32:54,334  [INFO] 
 
2025-07-16 21:32:54,341  [INFO] [D][12:00:56][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:32:54,345  [INFO] [D][12:00:56][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:54,346  [INFO] 
 
2025-07-16 21:32:55,220  [INFO] [D][12:00:57][GNSS]recv submsg id[1]
 
2025-07-16 21:32:55,224  [INFO] [D][12:00:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:32:55,227  [INFO] [D][12:00:57][GNSS]stop gps fail
 
2025-07-16 21:32:56,359  [INFO] [D][12:00:58][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:56,360  [INFO] 
 
2025-07-16 21:32:58,377  [INFO] [D][12:01:00][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:58,378  [INFO] 
 
2025-07-16 21:33:00,388  [INFO] [D][12:01:02][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:00,389  [INFO] 
 
2025-07-16 21:33:02,408  [INFO] [D][12:01:04][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:02,410  [INFO] 
 
2025-07-16 21:33:04,267  [INFO] [E][12:01:06][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:04,270  [INFO] [D][12:01:06][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:04,275  [INFO] [D][12:01:06][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:04,428  [INFO] [D][12:01:06][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:33:04,431  [INFO] [D][12:01:06][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:33:04,431  [INFO] 
 
2025-07-16 21:33:04,439  [INFO] [D][12:01:06][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:33:04,442  [INFO] [D][12:01:06][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:04,443  [INFO] 
 
2025-07-16 21:33:05,273  [INFO] [D][12:01:07][GNSS]recv submsg id[1]
 
2025-07-16 21:33:05,276  [INFO] [D][12:01:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:33:05,279  [INFO] [D][12:01:07][GNSS]start gps fail
 
2025-07-16 21:33:09,455  [INFO] [D][12:01:11][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:09,456  [INFO] 
 
2025-07-16 21:33:14,318  [INFO] [E][12:01:16][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:14,320  [INFO] [D][12:01:16][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:14,324  [INFO] [D][12:01:16][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:14,466  [INFO] [D][12:01:16][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:33:14,470  [INFO] [D][12:01:16][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:33:14,471  [INFO] 
 
2025-07-16 21:33:14,477  [INFO] [D][12:01:16][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:33:14,482  [INFO] [D][12:01:16][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:14,483  [INFO] 
 
2025-07-16 21:33:15,320  [INFO] [D][12:01:17][GNSS]recv submsg id[1]
 
2025-07-16 21:33:15,324  [INFO] [D][12:01:17][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:33:15,327  [INFO] [D][12:01:17][GNSS]stop gps fail
 
2025-07-16 21:33:16,497  [INFO] [D][12:01:18][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:16,498  [INFO] 
 
2025-07-16 21:33:18,516  [INFO] [D][12:01:20][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:18,517  [INFO] 
 
2025-07-16 21:33:20,534  [INFO] [D][12:01:22][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:20,535  [INFO] 
 
2025-07-16 21:33:22,553  [INFO] [D][12:01:24][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:22,554  [INFO] 
 
2025-07-16 21:33:24,356  [INFO] [E][12:01:26][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:24,358  [INFO] [D][12:01:26][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:24,363  [INFO] [D][12:01:26][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:24,568  [INFO] [D][12:01:26][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:33:24,571  [INFO] [D][12:01:26][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:33:24,572  [INFO] 
 
2025-07-16 21:33:24,580  [INFO] [D][12:01:26][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:33:24,583  [INFO] [D][12:01:26][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:24,583  [INFO] 
 
2025-07-16 21:33:25,358  [INFO] [D][12:01:27][GNSS]recv submsg id[1]
 
2025-07-16 21:33:25,362  [INFO] [D][12:01:27][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:33:25,365  [INFO] [D][12:01:27][GNSS]start gps fail
 
2025-07-16 21:33:29,601  [INFO] [D][12:01:32][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:29,602  [INFO] 
 
2025-07-16 21:33:34,393  [INFO] [E][12:01:36][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:34,397  [INFO] [D][12:01:36][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:34,401  [INFO] [D][12:01:36][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:34,618  [INFO] [D][12:01:37][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:33:34,620  [INFO] [D][12:01:37][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:33:34,622  [INFO] 
 
2025-07-16 21:33:34,629  [INFO] [D][12:01:37][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:33:34,633  [INFO] [D][12:01:37][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:34,634  [INFO] 
 
2025-07-16 21:33:35,397  [INFO] [D][12:01:37][GNSS]recv submsg id[1]
 
2025-07-16 21:33:35,400  [INFO] [D][12:01:37][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:33:35,403  [INFO] [D][12:01:37][GNSS]stop gps fail
 
2025-07-16 21:33:36,649  [INFO] [D][12:01:39][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:36,649  [INFO] 
 
2025-07-16 21:33:38,665  [INFO] [D][12:01:41][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:38,668  [INFO] 
 
2025-07-16 21:33:40,684  [INFO] [D][12:01:43][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:40,685  [INFO] 
 
2025-07-16 21:33:42,696  [INFO] [D][12:01:45][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:42,697  [INFO] 
 
2025-07-16 21:33:44,430  [INFO] [E][12:01:46][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:44,432  [INFO] [D][12:01:46][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:44,437  [INFO] [D][12:01:46][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:44,706  [INFO] [D][12:01:47][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:33:44,709  [INFO] [D][12:01:47][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:33:44,710  [INFO] 
 
2025-07-16 21:33:44,719  [INFO] [D][12:01:47][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:33:44,722  [INFO] [D][12:01:47][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:44,723  [INFO] 
 
2025-07-16 21:33:45,432  [INFO] [D][12:01:47][GNSS]recv submsg id[1]
 
2025-07-16 21:33:45,436  [INFO] [D][12:01:47][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:33:45,440  [INFO] [D][12:01:47][GNSS]start gps fail
 
2025-07-16 21:33:49,736  [INFO] [D][12:01:52][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:49,738  [INFO] 
 
2025-07-16 21:33:54,468  [INFO] [E][12:01:56][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:54,471  [INFO] [D][12:01:56][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:54,475  [INFO] [D][12:01:56][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:54,756  [INFO] [D][12:01:57][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:33:54,758  [INFO] [D][12:01:57][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:33:54,760  [INFO] 
 
2025-07-16 21:33:54,767  [INFO] [D][12:01:57][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:33:54,772  [INFO] [D][12:01:57][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:54,773  [INFO] 
 
2025-07-16 21:33:55,471  [INFO] [D][12:01:57][GNSS]recv submsg id[1]
 
2025-07-16 21:33:55,474  [INFO] [D][12:01:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:33:55,477  [INFO] [D][12:01:57][GNSS]stop gps fail
 
2025-07-16 21:33:56,791  [INFO] [D][12:01:59][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:56,791  [INFO] 
 
2025-07-16 21:33:58,804  [INFO] [D][12:02:01][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:58,804  [INFO] 
 
2025-07-16 21:34:00,823  [INFO] [D][12:02:03][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:34:00,823  [INFO] 
 
2025-07-16 21:34:02,839  [INFO] [D][12:02:05][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:34:02,840  [INFO] 
 
2025-07-16 21:34:04,508  [INFO] [E][12:02:06][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:04,510  [INFO] [D][12:02:06][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:04,515  [INFO] [D][12:02:06][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:04,850  [INFO] [D][12:02:07][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:34:04,852  [INFO] [D][12:02:07][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:34:04,853  [INFO] 
 
2025-07-16 21:34:04,860  [INFO] [D][12:02:07][CAT1]gsm read msg sub id: 1
 
2025-07-16 21:34:04,862  [INFO] [D][12:02:07][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:04,863  [INFO] 
 
2025-07-16 21:34:05,510  [INFO] [D][12:02:07][GNSS]recv submsg id[1]
 
2025-07-16 21:34:05,514  [INFO] [D][12:02:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:34:05,518  [INFO] [D][12:02:07][GNSS]start gps fail
 
2025-07-16 21:34:06,742  [INFO] [D][12:02:09][COMM]appComBuriedPack:module=9,type=2,localtime=1730203329, cnt=4
 
2025-07-16 21:34:06,784  [INFO] [D][12:02:09][COMM]Main Task receive event:102
 
2025-07-16 21:34:06,789  [INFO] [D][12:02:09][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:34:14,550  [INFO] [E][12:02:16][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:14,552  [INFO] [D][12:02:16][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:14,556  [INFO] [D][12:02:16][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:14,741  [INFO] [D][12:02:17][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:14,742  [INFO] 
 
2025-07-16 21:34:15,757  [INFO] [D][12:02:18][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:15,758  [INFO] 
 
2025-07-16 21:34:16,770  [INFO] [D][12:02:19][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:16,771  [INFO] 
 
2025-07-16 21:34:17,782  [INFO] [D][12:02:20][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:17,783  [INFO] 
 
2025-07-16 21:34:18,800  [INFO] [D][12:02:21][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:18,801  [INFO] 
 
2025-07-16 21:34:19,815  [INFO] [D][12:02:22][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:19,815  [INFO] 
 
2025-07-16 21:34:20,830  [INFO] [D][12:02:23][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:20,831  [INFO] 
 
2025-07-16 21:34:21,845  [INFO] [D][12:02:24][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:21,845  [INFO] 
 
2025-07-16 21:34:22,860  [INFO] [D][12:02:25][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:22,862  [INFO] 
 
2025-07-16 21:34:23,875  [INFO] [D][12:02:26][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:23,876  [INFO] 
 
2025-07-16 21:34:24,593  [INFO] [E][12:02:27][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:24,595  [INFO] [D][12:02:27][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:24,599  [INFO] [D][12:02:27][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:24,891  [INFO] [D][12:02:27][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:24,892  [INFO] 
 
2025-07-16 21:34:25,902  [INFO] [D][12:02:28][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:25,903  [INFO] 
 
2025-07-16 21:34:26,920  [INFO] [D][12:02:29][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:26,921  [INFO] 
 
2025-07-16 21:34:27,932  [INFO] [D][12:02:30][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:27,933  [INFO] 
 
2025-07-16 21:34:28,950  [INFO] [D][12:02:31][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:28,951  [INFO] 
 
2025-07-16 21:34:29,966  [INFO] [D][12:02:32][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:29,966  [INFO] 
 
2025-07-16 21:34:30,979  [INFO] [D][12:02:33][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:30,980  [INFO] 
 
2025-07-16 21:34:31,995  [INFO] [D][12:02:34][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:31,995  [INFO] 
 
2025-07-16 21:34:33,008  [INFO] [D][12:02:35][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:33,008  [INFO] 
 
2025-07-16 21:34:34,025  [INFO] [D][12:02:36][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:34,026  [INFO] 
 
2025-07-16 21:34:34,633  [INFO] [E][12:02:37][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:34,636  [INFO] [D][12:02:37][GNSS]GPS reload stop. ret=-3
 
2025-07-16 21:34:34,640  [INFO] [D][12:02:37][GNSS]GPS reload start. ret=-3
 
2025-07-16 21:34:35,043  [INFO] [D][12:02:37][CAT1]exec over: func id: 1, ret: -14
 
2025-07-16 21:34:35,045  [INFO] [D][12:02:37][CAT1]sub id: 1, ret: -14
 
2025-07-16 21:34:35,046  [INFO] 
 
2025-07-16 21:34:35,051  [INFO] [D][12:02:37][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:34:35,057  [INFO] [D][12:02:37][SAL ]handle subcmd ack sub_id[1], socket[0], result[-14]
 
2025-07-16 21:34:35,062  [INFO] [D][12:02:37][SAL ]gsm power on ind rst[-14]
 
2025-07-16 21:34:35,065  [INFO] [D][12:02:37][M2M ]m2m gsm power on, ret[255]
 
2025-07-16 21:34:35,067  [INFO] [E][12:02:37][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 21:34:35,074  [INFO] [D][12:02:37][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:34:35,076  [INFO] [D][12:02:37][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:34:35,076  [INFO] 
 
2025-07-16 21:34:35,081  [INFO] [D][12:02:37][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
 
2025-07-16 21:34:35,087  [INFO] [D][12:02:37][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET_ACK
 
2025-07-16 21:34:35,093  [INFO] [D][12:02:37][COMM]Main Task receive event:98
 
2025-07-16 21:34:35,097  [INFO] [D][12:02:37][COMM]Err Event 2 Cnt
 
2025-07-16 21:34:35,102  [INFO] [D][12:02:37][COMM]Main Task receive event:98 finished processing
 
2025-07-16 21:34:40,071  [INFO] [D][12:02:42][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:34:40,072  [INFO] 
 
2025-07-16 21:34:44,672  [INFO] [E][12:02:47][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:44,675  [INFO] [D][12:02:47][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:44,679  [INFO] [D][12:02:47][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:45,089  [INFO] [D][12:02:47][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:34:45,091  [INFO] [D][12:02:47][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:34:45,092  [INFO] 
 
2025-07-16 21:34:45,100  [INFO] [D][12:02:47][CAT1]gsm read msg sub id: 4
 
2025-07-16 21:34:45,102  [INFO] [W][12:02:47][CAT1]gsm_module_reboot
 
2025-07-16 21:34:45,103  [INFO] 
 
2025-07-16 21:34:45,676  [INFO] [D][12:02:48][GNSS]recv submsg id[1]
 
2025-07-16 21:34:45,679  [INFO] [D][12:02:48][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:34:45,682  [INFO] [D][12:02:48][GNSS]stop gps fail
 
2025-07-16 21:34:53,471  [INFO] [D][12:02:55][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:53,472  [INFO] 
 
2025-07-16 21:34:53,706  [INFO] [D][12:02:56][GNSS]location_callback:event=3
 
2025-07-16 21:34:54,367  [INFO] [D][12:02:56][COMM]Main Task receive event:14
 
2025-07-16 21:34:54,373  [INFO] [D][12:02:56][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:34:54,375  [INFO] [D][12:02:56][GNSS]stop event:3
 
2025-07-16 21:34:54,378  [INFO] [D][12:02:56][GNSS]GPS stop. ret=0
 
2025-07-16 21:34:54,381  [INFO] [D][12:02:56][COMM]Period location failed
 
2025-07-16 21:34:54,383  [INFO] [D][12:02:56][COMM]getLocatInfoStep2:1
 
2025-07-16 21:34:54,384  [INFO] 
 
2025-07-16 21:34:54,396  [INFO] [D][12:02:56][COMM]f:frm_park_cam_get_info. done reason:[1],type:[0],result:[0x04],err:[0xFF],angle:[0xFFFF],dist:[255]!!!
 
2025-07-16 21:34:54,401  [INFO] [D][12:02:56][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:34:54,403  [INFO] [D][12:02:56][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:34:54,407  [INFO] [W][12:02:56][COMM]get bat state1 error
 
2025-07-16 21:34:54,409  [INFO] [D][12:02:56][GNSS]5F01 soc:255
 
2025-07-16 21:34:54,415  [INFO] [W][12:02:56][COMM]get mc state information fail
 
2025-07-16 21:34:54,420  [INFO] [W][12:02:56][COMM]get mc speed information fail
 
2025-07-16 21:34:54,423  [INFO] [W][12:02:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 21:34:54,428  [INFO] [D][12:02:56][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:34:54,431  [INFO] [D][12:02:56][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:34:54,434  [INFO] [D][12:02:56][GNSS]nSatsInUse 0
 
2025-07-16 21:34:54,447  [INFO] [D][12:02:56][COMM]realtime_info.pitch_angle=127,realtime_info.roll_angle=32767,realtime_info.nav_angle=32763
 
2025-07-16 21:34:54,458  [INFO] [W][12:02:56][COMM]5F04 LocFail:reason:0x01;diff:43376;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:34:54,462  [INFO] [W][12:02:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:34:54,468  [INFO] [W][12:02:56][COMM]get mc power mode information fail
 
2025-07-16 21:34:54,469  [INFO] [D][12:02:56][COMM]can to 485 :254
 
2025-07-16 21:34:54,470  [INFO] 
 
2025-07-16 21:34:54,478  [INFO] [W][12:02:56][PROT]remove success[1730203376],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:34:54,487  [INFO] [W][12:02:56][PROT]add success [1730203376],send_path[2],type[5F04],priority[0],index[3],used[1]
 
2025-07-16 21:34:54,492  [INFO] [D][12:02:56][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
 
2025-07-16 21:34:54,499  [INFO] [D][12:02:56][M2M ]m2m_task: gpc:[0],gpo:[0]
 
2025-07-16 21:34:54,501  [INFO] [D][12:02:56][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:54,502  [INFO] 
 
2025-07-16 21:34:55,500  [INFO] [D][12:02:57][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:55,501  [INFO] 
 
2025-07-16 21:34:56,424  [INFO] [D][12:02:58][M2M ]get csq[7]
 
2025-07-16 21:34:56,516  [INFO] [D][12:02:58][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:56,517  [INFO] 
 
2025-07-16 21:34:56,678  [INFO] [D][12:02:59][COMM]lastHelmetOrderIdV2:00
 
2025-07-16 21:34:56,679  [INFO] >>>>>RESEND ALLSTATE<<<<<
 
2025-07-16 21:34:56,687  [INFO] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:34:56,695  [INFO] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 21:34:56,698  [INFO] [D][12:02:59][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:34:56,703  [INFO] [D][12:02:59][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:34:56,706  [INFO] [W][12:02:59][COMM]get soc error
 
2025-07-16 21:34:56,709  [INFO] [W][12:02:59][GNSS]stop locating
 
2025-07-16 21:34:56,712  [INFO] [D][12:02:59][GNSS]all continue location stop
 
2025-07-16 21:34:56,717  [INFO] [W][12:02:59][GNSS]sing locating running
 
2025-07-16 21:34:56,724  [INFO] [E][12:02:59][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:34:56,727  [INFO] [D][12:02:59][COMM]report elecbike, soc 0, reason 2
 
2025-07-16 21:34:56,737  [INFO] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:34:56,745  [INFO] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 21:34:56,747  [INFO] [D][12:02:59][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:34:56,754  [INFO] [D][12:02:59][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:34:56,762  [INFO] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:34:56,771  [INFO] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 21:34:56,780  [INFO] [D][12:02:59][COMM]buried data a:10,b:2,c:255,d:0,f:16843008,k:4770,l:404,m:11,n:13,o:13,p:1052,q:1972,r:4812,z:665
 
2025-07-16 21:34:56,790  [INFO] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 21:34:56,798  [INFO] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 21:34:56,801  [INFO] [D][12:02:59][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:34:56,807  [INFO] [D][12:02:59][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:34:56,810  [INFO] [D][12:02:59][GNSS]nSatsInUse 0
 
2025-07-16 21:34:56,823  [INFO] [W][12:02:59][COMM]5A07 LocFail:reason:0x01;diff:43379;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:34:56,826  [INFO] [W][12:02:59][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:34:56,834  [INFO] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 21:34:56,844  [INFO] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[5A07],priority[0],index[8],used[1]
 
2025-07-16 21:34:56,850  [INFO] [D][12:02:59][COMM]Main Task receive event:14 finished processing
 
2025-07-16 21:34:56,852  [INFO] [D][12:02:59][COMM]Main Task receive event:54
 
2025-07-16 21:34:56,858  [INFO] [D][12:02:59][COMM][D301]:type:1, trace id:0
 
2025-07-16 21:34:56,860  [INFO] [D][12:02:59][COMM]get bat basic info err
 
2025-07-16 21:34:56,868  [INFO] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-16 21:34:56,876  [INFO] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[D302],priority[0],index[9],used[1]
 
2025-07-16 21:34:56,883  [INFO] [D][12:02:59][COMM]Main Task receive event:54 finished processing
 
2025-07-16 21:34:57,529  [INFO] [D][12:02:59][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:57,531  [INFO] 
 
2025-07-16 21:34:58,546  [INFO] [D][12:03:00][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:58,547  [INFO] 
 
2025-07-16 21:34:59,559  [INFO] [D][12:03:01][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:59,559  [INFO] 
 
2025-07-16 21:35:00,575  [INFO] [D][12:03:02][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:00,576  [INFO] 
 
2025-07-16 21:35:01,589  [INFO] [D][12:03:04][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:01,589  [INFO] 
 
2025-07-16 21:35:02,604  [INFO] [D][12:03:05][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:02,605  [INFO] 
 
2025-07-16 21:35:03,620  [INFO] [D][12:03:06][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:03,621  [INFO] 
 
2025-07-16 21:35:04,633  [INFO] [D][12:03:07][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:04,634  [INFO] 
 
2025-07-16 21:35:05,648  [INFO] [D][12:03:08][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:05,649  [INFO] 
 
2025-07-16 21:35:06,664  [INFO] [D][12:03:09][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:06,665  [INFO] 
 
2025-07-16 21:35:07,678  [INFO] [D][12:03:10][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:07,679  [INFO] 
 
2025-07-16 21:35:08,488  [INFO] [D][12:03:10][COMM]appComBuriedPack:module=9,type=2,localtime=1730203390, cnt=5
 
2025-07-16 21:35:08,520  [INFO] [D][12:03:10][COMM]Main Task receive event:102
 
2025-07-16 21:35:08,528  [INFO] [D][12:03:10][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:35:08,693  [INFO] [D][12:03:11][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:08,694  [INFO] 
 
2025-07-16 21:35:09,707  [INFO] [D][12:03:12][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:09,708  [INFO] 
 
2025-07-16 21:35:10,724  [INFO] [D][12:03:13][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:10,724  [INFO] 
 
2025-07-16 21:35:11,738  [INFO] [D][12:03:14][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:11,739  [INFO] 
 
2025-07-16 21:35:12,754  [INFO] [D][12:03:15][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:12,754  [INFO] 
 
2025-07-16 21:35:13,765  [INFO] [D][12:03:16][CAT1]exec over: func id: 4, ret: -32
 
2025-07-16 21:35:13,768  [INFO] [D][12:03:16][CAT1]sub id: 4, ret: -32
 
2025-07-16 21:35:13,769  [INFO] 
 
2025-07-16 21:35:13,773  [INFO] [D][12:03:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:35:13,779  [INFO] [D][12:03:16][SAL ]handle subcmd ack sub_id[4], socket[0], result[-32]
 
2025-07-16 21:35:13,784  [INFO] [D][12:03:16][M2M ]m2m gsm reset done, ret[255]
 
2025-07-16 21:35:13,787  [INFO] [D][12:03:16][M2M ]reset OK, ret err
 
2025-07-16 21:35:13,790  [INFO] [D][12:03:16][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:35:13,795  [INFO] [D][12:03:16][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:13,796  [INFO] 
 
2025-07-16 21:35:13,798  [INFO] [D][12:03:16][M2M ]m2m switch to: M2M_GSM_PWR_ON
 
2025-07-16 21:35:13,803  [INFO] [D][12:03:16][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
 
2025-07-16 21:35:15,447  [INFO] [D][12:03:17][COMM]Main Task receive event:21
 
2025-07-16 21:35:15,537  [INFO] [D][12:03:17][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:35:15,544  [INFO] [D][12:03:17][HSDK]hexlog index save 0 25088 39 @ 1536 : 0
 
2025-07-16 21:35:15,546  [INFO] [D][12:03:17][HSDK]write save hexlog index [0]
 
2025-07-16 21:35:16,156  [INFO] [W][12:03:18][COMM]Power Off
 
2025-07-16 21:35:16,162  [INFO] [D][12:03:18][COMM]Ready to power off, please pull out the cable
 
2025-07-16 21:35:16,169  [INFO] [D][12:03:18][COMM]Main Task receive event:21 finished processing
 
2025-07-16 21:35:18,796  [INFO] [D][12:03:21][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:18,796  [INFO] 
 
2025-07-16 21:35:23,815  [INFO] [D][12:03:26][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:35:23,817  [INFO] [D][12:03:26][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:35:23,818  [INFO] 
 
2025-07-16 21:35:23,826  [INFO] [D][12:03:26][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:35:23,829  [INFO] [D][12:03:26][GNSS]recv submsg id[1]
 
2025-07-16 21:35:23,835  [INFO] [D][12:03:26][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:35:23,840  [INFO] [D][12:03:26][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:23,841  [INFO] 
 
2025-07-16 21:35:23,842  [INFO] [D][12:03:26][GNSS]stop gps fail
 
2025-07-16 21:35:25,846  [INFO] [D][12:03:28][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:25,847  [INFO] 
 
2025-07-16 21:35:27,866  [INFO] [D][12:03:30][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:27,866  [INFO] 
 
2025-07-16 21:35:29,882  [INFO] [D][12:03:32][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:29,883  [INFO] 
 
2025-07-16 21:35:31,897  [INFO] [D][12:03:34][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:31,897  [INFO] 
 
2025-07-16 21:35:33,907  [INFO] [D][12:03:36][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:35:33,910  [INFO] [D][12:03:36][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:35:33,911  [INFO] 
 
2025-07-16 21:35:33,919  [INFO] [D][12:03:36][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:35:33,922  [INFO] [D][12:03:36][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:33,923  [INFO] 
 
2025-07-16 21:35:34,868  [INFO] [D][12:03:37][GNSS]recv submsg id[1]
 
2025-07-16 21:35:34,871  [INFO] [D][12:03:37][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:35:34,874  [INFO] [D][12:03:37][GNSS]start gps fail
 
2025-07-16 21:35:38,937  [INFO] [D][12:03:41][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:38,938  [INFO] 
 
2025-07-16 21:35:43,955  [INFO] [D][12:03:46][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:35:43,957  [INFO] [D][12:03:46][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:35:43,960  [INFO] 
 
2025-07-16 21:35:43,966  [INFO] [D][12:03:46][CAT1]gsm read msg sub id: 26
 
2025-07-16 21:35:43,969  [INFO] [D][12:03:46][CAT1]tx ret[18] >>> AT+WIFISCAN=8,45
 
2025-07-16 21:35:43,970  [INFO] 
 
2025-07-16 21:35:44,904  [INFO] [D][12:03:47][GNSS]recv submsg id[1]
 
2025-07-16 21:35:44,908  [INFO] [D][12:03:47][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:35:44,911  [INFO] [D][12:03:47][GNSS]stop gps fail
 
2025-07-16 21:35:44,979  [INFO] [D][12:03:47][CAT1]exec over: func id: 26, ret: -231
 
2025-07-16 21:35:44,990  [INFO] [D][12:03:47][CAT1]gsm read msg sub id: 13
 
2025-07-16 21:35:44,992  [INFO] [D][12:03:47][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:44,993  [INFO] 
 
2025-07-16 21:35:45,502  [INFO] [D][12:03:47][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:45,503  [INFO] 
 
2025-07-16 21:35:46,015  [INFO] [D][12:03:48][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:46,017  [INFO] 
 
2025-07-16 21:35:46,527  [INFO] [D][12:03:48][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:46,528  [INFO] 
 
2025-07-16 21:35:47,051  [INFO] [D][12:03:49][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:47,053  [INFO] 
 
2025-07-16 21:35:47,562  [INFO] [D][12:03:49][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:47,562  [INFO] 
 
2025-07-16 21:35:48,076  [INFO] [D][12:03:50][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:48,077  [INFO] 
 
2025-07-16 21:35:48,588  [INFO] [D][12:03:51][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:48,588  [INFO] 
 
2025-07-16 21:35:49,100  [INFO] [D][12:03:51][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:49,101  [INFO] 
 
2025-07-16 21:35:49,612  [INFO] [D][12:03:52][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:49,613  [INFO] 
 
2025-07-16 21:35:49,924  [INFO] [D][12:03:52][GNSS]frm_wifi_scan_callback: scan timeout
 
2025-07-16 21:35:50,125  [INFO] [D][12:03:52][CAT1]exec over: func id: 13, ret: -131
 
2025-07-16 21:35:50,136  [INFO] [D][12:03:52][CAT1]gsm read msg sub id: 1
 
2025-07-16 21:35:50,138  [INFO] [D][12:03:52][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:50,139  [INFO] 
 
2025-07-16 21:36:00,012  [INFO] [D][12:04:02][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:00,014  [INFO] 
 
2025-07-16 21:36:01,026  [INFO] [D][12:04:03][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:01,027  [INFO] 
 
2025-07-16 21:36:02,044  [INFO] [D][12:04:04][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:02,045  [INFO] 
 
2025-07-16 21:36:03,066  [INFO] [D][12:04:05][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:03,067  [INFO] 
 
2025-07-16 21:36:04,078  [INFO] [D][12:04:06][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:04,079  [INFO] 
 
2025-07-16 21:36:05,095  [INFO] [D][12:04:07][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:05,096  [INFO] 
 
2025-07-16 21:36:06,110  [INFO] [D][12:04:08][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:06,111  [INFO] 
 
2025-07-16 21:36:07,125  [INFO] [D][12:04:09][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:07,125  [INFO] 
 
2025-07-16 21:36:08,140  [INFO] [D][12:04:10][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:08,141  [INFO] 
 
2025-07-16 21:36:08,936  [INFO] [D][12:04:11][COMM]appComBuriedPack:module=9,type=2,localtime=1730203451, cnt=6
 
2025-07-16 21:36:08,964  [INFO] [D][12:04:11][COMM]Main Task receive event:102
 
2025-07-16 21:36:08,970  [INFO] [D][12:04:11][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:36:09,154  [INFO] [D][12:04:11][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:09,155  [INFO] 
 
2025-07-16 21:36:10,168  [INFO] [D][12:04:12][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:10,169  [INFO] 
 
2025-07-16 21:36:11,184  [INFO] [D][12:04:13][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:11,185  [INFO] 
 
2025-07-16 21:36:12,198  [INFO] [D][12:04:14][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:12,199  [INFO] 
 
2025-07-16 21:36:13,212  [INFO] [D][12:04:15][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:13,213  [INFO] 
 
2025-07-16 21:36:14,229  [INFO] [D][12:04:16][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:14,230  [INFO] 
 
2025-07-16 21:36:15,241  [INFO] [D][12:04:17][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:15,242  [INFO] 
 
2025-07-16 21:36:16,256  [INFO] [D][12:04:18][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:16,258  [INFO] 
 
2025-07-16 21:36:17,272  [INFO] [D][12:04:19][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:17,273  [INFO] 
 
2025-07-16 21:36:18,283  [INFO] [D][12:04:20][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:18,284  [INFO] 
 
2025-07-16 21:36:19,300  [INFO] [D][12:04:21][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:19,302  [INFO] 
 
2025-07-16 21:36:20,318  [INFO] [D][12:04:22][CAT1]exec over: func id: 1, ret: -14
 
2025-07-16 21:36:20,321  [INFO] [D][12:04:22][CAT1]sub id: 1, ret: -14
 
2025-07-16 21:36:20,322  [INFO] 
 
2025-07-16 21:36:20,328  [INFO] [D][12:04:22][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:36:20,333  [INFO] [D][12:04:22][SAL ]handle subcmd ack sub_id[1], socket[0], result[-14]
 
2025-07-16 21:36:20,338  [INFO] [D][12:04:22][SAL ]gsm power on ind rst[-14]
 
2025-07-16 21:36:20,341  [INFO] [D][12:04:22][M2M ]m2m gsm power on, ret[255]
 
2025-07-16 21:36:20,347  [INFO] [E][12:04:22][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-16 21:36:20,349  [INFO] [E][12:04:22][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 21:36:20,355  [INFO] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
 
2025-07-16 21:36:20,360  [INFO] [E][12:04:22][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-16 21:36:20,362  [INFO] [D][12:04:22][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:36:20,368  [INFO] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:36:20,371  [INFO] [D][12:04:22][PROT]index:5 1730203462
 
2025-07-16 21:36:20,374  [INFO] [D][12:04:22][PROT]is_send:0
 
2025-07-16 21:36:20,377  [INFO] [D][12:04:22][PROT]sequence_num:56
 
2025-07-16 21:36:20,380  [INFO] [D][12:04:22][PROT]retry_timeout:0
 
2025-07-16 21:36:20,383  [INFO] [D][12:04:22][PROT]retry_times:3
 
2025-07-16 21:36:20,385  [INFO] [D][12:04:22][PROT]send_path:0x2
 
2025-07-16 21:36:20,391  [INFO] [D][12:04:22][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:36:20,399  [INFO] [D][12:04:22][PROT]===========================================================
 
2025-07-16 21:36:20,405  [INFO] [W][12:04:22][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203462]
 
2025-07-16 21:36:20,411  [INFO] [D][12:04:22][PROT]===========================================================
 
2025-07-16 21:36:20,415  [INFO] [D][12:04:22][COMM]PB encode data:29
 
2025-07-16 21:36:20,419  [INFO] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:36:20,424  [INFO] [D][12:04:22][PROT]sending traceid [9999999999900027]
 
2025-07-16 21:36:20,429  [INFO] [D][12:04:22][PROT]Send_TO_M2M [1730203462]
 
2025-07-16 21:36:20,435  [INFO] [D][12:04:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
 
2025-07-16 21:36:20,437  [INFO] [D][12:04:22][M2M ]m2m_task: gpc:[0],gpo:[0]
 
2025-07-16 21:36:20,444  [INFO] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:36:20,449  [INFO] [E][12:04:22][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:36:20,452  [INFO] [E][12:04:22][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:36:20,455  [INFO] [D][12:04:22][M2M ]m2m send data len[-1]
 
2025-07-16 21:36:20,461  [INFO] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:36:20,466  [INFO] [E][12:04:22][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:36:20,468  [INFO] [E][12:04:22][PROT]M2M Send Fail [1730203462]
 
2025-07-16 21:36:20,471  [INFO] [D][12:04:22][PROT]CLEAN,SEND:5
 
2025-07-16 21:36:20,477  [INFO] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:36:20,482  [INFO] [D][12:04:22][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:36:20,485  [INFO] [D][12:04:22][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:36:20,486  [INFO] 
 
2025-07-16 21:36:20,490  [INFO] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:36:20,494  [INFO] [D][12:04:22][COMM]Main Task receive event:2
 
2025-07-16 21:36:20,503  [INFO] [D][12:04:22][FCTY]==========REBOOT E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 21:36:20,506  [INFO] [D][12:04:22][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 21:36:22,811  [INFO] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 21:36:22,814  [INFO] flash is 24bit address mode

 
2025-07-16 21:36:22,817  [INFO] SPI Flash init success, ID is 0xC84018

 
2025-07-16 21:36:22,819  [INFO] HW SW version: 5340 109

 
2025-07-16 21:36:22,823  [INFO] netcore sw 105, netboot sw 102
 
2025-07-16 21:36:22,826  [INFO] get_boot_mode a5a5
 
2025-07-16 21:36:22,826  [INFO] is_app_complete 1
 
2025-07-16 21:36:23,026  [INFO] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 21:36:23,027  [INFO] [ADC]Timer status: enabled

 
2025-07-16 21:36:23,028  [INFO] [ADC]init adc success.

 
2025-07-16 21:36:23,659  [INFO] para ret:306,valid:aa

 
2025-07-16 21:36:23,673  [INFO] [W][12:04:24][COMM]BKP RESET_MODE[a5a5], reason[1-1]
 
2025-07-16 21:36:23,676  [INFO] [E][12:04:24][COMM]RESETREAS:0x00000008
 
2025-07-16 21:36:23,682  [INFO] [E][12:04:24][COMM]Multirider mode not support: 255
 
2025-07-16 21:36:23,687  [INFO] [W][12:04:24][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 21:36:23,692  [INFO] [W][12:04:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 21:36:23,695  [INFO] [W][12:04:24][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 21:36:23,701  [INFO] [W][12:04:24][FCTY]DeviceID    = 460130020284403
 
2025-07-16 21:36:23,704  [INFO] [W][12:04:24][FCTY]HardwareID  = 868667086862221
 
2025-07-16 21:36:23,710  [INFO] [W][12:04:24][FCTY]MoBikeID    = 9999999999
 
2025-07-16 21:36:23,712  [INFO] [W][12:04:24][FCTY]LockID      = F050821689
 
2025-07-16 21:36:23,715  [INFO] [W][12:04:24][FCTY]BLEFWVersion= 105
 
2025-07-16 21:36:23,720  [INFO] [W][12:04:24][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-16 21:36:23,723  [INFO] [W][12:04:24][FCTY]Bat         = 0 mv
 
2025-07-16 21:36:23,725  [INFO] [W][12:04:24][FCTY]Current     = 0 ma
 
2025-07-16 21:36:23,732  [INFO] [W][12:04:24][FCTY]VBUS        = 0 mv
 
2025-07-16 21:36:23,738  [INFO] [W][12:04:24][FCTY]TEMP= 0,BATID= 662484,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 21:36:23,741  [INFO] [W][12:04:24][FCTY]Ext battery vol = 1, adc = 75
 
2025-07-16 21:36:23,746  [INFO] [W][12:04:24][FCTY]Bike Type flag is invalied
 
2025-07-16 21:36:23,749  [INFO] [W][12:04:24][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 21:36:23,752  [INFO] [W][12:04:24][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 21:36:23,757  [INFO] [W][12:04:24][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 21:36:23,760  [INFO] [W][12:04:24][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 21:36:23,763  [INFO] [W][12:04:24][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 21:36:23,766  [INFO] [W][12:04:24][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 21:36:23,770  [INFO] [W][12:04:24][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 21:36:23,824  [INFO] [W][12:04:24][GNSS]start sing locating
 
2025-07-16 21:36:24,226  [INFO] [E][12:04:25][COMM]1x1 rx timeout
 
2025-07-16 21:36:24,630  [INFO] [E][12:04:25][COMM]1x1 rx timeout
 
2025-07-16 21:36:24,634  [INFO] [E][12:04:25][COMM]1x1 tp timeout
 
2025-07-16 21:36:24,636  [INFO] [E][12:04:25][COMM]1x1 error -3.
 
2025-07-16 21:36:24,639  [INFO] [W][12:04:25][COMM]Bat auth off fail, error:-1
 
2025-07-16 21:36:24,645  [INFO] [E][12:04:25][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 21:36:24,647  [INFO] [W][12:04:25][COMM]Init MC LOCK_STATE 2
 
2025-07-16 21:36:24,658  [INFO] [W][12:04:25][PROT]remove success[1730203465],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:36:24,665  [INFO] [W][12:04:25][PROT]add success [1730203465],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 21:36:25,749  [INFO] [W][12:04:26][PROT]remove success[1730203466],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:36:25,756  [INFO] [W][12:04:26][PROT]add success [1730203466],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 21:36:35,908  [INFO] [W][12:04:36][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730203476]
 
2025-07-16 21:36:41,766  [INFO] [W][12:04:42][PROT]remove success[1730203482],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:36:41,774  [INFO] [W][12:04:42][PROT]add success [1730203482],send_path[2],type[5103],priority[0],index[2],used[1]
 
2025-07-16 21:36:42,968  [INFO] [W][12:04:43][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730203483]
 
2025-07-16 21:36:48,371  [INFO] [W][12:04:49][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730203489]
 
2025-07-16 21:36:53,773  [INFO] [W][12:04:54][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730203494]
 
2025-07-16 21:36:59,181  [INFO] [W][12:05:00][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730203500]
 
2025-07-16 21:37:40,530  [INFO] [W][12:05:41][PROT]remove success[1730203541],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:37:40,538  [INFO] [W][12:05:41][PROT]add success [1730203541],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 21:37:40,543  [INFO] [W][12:05:41][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730203541]
 
2025-07-16 21:38:09,046  [INFO] [W][12:06:10][GNSS][RTK]enough, report now.
 
2025-07-16 21:38:09,478  [INFO] [E][12:06:10][COMM]1x1 rx timeout
 
2025-07-16 21:38:09,885  [INFO] [E][12:06:10][COMM]1x1 rx timeout
 
2025-07-16 21:38:09,886  [INFO] [E][12:06:10][COMM]1x1 tp timeout
 
2025-07-16 21:38:09,890  [INFO] [E][12:06:10][COMM]1x1 error -3.
 
2025-07-16 21:38:09,893  [INFO] [E][12:06:10][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:38:09,901  [INFO] [W][12:06:10][PROT]remove success[1730203570],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:38:09,908  [INFO] [W][12:06:10][PROT]add success [1730203570],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 21:38:09,922  [INFO] [W][12:06:10][COMM]5A07 LocFail:reason:0x07;diff:5518;LocUsedTime:76;LocStatus|Type:3|000;HDOP:01;SatsView:19;SatsSNR35:02
 
2025-07-16 21:38:09,929  [INFO] [W][12:06:10][COMM]5A07 LocFail:GpsSpeed:00;alt:0056;lon:114340371   lat:23012020
 
2025-07-16 21:38:09,937  [INFO] [W][12:06:10][PROT]remove success[1730203570],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:38:09,943  [INFO] [W][12:06:10][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203570]
 
2025-07-16 21:38:09,951  [INFO] [W][12:06:10][PROT]add success [1730203570],send_path[2],type[5A07],priority[0],index[1],used[1]
 
2025-07-16 21:38:15,310  [INFO] [W][12:06:16][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203576]
 
2025-07-16 21:38:20,716  [INFO] [W][12:06:21][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203581]
 
2025-07-16 21:38:26,119  [INFO] [W][12:06:27][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203587]
 
2025-07-16 21:38:31,528  [INFO] [W][12:06:32][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203592]
 
2025-07-16 21:38:36,930  [INFO] [W][12:06:37][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203597]
 
2025-07-16 21:38:42,339  [INFO] [W][12:06:43][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203603]
 
2025-07-16 21:38:47,742  [INFO] [W][12:06:48][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203608]
 
2025-07-16 21:38:53,149  [INFO] [W][12:06:54][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203614]
 
2025-07-16 21:38:58,555  [INFO] [W][12:06:59][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203619]
 
2025-07-16 21:39:03,974  [INFO] [W][12:07:04][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730203624]
 
2025-07-16 21:39:11,102  [INFO] [E][12:07:12][COMM]1x1 rx timeout
 
2025-07-16 21:39:11,506  [INFO] [E][12:07:12][COMM]1x1 rx timeout
 
2025-07-16 21:39:11,509  [INFO] [E][12:07:12][COMM]1x1 tp timeout
 
2025-07-16 21:39:11,511  [INFO] [E][12:07:12][COMM]1x1 error -3.
 
2025-07-16 21:39:11,514  [INFO] [E][12:07:12][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:39:11,523  [INFO] [E][12:07:12][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 21:39:11,531  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:39:11,539  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[5E01],priority[3],index[0],used[1]
 
2025-07-16 21:39:11,547  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:39:11,555  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[C001],priority[0],index[1],used[1]
 
2025-07-16 21:39:11,562  [INFO] [W][12:07:12][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730203632]
 
2025-07-16 21:39:11,810  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:39:11,818  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[5006],priority[2],index[2],used[1]
 
2025-07-16 21:39:11,821  [INFO] [W][12:07:12][COMM]get soc error
 
2025-07-16 21:39:11,824  [INFO] [W][12:07:12][GNSS]stop locating
 
2025-07-16 21:39:11,826  [INFO] [W][12:07:12][GNSS]sing locating running
 
2025-07-16 21:39:11,832  [INFO] [E][12:07:12][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:39:11,841  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:39:11,849  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[5D05],priority[3],index[3],used[1]
 
2025-07-16 21:39:11,860  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:39:11,868  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[FF0E],priority[0],index[4],used[1]
 
2025-07-16 21:39:11,876  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:39:11,885  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[C001],priority[0],index[5],used[1]
 
2025-07-16 21:39:11,893  [INFO] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:39:11,901  [INFO] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[D302],priority[0],index[6],used[1]
 
2025-07-16 21:39:16,966  [INFO] [W][12:07:17][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730203637]
 
2025-07-16 21:39:22,371  [INFO] [W][12:07:23][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730203643]
 
2025-07-16 21:39:27,790  [INFO] [W][12:07:28][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203648]
 
2025-07-16 21:39:33,196  [INFO] [W][12:07:34][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203654]
 
2025-07-16 21:39:35,995  [INFO] [W][12:07:36][PROT]remove success[1730203656],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:39:36,003  [INFO] [W][12:07:36][PROT]add success [1730203656],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:39:38,604  [INFO] [W][12:07:39][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203659]
 
2025-07-16 21:39:38,607  [INFO] [E][12:07:39][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:39:38,612  [INFO] [E][12:07:39][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:39:38,617  [INFO] [E][12:07:39][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:39:38,623  [INFO] [E][12:07:39][PROT]M2M Send Fail [1730203659]
 
2025-07-16 21:39:40,701  [INFO] [W][12:07:41][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730203661]
 
2025-07-16 21:39:46,121  [INFO] [W][12:07:47][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:46,217  [INFO] [W][12:07:47][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:46,312  [INFO] [W][12:07:47][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:46,396  [INFO] [W][12:07:47][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:51,804  [INFO] [W][12:07:52][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730203672]
 
2025-07-16 21:39:57,210  [INFO] [W][12:07:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730203678]
 
2025-07-16 21:40:02,627  [INFO] [W][12:08:03][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203683]
 
2025-07-16 21:40:49,299  [INFO] [W][12:08:50][PROT]remove success[1730203730],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:40:49,305  [INFO] [W][12:08:50][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203730]
 
2025-07-16 21:40:49,312  [INFO] [W][12:08:50][PROT]add success [1730203730],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:41:30,234  [INFO] [W][12:09:31][PROT]remove success[1730203771],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:41:30,241  [INFO] [W][12:09:31][PROT]add success [1730203771],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:41:30,247  [INFO] [W][12:09:31][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203771]
 
2025-07-16 21:42:11,253  [INFO] [W][12:10:12][PROT]remove success[1730203812],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:42:11,258  [INFO] [W][12:10:12][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203812]
 
2025-07-16 21:42:11,265  [INFO] [W][12:10:12][PROT]add success [1730203812],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:42:52,201  [INFO] [W][12:10:53][PROT]remove success[1730203853],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:42:52,209  [INFO] [W][12:10:53][PROT]add success [1730203853],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:42:52,215  [INFO] [W][12:10:53][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203853]
 
2025-07-16 21:43:33,134  [INFO] [W][12:11:34][PROT]remove success[1730203894],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:43:33,143  [INFO] [W][12:11:34][PROT]add success [1730203894],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:43:33,147  [INFO] [W][12:11:34][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203894]
 
2025-07-16 21:44:14,051  [INFO] [W][12:12:15][PROT]remove success[1730203935],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:44:14,057  [INFO] [W][12:12:15][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203935]
 
2025-07-16 21:44:14,065  [INFO] [W][12:12:15][PROT]add success [1730203935],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:44:54,976  [INFO] [W][12:12:55][PROT]remove success[1730203975],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:44:54,985  [INFO] [W][12:12:55][PROT]add success [1730203975],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:44:54,991  [INFO] [W][12:12:55][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203975]
 
2025-07-16 21:45:36,010  [INFO] [W][12:13:37][PROT]remove success[1730204017],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:45:36,018  [INFO] [W][12:13:37][PROT]add success [1730204017],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:45:36,024  [INFO] [W][12:13:37][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204017]
 
2025-07-16 21:46:16,945  [INFO] [W][12:14:17][PROT]remove success[1730204057],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:46:16,958  [INFO] [W][12:14:17][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204057]
 
2025-07-16 21:46:16,965  [INFO] [W][12:14:17][PROT]add success [1730204057],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:46:57,858  [INFO] [W][12:14:58][PROT]remove success[1730204098],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:46:57,866  [INFO] [W][12:14:58][PROT]add success [1730204098],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:46:57,871  [INFO] [W][12:14:58][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204098]
 
2025-07-16 21:47:38,860  [INFO] [W][12:15:39][PROT]remove success[1730204139],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:47:38,869  [INFO] [W][12:15:39][PROT]add success [1730204139],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:47:38,874  [INFO] [W][12:15:39][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204139]
 
2025-07-16 21:48:19,802  [INFO] [W][12:16:20][PROT]remove success[1730204180],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:48:19,811  [INFO] [W][12:16:20][PROT]add success [1730204180],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:48:19,816  [INFO] [W][12:16:20][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204180]
 
2025-07-16 21:49:00,828  [INFO] [W][12:17:01][PROT]remove success[1730204221],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:49:00,835  [INFO] [W][12:17:01][PROT]add success [1730204221],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:49:00,840  [INFO] [W][12:17:01][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204221]
 
2025-07-16 21:49:43,078  [INFO] [W][12:17:44][PROT]remove success[1730204264],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:49:43,086  [INFO] [W][12:17:44][PROT]add success [1730204264],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:49:43,091  [INFO] [W][12:17:44][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204264]
 
2025-07-16 21:50:23,991  [INFO] [W][12:18:25][PROT]remove success[1730204305],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:50:23,997  [INFO] [W][12:18:25][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204305]
 
2025-07-16 21:50:24,005  [INFO] [W][12:18:25][PROT]add success [1730204305],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:51:04,909  [INFO] [W][12:19:05][PROT]remove success[1730204345],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:51:04,917  [INFO] [W][12:19:05][PROT]add success [1730204345],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:51:04,923  [INFO] [W][12:19:05][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204345]
 
2025-07-16 21:51:45,929  [INFO] [W][12:19:46][PROT]remove success[1730204386],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:51:45,935  [INFO] [W][12:19:46][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204386]
 
2025-07-16 21:51:45,942  [INFO] [W][12:19:46][PROT]add success [1730204386],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:52:26,836  [INFO] [W][12:20:27][PROT]remove success[1730204427],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:52:26,842  [INFO] [W][12:20:27][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204427]
 
2025-07-16 21:52:26,848  [INFO] [W][12:20:27][PROT]add success [1730204427],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:53:07,845  [INFO] [W][12:21:08][PROT]remove success[1730204468],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:53:07,853  [INFO] [W][12:21:08][PROT]add success [1730204468],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:53:07,858  [INFO] [W][12:21:08][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204468]
 
2025-07-16 21:53:48,757  [INFO] [W][12:21:49][PROT]remove success[1730204509],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:53:48,764  [INFO] [W][12:21:49][PROT]add success [1730204509],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:53:48,771  [INFO] [W][12:21:49][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204509]
 
2025-07-16 21:54:29,769  [INFO] [W][12:22:30][PROT]remove success[1730204550],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:54:29,777  [INFO] [W][12:22:30][PROT]add success [1730204550],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:54:29,783  [INFO] [W][12:22:30][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204550]
 
2025-07-16 21:55:10,697  [INFO] [W][12:23:11][PROT]remove success[1730204591],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:55:10,702  [INFO] [W][12:23:11][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204591]
 
2025-07-16 21:55:10,709  [INFO] [W][12:23:11][PROT]add success [1730204591],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:55:51,603  [INFO] [W][12:23:52][PROT]remove success[1730204632],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:55:51,612  [INFO] [W][12:23:52][PROT]add success [1730204632],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:55:51,617  [INFO] [W][12:23:52][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204632]
 
2025-07-16 21:56:32,616  [INFO] [W][12:24:33][PROT]remove success[1730204673],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:56:32,623  [INFO] [W][12:24:33][PROT]add success [1730204673],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:56:32,628  [INFO] [W][12:24:33][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204673]
 
2025-07-16 21:57:34,355  [INFO] [W][12:25:35][COMM]Power Off
 
