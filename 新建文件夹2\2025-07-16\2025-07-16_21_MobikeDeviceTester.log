2025-07-16 21:00:02,133  [DEBUG] [W][11:28:04][GNSS][RTK]enough, report now.
 
2025-07-16 21:00:02,190  [DEBUG] [D][11:28:04][COMM]Main Task receive event:13
 
2025-07-16 21:00:02,193  [DEBUG] [D][11:28:04][GNSS]stop event:1
 
2025-07-16 21:00:02,196  [DEBUG] [D][11:28:04][GNSS]GPS stop. ret=0
 
2025-07-16 21:00:02,198  [DEBUG] [D][11:28:04][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:00:02,207  [DEBUG] [D][11:28:04][COMM]frm_mc_bat_power_on EXT_BAT_STATE_POWERON_TIMEOUT, bat is on:0
 
2025-07-16 21:00:02,213  [DEBUG] [D][11:28:04][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON, Ext48v = 2.
 
2025-07-16 21:00:02,218  [DEBUG] [D][11:28:04][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:00:02,219  [DEBUG] 
 
2025-07-16 21:00:02,220  [DEBUG] [D][11:28:04][COMM]open bat mos
 
2025-07-16 21:00:02,223  [DEBUG] [D][11:28:04][COMM]1x1 tx_id:3,7, tx_len:2
 
2025-07-16 21:00:02,227  [DEBUG] [D][11:28:04][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:00:02,256  [DEBUG] [D][11:28:04][CAT1]<<< 
 
2025-07-16 21:00:02,256  [DEBUG] OK
 
2025-07-16 21:00:02,266  [DEBUG] 
 
2025-07-16 21:00:02,270  [DEBUG] [D][11:28:04][CAT1]tx ret[13] >>> AT+GPSRTK=0
 
2025-07-16 21:00:02,271  [DEBUG] 
 
2025-07-16 21:00:02,289  [DEBUG] [D][11:28:04][CAT1]<<< 
 
2025-07-16 21:00:02,290  [DEBUG] OK
 
2025-07-16 21:00:02,299  [DEBUG] 
 
2025-07-16 21:00:02,301  [DEBUG] [D][11:28:04][CAT1]tx ret[12] >>> AT+GPSDR=0
 
2025-07-16 21:00:02,302  [DEBUG] 
 
2025-07-16 21:00:02,320  [DEBUG] [D][11:28:04][CAT1]<<< 
 
2025-07-16 21:00:02,321  [DEBUG] OK
 
2025-07-16 21:00:02,322  [DEBUG] 
 
2025-07-16 21:00:02,327  [DEBUG] [D][11:28:04][CAT1]exec over: func id: 24, ret: 6
 
2025-07-16 21:00:02,328  [DEBUG] [D][11:28:04][CAT1]sub id: 24, ret: 6
 
2025-07-16 21:00:02,329  [DEBUG] 
 
2025-07-16 21:00:02,596  [DEBUG] [E][11:28:04][COMM]1x1 rx timeout
 
2025-07-16 21:00:02,598  [DEBUG] [D][11:28:04][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:00:03,000  [DEBUG] [E][11:28:05][COMM]1x1 rx timeout
 
2025-07-16 21:00:03,002  [DEBUG] [E][11:28:05][COMM]1x1 tp timeout
 
2025-07-16 21:00:03,005  [DEBUG] [E][11:28:05][COMM]1x1 error -3.
 
2025-07-16 21:00:03,008  [DEBUG] [E][11:28:05][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:00:03,014  [DEBUG] [D][11:28:05][COMM]frm_peripheral_device_poweron type 12.... 
 
2025-07-16 21:00:03,017  [DEBUG] [D][11:28:05][COMM]get Acckey 1 and value:1
 
2025-07-16 21:00:03,023  [DEBUG] [D][11:28:05][COMM]First location,do verification
 
2025-07-16 21:00:03,028  [DEBUG] [D][11:28:05][M2M ]m2m_task:m_m2m_thread_setting_queue:0
 
2025-07-16 21:00:03,030  [DEBUG] [D][11:28:05][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:03,036  [DEBUG] [D][11:28:05][COMM][frmJournal_read][210] read fail
 
2025-07-16 21:00:03,039  [DEBUG] [D][11:28:05][COMM]get hw mark: ffffffff
 
2025-07-16 21:00:03,041  [DEBUG] [D][11:28:05][COMM]bat type 255
 
2025-07-16 21:00:03,050  [DEBUG] [W][11:28:05][PROT]remove success[1730201285],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:00:03,058  [DEBUG] [D][11:28:05][HSDK][0] flush to flash addr:[0xE45D00] --- write len --- [256]
 
2025-07-16 21:00:03,066  [DEBUG] [W][11:28:05][PROT]add success [1730201285],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 21:00:03,072  [DEBUG] [D][11:28:05][COMM]photo upload succeed!!! send ack
 
2025-07-16 21:00:03,075  [DEBUG] [D][11:28:05][COMM]photo upload taskid:[00000000 00000000]
 
2025-07-16 21:00:03,086  [DEBUG] [W][11:28:05][PROT]remove success[1730201285],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:00:03,095  [DEBUG] [W][11:28:05][PROT]add success [1730201285],send_path[2],type[6A01],priority[0],index[1],used[1]
 
2025-07-16 21:00:03,097  [DEBUG] [D][11:28:05][GNSS]nTotalNumSatsInView = 18
 
2025-07-16 21:00:03,104  [DEBUG] [D][11:28:05][GNSS]nPRN = 21, nSignalQuality = 22, Elev = 35, Azimuth = 009A
 
2025-07-16 21:00:03,107  [DEBUG] [D][11:28:05][PROT]index:0 1730201285
 
2025-07-16 21:00:03,110  [DEBUG] [D][11:28:05][PROT]is_send:0
 
2025-07-16 21:00:03,113  [DEBUG] [D][11:28:05][PROT]sequence_num:3
 
2025-07-16 21:00:03,116  [DEBUG] [D][11:28:05][PROT]retry_timeout:0
 
2025-07-16 21:00:03,118  [DEBUG] [D][11:28:05][PROT]retry_times:10
 
2025-07-16 21:00:03,122  [DEBUG] [D][11:28:05][PROT]send_path:0x2
 
2025-07-16 21:00:03,128  [DEBUG] [D][11:28:05][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:03,134  [DEBUG] [D][11:28:05][PROT]===========================================================
 
2025-07-16 21:00:03,140  [DEBUG] [W][11:28:05][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201285]
 
2025-07-16 21:00:03,147  [DEBUG] [D][11:28:05][PROT]===========================================================
 
2025-07-16 21:00:03,153  [DEBUG] [D][11:28:05][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:03,155  [DEBUG] [D][11:28:05][PROT]Send_TO_M2M [1730201285]
 
2025-07-16 21:00:03,161  [DEBUG] [D][11:28:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:03,163  [DEBUG] [D][11:28:05][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:03,170  [DEBUG] [D][11:28:05][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:03,173  [DEBUG] [D][11:28:05][M2M ]m2m send data len[198]
 
2025-07-16 21:00:03,175  [DEBUG] [D][11:28:05][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:03,184  [DEBUG] [D][11:28:05][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:03,192  [DEBUG] [D][11:28:05][GNSS]nPRN = 17, nSignalQuality = 21, Elev = 2D, Azimuth = 010C
 
2025-07-16 21:00:03,198  [DEBUG] [D][11:28:05][GNSS]nPRN = 0D, nSignalQuality = 1F, Elev = 33, Azimuth = 00DD
 
2025-07-16 21:00:03,207  [DEBUG] [D][11:28:05][GNSS]nPRN = 26, nSignalQuality = 1F, Elev = 25, Azimuth = 00BF
 
2025-07-16 21:00:03,212  [DEBUG] [D][11:28:05][GNSS]nPRN = 21, nSignalQuality = 17, Elev = 35, Azimuth = 009A
 
2025-07-16 21:00:03,221  [DEBUG] [D][11:28:05][GNSS]nPRN = 17, nSignalQuality = 15, Elev = 2D, Azimuth = 010C
 
2025-07-16 21:00:03,226  [DEBUG] [D][11:28:05][GNSS]nPRN = 26, nSignalQuality = 14, Elev = 25, Azimuth = 00BF
 
2025-07-16 21:00:03,232  [DEBUG] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,240  [DEBUG] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,245  [DEBUG] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,253  [DEBUG] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,260  [DEBUG] [D][11:28:05][GNSS]nPRN = 00, nSignalQuality = 00, Elev = 00, Azimuth = 0000
 
2025-07-16 21:00:03,265  [DEBUG] [D][11:28:05][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:03,267  [DEBUG] [D][11:28:05][GNSS]nSatsAvgSNR 28, nSatsSNROver35 0
 
2025-07-16 21:00:03,273  [DEBUG] [D][11:28:05][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:03,273  [DEBUG] 
 
2025-07-16 21:00:03,279  [DEBUG] [D][11:28:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:03,281  [DEBUG] [D][11:28:05][GNSS]nSatsInUse 4
 
2025-07-16 21:00:03,284  [DEBUG] [D][11:28:05][GNSS]DOP: 9.670000 4.270000 8.680000
 
2025-07-16 21:00:03,295  [DEBUG] [W][11:28:05][COMM]5A07 LocFail:reason:0x07;diff:5517;LocUsedTime:58;LocStatus|Type:3|000;HDOP:04;SatsView:18;SatsSNR35:00
 
2025-07-16 21:00:03,304  [DEBUG] [W][11:28:05][COMM]5A07 LocFail:GpsSpeed:00;alt:0069;lon:114340339   lat:23012004
 
2025-07-16 21:00:03,311  [DEBUG] [W][11:28:05][PROT]remove success[1730201285],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:00:03,321  [DEBUG] [W][11:28:05][PROT]add success [1730201285],send_path[2],type[5A07],priority[0],index[2],used[1]
 
2025-07-16 21:00:03,326  [DEBUG] [D][11:28:05][COMM]f:frm_rs485_park_cam_start_get_ver_timer. no 48v
 
2025-07-16 21:00:03,331  [DEBUG] [D][11:28:05][COMM]frm_peripheral_device_poweroff type 0.... 
 
2025-07-16 21:00:03,337  [DEBUG] [D][11:28:05][COMM]Main Task receive event:13 finished processing
 
2025-07-16 21:00:03,339  [DEBUG] [D][11:28:05][GNSS]recv submsg id[1]
 
2025-07-16 21:00:03,345  [DEBUG] [D][11:28:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
 
2025-07-16 21:00:03,350  [DEBUG] [D][11:28:05][GNSS]location stop evt done evt
 
2025-07-16 21:00:03,353  [DEBUG] [D][11:28:05][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:03,374  [DEBUG] 0063B98C113311331133113311331B88B578FBF441C6D66DA30AF54A32DB79F21799AE6446B132481B9E3317450587FB8BDFEA7AE129540BEB958735BBB44DD0BF63848FC683075AC5D8110EB2F9A098582A97DDC971D4087770C6690CF3628D50D685
 
2025-07-16 21:00:03,375  [DEBUG] [D][11:28:05][CAT1]<<< 
 
2025-07-16 21:00:03,375  [DEBUG] SEND OK
 
2025-07-16 21:00:03,376  [DEBUG] 
 
2025-07-16 21:00:03,378  [DEBUG] [D][11:28:05][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:03,384  [DEBUG] [D][11:28:05][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:03,385  [DEBUG] 
 
2025-07-16 21:00:03,387  [DEBUG] [D][11:28:05][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:03,393  [DEBUG] [D][11:28:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:03,397  [DEBUG] [D][11:28:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:03,401  [DEBUG] [D][11:28:05][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:03,406  [DEBUG] [D][11:28:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:03,409  [DEBUG] [D][11:28:05][PROT]M2M Send ok [1730201285]
 
2025-07-16 21:00:08,409  [DEBUG] [D][11:28:10][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:08,422  [DEBUG] [D][11:28:10][PROT]index:0 1730201290
 
2025-07-16 21:00:08,425  [DEBUG] [D][11:28:10][PROT]is_send:0
 
2025-07-16 21:00:08,428  [DEBUG] [D][11:28:10][PROT]sequence_num:3
 
2025-07-16 21:00:08,430  [DEBUG] [D][11:28:10][PROT]retry_timeout:0
 
2025-07-16 21:00:08,433  [DEBUG] [D][11:28:10][PROT]retry_times:9
 
2025-07-16 21:00:08,436  [DEBUG] [D][11:28:10][PROT]send_path:0x2
 
2025-07-16 21:00:08,441  [DEBUG] [D][11:28:10][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:08,447  [DEBUG] [D][11:28:10][PROT]===========================================================
 
2025-07-16 21:00:08,453  [DEBUG] [D][11:28:10][HSDK]need to erase for write: is[0x0] ie[0x4E00]
 
2025-07-16 21:00:08,461  [DEBUG] [D][11:28:10][HSDK][0] flush to flash addr:[0xE45E00] --- write len --- [256]
 
2025-07-16 21:00:08,467  [DEBUG] [W][11:28:10][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201290]
 
2025-07-16 21:00:08,473  [DEBUG] [D][11:28:10][PROT]===========================================================
 
2025-07-16 21:00:08,478  [DEBUG] [D][11:28:10][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:08,480  [DEBUG] [D][11:28:10][PROT]Send_TO_M2M [1730201290]
 
2025-07-16 21:00:08,487  [DEBUG] [D][11:28:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:08,492  [DEBUG] [D][11:28:10][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:08,495  [DEBUG] [D][11:28:10][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:08,498  [DEBUG] [D][11:28:10][M2M ]m2m send data len[198]
 
2025-07-16 21:00:08,503  [DEBUG] [D][11:28:10][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:08,512  [DEBUG] [D][11:28:10][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:08,517  [DEBUG] [D][11:28:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:08,520  [DEBUG] [D][11:28:10][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:08,526  [DEBUG] [D][11:28:10][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:08,526  [DEBUG] 
 
2025-07-16 21:00:08,528  [DEBUG] [D][11:28:10][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:08,549  [DEBUG] 0063B981113311331133113311331B88B52D3CDCB091FE84927DB9F1E787FFABE036418D452C6FD7AB45386AC25C65DEA588EE782ACA7C54430EC5DF0E2D2F1A8562315B26247B14B897AF8F0791F830879D94A271E195A378AA86561D779299AEA98A
 
2025-07-16 21:00:08,550  [DEBUG] [D][11:28:10][CAT1]<<< 
 
2025-07-16 21:00:08,551  [DEBUG] SEND OK
 
2025-07-16 21:00:08,551  [DEBUG] 
 
2025-07-16 21:00:08,552  [DEBUG] [D][11:28:10][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:08,559  [DEBUG] [D][11:28:10][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:08,560  [DEBUG] 
 
2025-07-16 21:00:08,561  [DEBUG] [D][11:28:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:08,571  [DEBUG] [D][11:28:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:08,572  [DEBUG] [D][11:28:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:08,575  [DEBUG] [D][11:28:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:08,581  [DEBUG] [D][11:28:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:08,585  [DEBUG] [D][11:28:10][PROT]M2M Send ok [1730201290]
 
2025-07-16 21:00:10,716  [DEBUG] [D][11:28:12][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 2.
 
2025-07-16 21:00:13,825  [DEBUG] [D][11:28:16][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:13,838  [DEBUG] [D][11:28:16][PROT]index:0 1730201296
 
2025-07-16 21:00:13,841  [DEBUG] [D][11:28:16][PROT]is_send:0
 
2025-07-16 21:00:13,843  [DEBUG] [D][11:28:16][PROT]sequence_num:3
 
2025-07-16 21:00:13,847  [DEBUG] [D][11:28:16][PROT]retry_timeout:0
 
2025-07-16 21:00:13,848  [DEBUG] [D][11:28:16][PROT]retry_times:8
 
2025-07-16 21:00:13,851  [DEBUG] [D][11:28:16][PROT]send_path:0x2
 
2025-07-16 21:00:13,857  [DEBUG] [D][11:28:16][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:13,863  [DEBUG] [D][11:28:16][PROT]===========================================================
 
2025-07-16 21:00:13,869  [DEBUG] [W][11:28:16][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201296]
 
2025-07-16 21:00:13,877  [DEBUG] [D][11:28:16][PROT]===========================================================
 
2025-07-16 21:00:13,883  [DEBUG] [D][11:28:16][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:13,885  [DEBUG] [D][11:28:16][PROT]Send_TO_M2M [1730201296]
 
2025-07-16 21:00:13,891  [DEBUG] [D][11:28:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:13,893  [DEBUG] [D][11:28:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:13,899  [DEBUG] [D][11:28:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:13,902  [DEBUG] [D][11:28:16][M2M ]m2m send data len[198]
 
2025-07-16 21:00:13,905  [DEBUG] [D][11:28:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:13,913  [DEBUG] [D][11:28:16][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:13,919  [DEBUG] [D][11:28:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:13,921  [DEBUG] [D][11:28:16][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:13,922  [DEBUG] 
 
2025-07-16 21:00:13,927  [DEBUG] [D][11:28:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:13,932  [DEBUG] [D][11:28:16][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:13,952  [DEBUG] 0063B982113311331133113311331B88B52CF4F6F40211E61B938377D325EB14DE6F10B3BB272B8B29D2D66E6B24E490F216577D09743598BF8576D7807A7886E5AE2A7C44604D7BAF0EDC7BDDCE0D1B1BC0F6E7645BDEED8DB9430DD1C2754946E743
 
2025-07-16 21:00:13,953  [DEBUG] [D][11:28:16][CAT1]<<< 
 
2025-07-16 21:00:13,953  [DEBUG] SEND OK
 
2025-07-16 21:00:13,953  [DEBUG] 
 
2025-07-16 21:00:13,958  [DEBUG] [D][11:28:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:13,961  [DEBUG] [D][11:28:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:13,961  [DEBUG] 
 
2025-07-16 21:00:13,966  [DEBUG] [D][11:28:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:13,971  [DEBUG] [D][11:28:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:13,976  [DEBUG] [D][11:28:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:13,982  [DEBUG] [D][11:28:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:13,986  [DEBUG] [D][11:28:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:13,988  [DEBUG] [D][11:28:16][PROT]M2M Send ok [1730201296]
 
2025-07-16 21:00:14,784  [DEBUG] [D][11:28:16][COMM]msg 0226 loss. last_tick:0. cur_tick:100001. period:10000
 
2025-07-16 21:00:14,785  [DEBUG] 
 
2025-07-16 21:00:14,789  [DEBUG] [D][11:28:16][COMM]msg 0227 loss. last_tick:0. cur_tick:100002. period:10000
 
2025-07-16 21:00:14,790  [DEBUG] 
 
2025-07-16 21:00:14,797  [DEBUG] [D][11:28:16][COMM]msg 0228 loss. last_tick:0. cur_tick:100003. period:10000
 
2025-07-16 21:00:14,798  [DEBUG] 
 
2025-07-16 21:00:14,803  [DEBUG] [D][11:28:16][COMM]msg 0261 loss. last_tick:0. cur_tick:100003. period:10000
 
2025-07-16 21:00:14,803  [DEBUG] 
 
2025-07-16 21:00:14,811  [DEBUG] [D][11:28:16][COMM]msg 0262 loss. last_tick:0. cur_tick:100004. period:10000
 
2025-07-16 21:00:14,813  [DEBUG] 
 
2025-07-16 21:00:14,817  [DEBUG] [D][11:28:16][COMM]msg 0263 loss. last_tick:0. cur_tick:100004. period:10000
 
2025-07-16 21:00:14,818  [DEBUG] 
 
2025-07-16 21:00:14,826  [DEBUG] [D][11:28:16][COMM]msg 0281 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-16 21:00:14,827  [DEBUG] 
 
2025-07-16 21:00:14,831  [DEBUG] [D][11:28:16][COMM]msg 0282 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-16 21:00:14,832  [DEBUG] 
 
2025-07-16 21:00:14,840  [DEBUG] [D][11:28:16][COMM]msg 0283 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-16 21:00:14,841  [DEBUG] 
 
2025-07-16 21:00:14,845  [DEBUG] [D][11:28:16][COMM]msg 02A1 loss. last_tick:0. cur_tick:100006. period:10000
 
2025-07-16 21:00:14,845  [DEBUG] 
 
2025-07-16 21:00:14,853  [DEBUG] [D][11:28:16][COMM]msg 02A2 loss. last_tick:0. cur_tick:100006. period:10000
 
2025-07-16 21:00:14,853  [DEBUG] 
 
2025-07-16 21:00:14,858  [DEBUG] [D][11:28:16][COMM]msg 02A3 loss. last_tick:0. cur_tick:100007. period:10000
 
2025-07-16 21:00:14,858  [DEBUG] 
 
2025-07-16 21:00:14,867  [DEBUG] [D][11:28:16][COMM]msg 02C3 loss. last_tick:0. cur_tick:100007. period:10000
 
2025-07-16 21:00:14,867  [DEBUG] 
 
2025-07-16 21:00:14,872  [DEBUG] [D][11:28:16][COMM]msg 02C4 loss. last_tick:0. cur_tick:100008. period:10000
 
2025-07-16 21:00:14,874  [DEBUG] 
 
2025-07-16 21:00:14,881  [DEBUG] [D][11:28:16][COMM]msg 02C5 loss. last_tick:0. cur_tick:100008. period:10000
 
2025-07-16 21:00:14,882  [DEBUG] 
 
2025-07-16 21:00:14,887  [DEBUG] [D][11:28:16][COMM]msg 02E3 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-16 21:00:14,887  [DEBUG] 
 
2025-07-16 21:00:14,894  [DEBUG] [D][11:28:16][COMM]msg 02E4 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-16 21:00:14,895  [DEBUG] 
 
2025-07-16 21:00:14,900  [DEBUG] [D][11:28:16][COMM]msg 02E5 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-16 21:00:14,901  [DEBUG] 
 
2025-07-16 21:00:14,910  [DEBUG] [D][11:28:16][COMM]msg 0302 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-16 21:00:14,910  [DEBUG] 
 
2025-07-16 21:00:14,915  [DEBUG] [D][11:28:16][COMM]msg 0303 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-16 21:00:14,915  [DEBUG] 
 
2025-07-16 21:00:14,923  [DEBUG] [D][11:28:16][COMM]msg 0304 loss. last_tick:0. cur_tick:100011. period:10000
 
2025-07-16 21:00:14,924  [DEBUG] 
 
2025-07-16 21:00:14,928  [DEBUG] [D][11:28:16][COMM]msg 02E6 loss. last_tick:0. cur_tick:100011. period:10000
 
2025-07-16 21:00:14,929  [DEBUG] 
 
2025-07-16 21:00:14,938  [DEBUG] [D][11:28:16][COMM]msg 02E7 loss. last_tick:0. cur_tick:100012. period:10000
 
2025-07-16 21:00:14,938  [DEBUG] 
 
2025-07-16 21:00:14,942  [DEBUG] [D][11:28:16][COMM]msg 0305 loss. last_tick:0. cur_tick:100012. period:10000
 
2025-07-16 21:00:14,942  [DEBUG] 
 
2025-07-16 21:00:14,951  [DEBUG] [D][11:28:17][COMM]msg 0306 loss. last_tick:0. cur_tick:100013. period:10000
 
2025-07-16 21:00:14,951  [DEBUG] 
 
2025-07-16 21:00:14,955  [DEBUG] [D][11:28:17][COMM]msg 02A8 loss. last_tick:0. cur_tick:100013. period:10000
 
2025-07-16 21:00:14,956  [DEBUG] 
 
2025-07-16 21:00:14,964  [DEBUG] [D][11:28:17][COMM]msg 02A9 loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-16 21:00:14,965  [DEBUG] 
 
2025-07-16 21:00:14,970  [DEBUG] [D][11:28:17][COMM]msg 02AA loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-16 21:00:14,971  [DEBUG] 
 
2025-07-16 21:00:14,978  [DEBUG] [D][11:28:17][COMM]msg 02AB loss. last_tick:0. cur_tick:100015. period:10000
 
2025-07-16 21:00:14,979  [DEBUG] 
 
2025-07-16 21:00:14,983  [DEBUG] [D][11:28:17][COMM]msg 02AD loss. last_tick:0. cur_tick:100015. period:10000
 
2025-07-16 21:00:14,985  [DEBUG] 
 
2025-07-16 21:00:14,992  [DEBUG] [D][11:28:17][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100015. period:10000. j,i:0 53
 
2025-07-16 21:00:14,993  [DEBUG] 
 
2025-07-16 21:00:15,000  [DEBUG] [D][11:28:17][COMM]bat msg 02AE loss. last_tick:0. cur_tick:100016. period:10000. j,i:1 54
 
2025-07-16 21:00:15,001  [DEBUG] 
 
2025-07-16 21:00:15,009  [DEBUG] [D][11:28:17][COMM]bat msg 024A loss. last_tick:0. cur_tick:100016. period:10000. j,i:13 66
 
2025-07-16 21:00:15,010  [DEBUG] 
 
2025-07-16 21:00:15,018  [DEBUG] [D][11:28:17][COMM]bat msg 024B loss. last_tick:0. cur_tick:100017. period:10000. j,i:14 67
 
2025-07-16 21:00:15,019  [DEBUG] 
 
2025-07-16 21:00:15,026  [DEBUG] [D][11:28:17][COMM]bat msg 024C loss. last_tick:0. cur_tick:100018. period:10000. j,i:15 68
 
2025-07-16 21:00:15,027  [DEBUG] 
 
2025-07-16 21:00:15,034  [DEBUG] [D][11:28:17][COMM]bat msg 024D loss. last_tick:0. cur_tick:100018. period:10000. j,i:16 69
 
2025-07-16 21:00:15,034  [DEBUG] 
 
2025-07-16 21:00:15,043  [DEBUG] [D][11:28:17][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100018. period:10000. j,i:19 72
 
2025-07-16 21:00:15,043  [DEBUG] 
 
2025-07-16 21:00:15,051  [DEBUG] [D][11:28:17][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100019. period:10000. j,i:20 73
 
2025-07-16 21:00:15,052  [DEBUG] 
 
2025-07-16 21:00:15,059  [DEBUG] [D][11:28:17][COMM]bat msg 025A loss. last_tick:0. cur_tick:100019. period:10000. j,i:24 77
 
2025-07-16 21:00:15,060  [DEBUG] 
 
2025-07-16 21:00:15,067  [DEBUG] [D][11:28:17][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100020
 
2025-07-16 21:00:15,076  [DEBUG] [D][11:28:17][COMM]CAN message bat fault change: 0x06E61FFC->0x07FFFFFF 100020
 
2025-07-16 21:00:15,082  [DEBUG] [D][11:28:17][COMM]CAN fault change: 0x0000000300010F05->0x0000000300010F07 100021
 
2025-07-16 21:00:19,246  [DEBUG] [D][11:28:21][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:19,259  [DEBUG] [D][11:28:21][PROT]index:0 1730201301
 
2025-07-16 21:00:19,262  [DEBUG] [D][11:28:21][PROT]is_send:0
 
2025-07-16 21:00:19,264  [DEBUG] [D][11:28:21][PROT]sequence_num:3
 
2025-07-16 21:00:19,268  [DEBUG] [D][11:28:21][PROT]retry_timeout:0
 
2025-07-16 21:00:19,270  [DEBUG] [D][11:28:21][PROT]retry_times:7
 
2025-07-16 21:00:19,273  [DEBUG] [D][11:28:21][PROT]send_path:0x2
 
2025-07-16 21:00:19,278  [DEBUG] [D][11:28:21][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:19,285  [DEBUG] [D][11:28:21][PROT]===========================================================
 
2025-07-16 21:00:19,290  [DEBUG] [W][11:28:21][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201301]
 
2025-07-16 21:00:19,298  [DEBUG] [D][11:28:21][PROT]===========================================================
 
2025-07-16 21:00:19,305  [DEBUG] [D][11:28:21][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:19,307  [DEBUG] [D][11:28:21][PROT]Send_TO_M2M [1730201301]
 
2025-07-16 21:00:19,313  [DEBUG] [D][11:28:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:19,315  [DEBUG] [D][11:28:21][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:19,321  [DEBUG] [D][11:28:21][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:19,323  [DEBUG] [D][11:28:21][M2M ]m2m send data len[198]
 
2025-07-16 21:00:19,326  [DEBUG] [D][11:28:21][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:19,334  [DEBUG] [D][11:28:21][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:19,340  [DEBUG] [D][11:28:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:19,345  [DEBUG] [D][11:28:21][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:19,348  [DEBUG] [D][11:28:21][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:19,349  [DEBUG] 
 
2025-07-16 21:00:19,353  [DEBUG] [D][11:28:21][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:19,373  [DEBUG] 0063B98E113311331133113311331B88B56686E4A4809FDC4BC70487A7F8F373EC4FE57502BC9D70A885EB20F30B0EFC62E058BF0CE0BC11E79EF83468BB720D8A8D2596B1BC927D5A60973542A212701A8534D3442988B4301AF7C3B72702888A6647
 
2025-07-16 21:00:19,374  [DEBUG] [D][11:28:21][CAT1]<<< 
 
2025-07-16 21:00:19,375  [DEBUG] SEND OK
 
2025-07-16 21:00:19,375  [DEBUG] 
 
2025-07-16 21:00:19,380  [DEBUG] [D][11:28:21][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:19,382  [DEBUG] [D][11:28:21][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:19,383  [DEBUG] 
 
2025-07-16 21:00:19,388  [DEBUG] [D][11:28:21][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:19,393  [DEBUG] [D][11:28:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:19,398  [DEBUG] [D][11:28:21][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:19,401  [DEBUG] [D][11:28:21][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:19,407  [DEBUG] [D][11:28:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:19,409  [DEBUG] [D][11:28:21][PROT]M2M Send ok [1730201301]
 
2025-07-16 21:00:24,654  [DEBUG] [D][11:28:26][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:24,666  [DEBUG] [D][11:28:26][PROT]index:0 1730201306
 
2025-07-16 21:00:24,668  [DEBUG] [D][11:28:26][PROT]is_send:0
 
2025-07-16 21:00:24,671  [DEBUG] [D][11:28:26][PROT]sequence_num:3
 
2025-07-16 21:00:24,674  [DEBUG] [D][11:28:26][PROT]retry_timeout:0
 
2025-07-16 21:00:24,676  [DEBUG] [D][11:28:26][PROT]retry_times:6
 
2025-07-16 21:00:24,679  [DEBUG] [D][11:28:26][PROT]send_path:0x2
 
2025-07-16 21:00:24,684  [DEBUG] [D][11:28:26][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:24,691  [DEBUG] [D][11:28:26][PROT]===========================================================
 
2025-07-16 21:00:24,696  [DEBUG] [W][11:28:26][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201306]
 
2025-07-16 21:00:24,705  [DEBUG] [D][11:28:26][PROT]===========================================================
 
2025-07-16 21:00:24,710  [DEBUG] [D][11:28:26][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:24,713  [DEBUG] [D][11:28:26][PROT]Send_TO_M2M [1730201306]
 
2025-07-16 21:00:24,720  [DEBUG] [D][11:28:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:24,722  [DEBUG] [D][11:28:26][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:24,727  [DEBUG] [D][11:28:26][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:24,730  [DEBUG] [D][11:28:26][M2M ]m2m send data len[198]
 
2025-07-16 21:00:24,732  [DEBUG] [D][11:28:26][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:24,742  [DEBUG] [D][11:28:26][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:24,746  [DEBUG] [D][11:28:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:24,752  [DEBUG] [D][11:28:26][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:24,755  [DEBUG] [D][11:28:26][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:24,756  [DEBUG] 
 
2025-07-16 21:00:24,760  [DEBUG] [D][11:28:26][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:24,778  [DEBUG] 0063B983113311331133113311331B88B5E39339ABCB263D0AC57CD7071BE12A1E284900B1E464ADED305AB75F9849C97C863C0B1E8FA622E4415B9AF6537B106F12B0C8D2E5011FCD84BCA84AAFCEEA073602A5A923D15B1BFAD8424D6220DB5AF461
 
2025-07-16 21:00:24,779  [DEBUG] [D][11:28:26][CAT1]<<< 
 
2025-07-16 21:00:24,780  [DEBUG] SEND OK
 
2025-07-16 21:00:24,780  [DEBUG] 
 
2025-07-16 21:00:24,785  [DEBUG] [D][11:28:26][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:24,788  [DEBUG] [D][11:28:26][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:24,789  [DEBUG] 
 
2025-07-16 21:00:24,794  [DEBUG] [D][11:28:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:24,800  [DEBUG] [D][11:28:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:24,804  [DEBUG] [D][11:28:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:24,807  [DEBUG] [D][11:28:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:24,814  [DEBUG] [D][11:28:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:24,815  [DEBUG] [D][11:28:26][PROT]M2M Send ok [1730201306]
 
2025-07-16 21:00:30,057  [DEBUG] [D][11:28:32][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:30,068  [DEBUG] [D][11:28:32][PROT]index:0 1730201312
 
2025-07-16 21:00:30,072  [DEBUG] [D][11:28:32][PROT]is_send:0
 
2025-07-16 21:00:30,075  [DEBUG] [D][11:28:32][PROT]sequence_num:3
 
2025-07-16 21:00:30,077  [DEBUG] [D][11:28:32][PROT]retry_timeout:0
 
2025-07-16 21:00:30,080  [DEBUG] [D][11:28:32][PROT]retry_times:5
 
2025-07-16 21:00:30,082  [DEBUG] [D][11:28:32][PROT]send_path:0x2
 
2025-07-16 21:00:30,088  [DEBUG] [D][11:28:32][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:30,094  [DEBUG] [D][11:28:32][PROT]===========================================================
 
2025-07-16 21:00:30,099  [DEBUG] [W][11:28:32][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201312]
 
2025-07-16 21:00:30,109  [DEBUG] [D][11:28:32][PROT]===========================================================
 
2025-07-16 21:00:30,114  [DEBUG] [D][11:28:32][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:30,117  [DEBUG] [D][11:28:32][PROT]Send_TO_M2M [1730201312]
 
2025-07-16 21:00:30,122  [DEBUG] [D][11:28:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:30,124  [DEBUG] [D][11:28:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:30,129  [DEBUG] [D][11:28:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:30,132  [DEBUG] [D][11:28:32][M2M ]m2m send data len[198]
 
2025-07-16 21:00:30,135  [DEBUG] [D][11:28:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:30,144  [DEBUG] [D][11:28:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:30,151  [DEBUG] [D][11:28:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:30,156  [DEBUG] [D][11:28:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:30,158  [DEBUG] [D][11:28:32][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:30,159  [DEBUG] 
 
2025-07-16 21:00:30,163  [DEBUG] [D][11:28:32][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:30,182  [DEBUG] 0063B985113311331133113311331B88B58342F9F361BA6B48EF19BCDAE69ACF50BB3ABBEAEADEAD4A50DA74EDF2EF8940EEE5C7B2EE5C0BB3B085B672FB6C767D6A2D98F12194807DEC785B64B4CFA744C80675C6743101618E3FFE18FACD33112DCB
 
2025-07-16 21:00:30,183  [DEBUG] [D][11:28:32][CAT1]<<< 
 
2025-07-16 21:00:30,184  [DEBUG] SEND OK
 
2025-07-16 21:00:30,184  [DEBUG] 
 
2025-07-16 21:00:30,188  [DEBUG] [D][11:28:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:30,191  [DEBUG] [D][11:28:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:30,191  [DEBUG] 
 
2025-07-16 21:00:30,197  [DEBUG] [D][11:28:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:30,202  [DEBUG] [D][11:28:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:30,208  [DEBUG] [D][11:28:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:30,211  [DEBUG] [D][11:28:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:30,217  [DEBUG] [D][11:28:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:30,219  [DEBUG] [D][11:28:32][PROT]M2M Send ok [1730201312]
 
2025-07-16 21:00:35,462  [DEBUG] [D][11:28:37][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:35,474  [DEBUG] [D][11:28:37][PROT]index:0 1730201317
 
2025-07-16 21:00:35,477  [DEBUG] [D][11:28:37][PROT]is_send:0
 
2025-07-16 21:00:35,480  [DEBUG] [D][11:28:37][PROT]sequence_num:3
 
2025-07-16 21:00:35,482  [DEBUG] [D][11:28:37][PROT]retry_timeout:0
 
2025-07-16 21:00:35,485  [DEBUG] [D][11:28:37][PROT]retry_times:4
 
2025-07-16 21:00:35,488  [DEBUG] [D][11:28:37][PROT]send_path:0x2
 
2025-07-16 21:00:35,493  [DEBUG] [D][11:28:37][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:35,500  [DEBUG] [D][11:28:37][PROT]===========================================================
 
2025-07-16 21:00:35,505  [DEBUG] [W][11:28:37][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201317]
 
2025-07-16 21:00:35,513  [DEBUG] [D][11:28:37][PROT]===========================================================
 
2025-07-16 21:00:35,520  [DEBUG] [D][11:28:37][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:35,522  [DEBUG] [D][11:28:37][PROT]Send_TO_M2M [1730201317]
 
2025-07-16 21:00:35,528  [DEBUG] [D][11:28:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:35,530  [DEBUG] [D][11:28:37][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:35,536  [DEBUG] [D][11:28:37][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:35,538  [DEBUG] [D][11:28:37][M2M ]m2m send data len[198]
 
2025-07-16 21:00:35,541  [DEBUG] [D][11:28:37][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:35,551  [DEBUG] [D][11:28:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:35,555  [DEBUG] [D][11:28:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:35,561  [DEBUG] [D][11:28:37][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:35,563  [DEBUG] [D][11:28:37][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:35,563  [DEBUG] 
 
2025-07-16 21:00:35,568  [DEBUG] [D][11:28:37][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:35,588  [DEBUG] 0063B986113311331133113311331B88B5376DA48774A2D6F2E903CB54118BC228D53A02E328DE031619E662E5499810707B6E95715F1EBE3E0F0259DB7A1034C0B2C1F9FC3C63548E270382FB46E6C765BFFA358890968538A820C21F3AB19FBA1285
 
2025-07-16 21:00:35,588  [DEBUG] [D][11:28:37][CAT1]<<< 
 
2025-07-16 21:00:35,589  [DEBUG] SEND OK
 
2025-07-16 21:00:35,589  [DEBUG] 
 
2025-07-16 21:00:35,594  [DEBUG] [D][11:28:37][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:35,596  [DEBUG] [D][11:28:37][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:35,597  [DEBUG] 
 
2025-07-16 21:00:35,602  [DEBUG] [D][11:28:37][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:35,608  [DEBUG] [D][11:28:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:35,613  [DEBUG] [D][11:28:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:35,616  [DEBUG] [D][11:28:37][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:35,623  [DEBUG] [D][11:28:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:35,625  [DEBUG] [D][11:28:37][PROT]M2M Send ok [1730201317]
 
2025-07-16 21:00:40,868  [DEBUG] [D][11:28:43][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:40,880  [DEBUG] [D][11:28:43][PROT]index:0 1730201323
 
2025-07-16 21:00:40,884  [DEBUG] [D][11:28:43][PROT]is_send:0
 
2025-07-16 21:00:40,886  [DEBUG] [D][11:28:43][PROT]sequence_num:3
 
2025-07-16 21:00:40,889  [DEBUG] [D][11:28:43][PROT]retry_timeout:0
 
2025-07-16 21:00:40,891  [DEBUG] [D][11:28:43][PROT]retry_times:3
 
2025-07-16 21:00:40,895  [DEBUG] [D][11:28:43][PROT]send_path:0x2
 
2025-07-16 21:00:40,900  [DEBUG] [D][11:28:43][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:40,907  [DEBUG] [D][11:28:43][PROT]===========================================================
 
2025-07-16 21:00:40,912  [DEBUG] [W][11:28:43][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201323]
 
2025-07-16 21:00:40,921  [DEBUG] [D][11:28:43][PROT]===========================================================
 
2025-07-16 21:00:40,926  [DEBUG] [D][11:28:43][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:40,928  [DEBUG] [D][11:28:43][PROT]Send_TO_M2M [1730201323]
 
2025-07-16 21:00:40,934  [DEBUG] [D][11:28:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:40,936  [DEBUG] [D][11:28:43][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:40,944  [DEBUG] [D][11:28:43][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:40,945  [DEBUG] [D][11:28:43][M2M ]m2m send data len[198]
 
2025-07-16 21:00:40,948  [DEBUG] [D][11:28:43][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:40,957  [DEBUG] [D][11:28:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:40,962  [DEBUG] [D][11:28:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:40,967  [DEBUG] [D][11:28:43][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:40,970  [DEBUG] [D][11:28:43][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:40,971  [DEBUG] 
 
2025-07-16 21:00:40,975  [DEBUG] [D][11:28:43][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:40,994  [DEBUG] 0063B989113311331133113311331B88B5FF6EEFDCB323D369393AD99FDCBDE817827C1A14F19CFAF469BBAFF21F36B9C77D25AA685DC7DECDDF0D0FF7322DED2D4DBF152553D4F25699E4863C4CFE232006B46177691CBF8FB9E1B6F7BCEA17860680
 
2025-07-16 21:00:40,995  [DEBUG] [D][11:28:43][CAT1]<<< 
 
2025-07-16 21:00:40,996  [DEBUG] SEND OK
 
2025-07-16 21:00:40,996  [DEBUG] 
 
2025-07-16 21:00:41,001  [DEBUG] [D][11:28:43][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:41,004  [DEBUG] [D][11:28:43][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:41,004  [DEBUG] 
 
2025-07-16 21:00:41,009  [DEBUG] [D][11:28:43][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:41,014  [DEBUG] [D][11:28:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:41,019  [DEBUG] [D][11:28:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:41,023  [DEBUG] [D][11:28:43][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:41,029  [DEBUG] [D][11:28:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:41,032  [DEBUG] [D][11:28:43][PROT]M2M Send ok [1730201323]
 
2025-07-16 21:00:46,275  [DEBUG] [D][11:28:48][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:46,288  [DEBUG] [D][11:28:48][PROT]index:0 1730201328
 
2025-07-16 21:00:46,290  [DEBUG] [D][11:28:48][PROT]is_send:0
 
2025-07-16 21:00:46,293  [DEBUG] [D][11:28:48][PROT]sequence_num:3
 
2025-07-16 21:00:46,296  [DEBUG] [D][11:28:48][PROT]retry_timeout:0
 
2025-07-16 21:00:46,298  [DEBUG] [D][11:28:48][PROT]retry_times:2
 
2025-07-16 21:00:46,301  [DEBUG] [D][11:28:48][PROT]send_path:0x2
 
2025-07-16 21:00:46,307  [DEBUG] [D][11:28:48][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:46,313  [DEBUG] [D][11:28:48][PROT]===========================================================
 
2025-07-16 21:00:46,320  [DEBUG] [W][11:28:48][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201328]
 
2025-07-16 21:00:46,327  [DEBUG] [D][11:28:48][PROT]===========================================================
 
2025-07-16 21:00:46,332  [DEBUG] [D][11:28:48][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:46,335  [DEBUG] [D][11:28:48][PROT]Send_TO_M2M [1730201328]
 
2025-07-16 21:00:46,341  [DEBUG] [D][11:28:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:46,343  [DEBUG] [D][11:28:48][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:46,349  [DEBUG] [D][11:28:48][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:46,351  [DEBUG] [D][11:28:48][M2M ]m2m send data len[198]
 
2025-07-16 21:00:46,354  [DEBUG] [D][11:28:48][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:46,364  [DEBUG] [D][11:28:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:46,368  [DEBUG] [D][11:28:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:46,374  [DEBUG] [D][11:28:48][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:46,376  [DEBUG] [D][11:28:48][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:46,377  [DEBUG] 
 
2025-07-16 21:00:46,382  [DEBUG] [D][11:28:48][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:46,400  [DEBUG] 0063B987113311331133113311331B88B527905B6FECDB7FFA4F917CA16CEBBC6CB5609BBD0290B23D3B871E4BF565D9F2F440043170671DA520EE8357486F4AF703B29E1CE7A433A5020BFD5279C6D42D8164EAB7F29F55C9ED8D4EC75341DC805461
 
2025-07-16 21:00:46,401  [DEBUG] [D][11:28:48][CAT1]<<< 
 
2025-07-16 21:00:46,401  [DEBUG] SEND OK
 
2025-07-16 21:00:46,402  [DEBUG] 
 
2025-07-16 21:00:46,407  [DEBUG] [D][11:28:48][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:46,409  [DEBUG] [D][11:28:48][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:46,411  [DEBUG] 
 
2025-07-16 21:00:46,415  [DEBUG] [D][11:28:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:46,421  [DEBUG] [D][11:28:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:46,426  [DEBUG] [D][11:28:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:46,430  [DEBUG] [D][11:28:48][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:46,435  [DEBUG] [D][11:28:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:46,438  [DEBUG] [D][11:28:48][PROT]M2M Send ok [1730201328]
 
2025-07-16 21:00:51,681  [DEBUG] [D][11:28:53][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:51,693  [DEBUG] [D][11:28:53][PROT]index:0 1730201333
 
2025-07-16 21:00:51,695  [DEBUG] [D][11:28:53][PROT]is_send:0
 
2025-07-16 21:00:51,698  [DEBUG] [D][11:28:53][PROT]sequence_num:3
 
2025-07-16 21:00:51,700  [DEBUG] [D][11:28:53][PROT]retry_timeout:0
 
2025-07-16 21:00:51,704  [DEBUG] [D][11:28:53][PROT]retry_times:1
 
2025-07-16 21:00:51,707  [DEBUG] [D][11:28:53][PROT]send_path:0x2
 
2025-07-16 21:00:51,712  [DEBUG] [D][11:28:53][PROT]min_index:0, type:0x0306, priority:3
 
2025-07-16 21:00:51,718  [DEBUG] [D][11:28:53][PROT]===========================================================
 
2025-07-16 21:00:51,724  [DEBUG] [W][11:28:53][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201333]
 
2025-07-16 21:00:51,733  [DEBUG] [D][11:28:53][PROT]===========================================================
 
2025-07-16 21:00:51,738  [DEBUG] [D][11:28:53][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:00:51,740  [DEBUG] [D][11:28:53][PROT]Send_TO_M2M [1730201333]
 
2025-07-16 21:00:51,746  [DEBUG] [D][11:28:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:51,748  [DEBUG] [D][11:28:53][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:51,755  [DEBUG] [D][11:28:53][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:51,757  [DEBUG] [D][11:28:53][M2M ]m2m send data len[198]
 
2025-07-16 21:00:51,760  [DEBUG] [D][11:28:53][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:51,769  [DEBUG] [D][11:28:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a058] format[0]
 
2025-07-16 21:00:51,774  [DEBUG] [D][11:28:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:51,779  [DEBUG] [D][11:28:53][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:51,781  [DEBUG] [D][11:28:53][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:00:51,783  [DEBUG] 
 
2025-07-16 21:00:51,787  [DEBUG] [D][11:28:53][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:00:51,807  [DEBUG] 0063B984113311331133113311331B88B527B76A8CE1A29CCCBF94EA796136EEE63814809E71223991AB33B6612800AA89A8B66E48A461EDF2CC73F4EBC5396E491D63F63768B463BBA68D3B2A081D392FEE1E637A68E06EF56710552D8C63D66B802C
 
2025-07-16 21:00:51,807  [DEBUG] [D][11:28:53][CAT1]<<< 
 
2025-07-16 21:00:51,808  [DEBUG] SEND OK
 
2025-07-16 21:00:51,808  [DEBUG] 
 
2025-07-16 21:00:51,813  [DEBUG] [D][11:28:53][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:51,815  [DEBUG] [D][11:28:53][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:51,815  [DEBUG] 
 
2025-07-16 21:00:51,821  [DEBUG] [D][11:28:53][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:51,827  [DEBUG] [D][11:28:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:51,832  [DEBUG] [D][11:28:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:51,835  [DEBUG] [D][11:28:53][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:51,840  [DEBUG] [D][11:28:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:51,843  [DEBUG] [D][11:28:53][PROT]M2M Send ok [1730201333]
 
2025-07-16 21:00:57,084  [DEBUG] [D][11:28:59][PROT]CLEAN,SEND:0
 
2025-07-16 21:00:57,094  [DEBUG] [D][11:28:59][PROT]CLEAN:0
 
2025-07-16 21:00:57,107  [DEBUG] [D][11:28:59][PROT]index:1 1730201339
 
2025-07-16 21:00:57,109  [DEBUG] [D][11:28:59][PROT]is_send:0
 
2025-07-16 21:00:57,112  [DEBUG] [D][11:28:59][PROT]sequence_num:4
 
2025-07-16 21:00:57,115  [DEBUG] [D][11:28:59][PROT]retry_timeout:0
 
2025-07-16 21:00:57,118  [DEBUG] [D][11:28:59][PROT]retry_times:3
 
2025-07-16 21:00:57,120  [DEBUG] [D][11:28:59][PROT]send_path:0x2
 
2025-07-16 21:00:57,127  [DEBUG] [D][11:28:59][PROT]min_index:1, type:0x6A01, priority:0
 
2025-07-16 21:00:57,133  [DEBUG] [D][11:28:59][PROT]===========================================================
 
2025-07-16 21:00:57,138  [DEBUG] [W][11:28:59][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201339]
 
2025-07-16 21:00:57,147  [DEBUG] [D][11:28:59][PROT]===========================================================
 
2025-07-16 21:00:57,149  [DEBUG] [D][11:28:59][PROT]sending traceid [00]
 
2025-07-16 21:00:57,155  [DEBUG] [D][11:28:59][PROT]Send_TO_M2M [1730201339]
 
2025-07-16 21:00:57,158  [DEBUG] [D][11:28:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:00:57,163  [DEBUG] [D][11:28:59][SAL ]sock send credit cnt[6]
 
2025-07-16 21:00:57,166  [DEBUG] [D][11:28:59][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:00:57,168  [DEBUG] [D][11:28:59][M2M ]m2m send data len[102]
 
2025-07-16 21:00:57,174  [DEBUG] [D][11:28:59][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:00:57,183  [DEBUG] [D][11:28:59][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a058] format[0]
 
2025-07-16 21:00:57,188  [DEBUG] [D][11:28:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:00:57,191  [DEBUG] [D][11:28:59][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:00:57,196  [DEBUG] [D][11:28:59][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:00:57,197  [DEBUG] 
 
2025-07-16 21:00:57,201  [DEBUG] [D][11:28:59][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:00:57,211  [DEBUG] 0033B98B113311331188BB88BB88BB88BB45D6D980D71B270834A9942274282319BA01E39F0E044CDD18341A782C791BA6C595
 
2025-07-16 21:00:57,213  [DEBUG] [D][11:28:59][CAT1]<<< 
 
2025-07-16 21:00:57,213  [DEBUG] SEND OK
 
2025-07-16 21:00:57,214  [DEBUG] 
 
2025-07-16 21:00:57,219  [DEBUG] [D][11:28:59][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:00:57,221  [DEBUG] [D][11:28:59][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:00:57,222  [DEBUG] 
 
2025-07-16 21:00:57,224  [DEBUG] [D][11:28:59][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:00:57,233  [DEBUG] [D][11:28:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:00:57,234  [DEBUG] [D][11:28:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:00:57,239  [DEBUG] [D][11:28:59][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:00:57,244  [DEBUG] [D][11:28:59][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:00:57,248  [DEBUG] [D][11:28:59][PROT]M2M Send ok [1730201339]
 
2025-07-16 21:01:02,498  [DEBUG] [D][11:29:04][PROT]CLEAN,SEND:1
 
2025-07-16 21:01:02,511  [DEBUG] [D][11:29:04][PROT]index:1 1730201344
 
2025-07-16 21:01:02,514  [DEBUG] [D][11:29:04][PROT]is_send:0
 
2025-07-16 21:01:02,517  [DEBUG] [D][11:29:04][PROT]sequence_num:4
 
2025-07-16 21:01:02,519  [DEBUG] [D][11:29:04][PROT]retry_timeout:0
 
2025-07-16 21:01:02,522  [DEBUG] [D][11:29:04][PROT]retry_times:2
 
2025-07-16 21:01:02,526  [DEBUG] [D][11:29:04][PROT]send_path:0x2
 
2025-07-16 21:01:02,531  [DEBUG] [D][11:29:04][PROT]min_index:1, type:0x6A01, priority:0
 
2025-07-16 21:01:02,537  [DEBUG] [D][11:29:04][PROT]===========================================================
 
2025-07-16 21:01:02,542  [DEBUG] [W][11:29:04][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201344]
 
2025-07-16 21:01:02,550  [DEBUG] [D][11:29:04][PROT]===========================================================
 
2025-07-16 21:01:02,553  [DEBUG] [D][11:29:04][PROT]sending traceid [00]
 
2025-07-16 21:01:02,558  [DEBUG] [D][11:29:04][PROT]Send_TO_M2M [1730201344]
 
2025-07-16 21:01:02,561  [DEBUG] [D][11:29:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:02,567  [DEBUG] [D][11:29:04][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:02,569  [DEBUG] [D][11:29:04][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:02,572  [DEBUG] [D][11:29:04][M2M ]m2m send data len[102]
 
2025-07-16 21:01:02,579  [DEBUG] [D][11:29:04][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:02,588  [DEBUG] [D][11:29:04][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a058] format[0]
 
2025-07-16 21:01:02,592  [DEBUG] [D][11:29:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:02,594  [DEBUG] [D][11:29:04][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:02,602  [DEBUG] [D][11:29:04][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:02,602  [DEBUG] 
 
2025-07-16 21:01:02,605  [DEBUG] [D][11:29:04][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:02,614  [DEBUG] 0033B9A8113311331188BB88BB88BB88BB66E8E3EA8FDF426FE34BC847BD9DA5416216B85688C65C83D39E16A8A67AADFEDDAC
 
2025-07-16 21:01:02,617  [DEBUG] [D][11:29:04][CAT1]<<< 
 
2025-07-16 21:01:02,618  [DEBUG] SEND OK
 
2025-07-16 21:01:02,618  [DEBUG] 
 
2025-07-16 21:01:02,623  [DEBUG] [D][11:29:04][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:02,625  [DEBUG] [D][11:29:04][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:02,626  [DEBUG] 
 
2025-07-16 21:01:02,628  [DEBUG] [D][11:29:04][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:02,636  [DEBUG] [D][11:29:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:02,638  [DEBUG] [D][11:29:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:02,642  [DEBUG] [D][11:29:04][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:02,649  [DEBUG] [D][11:29:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:02,651  [DEBUG] [D][11:29:04][PROT]M2M Send ok [1730201344]
 
2025-07-16 21:01:03,511  [DEBUG] [D][11:29:05][COMM]Main Task receive event:118
 
2025-07-16 21:01:03,516  [DEBUG] [D][11:29:05][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:01:03,522  [DEBUG] [D][11:29:05][COMM]frm_mc_bat_power_on EXT_BAT_STATE_POWERON_TIMEOUT, bat is on:0
 
2025-07-16 21:01:03,529  [DEBUG] [D][11:29:05][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON, Ext48v = 2.
 
2025-07-16 21:01:03,532  [DEBUG] [D][11:29:05][COMM]open bat mos
 
2025-07-16 21:01:03,535  [DEBUG] [D][11:29:05][COMM]1x1 tx_id:3,7, tx_len:2
 
2025-07-16 21:01:03,539  [DEBUG] [D][11:29:05][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:01:03,915  [DEBUG] [E][11:29:06][COMM]1x1 rx timeout
 
2025-07-16 21:01:03,916  [DEBUG] [D][11:29:06][COMM]1x1 frm_can_tp_send ok
 
2025-07-16 21:01:04,319  [DEBUG] [E][11:29:06][COMM]1x1 rx timeout
 
2025-07-16 21:01:04,325  [DEBUG] [D][11:29:06][HSDK][0] flush to flash addr:[0xE45F00] --- write len --- [256]
 
2025-07-16 21:01:04,326  [DEBUG] [E][11:29:06][COMM]1x1 tp timeout
 
2025-07-16 21:01:04,329  [DEBUG] [E][11:29:06][COMM]1x1 error -3.
 
2025-07-16 21:01:04,334  [DEBUG] [E][11:29:06][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:01:04,340  [DEBUG] [D][11:29:06][COMM]frm_peripheral_device_poweron type 12.... 
 
2025-07-16 21:01:04,343  [DEBUG] [D][11:29:06][COMM]get Acckey 1 and value:1
 
2025-07-16 21:01:04,345  [DEBUG] [D][11:29:06][COMM]get mc hw info fail
 
2025-07-16 21:01:04,349  [DEBUG] [D][11:29:06][COMM]get bat hw info fail
 
2025-07-16 21:01:04,355  [DEBUG] [D][11:29:06][COMM]get helmet hw info fail,rt:-3
 
2025-07-16 21:01:04,360  [DEBUG] [D][11:29:06][COMM]get helmet rope hw info fail,rt:-3
 
2025-07-16 21:01:04,361  [DEBUG] 
 
2025-07-16 21:01:04,362  [DEBUG] [D][11:29:06][COMM]get weight hw info fail,rt:-3
 
2025-07-16 21:01:04,369  [DEBUG] [D][11:29:06][COMM][9101][unknown camera type:0]
 
2025-07-16 21:01:04,371  [DEBUG] [D][11:29:06][COMM]get arm version info succ
 
2025-07-16 21:01:04,377  [DEBUG] [D][11:29:06][COMM][9158][unknown camera type:0]
 
2025-07-16 21:01:04,382  [DEBUG] [E][11:29:06][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 21:01:04,388  [DEBUG] [D][11:29:06][COMM]frm_rfid_get_hwinfo hw ver err,rt:0x204
 
2025-07-16 21:01:04,393  [DEBUG] [D][11:29:06][COMM]frm_rfid_get_hwinfo sw ver err,rt:0x204
 
2025-07-16 21:01:04,399  [DEBUG] [D][11:29:06][COMM]frm_rfid_get_hwinfo boot ver err,rt:0x204
 
2025-07-16 21:01:04,404  [DEBUG] [D][11:29:06][COMM]frm_rfid_get_hwinfo id err,rt:0x204
 
2025-07-16 21:01:04,407  [DEBUG] [D][11:29:06][COMM]get rfid hw info fail,rt:-1
 
2025-07-16 21:01:04,408  [DEBUG] 
 
2025-07-16 21:01:04,412  [DEBUG] [D][11:29:06][COMM]frm_peripheral_device_poweroff type 11.... 
 
2025-07-16 21:01:04,423  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:01:04,432  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[5E01],priority[3],index[0],used[1]
 
2025-07-16 21:01:04,434  [DEBUG] [D][11:29:06][COMM]a:5,b:7,c:255
 
2025-07-16 21:01:04,444  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:01:04,452  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 21:01:04,454  [DEBUG] [D][11:29:06][CAT1]gsm read msg sub id: 13
 
2025-07-16 21:01:04,460  [DEBUG] [D][11:29:06][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:01:04,461  [DEBUG] 
 
2025-07-16 21:01:04,461  [DEBUG] [D][11:29:06][CAT1]<<< 
 
2025-07-16 21:01:04,462  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:01:04,462  [DEBUG] 
 
2025-07-16 21:01:04,464  [DEBUG] OK
 
2025-07-16 21:01:04,464  [DEBUG] 
 
2025-07-16 21:01:04,468  [DEBUG] [D][11:29:06][CAT1]exec over: func id: 13, ret: 21
 
2025-07-16 21:01:04,469  [DEBUG] [D][11:29:06][M2M ]get csq[31]
 
2025-07-16 21:01:04,625  [DEBUG] [D][11:29:06][COMM]lastHelmetOrderIdV2:00
 
2025-07-16 21:01:04,629  [DEBUG] >>>>>RESEND ALLSTATE<<<<<
 
2025-07-16 21:01:04,636  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:01:04,645  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 21:01:04,648  [DEBUG] [D][11:29:06][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:01:04,654  [DEBUG] [D][11:29:06][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:01:04,659  [DEBUG] [D][11:29:06][HSDK][0] flush to flash addr:[0xE46000] --- write len --- [256]
 
2025-07-16 21:01:04,661  [DEBUG] [W][11:29:06][COMM]get soc error
 
2025-07-16 21:01:04,664  [DEBUG] [W][11:29:06][GNSS]stop locating
 
2025-07-16 21:01:04,669  [DEBUG] [D][11:29:06][GNSS]all continue location stop
 
2025-07-16 21:01:04,673  [DEBUG] [W][11:29:06][GNSS]sing locating running
 
2025-07-16 21:01:04,679  [DEBUG] [E][11:29:06][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:01:04,684  [DEBUG] [D][11:29:06][COMM]report elecbike, soc 0, reason 2
 
2025-07-16 21:01:04,692  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:01:04,701  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 21:01:04,706  [DEBUG] [D][11:29:06][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:01:04,709  [DEBUG] [D][11:29:06][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:01:04,717  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:01:04,725  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 21:01:04,738  [DEBUG] [D][11:29:06][COMM]buried data a:10,b:2,c:255,d:0,f:16843008,k:5535,l:151,m:11,n:11,o:7,p:1676,q:2149,r:5516,z:665
 
2025-07-16 21:01:04,745  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 21:01:04,753  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 21:01:04,760  [DEBUG] [D][11:29:06][COMM]Main Task receive event:118 finished processing
 
2025-07-16 21:01:04,766  [DEBUG] [D][11:29:06][COMM]Main Task receive event:54
 
2025-07-16 21:01:04,768  [DEBUG] [D][11:29:06][COMM][D301]:type:1, trace id:20017150
 
2025-07-16 21:01:04,773  [DEBUG] [D][11:29:06][COMM]get bat basic info err
 
2025-07-16 21:01:04,779  [DEBUG] [D][11:29:06][HSDK][0] flush to flash addr:[0xE46100] --- write len --- [256]
 
2025-07-16 21:01:04,787  [DEBUG] [W][11:29:06][PROT]remove success[1730201346],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 21:01:04,796  [DEBUG] [W][11:29:06][PROT]add success [1730201346],send_path[2],type[D302],priority[0],index[8],used[1]
 
2025-07-16 21:01:04,803  [DEBUG] [D][11:29:06][COMM]Main Task receive event:54 finished processing
 
2025-07-16 21:01:07,907  [DEBUG] [D][11:29:10][PROT]CLEAN,SEND:1
 
2025-07-16 21:01:07,919  [DEBUG] [D][11:29:10][PROT]index:0 1730201350
 
2025-07-16 21:01:07,922  [DEBUG] [D][11:29:10][PROT]is_send:0
 
2025-07-16 21:01:07,925  [DEBUG] [D][11:29:10][PROT]sequence_num:6
 
2025-07-16 21:01:07,928  [DEBUG] [D][11:29:10][PROT]retry_timeout:0
 
2025-07-16 21:01:07,930  [DEBUG] [D][11:29:10][PROT]retry_times:3
 
2025-07-16 21:01:07,933  [DEBUG] [D][11:29:10][PROT]send_path:0x2
 
2025-07-16 21:01:07,938  [DEBUG] [D][11:29:10][PROT]min_index:0, type:0x5E01, priority:3
 
2025-07-16 21:01:07,944  [DEBUG] [D][11:29:10][PROT]===========================================================
 
2025-07-16 21:01:07,950  [DEBUG] [W][11:29:10][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201350]
 
2025-07-16 21:01:07,959  [DEBUG] [D][11:29:10][PROT]===========================================================
 
2025-07-16 21:01:07,964  [DEBUG] [D][11:29:10][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:07,967  [DEBUG] [D][11:29:10][PROT]Send_TO_M2M [1730201350]
 
2025-07-16 21:01:07,972  [DEBUG] [D][11:29:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:07,974  [DEBUG] [D][11:29:10][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:07,981  [DEBUG] [D][11:29:10][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:07,982  [DEBUG] [D][11:29:10][M2M ]m2m send data len[678]
 
2025-07-16 21:01:07,986  [DEBUG] [D][11:29:10][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:07,995  [DEBUG] [D][11:29:10][SAL ]cellular SEND socket id[0] type[1], len[678], data[0x2005a058] format[0]
 
2025-07-16 21:01:08,000  [DEBUG] [D][11:29:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:08,005  [DEBUG] [D][11:29:10][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:08,009  [DEBUG] [D][11:29:10][CAT1]tx ret[17] >>> AT+QISEND=0,678
 
2025-07-16 21:01:08,009  [DEBUG] 
 
2025-07-16 21:01:08,013  [DEBUG] [D][11:29:10][CAT1]Send Data To Server[678][678] ... ->:
 
2025-07-16 21:01:08,079  [DEBUG] 0153B9AA113311331133113311331B88B5B5D0C7286200D529928FBA31FC3C3A67ED0C7957674C7E9C43D95C6C81F62C6905A43880E47256F9BE52CA5791C312BEFE0E9EAB96F0796DE38703ED8AE377E63FE22C31F831BA5A5269DE2441E760B166F71DDDAB067D20019904B15985640591B0137C2F925ED742842115C9821A0F64E113176EB3B68BE07B0C00FC927DB7DE30EE687159BBA51CE071A5916D1A5A601D12CB2489B58BFE9ECDFE008B618E778C1AC46C3B0B70067551CCA53EE684771405F0B0FB2B0AC992EEEE76F01318BBCB38B8F3A5F2E57154896C153F035C766D1D82498B0B2CD9BFD2DCB2F3780265B27B01B399F8A181F2247B4B0FF468BB96715C42AEF9A2922DB0413060DEC203E023EAD6FE3E06BAB728465F5F8FB0EA2EC244C0FCE9E7E2EA76D4FA376BE646F309F511ABF7C6EB6A02212969418A21E4DB8A488C9209703F3E2599588E49DEBC
 
2025-07-16 21:01:08,080  [DEBUG] [D][11:29:10][CAT1]<<< 
 
2025-07-16 21:01:08,081  [DEBUG] SEND OK
 
2025-07-16 21:01:08,081  [DEBUG] 
 
2025-07-16 21:01:08,081  [DEBUG] [D][11:29:10][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:08,084  [DEBUG] [D][11:29:10][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:08,084  [DEBUG] 
 
2025-07-16 21:01:08,089  [DEBUG] [D][11:29:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:08,094  [DEBUG] [D][11:29:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:08,099  [DEBUG] [D][11:29:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:08,103  [DEBUG] [D][11:29:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:08,108  [DEBUG] [D][11:29:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:08,111  [DEBUG] [D][11:29:10][PROT]M2M Send ok [1730201350]
 
2025-07-16 21:01:12,013  [DEBUG] [D][11:29:14][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 1.
 
2025-07-16 21:01:13,321  [DEBUG] [D][11:29:15][PROT]CLEAN,SEND:0
 
2025-07-16 21:01:13,334  [DEBUG] [D][11:29:15][PROT]index:0 1730201355
 
2025-07-16 21:01:13,337  [DEBUG] [D][11:29:15][PROT]is_send:0
 
2025-07-16 21:01:13,339  [DEBUG] [D][11:29:15][PROT]sequence_num:6
 
2025-07-16 21:01:13,342  [DEBUG] [D][11:29:15][PROT]retry_timeout:0
 
2025-07-16 21:01:13,345  [DEBUG] [D][11:29:15][PROT]retry_times:2
 
2025-07-16 21:01:13,347  [DEBUG] [D][11:29:15][PROT]send_path:0x2
 
2025-07-16 21:01:13,353  [DEBUG] [D][11:29:15][PROT]min_index:0, type:0x5E01, priority:3
 
2025-07-16 21:01:13,360  [DEBUG] [D][11:29:15][PROT]===========================================================
 
2025-07-16 21:01:13,365  [DEBUG] [W][11:29:15][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201355]
 
2025-07-16 21:01:13,374  [DEBUG] [D][11:29:15][PROT]===========================================================
 
2025-07-16 21:01:13,380  [DEBUG] [D][11:29:15][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:13,382  [DEBUG] [D][11:29:15][PROT]Send_TO_M2M [1730201355]
 
2025-07-16 21:01:13,387  [DEBUG] [D][11:29:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:13,389  [DEBUG] [D][11:29:15][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:13,396  [DEBUG] [D][11:29:15][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:13,398  [DEBUG] [D][11:29:15][M2M ]m2m send data len[678]
 
2025-07-16 21:01:13,401  [DEBUG] [D][11:29:15][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:13,410  [DEBUG] [D][11:29:15][SAL ]cellular SEND socket id[0] type[1], len[678], data[0x2005a058] format[0]
 
2025-07-16 21:01:13,416  [DEBUG] [D][11:29:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:13,420  [DEBUG] [D][11:29:15][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:13,423  [DEBUG] [D][11:29:15][CAT1]tx ret[17] >>> AT+QISEND=0,678
 
2025-07-16 21:01:13,424  [DEBUG] 
 
2025-07-16 21:01:13,428  [DEBUG] [D][11:29:15][CAT1]Send Data To Server[678][678] ... ->:
 
2025-07-16 21:01:13,494  [DEBUG] 0153B9AF113311331133113311331B88B5E5A60B8278178B641F23A17DA3FC17FA0049A6B99C13B7D3ECA0CE82CDD4E0DBD53A3FFE7D3820F60D259FA772C366BE13E80474758D152E15A5510B5734F8C82B97CEE1527DE5C385BEB1DEDA3CE5176EB1BFA7A92D8F990A2CA63A3A9149F68FCC9C39202A45BE4CB47CFA3B41B764D4328E4097D71EE6C233D56B4153DA93D680F270D05CF6446CEDD0AF5866F6982E4C772563D1A97CB2702D43F1E9C4CAA9494C5C7C9AA747092768576234DFDC318CC2B8378AE2AD38DA89809D0B552A1C7D9BB3A7E0340D4F834BCD7C5820C4DF23BC4B940AADD9A98E1BBF5DABA05141FA60B501F663E98F1F34DB8D29CA1A5D2E1EE18587FFFBBBF71E0AE218FBAEAD80DBDF7C1BB2EAC49785905DAA4022BFD8FD4FDEAC285A8482B74AE4C1CE5EFEAB49AA531EEFAA3A016E3FA29A8C8768FD39EF575F5FC7C9FC71828E8E4A69ABA4
 
2025-07-16 21:01:13,495  [DEBUG] [D][11:29:15][CAT1]<<< 
 
2025-07-16 21:01:13,496  [DEBUG] SEND OK
 
2025-07-16 21:01:13,496  [DEBUG] 
 
2025-07-16 21:01:13,496  [DEBUG] [D][11:29:15][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:13,499  [DEBUG] [D][11:29:15][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:13,500  [DEBUG] 
 
2025-07-16 21:01:13,504  [DEBUG] [D][11:29:15][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:13,510  [DEBUG] [D][11:29:15][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:13,514  [DEBUG] [D][11:29:15][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:13,518  [DEBUG] [D][11:29:15][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:13,523  [DEBUG] [D][11:29:15][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:13,526  [DEBUG] [D][11:29:15][PROT]M2M Send ok [1730201355]
 
2025-07-16 21:01:18,738  [DEBUG] [D][11:29:20][PROT]CLEAN,SEND:0
 
2025-07-16 21:01:18,751  [DEBUG] [D][11:29:20][PROT]index:0 1730201360
 
2025-07-16 21:01:18,753  [DEBUG] [D][11:29:20][PROT]is_send:0
 
2025-07-16 21:01:18,757  [DEBUG] [D][11:29:20][PROT]sequence_num:6
 
2025-07-16 21:01:18,760  [DEBUG] [D][11:29:20][PROT]retry_timeout:0
 
2025-07-16 21:01:18,762  [DEBUG] [D][11:29:20][PROT]retry_times:1
 
2025-07-16 21:01:18,765  [DEBUG] [D][11:29:20][PROT]send_path:0x2
 
2025-07-16 21:01:18,770  [DEBUG] [D][11:29:20][PROT]min_index:0, type:0x5E01, priority:3
 
2025-07-16 21:01:18,777  [DEBUG] [D][11:29:20][PROT]===========================================================
 
2025-07-16 21:01:18,782  [DEBUG] [W][11:29:20][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201360]
 
2025-07-16 21:01:18,790  [DEBUG] [D][11:29:20][PROT]===========================================================
 
2025-07-16 21:01:18,795  [DEBUG] [D][11:29:20][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:18,798  [DEBUG] [D][11:29:20][PROT]Send_TO_M2M [1730201360]
 
2025-07-16 21:01:18,805  [DEBUG] [D][11:29:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:18,807  [DEBUG] [D][11:29:20][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:18,812  [DEBUG] [D][11:29:20][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:18,815  [DEBUG] [D][11:29:20][M2M ]m2m send data len[678]
 
2025-07-16 21:01:18,817  [DEBUG] [D][11:29:20][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:18,827  [DEBUG] [D][11:29:20][SAL ]cellular SEND socket id[0] type[1], len[678], data[0x2005a058] format[0]
 
2025-07-16 21:01:18,832  [DEBUG] [D][11:29:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:18,838  [DEBUG] [D][11:29:20][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:18,841  [DEBUG] [D][11:29:20][CAT1]tx ret[17] >>> AT+QISEND=0,678
 
2025-07-16 21:01:18,841  [DEBUG] 
 
2025-07-16 21:01:18,846  [DEBUG] [D][11:29:20][CAT1]Send Data To Server[678][678] ... ->:
 
2025-07-16 21:01:18,911  [DEBUG] 0153B9A0113311331133113311331B88B5E036616840F50032278D1C208A3029F80D6EC6724D592ADCCDD454E4DEFC115238E137241635F1AE9D1CE1AF835E6990366B19317FA3B218D7D8264061EA0274A9917C53C4FA076C43802552EB21805CDDDE22565DF32983B933271E4341674F0D69520AC96803B98273BBE2386C89B576E7BEA11E94619E212F74DDD98A631D9E179137BF808D01BFC99DD36413D0E752DDED1010E5930B6AC98EAE530BBC5685F164CD3BC3319219B624C057788556F0A40479F50BE3DDD95E4A3557EE8895052D79450ACEA392EFD846BC1F678BF9411EE7F098C8E00F4BD83F657C6F3FC7DF86C9F8B1A58E5867029480047C2827CEB04A16DB7E5AD91C0175D6D47966C772C67E15CD8134E6336BC7FE084A0505E908B3E1F73F9920B995F79ACD3C0179C57954A7E5179D8B0A10640EEDF7A2A7C6EC1F5A4744C46D3722695C97C457A50F14
 
2025-07-16 21:01:18,912  [DEBUG] [D][11:29:21][CAT1]<<< 
 
2025-07-16 21:01:18,913  [DEBUG] SEND OK
 
2025-07-16 21:01:18,913  [DEBUG] 
 
2025-07-16 21:01:18,914  [DEBUG] [D][11:29:21][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:18,916  [DEBUG] [D][11:29:21][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:18,917  [DEBUG] 
 
2025-07-16 21:01:18,921  [DEBUG] [D][11:29:21][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:18,927  [DEBUG] [D][11:29:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:18,932  [DEBUG] [D][11:29:21][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:18,936  [DEBUG] [D][11:29:21][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:18,941  [DEBUG] [D][11:29:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:18,943  [DEBUG] [D][11:29:21][PROT]M2M Send ok [1730201361]
 
2025-07-16 21:01:24,154  [DEBUG] [D][11:29:26][PROT]CLEAN,SEND:0
 
2025-07-16 21:01:24,165  [DEBUG] [D][11:29:26][PROT]CLEAN:0
 
2025-07-16 21:01:24,176  [DEBUG] [D][11:29:26][PROT]index:5 1730201366
 
2025-07-16 21:01:24,179  [DEBUG] [D][11:29:26][PROT]is_send:0
 
2025-07-16 21:01:24,182  [DEBUG] [D][11:29:26][PROT]sequence_num:9
 
2025-07-16 21:01:24,185  [DEBUG] [D][11:29:26][PROT]retry_timeout:0
 
2025-07-16 21:01:24,187  [DEBUG] [D][11:29:26][PROT]retry_times:3
 
2025-07-16 21:01:24,190  [DEBUG] [D][11:29:26][PROT]send_path:0x2
 
2025-07-16 21:01:24,196  [DEBUG] [D][11:29:26][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:01:24,201  [DEBUG] [D][11:29:26][PROT]===========================================================
 
2025-07-16 21:01:24,208  [DEBUG] [W][11:29:26][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201366]
 
2025-07-16 21:01:24,216  [DEBUG] [D][11:29:26][PROT]===========================================================
 
2025-07-16 21:01:24,218  [DEBUG] [D][11:29:26][COMM]PB encode data:29
 
2025-07-16 21:01:24,224  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:01:24,230  [DEBUG] [D][11:29:26][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:24,233  [DEBUG] [D][11:29:26][PROT]Send_TO_M2M [1730201366]
 
2025-07-16 21:01:24,238  [DEBUG] [D][11:29:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:24,241  [DEBUG] [D][11:29:26][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:24,246  [DEBUG] [D][11:29:26][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:24,249  [DEBUG] [D][11:29:26][M2M ]m2m send data len[134]
 
2025-07-16 21:01:24,251  [DEBUG] [D][11:29:26][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:24,260  [DEBUG] [D][11:29:26][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x2005a058] format[0]
 
2025-07-16 21:01:24,266  [DEBUG] [D][11:29:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:24,272  [DEBUG] [D][11:29:26][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:24,274  [DEBUG] [D][11:29:26][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:01:24,275  [DEBUG] 
 
2025-07-16 21:01:24,279  [DEBUG] [D][11:29:26][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:01:24,292  [DEBUG] 0043B6AD113311331133113311331B88B5B8235811E3D8721922B56859C8A409BAFF5FDB1FC3A4CA1FB658B89947CB0771751C03688245677807252926E22BE09FD769
 
2025-07-16 21:01:24,293  [DEBUG] [D][11:29:26][CAT1]<<< 
 
2025-07-16 21:01:24,294  [DEBUG] SEND OK
 
2025-07-16 21:01:24,294  [DEBUG] 
 
2025-07-16 21:01:24,301  [DEBUG] [D][11:29:26][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:24,302  [DEBUG] [D][11:29:26][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:24,302  [DEBUG] 
 
2025-07-16 21:01:24,307  [DEBUG] [D][11:29:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:24,313  [DEBUG] [D][11:29:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:24,318  [DEBUG] [D][11:29:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:24,321  [DEBUG] [D][11:29:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:24,328  [DEBUG] [D][11:29:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:24,330  [DEBUG] [D][11:29:26][PROT]M2M Send ok [1730201366]
 
2025-07-16 21:01:26,192  [DEBUG] [D][11:29:28][CAT1]closed : 0
 
2025-07-16 21:01:26,197  [DEBUG] [D][11:29:28][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:01:26,199  [DEBUG] [D][11:29:28][SAL ]socket closed id[0]
 
2025-07-16 21:01:26,206  [DEBUG] [D][11:29:28][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:01:26,211  [DEBUG] [D][11:29:28][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:01:26,215  [DEBUG] [D][11:29:28][M2M ]m2m select fd[4]
 
2025-07-16 21:01:26,220  [DEBUG] [D][11:29:28][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:01:26,223  [DEBUG] [D][11:29:28][M2M ]tcpclient close[4]
 
2025-07-16 21:01:26,226  [DEBUG] [D][11:29:28][SAL ]socket[4] has closed
 
2025-07-16 21:01:26,231  [DEBUG] [D][11:29:28][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:01:26,238  [DEBUG] [D][11:29:28][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
 
2025-07-16 21:01:26,240  [DEBUG] [D][11:29:28][COMM]Main Task receive event:86
 
2025-07-16 21:01:26,247  [DEBUG] [W][11:29:28][PROT]remove success[1730201368],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:01:26,259  [DEBUG] [W][11:29:28][PROT]add success [1730201368],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:01:26,263  [DEBUG] [D][11:29:28][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:01:29,572  [DEBUG] [D][11:29:31][PROT]CLEAN,SEND:5
 
2025-07-16 21:01:29,584  [DEBUG] [D][11:29:31][PROT]index:5 1730201371
 
2025-07-16 21:01:29,587  [DEBUG] [D][11:29:31][PROT]is_send:0
 
2025-07-16 21:01:29,590  [DEBUG] [D][11:29:31][PROT]sequence_num:9
 
2025-07-16 21:01:29,593  [DEBUG] [D][11:29:31][PROT]retry_timeout:0
 
2025-07-16 21:01:29,595  [DEBUG] [D][11:29:31][PROT]retry_times:2
 
2025-07-16 21:01:29,598  [DEBUG] [D][11:29:31][PROT]send_path:0x2
 
2025-07-16 21:01:29,603  [DEBUG] [D][11:29:31][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:01:29,609  [DEBUG] [D][11:29:31][PROT]===========================================================
 
2025-07-16 21:01:29,615  [DEBUG] [W][11:29:31][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201371]
 
2025-07-16 21:01:29,624  [DEBUG] [D][11:29:31][PROT]===========================================================
 
2025-07-16 21:01:29,626  [DEBUG] [D][11:29:31][COMM]PB encode data:29
 
2025-07-16 21:01:29,632  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:01:29,637  [DEBUG] [D][11:29:31][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:29,640  [DEBUG] [D][11:29:31][PROT]Send_TO_M2M [1730201371]
 
2025-07-16 21:01:29,645  [DEBUG] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:29,651  [DEBUG] [E][11:29:31][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:01:29,653  [DEBUG] [E][11:29:31][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:01:29,656  [DEBUG] [D][11:29:31][M2M ]m2m send data len[-1]
 
2025-07-16 21:01:29,662  [DEBUG] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:29,671  [DEBUG] [D][11:29:31][HSDK][0] flush to flash addr:[0xE46200] --- write len --- [256]
 
2025-07-16 21:01:29,673  [DEBUG] [E][11:29:31][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:01:29,678  [DEBUG] [E][11:29:31][PROT]M2M Send Fail [1730201371]
 
2025-07-16 21:01:29,682  [DEBUG] [D][11:29:31][PROT]CLEAN,SEND:5
 
2025-07-16 21:01:29,687  [DEBUG] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:01:29,690  [DEBUG] [D][11:29:31][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:01:29,693  [DEBUG] [D][11:29:31][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:01:29,693  [DEBUG] 
 
2025-07-16 21:01:29,699  [DEBUG] [D][11:29:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:01:29,701  [DEBUG] [D][11:29:31][CAT1]<<< 
 
2025-07-16 21:01:29,703  [DEBUG] +CGATT: 1
 
2025-07-16 21:01:29,704  [DEBUG] 
 
2025-07-16 21:01:29,705  [DEBUG] OK
 
2025-07-16 21:01:29,705  [DEBUG] 
 
2025-07-16 21:01:29,708  [DEBUG] [D][11:29:31][CAT1]tx ret[12] >>> AT+CGATT=0
 
2025-07-16 21:01:29,709  [DEBUG] 
 
2025-07-16 21:01:29,975  [DEBUG] [D][11:29:32][CAT1]<<< 
 
2025-07-16 21:01:29,976  [DEBUG] OK
 
2025-07-16 21:01:29,976  [DEBUG] 
 
2025-07-16 21:01:29,980  [DEBUG] [D][11:29:32][CAT1]exec over: func id: 10, ret: 6
 
2025-07-16 21:01:29,983  [DEBUG] [D][11:29:32][CAT1]sub id: 10, ret: 6
 
2025-07-16 21:01:29,983  [DEBUG] 
 
2025-07-16 21:01:29,988  [DEBUG] [D][11:29:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:29,994  [DEBUG] [D][11:29:32][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
 
2025-07-16 21:01:29,996  [DEBUG] [D][11:29:32][M2M ]m2m gsm shut done, ret[0]
 
2025-07-16 21:01:30,001  [DEBUG] [D][11:29:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:01:30,007  [DEBUG] [D][11:29:32][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:01:30,013  [DEBUG] [D][11:29:32][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:01:30,018  [DEBUG] [D][11:29:32][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:01:30,024  [DEBUG] [D][11:29:32][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:01:30,030  [DEBUG] [D][11:29:32][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:01:30,035  [DEBUG] [D][11:29:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:01:30,040  [DEBUG] [D][11:29:32][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:01:30,043  [DEBUG] [D][11:29:32][CAT1]at ops open socket[0]
 
2025-07-16 21:01:30,046  [DEBUG] [D][11:29:32][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:01:30,047  [DEBUG] 
 
2025-07-16 21:01:30,048  [DEBUG] [D][11:29:32][CAT1]<<< 
 
2025-07-16 21:01:30,051  [DEBUG] +CGATT: 0
 
2025-07-16 21:01:30,051  [DEBUG] 
 
2025-07-16 21:01:30,052  [DEBUG] OK
 
2025-07-16 21:01:30,052  [DEBUG] 
 
2025-07-16 21:01:30,055  [DEBUG] [D][11:29:32][CAT1]tx ret[12] >>> AT+CGATT=1
 
2025-07-16 21:01:30,056  [DEBUG] 
 
2025-07-16 21:01:30,360  [DEBUG] [D][11:29:32][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:01:30,411  [DEBUG] [D][11:29:32][CAT1]pdpdeact urc len[22]
 
2025-07-16 21:01:30,418  [DEBUG] [D][11:29:32][COMM]f:frm_violent_loading_over_thr_process. is_first_over pitch:[127]
 
2025-07-16 21:01:30,419  [DEBUG] 
 
2025-07-16 21:01:31,360  [DEBUG] [D][11:29:33][COMM]176590 imu init OK
 
2025-07-16 21:01:31,395  [DEBUG] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,396  [DEBUG] OK
 
2025-07-16 21:01:31,406  [DEBUG] 
 
2025-07-16 21:01:31,409  [DEBUG] [D][11:29:33][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:01:31,409  [DEBUG] 
 
2025-07-16 21:01:31,437  [DEBUG] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,438  [DEBUG] +CGATT: 1
 
2025-07-16 21:01:31,444  [DEBUG] 
 
2025-07-16 21:01:31,444  [DEBUG] OK
 
2025-07-16 21:01:31,448  [DEBUG] 
 
2025-07-16 21:01:31,451  [DEBUG] [D][11:29:33][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:01:31,452  [DEBUG] 
 
2025-07-16 21:01:31,469  [DEBUG] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,472  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:01:31,473  [DEBUG] 
 
2025-07-16 21:01:31,473  [DEBUG] OK
 
2025-07-16 21:01:31,481  [DEBUG] 
 
2025-07-16 21:01:31,484  [DEBUG] [D][11:29:33][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:01:31,485  [DEBUG] 
 
2025-07-16 21:01:31,504  [DEBUG] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,504  [DEBUG] OK
 
2025-07-16 21:01:31,514  [DEBUG] 
 
2025-07-16 21:01:31,518  [DEBUG] [D][11:29:33][CAT1]tx ret[12] >>> AT+QIACT=1
 
2025-07-16 21:01:31,518  [DEBUG] 
 
2025-07-16 21:01:31,548  [DEBUG] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,550  [DEBUG] OK
 
2025-07-16 21:01:31,558  [DEBUG] 
 
2025-07-16 21:01:31,565  [DEBUG] [D][11:29:33][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:01:31,566  [DEBUG] 
 
2025-07-16 21:01:31,581  [DEBUG] [D][11:29:33][CAT1]<<< 
 
2025-07-16 21:01:31,581  [DEBUG] OK
 
2025-07-16 21:01:31,582  [DEBUG] 
 
2025-07-16 21:01:31,587  [DEBUG] [D][11:29:33][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:01:31,810  [DEBUG] [D][11:29:34][CAT1]opened : 0, 0
 
2025-07-16 21:01:31,813  [DEBUG] [D][11:29:34][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:31,819  [DEBUG] [D][11:29:34][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:01:31,824  [DEBUG] [D][11:29:34][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:01:31,830  [DEBUG] [D][11:29:34][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
 
2025-07-16 21:01:31,832  [DEBUG] [D][11:29:34][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:31,837  [DEBUG] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:31,840  [DEBUG] [D][11:29:34][PROT]index:5 1730201374
 
2025-07-16 21:01:31,843  [DEBUG] [D][11:29:34][PROT]is_send:0
 
2025-07-16 21:01:31,846  [DEBUG] [D][11:29:34][PROT]sequence_num:9
 
2025-07-16 21:01:31,848  [DEBUG] [D][11:29:34][PROT]retry_timeout:0
 
2025-07-16 21:01:31,851  [DEBUG] [D][11:29:34][PROT]retry_times:1
 
2025-07-16 21:01:31,855  [DEBUG] [D][11:29:34][PROT]send_path:0x2
 
2025-07-16 21:01:31,860  [DEBUG] [D][11:29:34][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:01:31,869  [DEBUG] [D][11:29:34][PROT]===========================================================
 
2025-07-16 21:01:31,874  [DEBUG] [W][11:29:34][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201374]
 
2025-07-16 21:01:31,880  [DEBUG] [D][11:29:34][PROT]===========================================================
 
2025-07-16 21:01:31,884  [DEBUG] [D][11:29:34][COMM]PB encode data:29
 
2025-07-16 21:01:31,887  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:01:31,893  [DEBUG] [D][11:29:34][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:31,898  [DEBUG] [D][11:29:34][PROT]Send_TO_M2M [1730201374]
 
2025-07-16 21:01:31,902  [DEBUG] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:31,907  [DEBUG] [D][11:29:34][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:31,910  [DEBUG] [D][11:29:34][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:31,913  [DEBUG] [D][11:29:34][M2M ]m2m send data len[134]
 
2025-07-16 21:01:31,919  [DEBUG] [D][11:29:34][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:31,927  [DEBUG] [D][11:29:34][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:01:31,929  [DEBUG] [D][11:29:34][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:31,934  [DEBUG] [D][11:29:34][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:01:31,935  [DEBUG] 
 
2025-07-16 21:01:31,940  [DEBUG] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:31,946  [DEBUG] [D][11:29:34][CAT1]Send Data To Server[134][134] ... ->:
 
2025-07-16 21:01:31,958  [DEBUG] 0043B6A1113311331133113311331B88B56BAE37795C88C027274E8E79401037039DA9D0B83BE4D11AD57A17CF645D906C3FCA1D48EE9248AD8A1E44A16AB0EEB81DCE
 
2025-07-16 21:01:31,960  [DEBUG] [D][11:29:34][CAT1]<<< 
 
2025-07-16 21:01:31,961  [DEBUG] SEND OK
 
2025-07-16 21:01:31,961  [DEBUG] 
 
2025-07-16 21:01:31,966  [DEBUG] [D][11:29:34][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:31,968  [DEBUG] [D][11:29:34][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:31,969  [DEBUG] 
 
2025-07-16 21:01:31,971  [DEBUG] [D][11:29:34][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:31,979  [DEBUG] [D][11:29:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:31,982  [DEBUG] [D][11:29:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:31,985  [DEBUG] [D][11:29:34][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:31,990  [DEBUG] [D][11:29:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:31,994  [DEBUG] [D][11:29:34][PROT]M2M Send ok [1730201374]
 
2025-07-16 21:01:37,223  [DEBUG] [D][11:29:39][PROT]CLEAN,SEND:5
 
2025-07-16 21:01:37,235  [DEBUG] [D][11:29:39][PROT]CLEAN:5
 
2025-07-16 21:01:37,247  [DEBUG] [D][11:29:39][PROT]index:4 1730201379
 
2025-07-16 21:01:37,249  [DEBUG] [D][11:29:39][PROT]is_send:0
 
2025-07-16 21:01:37,252  [DEBUG] [D][11:29:39][PROT]sequence_num:8
 
2025-07-16 21:01:37,255  [DEBUG] [D][11:29:39][PROT]retry_timeout:0
 
2025-07-16 21:01:37,257  [DEBUG] [D][11:29:39][PROT]retry_times:1
 
2025-07-16 21:01:37,260  [DEBUG] [D][11:29:39][PROT]send_path:0x2
 
2025-07-16 21:01:37,266  [DEBUG] [D][11:29:39][PROT]min_index:4, type:0x5006, priority:2
 
2025-07-16 21:01:37,273  [DEBUG] [D][11:29:39][PROT]===========================================================
 
2025-07-16 21:01:37,278  [DEBUG] [W][11:29:39][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730201379]
 
2025-07-16 21:01:37,287  [DEBUG] [D][11:29:39][PROT]===========================================================
 
2025-07-16 21:01:37,289  [DEBUG] [D][11:29:39][COMM]PB encode data:39
 
2025-07-16 21:01:37,298  [DEBUG] 0A25080210841E18C8012000280030003800401F4818600070018001B0228A0106000000000000
 
2025-07-16 21:01:37,299  [DEBUG] [D][11:29:39][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:37,305  [DEBUG] [D][11:29:39][PROT]Send_TO_M2M [1730201379]
 
2025-07-16 21:01:37,308  [DEBUG] [D][11:29:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:37,314  [DEBUG] [D][11:29:39][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:37,317  [DEBUG] [D][11:29:39][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:37,322  [DEBUG] [D][11:29:39][M2M ]m2m send data len[134]
 
2025-07-16 21:01:37,324  [DEBUG] [D][11:29:39][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:37,333  [DEBUG] [D][11:29:39][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:01:37,339  [DEBUG] [D][11:29:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:37,342  [DEBUG] [D][11:29:39][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:37,347  [DEBUG] [D][11:29:39][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:01:37,348  [DEBUG] 
 
2025-07-16 21:01:37,352  [DEBUG] [D][11:29:39][CAT1]Send Data To Server[134][134] ... ->:
 
2025-07-16 21:01:37,364  [DEBUG] 0043B6A2113311331133113311331B88B52AC52146B2A001AAF8CC7DB585F95405AC5F42EA4B8B8CFCDE79A8482A36E2CED0B37FB9324CF2FA48EF05495E9EF01AA645
 
2025-07-16 21:01:37,367  [DEBUG] [D][11:29:39][CAT1]<<< 
 
2025-07-16 21:01:37,368  [DEBUG] SEND OK
 
2025-07-16 21:01:37,368  [DEBUG] 
 
2025-07-16 21:01:37,373  [DEBUG] [D][11:29:39][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:37,375  [DEBUG] [D][11:29:39][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:37,376  [DEBUG] 
 
2025-07-16 21:01:37,380  [DEBUG] [D][11:29:39][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:37,387  [DEBUG] [D][11:29:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:37,389  [DEBUG] [D][11:29:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:37,395  [DEBUG] [D][11:29:39][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:37,397  [DEBUG] [D][11:29:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:37,402  [DEBUG] [D][11:29:39][PROT]M2M Send ok [1730201379]
 
2025-07-16 21:01:41,550  [DEBUG] [D][11:29:43][COMM]Main Task receive event:99
 
2025-07-16 21:01:41,555  [DEBUG] [D][11:29:43][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:01:41,565  [DEBUG] [D][11:29:43][COMM]handlerPeriodWakeup, g_elecBatMissedCount = 1, time = 1730201383, allstateRepSeconds = 1730199600
 
2025-07-16 21:01:41,569  [DEBUG] [D][11:29:43][COMM]Main Task receive event:99 finished processing
 
2025-07-16 21:01:41,576  [DEBUG] [D][11:29:43][COMM]Main Task receive event:146
 
2025-07-16 21:01:41,578  [DEBUG] [D][11:29:43][COMM]a:13,b:0,c:0,d:0,e:0
 
2025-07-16 21:01:41,586  [DEBUG] [W][11:29:43][PROT]remove success[1730201383],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:01:41,594  [DEBUG] [W][11:29:43][PROT]add success [1730201383],send_path[2],type[C001],priority[0],index[5],used[1]
 
2025-07-16 21:01:41,602  [DEBUG] [D][11:29:43][COMM]Main Task receive event:146 finished processing
 
2025-07-16 21:01:42,643  [DEBUG] [D][11:29:44][PROT]CLEAN,SEND:4
 
2025-07-16 21:01:42,653  [DEBUG] [D][11:29:44][PROT]CLEAN:4
 
2025-07-16 21:01:42,662  [DEBUG] [D][11:29:44][COMM]Main Task receive event:79
 
2025-07-16 21:01:42,667  [DEBUG] [D][11:29:44][COMM]Receive protocol ACK TIMEOUT event
 
2025-07-16 21:01:42,673  [DEBUG] [D][11:29:44][COMM]Main Task receive event:79 finished processing
 
2025-07-16 21:01:42,675  [DEBUG] [D][11:29:44][PROT]index:1 1730201384
 
2025-07-16 21:01:42,678  [DEBUG] [D][11:29:44][PROT]is_send:0
 
2025-07-16 21:01:42,681  [DEBUG] [D][11:29:44][PROT]sequence_num:4
 
2025-07-16 21:01:42,683  [DEBUG] [D][11:29:44][PROT]retry_timeout:0
 
2025-07-16 21:01:42,686  [DEBUG] [D][11:29:44][PROT]retry_times:1
 
2025-07-16 21:01:42,688  [DEBUG] [D][11:29:44][PROT]send_path:0x2
 
2025-07-16 21:01:42,694  [DEBUG] [D][11:29:44][PROT]min_index:1, type:0x6A01, priority:0
 
2025-07-16 21:01:42,703  [DEBUG] [D][11:29:44][PROT]===========================================================
 
2025-07-16 21:01:42,708  [DEBUG] [W][11:29:44][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201384]
 
2025-07-16 21:01:42,715  [DEBUG] [D][11:29:44][PROT]===========================================================
 
2025-07-16 21:01:42,717  [DEBUG] [D][11:29:44][PROT]sending traceid [00]
 
2025-07-16 21:01:42,722  [DEBUG] [D][11:29:44][PROT]Send_TO_M2M [1730201384]
 
2025-07-16 21:01:42,728  [DEBUG] [D][11:29:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:42,730  [DEBUG] [D][11:29:44][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:42,733  [DEBUG] [D][11:29:44][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:42,739  [DEBUG] [D][11:29:44][M2M ]m2m send data len[102]
 
2025-07-16 21:01:42,742  [DEBUG] [D][11:29:44][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:42,751  [DEBUG] [D][11:29:44][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:01:42,756  [DEBUG] [D][11:29:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:42,759  [DEBUG] [D][11:29:44][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:42,765  [DEBUG] [D][11:29:44][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:42,766  [DEBUG] 
 
2025-07-16 21:01:42,769  [DEBUG] [D][11:29:44][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:42,779  [DEBUG] 0033B9AE113311331188BB88BB88BB88BBF06C04C5E4555FC197BB038B7E9658D300C431315FE496FC4B8956E9C629C932A150
 
2025-07-16 21:01:42,782  [DEBUG] [D][11:29:44][CAT1]<<< 
 
2025-07-16 21:01:42,783  [DEBUG] SEND OK
 
2025-07-16 21:01:42,783  [DEBUG] 
 
2025-07-16 21:01:42,787  [DEBUG] [D][11:29:44][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:42,789  [DEBUG] [D][11:29:44][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:42,790  [DEBUG] 
 
2025-07-16 21:01:42,795  [DEBUG] [D][11:29:44][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:42,800  [DEBUG] [D][11:29:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:42,802  [DEBUG] [D][11:29:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:42,809  [DEBUG] [D][11:29:44][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:42,811  [DEBUG] [D][11:29:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:42,816  [DEBUG] [D][11:29:44][PROT]M2M Send ok [1730201384]
 
2025-07-16 21:01:48,059  [DEBUG] [D][11:29:50][PROT]CLEAN,SEND:1
 
2025-07-16 21:01:48,069  [DEBUG] [D][11:29:50][PROT]CLEAN:1
 
2025-07-16 21:01:48,081  [DEBUG] [D][11:29:50][PROT]index:2 1730201390
 
2025-07-16 21:01:48,084  [DEBUG] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,087  [DEBUG] [D][11:29:50][PROT]sequence_num:5
 
2025-07-16 21:01:48,091  [DEBUG] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,094  [DEBUG] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,096  [DEBUG] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,101  [DEBUG] [D][11:29:50][PROT]min_index:2, type:0x5A07, priority:0
 
2025-07-16 21:01:48,108  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,113  [DEBUG] [W][11:29:50][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,121  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,124  [DEBUG] [D][11:29:50][COMM]PB encode data:176
 
2025-07-16 21:01:48,157  [DEBUG] 0AAD010AAA01180020FBFF0128D2B883B906300340076812A801ED8C83B906F801008A0209082110221835209A018A020908171021182D208C028A0209080D101F183320DD018A02090826101F182520BF018A0209082110171835209A018A020908171015182D208C028A020908261014182520BF018A020808001000180020008A020808001000180020008A020808001000180020008A020808001000180020008A02080800100018002000C00200
 
2025-07-16 21:01:48,160  [DEBUG] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,163  [DEBUG] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,168  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,171  [DEBUG] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,176  [DEBUG] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,179  [DEBUG] [D][11:29:50][M2M ]m2m send data len[422]
 
2025-07-16 21:01:48,185  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,194  [DEBUG] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[422], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,196  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,202  [DEBUG] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,205  [DEBUG] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,422
 
2025-07-16 21:01:48,206  [DEBUG] 
 
2025-07-16 21:01:48,210  [DEBUG] [D][11:29:50][CAT1]Send Data To Server[422][425] ... ->:
 
2025-07-16 21:01:48,253  [DEBUG] 00D3B6A3113311331133113311331B88B5B35115295C0DAAAD066FF44A0CE591A4E5ED42787E94FC6B77C235C930CD080F517D4AF65DF7101F935F75A1618022403DB36AB50EACAD8A9986875571596B0FBB4B4312A7897C335DD468C41EF2E93E244D324B6FA7AD42C7E086785DFC1E6D62FE7D36AA5298C5D4C92378AB4C41BE4F21B1EE14287DD0178EDDF979C92BADBA325589519570882F226128E552E135E307DD466583E0AF7E1CF774F0EEB5DC45EFD40F3073BE536FDFFE3CAED74F2ED27859512CCB0CF43F10FC88F4CA49BCBE3D
 
2025-07-16 21:01:48,254  [DEBUG] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,255  [DEBUG] SEND OK
 
2025-07-16 21:01:48,255  [DEBUG] 
 
2025-07-16 21:01:48,256  [DEBUG] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,261  [DEBUG] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,262  [DEBUG] 
 
2025-07-16 21:01:48,263  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,268  [DEBUG] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,273  [DEBUG] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,277  [DEBUG] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,283  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,285  [DEBUG] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,288  [DEBUG] [D][11:29:50][PROT]CLEAN:2
 
2025-07-16 21:01:48,290  [DEBUG] [D][11:29:50][PROT]index:3 1730201390
 
2025-07-16 21:01:48,293  [DEBUG] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,296  [DEBUG] [D][11:29:50][PROT]sequence_num:7
 
2025-07-16 21:01:48,302  [DEBUG] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,305  [DEBUG] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,308  [DEBUG] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,310  [DEBUG] [D][11:29:50][PROT]min_index:3, type:0xC001, priority:0
 
2025-07-16 21:01:48,319  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,325  [DEBUG] [W][11:29:50][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,333  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,335  [DEBUG] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,341  [DEBUG] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,344  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,349  [DEBUG] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,352  [DEBUG] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,357  [DEBUG] [D][11:29:50][M2M ]m2m send data len[102]
 
2025-07-16 21:01:48,360  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,369  [DEBUG] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,372  [DEBUG] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,378  [DEBUG] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:48,378  [DEBUG] 
 
2025-07-16 21:01:48,382  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,387  [DEBUG] [D][11:29:50][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:48,397  [DEBUG] 0033B9A5113311331133113311331B88B5FF233ED25B3956931B2067A77C2CB15ADA431A6E6C4A36F9E83E53F660D7D04F5143
 
2025-07-16 21:01:48,399  [DEBUG] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,399  [DEBUG] SEND OK
 
2025-07-16 21:01:48,399  [DEBUG] 
 
2025-07-16 21:01:48,405  [DEBUG] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,408  [DEBUG] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,408  [DEBUG] 
 
2025-07-16 21:01:48,410  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,420  [DEBUG] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,421  [DEBUG] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,427  [DEBUG] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,431  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,436  [DEBUG] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,438  [DEBUG] [D][11:29:50][PROT]CLEAN:3
 
2025-07-16 21:01:48,441  [DEBUG] [D][11:29:50][PROT]index:6 1730201390
 
2025-07-16 21:01:48,444  [DEBUG] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,446  [DEBUG] [D][11:29:50][PROT]sequence_num:10
 
2025-07-16 21:01:48,449  [DEBUG] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,452  [DEBUG] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,454  [DEBUG] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,461  [DEBUG] [D][11:29:50][PROT]min_index:6, type:0xFF0E, priority:0
 
2025-07-16 21:01:48,467  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,472  [DEBUG] [W][11:29:50][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,481  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,486  [DEBUG] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,488  [DEBUG] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,494  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,496  [DEBUG] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,503  [DEBUG] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,504  [DEBUG] [D][11:29:50][M2M ]m2m send data len[166]
 
2025-07-16 21:01:48,508  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,517  [DEBUG] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,521  [DEBUG] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,525  [DEBUG] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,166
 
2025-07-16 21:01:48,526  [DEBUG] 
 
2025-07-16 21:01:48,531  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,536  [DEBUG] [D][11:29:50][CAT1]Send Data To Server[166][169] ... ->:
 
2025-07-16 21:01:48,551  [DEBUG] 0053B9A6113311331133113311331B88B5675E855DF7BE53CD724DFC32697E88A9E2234602B03E99440BA317A3A33881970F7A9E22C6E8DF4A05B8A22D9BA1F351AE84B68873C9D65381D36EED33CD48DBA4A9
 
2025-07-16 21:01:48,553  [DEBUG] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,553  [DEBUG] SEND OK
 
2025-07-16 21:01:48,554  [DEBUG] 
 
2025-07-16 21:01:48,558  [DEBUG] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,561  [DEBUG] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,561  [DEBUG] 
 
2025-07-16 21:01:48,567  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,572  [DEBUG] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,577  [DEBUG] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,580  [DEBUG] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,587  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,589  [DEBUG] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,591  [DEBUG] [D][11:29:50][PROT]CLEAN:6
 
2025-07-16 21:01:48,594  [DEBUG] [D][11:29:50][PROT]index:7 1730201390
 
2025-07-16 21:01:48,597  [DEBUG] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,600  [DEBUG] [D][11:29:50][PROT]sequence_num:11
 
2025-07-16 21:01:48,603  [DEBUG] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,605  [DEBUG] [D][11:29:50][PROT]retry_times:1
 
2025-07-16 21:01:48,608  [DEBUG] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,614  [DEBUG] [D][11:29:50][PROT]min_index:7, type:0xC001, priority:0
 
2025-07-16 21:01:48,623  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,629  [DEBUG] [W][11:29:50][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,634  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,639  [DEBUG] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,642  [DEBUG] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,647  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,652  [DEBUG] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,655  [DEBUG] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,658  [DEBUG] [D][11:29:50][M2M ]m2m send data len[230]
 
2025-07-16 21:01:48,664  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,673  [DEBUG] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,675  [DEBUG] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,681  [DEBUG] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:01:48,682  [DEBUG] 
 
2025-07-16 21:01:48,687  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,689  [DEBUG] [D][11:29:50][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:01:48,712  [DEBUG] 0073B9A9113311331133113311331B88B582D2A4803F76DE5A81BF7E29494687B66290C1F0977189197DC85445B9C4E8446657994FA2454DDD84A5CBBA06F6E07501FFDA23B2D8B36ACA05EB19DCD1201E166A43885E5520CBBE0F43D6EB5226A97CB4F05B0A844B1CB802D49C50D5CD3DDC0B
 
2025-07-16 21:01:48,713  [DEBUG] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,714  [DEBUG] SEND OK
 
2025-07-16 21:01:48,714  [DEBUG] 
 
2025-07-16 21:01:48,716  [DEBUG] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,723  [DEBUG] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,724  [DEBUG] 
 
2025-07-16 21:01:48,725  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,734  [DEBUG] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,736  [DEBUG] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,739  [DEBUG] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,744  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,747  [DEBUG] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:48,749  [DEBUG] [D][11:29:50][PROT]CLEAN:7
 
2025-07-16 21:01:48,755  [DEBUG] [D][11:29:50][PROT]index:8 1730201390
 
2025-07-16 21:01:48,756  [DEBUG] [D][11:29:50][PROT]is_send:0
 
2025-07-16 21:01:48,762  [DEBUG] [D][11:29:50][PROT]sequence_num:12
 
2025-07-16 21:01:48,764  [DEBUG] [D][11:29:50][PROT]retry_timeout:0
 
2025-07-16 21:01:48,767  [DEBUG] [D][11:29:50][PROT]retry_times:3
 
2025-07-16 21:01:48,770  [DEBUG] [D][11:29:50][PROT]send_path:0x2
 
2025-07-16 21:01:48,775  [DEBUG] [D][11:29:50][PROT]min_index:8, type:0xD302, priority:0
 
2025-07-16 21:01:48,781  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,789  [DEBUG] [D][11:29:50][HSDK][0] flush to flash addr:[0xE46300] --- write len --- [256]
 
2025-07-16 21:01:48,795  [DEBUG] [W][11:29:50][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201390]
 
2025-07-16 21:01:48,800  [DEBUG] [D][11:29:50][PROT]===========================================================
 
2025-07-16 21:01:48,803  [DEBUG] [D][11:29:50][COMM]PB encode data:11
 
2025-07-16 21:01:48,805  [DEBUG] 0A0908011A013030013A00
 
2025-07-16 21:01:48,811  [DEBUG] [D][11:29:50][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:48,814  [DEBUG] [D][11:29:50][PROT]Send_TO_M2M [1730201390]
 
2025-07-16 21:01:48,819  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:48,822  [DEBUG] [D][11:29:50][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:48,829  [DEBUG] [D][11:29:50][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:48,831  [DEBUG] [D][11:29:50][M2M ]m2m send data len[102]
 
2025-07-16 21:01:48,836  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:48,846  [DEBUG] [D][11:29:50][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:01:48,847  [DEBUG] [D][11:29:50][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:48,855  [DEBUG] [D][11:29:50][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:48,856  [DEBUG] 
 
2025-07-16 21:01:48,856  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:48,861  [DEBUG] [D][11:29:50][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:48,873  [DEBUG] 0033B6A7113311331133113311331B88B57300823AA2FB17BE3A0C7398E9119E48250809815A8EA75237B0789DCDB554A84213
 
2025-07-16 21:01:48,874  [DEBUG] [D][11:29:50][CAT1]<<< 
 
2025-07-16 21:01:48,875  [DEBUG] SEND OK
 
2025-07-16 21:01:48,875  [DEBUG] 
 
2025-07-16 21:01:48,878  [DEBUG] [D][11:29:50][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:48,884  [DEBUG] [D][11:29:50][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:48,885  [DEBUG] 
 
2025-07-16 21:01:48,887  [DEBUG] [D][11:29:50][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:48,893  [DEBUG] [D][11:29:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:48,898  [DEBUG] [D][11:29:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:48,901  [DEBUG] [D][11:29:50][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:48,906  [DEBUG] [D][11:29:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:48,910  [DEBUG] [D][11:29:50][PROT]M2M Send ok [1730201390]
 
2025-07-16 21:01:53,545  [DEBUG] [D][11:29:55][COMM]Main Task receive event:7
 
2025-07-16 21:01:53,551  [DEBUG] [D][11:29:55][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:01:53,560  [DEBUG] [D][11:29:55][COMM]handlerPeriodRep, g_elecBatMissedCount = 1, time = 1730201395, allstateRepSeconds = 1730199600
 
2025-07-16 21:01:53,562  [DEBUG] [D][11:29:55][COMM]Open GPS Module...
 
2025-07-16 21:01:53,565  [DEBUG] [D][11:29:55][GNSS]start event:3
 
2025-07-16 21:01:53,570  [DEBUG] [D][11:29:55][GNSS]GPS start. ret=0
 
2025-07-16 21:01:53,573  [DEBUG] [W][11:29:55][GNSS]start sing locating
 
2025-07-16 21:01:53,579  [DEBUG] [D][11:29:55][GNSS]gps single mode only, do wifi scan.
 
2025-07-16 21:01:53,581  [DEBUG] [D][11:29:55][COMM]index:0,power_mode:0xFF
 
2025-07-16 21:01:53,584  [DEBUG] [D][11:29:55][COMM]index:1,sound_mode:0xFF
 
2025-07-16 21:01:53,590  [DEBUG] [D][11:29:55][COMM]index:2,gsensor_mode:0xFF
 
2025-07-16 21:01:53,593  [DEBUG] [D][11:29:55][COMM]index:3,report_freq_mode:0xFF
 
2025-07-16 21:01:53,598  [DEBUG] [D][11:29:55][COMM]index:4,report_period:0xFF
 
2025-07-16 21:01:53,601  [DEBUG] [D][11:29:55][COMM]index:5,normal_reset_mode:0xFF
 
2025-07-16 21:01:53,607  [DEBUG] [D][11:29:55][COMM]index:6,normal_reset_period:0xFF
 
2025-07-16 21:01:53,612  [DEBUG] [D][11:29:55][COMM]index:7,spock_over_speed:0xFF
 
2025-07-16 21:01:53,615  [DEBUG] [D][11:29:55][COMM]index:8,spock_limit_speed:0xFF
 
2025-07-16 21:01:53,620  [DEBUG] [D][11:29:55][COMM]index:9,spock_report_period_unlock:0xFF
 
2025-07-16 21:01:53,626  [DEBUG] [D][11:29:55][COMM]index:10,spock_report_period_unlock_unit:0xFF
 
2025-07-16 21:01:53,631  [DEBUG] [D][11:29:55][COMM]index:11,ble_scan_mode:0xFF
 
2025-07-16 21:01:53,634  [DEBUG] [D][11:29:55][COMM]index:12,ble_adv_mode:0xFF
 
2025-07-16 21:01:53,639  [DEBUG] [D][11:29:55][COMM]index:13,spock_audio_volumn:0xFF
 
2025-07-16 21:01:53,645  [DEBUG] [D][11:29:55][COMM]index:14,spock_low_bat_alarm_soc:0xFF
 
2025-07-16 21:01:53,648  [DEBUG] [D][11:29:55][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:01:53,650  [DEBUG] [D][11:29:55][COMM]index:15,bat_auth_mode:0xFF
 
2025-07-16 21:01:53,657  [DEBUG] [D][11:29:55][COMM]index:16,imu_config_params:0xFF
 
2025-07-16 21:01:53,662  [DEBUG] [D][11:29:55][COMM]index:17,long_connect_params:0xFF
 
2025-07-16 21:01:53,666  [DEBUG] [D][11:29:55][COMM]index:18,detain_mark:0xFF
 
2025-07-16 21:01:53,671  [DEBUG] [D][11:29:55][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:01:53,671  [DEBUG] 
 
2025-07-16 21:01:53,676  [DEBUG] [D][11:29:55][COMM]index:19,lock_pos_report_count:0xFF
 
2025-07-16 21:01:53,681  [DEBUG] [D][11:29:55][COMM]index:20,lock_pos_report_interval:0xFF
 
2025-07-16 21:01:53,685  [DEBUG] [D][11:29:55][COMM]index:21,mc_mode:0xFF
 
2025-07-16 21:01:53,687  [DEBUG] [D][11:29:55][COMM]index:22,S_mode:0xFF
 
2025-07-16 21:01:53,693  [DEBUG] [D][11:29:55][COMM]index:23,overweight:0xFF
 
2025-07-16 21:01:53,696  [DEBUG] [D][11:29:55][COMM]index:24,standstill_mode:0xFF
 
2025-07-16 21:01:53,701  [DEBUG] [D][11:29:55][COMM]index:25,night_mode:0xFF
 
2025-07-16 21:01:53,704  [DEBUG] [D][11:29:55][COMM]index:26,experiment1:0xFF
 
2025-07-16 21:01:53,707  [DEBUG] [D][11:29:55][COMM]index:27,experiment2:0xFF
 
2025-07-16 21:01:53,712  [DEBUG] [D][11:29:55][COMM]index:28,experiment3:0xFF
 
2025-07-16 21:01:53,715  [DEBUG] [D][11:29:55][COMM]index:29,experiment4:0xFF
 
2025-07-16 21:01:53,720  [DEBUG] [D][11:29:55][COMM]index:30,night_mode_start:0xFF
 
2025-07-16 21:01:53,723  [DEBUG] [D][11:29:55][COMM]index:31,night_mode_end:0xFF
 
2025-07-16 21:01:53,729  [DEBUG] [D][11:29:55][COMM]index:33,park_report_minutes:0xFF
 
2025-07-16 21:01:53,734  [DEBUG] [D][11:29:55][COMM]index:34,park_report_mode:0xFF
 
2025-07-16 21:01:53,740  [DEBUG] [D][11:29:55][COMM]index:35,mc_undervoltage_protection:0xFF
 
2025-07-16 21:01:53,743  [DEBUG] [D][11:29:55][COMM]index:38,charge_battery_para: FF
 
2025-07-16 21:01:53,749  [DEBUG] [D][11:29:55][COMM]index:39,multirider_mode:0xFF
 
2025-07-16 21:01:53,751  [DEBUG] [D][11:29:55][COMM]index:40,mc_launch_mode:0xFF
 
2025-07-16 21:01:53,757  [DEBUG] [D][11:29:55][COMM]index:41,head_light_enable_mode:0xFF
 
2025-07-16 21:01:53,762  [DEBUG] [D][11:29:55][COMM]index:42,set_time_ble_mode_begin_min:0xFF
 
2025-07-16 21:01:53,768  [DEBUG] [D][11:29:55][COMM]index:43,set_time_ble_mode_end_min:0xFF
 
2025-07-16 21:01:53,774  [DEBUG] [D][11:29:55][COMM]index:44,riding_duration_config:0xFF
 
2025-07-16 21:01:53,777  [DEBUG] [D][11:29:55][COMM]index:45,camera_park_angle_cfg:0xFF
 
2025-07-16 21:01:53,782  [DEBUG] [D][11:29:55][COMM]index:46,camera_park_type_cfg:0xFF
 
2025-07-16 21:01:53,788  [DEBUG] [D][11:29:55][COMM]index:47,bat_info_rep_cfg:0xFF
 
2025-07-16 21:01:53,790  [DEBUG] [D][11:29:55][COMM]index:48,shlmt_sensor_en:0xFF
 
2025-07-16 21:01:53,796  [DEBUG] [D][11:29:55][COMM]index:49,mc_load_startup:0xFF
 
2025-07-16 21:01:53,798  [DEBUG] [D][11:29:55][COMM]index:50,mc_tcs_mode:0xFF
 
2025-07-16 21:01:53,804  [DEBUG] [D][11:29:55][COMM]index:51,traffic_audio_play:0xFF
 
2025-07-16 21:01:53,806  [DEBUG] [D][11:29:55][COMM]index:52,traffic_mode:0xFF
 
2025-07-16 21:01:53,813  [DEBUG] [D][11:29:55][COMM]index:53,traffic_info_collect_freq:0xFF
 
2025-07-16 21:01:53,819  [DEBUG] [D][11:29:55][COMM]index:54,traffic_security_model_cycle:0xFF
 
2025-07-16 21:01:53,824  [DEBUG] [D][11:29:55][COMM]index:55,wheel_alarm_play_switch:255
 
2025-07-16 21:01:53,829  [DEBUG] [D][11:29:55][COMM]index:57,traffic_sens_cycle:0xFF
 
2025-07-16 21:01:53,834  [DEBUG] [D][11:29:55][COMM]index:58,traffic_light_threshold:0xFF
 
2025-07-16 21:01:53,840  [DEBUG] [D][11:29:55][COMM]index:59,traffic_retrograde_threshold:0xFF
 
2025-07-16 21:01:53,843  [DEBUG] [D][11:29:55][COMM]index:60,traffic_road_threshold:0xFF
 
2025-07-16 21:01:53,849  [DEBUG] [D][11:29:55][COMM]index:61,traffic_sens_threshold:0xFF
 
2025-07-16 21:01:53,854  [DEBUG] [D][11:29:55][COMM]index:63,experiment5:0xFF
 
2025-07-16 21:01:53,858  [DEBUG] [D][11:29:55][COMM]index:64,camera_park_markline_cfg:0xFF
 
2025-07-16 21:01:53,859  [DEBUG] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:53,873  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"
 
2025-07-16 21:01:53,873  [DEBUG] 
 
2025-07-16 21:01:53,873  [DEBUG] OK
 
2025-07-16 21:01:53,874  [DEBUG] 
 
2025-07-16 21:01:53,877  [DEBUG] [D][11:29:55][COMM]index:65,camera_park_fenceline_cfg:0xFF
 
2025-07-16 21:01:53,882  [DEBUG] [D][11:29:55][COMM]index:66,camera_park_distance_cfg:0xFF
 
2025-07-16 21:01:53,888  [DEBUG] [D][11:29:55][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
 
2025-07-16 21:01:53,893  [DEBUG] [D][11:29:55][COMM]index:68,camera_park_ps_cfg:0xFFFF
 
2025-07-16 21:01:53,895  [DEBUG] [D][11:29:55][COMM]index:70,camera_park_light_cfg:0xFF
 
2025-07-16 21:01:53,902  [DEBUG] [D][11:29:55][COMM]index:71,camera_park_self_check_cfg:0xFF
 
2025-07-16 21:01:53,907  [DEBUG] [D][11:29:55][COMM]index:72,experiment6:0xFF
 
2025-07-16 21:01:53,910  [DEBUG] [D][11:29:55][COMM]index:73,experiment7:0xFF
 
2025-07-16 21:01:53,915  [DEBUG] [D][11:29:55][COMM]index:74,load_messurement_cfg:0xff
 
2025-07-16 21:01:53,921  [DEBUG] [D][11:29:55][COMM]index:75,zero_value_from_server:-1
 
2025-07-16 21:01:53,923  [DEBUG] [D][11:29:55][COMM]index:76,multirider_threshold:255
 
2025-07-16 21:01:53,930  [DEBUG] [D][11:29:55][COMM]index:77,experiment8:255
 
2025-07-16 21:01:53,935  [DEBUG] [D][11:29:55][COMM]index:78,temp_park_audio_play_duration:255
 
2025-07-16 21:01:53,941  [DEBUG] [D][11:29:55][COMM]index:79,temp_park_tail_light_twinkle_duration:255
 
2025-07-16 21:01:53,946  [DEBUG] [D][11:29:55][COMM]index:80,temp_park_reminder_timeout_duration:255
 
2025-07-16 21:01:53,952  [DEBUG] [D][11:29:55][COMM]index:82,loc_report_low_speed_thr:255
 
2025-07-16 21:01:53,954  [DEBUG] [D][11:29:55][COMM]index:83,loc_report_interval:255
 
2025-07-16 21:01:53,959  [DEBUG] [D][11:29:55][COMM]index:84,multirider_threshold_p2:255
 
2025-07-16 21:01:53,967  [DEBUG] [D][11:29:55][COMM]index:85,multirider_strategy:255
 
2025-07-16 21:01:53,971  [DEBUG] [D][11:29:55][COMM]index:81,camera_park_similar_thr_cfg:0xFF
 
2025-07-16 21:01:53,977  [DEBUG] [D][11:29:55][COMM]index:86,camera_park_self_check_period_cfg:0xFF
 
2025-07-16 21:01:53,979  [DEBUG] [D][11:29:55][COMM]index:96,rope_sensor_cfg:0xFF
 
2025-07-16 21:01:53,985  [DEBUG] [D][11:29:55][COMM]index:90,weight_param:0xFF
 
2025-07-16 21:01:53,991  [DEBUG] [D][11:29:55][COMM]index:93,lock_anti_theft_mode:0xFF
 
2025-07-16 21:01:53,994  [DEBUG] [D][11:29:55][CAT1]tx ret[12] >>> AT+GPSCFG?
 
2025-07-16 21:01:53,995  [DEBUG] 
 
2025-07-16 21:01:53,999  [DEBUG] [D][11:29:55][COMM]index:94,high_temp_alarm_count:0xFF
 
2025-07-16 21:01:54,001  [DEBUG] [D][11:29:55][COMM]index:95,current_limit:0xFF
 
2025-07-16 21:01:54,010  [DEBUG] [D][11:29:55][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
 
2025-07-16 21:01:54,016  [DEBUG] [D][11:29:55][COMM]index:104,brake_release_protect:0xFF
 
2025-07-16 21:01:54,017  [DEBUG] 
 
2025-07-16 21:01:54,019  [DEBUG] [D][11:29:55][COMM]index:100,location_mode:0xFF
 
2025-07-16 21:01:54,024  [DEBUG] [D][11:29:55][COMM]index:110,display_speed_limit:255
 
2025-07-16 21:01:54,029  [DEBUG] [D][11:29:55][COMM]index:105,vo_unload_thr:0xFFFF
 
2025-07-16 21:01:54,032  [DEBUG] [D][11:29:55][COMM]index:107,vo_loading_angle_thr:0xFF
 
2025-07-16 21:01:54,038  [DEBUG] [D][11:29:55][COMM]index:108,vo_func_switch:0xFF
 
2025-07-16 21:01:54,046  [DEBUG] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:01:54,055  [DEBUG] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4205],priority[0],index[1],used[1]
 
2025-07-16 21:01:54,064  [DEBUG] [D][11:29:55][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:01:54,066  [DEBUG] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:01:54,071  [DEBUG] [D][11:29:55][COMM]report park limit config
 
2025-07-16 21:01:54,080  [DEBUG] [D][11:29:55][COMM]report park limit config:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:01:54,083  [DEBUG] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:01:54,086  [DEBUG] [D][11:29:55][COMM]
 
2025-07-16 21:01:54,094  [DEBUG] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:01:54,102  [DEBUG] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4701],priority[0],index[2],used[1]
 
2025-07-16 21:01:54,110  [DEBUG] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:01:54,122  [DEBUG] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4705],priority[0],index[3],used[1]
 
2025-07-16 21:01:54,125  [DEBUG] [D][11:29:55][COMM]bledev scan is invalid, will return
 
2025-07-16 21:01:54,130  [DEBUG] [D][11:29:55][COMM]------>period, report file manifest
 
2025-07-16 21:01:54,136  [DEBUG] [D][11:29:55][COMM]Main Task receive event:7 finished processing
 
2025-07-16 21:01:54,138  [DEBUG] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,141  [DEBUG] +GPSCFG:0,0,115200,1,0,65472,0,1,1
 
2025-07-16 21:01:54,142  [DEBUG] 
 
2025-07-16 21:01:54,143  [DEBUG] OK
 
2025-07-16 21:01:54,144  [DEBUG] 
 
2025-07-16 21:01:54,146  [DEBUG] [D][11:29:55][CAT1]tx ret[14] >>> AT+GPSPORT=1
 
2025-07-16 21:01:54,148  [DEBUG] 
 
2025-07-16 21:01:54,149  [DEBUG] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,150  [DEBUG] OK
 
2025-07-16 21:01:54,150  [DEBUG] 
 
2025-07-16 21:01:54,152  [DEBUG] [D][11:29:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1
 
2025-07-16 21:01:54,153  [DEBUG] 
 
2025-07-16 21:01:54,155  [DEBUG] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,156  [DEBUG] OK
 
2025-07-16 21:01:54,156  [DEBUG] 
 
2025-07-16 21:01:54,161  [DEBUG] [D][11:29:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0
 
2025-07-16 21:01:54,161  [DEBUG] 
 
2025-07-16 21:01:54,163  [DEBUG] [D][11:29:55][CAT1]<<< 
 
2025-07-16 21:01:54,164  [DEBUG] OK
 
2025-07-16 21:01:54,164  [DEBUG] 
 
2025-07-16 21:01:54,165  [DEBUG] [D][11:29:55][CAT1]tx ret[13] >>> AT+GPSPWR=1
 
2025-07-16 21:01:54,169  [DEBUG] 
 
2025-07-16 21:01:54,171  [DEBUG] [D][11:29:56][PROT]CLEAN,SEND:8
 
2025-07-16 21:01:54,174  [DEBUG] [D][11:29:56][PROT]index:8 1730201396
 
2025-07-16 21:01:54,178  [DEBUG] [D][11:29:56][PROT]is_send:0
 
2025-07-16 21:01:54,180  [DEBUG] [D][11:29:56][PROT]sequence_num:12
 
2025-07-16 21:01:54,182  [DEBUG] [D][11:29:56][PROT]retry_timeout:0
 
2025-07-16 21:01:54,186  [DEBUG] [D][11:29:56][PROT]retry_times:2
 
2025-07-16 21:01:54,188  [DEBUG] [D][11:29:56][PROT]send_path:0x2
 
2025-07-16 21:01:54,194  [DEBUG] [D][11:29:56][PROT]min_index:8, type:0xD302, priority:0
 
2025-07-16 21:01:54,201  [DEBUG] [D][11:29:56][PROT]===========================================================
 
2025-07-16 21:01:54,206  [DEBUG] [W][11:29:56][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201396]
 
2025-07-16 21:01:54,215  [DEBUG] [D][11:29:56][PROT]===========================================================
 
2025-07-16 21:01:54,217  [DEBUG] [D][11:29:56][COMM]PB encode data:11
 
2025-07-16 21:01:54,220  [DEBUG] 0A0908011A013030013A00
 
2025-07-16 21:01:54,226  [DEBUG] [D][11:29:56][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:01:54,228  [DEBUG] [D][11:29:56][PROT]Send_TO_M2M [1730201396]
 
2025-07-16 21:01:54,233  [DEBUG] [D][11:29:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:01:54,236  [DEBUG] [D][11:29:56][SAL ]sock send credit cnt[6]
 
2025-07-16 21:01:54,242  [DEBUG] [D][11:29:56][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:01:54,245  [DEBUG] [D][11:29:56][M2M ]m2m send data len[102]
 
2025-07-16 21:01:54,248  [DEBUG] [D][11:29:56][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:01:54,257  [DEBUG] [D][11:29:56][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a2a0] format[0]
 
2025-07-16 21:01:54,263  [DEBUG] [D][11:29:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:01:55,149  [DEBUG] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,150  [DEBUG] OK
 
2025-07-16 21:01:55,159  [DEBUG] 
 
2025-07-16 21:01:55,162  [DEBUG] [D][11:29:57][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F
 
2025-07-16 21:01:55,163  [DEBUG] 
 
2025-07-16 21:01:55,234  [DEBUG] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,235  [DEBUG] OK
 
2025-07-16 21:01:55,244  [DEBUG] 
 
2025-07-16 21:01:55,248  [DEBUG] [D][11:29:57][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:01:55,248  [DEBUG] 
 
2025-07-16 21:01:55,267  [DEBUG] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,278  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"
 
2025-07-16 21:01:55,278  [DEBUG] 
 
2025-07-16 21:01:55,279  [DEBUG] OK
 
2025-07-16 21:01:55,279  [DEBUG] 
 
2025-07-16 21:01:55,283  [DEBUG] [D][11:29:57][CAT1]tx ret[14] >>> AT+GPSMODE=1
 
2025-07-16 21:01:55,283  [DEBUG] 
 
2025-07-16 21:01:55,300  [DEBUG] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,301  [DEBUG] OK
 
2025-07-16 21:01:55,302  [DEBUG] 
 
2025-07-16 21:01:55,306  [DEBUG] [D][11:29:57][CAT1]exec over: func id: 23, ret: 6
 
2025-07-16 21:01:55,309  [DEBUG] [D][11:29:57][CAT1]sub id: 23, ret: 6
 
2025-07-16 21:01:55,310  [DEBUG] 
 
2025-07-16 21:01:55,314  [DEBUG] [D][11:29:57][CAT1]gsm read msg sub id: 26
 
2025-07-16 21:01:55,318  [DEBUG] [D][11:29:57][CAT1]tx ret[18] >>> AT+WIFISCAN=8,45
 
2025-07-16 21:01:55,320  [DEBUG] 
 
2025-07-16 21:01:55,334  [DEBUG] [D][11:29:57][CAT1]<<< 
 
2025-07-16 21:01:55,335  [DEBUG] OK
 
2025-07-16 21:01:55,337  [DEBUG] 
 
2025-07-16 21:01:55,340  [DEBUG] [D][11:29:57][CAT1]exec over: func id: 26, ret: 6
 
2025-07-16 21:01:55,348  [DEBUG] [D][11:29:57][CAT1]gsm read msg sub id: 21
 
2025-07-16 21:01:55,353  [DEBUG] [D][11:29:57][CAT1]tx ret[15] >>> AT+QCELLINFO?
 
2025-07-16 21:01:55,353  [DEBUG] 
 
2025-07-16 21:01:55,791  [DEBUG] [D][11:29:58][GNSS]recv submsg id[1]
 
2025-07-16 21:01:55,795  [DEBUG] [D][11:29:58][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
 
2025-07-16 21:01:55,837  [DEBUG] [D][11:29:58][CAT1]<<< 
 
2025-07-16 21:01:55,838  [DEBUG] OK
 
2025-07-16 21:01:55,839  [DEBUG] 
 
2025-07-16 21:01:55,842  [DEBUG] [D][11:29:58][CAT1]cell info report total[0]
 
2025-07-16 21:01:55,846  [DEBUG] [D][11:29:58][CAT1]exec over: func id: 21, ret: 6
 
2025-07-16 21:01:55,851  [DEBUG] [D][11:29:58][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:01:55,854  [DEBUG] [D][11:29:58][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:01:55,854  [DEBUG] 
 
2025-07-16 21:01:55,863  [DEBUG] [D][11:29:58][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:01:55,873  [DEBUG] 0033B6A4113311331133113311331B88B53C8D75E4B84C5C2F9AFEFD8A216470BC230BC0993499F6A57EEA5402282B81DC4D03
 
2025-07-16 21:01:55,893  [DEBUG] [D][11:29:58][CAT1]<<< 
 
2025-07-16 21:01:55,896  [DEBUG] SEND OK
 
2025-07-16 21:01:55,897  [DEBUG] 
 
2025-07-16 21:01:55,900  [DEBUG] [D][11:29:58][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:01:55,902  [DEBUG] [D][11:29:58][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:01:55,904  [DEBUG] 
 
2025-07-16 21:01:55,908  [DEBUG] [D][11:29:58][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:01:55,913  [DEBUG] [D][11:29:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:01:55,918  [DEBUG] [D][11:29:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:01:55,921  [DEBUG] [D][11:29:58][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:01:55,927  [DEBUG] [D][11:29:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:01:55,930  [DEBUG] [D][11:29:58][PROT]M2M Send ok [1730201398]
 
2025-07-16 21:02:01,232  [DEBUG] [D][11:30:03][PROT]CLEAN,SEND:8
 
2025-07-16 21:02:01,244  [DEBUG] [D][11:30:03][PROT]index:8 1730201403
 
2025-07-16 21:02:01,247  [DEBUG] [D][11:30:03][PROT]is_send:0
 
2025-07-16 21:02:01,250  [DEBUG] [D][11:30:03][PROT]sequence_num:12
 
2025-07-16 21:02:01,253  [DEBUG] [D][11:30:03][PROT]retry_timeout:0
 
2025-07-16 21:02:01,255  [DEBUG] [D][11:30:03][PROT]retry_times:1
 
2025-07-16 21:02:01,258  [DEBUG] [D][11:30:03][PROT]send_path:0x2
 
2025-07-16 21:02:01,264  [DEBUG] [D][11:30:03][PROT]min_index:8, type:0xD302, priority:0
 
2025-07-16 21:02:01,272  [DEBUG] [D][11:30:03][PROT]===========================================================
 
2025-07-16 21:02:01,278  [DEBUG] [D][11:30:03][HSDK][0] flush to flash addr:[0xE46400] --- write len --- [256]
 
2025-07-16 21:02:01,284  [DEBUG] [W][11:30:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201403]
 
2025-07-16 21:02:01,290  [DEBUG] [D][11:30:03][PROT]===========================================================
 
2025-07-16 21:02:01,295  [DEBUG] [D][11:30:03][COMM]PB encode data:11
 
2025-07-16 21:02:01,296  [DEBUG] 0A0908011A013030013A00
 
2025-07-16 21:02:01,300  [DEBUG] [D][11:30:03][PROT]sending traceid [9999999999900005]
 
2025-07-16 21:02:01,303  [DEBUG] [D][11:30:03][PROT]Send_TO_M2M [1730201403]
 
2025-07-16 21:02:01,311  [DEBUG] [D][11:30:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:01,314  [DEBUG] [D][11:30:03][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:01,317  [DEBUG] [D][11:30:03][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:01,320  [DEBUG] [D][11:30:03][M2M ]m2m send data len[102]
 
2025-07-16 21:02:01,325  [DEBUG] [D][11:30:03][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:01,334  [DEBUG] [D][11:30:03][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a010] format[0]
 
2025-07-16 21:02:01,339  [DEBUG] [D][11:30:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:01,342  [DEBUG] [D][11:30:03][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:01,348  [DEBUG] [D][11:30:03][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:02:01,348  [DEBUG] 
 
2025-07-16 21:02:01,349  [DEBUG] [D][11:30:03][CAT1]Send Data To Server[102][102] ... ->:
 
2025-07-16 21:02:01,363  [DEBUG] 0033B6AB113311331133113311331B88B562B7957B38C3C6FCA84C14F72F2518C3DCD271B669702EB09E85CEFFE835E8C2D168
 
2025-07-16 21:02:01,363  [DEBUG] [D][11:30:03][CAT1]<<< 
 
2025-07-16 21:02:01,364  [DEBUG] SEND OK
 
2025-07-16 21:02:01,365  [DEBUG] 
 
2025-07-16 21:02:01,367  [DEBUG] [D][11:30:03][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:01,372  [DEBUG] [D][11:30:03][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:01,373  [DEBUG] 
 
2025-07-16 21:02:01,376  [DEBUG] [D][11:30:03][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:01,381  [DEBUG] [D][11:30:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:01,386  [DEBUG] [D][11:30:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:01,389  [DEBUG] [D][11:30:03][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:01,396  [DEBUG] [D][11:30:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:01,399  [DEBUG] [D][11:30:03][PROT]M2M Send ok [1730201403]
 
2025-07-16 21:02:05,690  [DEBUG] +WIFISCAN:8,0,FCD733633E16,-34
 
2025-07-16 21:02:05,692  [DEBUG] +WIFISCAN:8,1,E22E0BF5E7EF,-61
 
2025-07-16 21:02:05,695  [DEBUG] +WIFISCAN:8,2,A400E2F462C0,-61
 
2025-07-16 21:02:05,698  [DEBUG] +WIFISCAN:8,3,A400E2F468E1,-77
 
2025-07-16 21:02:05,701  [DEBUG] +WIFISCAN:8,4,A400E2F468E2,-77
 
2025-07-16 21:02:05,704  [DEBUG] +WIFISCAN:8,5,A400E2F464E2,-79
 
2025-07-16 21:02:05,706  [DEBUG] +WIFISCAN:8,6,980D51F3B459,-79
 
2025-07-16 21:02:05,709  [DEBUG] +WIFISCAN:8,7,A400E2F464E0,-80
 
2025-07-16 21:02:05,712  [DEBUG] 
 
2025-07-16 21:02:05,715  [DEBUG] [D][11:30:07][CAT1]wifi scan report total[8]
 
2025-07-16 21:02:05,721  [DEBUG] [D][11:30:07][CAT1]wifi scan result rpt len[256], retval[256]
 
2025-07-16 21:02:05,832  [DEBUG] [D][11:30:08][GNSS]recv submsg id[3]
 
2025-07-16 21:02:05,835  [DEBUG] [D][11:30:08][GNSS]handlerWifiScanDone
 
2025-07-16 21:02:05,841  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[0]mac:FCD733633E16 ssid: rssi:-34
 
2025-07-16 21:02:05,849  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[1]mac:E22E0BF5E7EF ssid: rssi:-61
 
2025-07-16 21:02:05,854  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[2]mac:A400E2F462C0 ssid: rssi:-61
 
2025-07-16 21:02:05,863  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[3]mac:A400E2F468E1 ssid: rssi:-77
 
2025-07-16 21:02:05,868  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[4]mac:A400E2F468E2 ssid: rssi:-77
 
2025-07-16 21:02:05,877  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[5]mac:A400E2F464E2 ssid: rssi:-79
 
2025-07-16 21:02:05,883  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[6]mac:980D51F3B459 ssid: rssi:-79
 
2025-07-16 21:02:05,888  [DEBUG] [D][11:30:08][GNSS]frm_wifi_scan_callback:[7]mac:A400E2F464E0 ssid: rssi:-80
 
2025-07-16 21:02:05,898  [DEBUG] [W][11:30:08][PROT]remove success[1730201408],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:02:05,906  [DEBUG] [W][11:30:08][PROT]add success [1730201408],send_path[2],type[5103],priority[0],index[4],used[1]
 
2025-07-16 21:02:06,079  [DEBUG] [D][11:30:08][CAT1]closed : 0
 
2025-07-16 21:02:06,084  [DEBUG] [D][11:30:08][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:02:06,088  [DEBUG] [D][11:30:08][SAL ]socket closed id[0]
 
2025-07-16 21:02:06,091  [DEBUG] [D][11:30:08][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:02:06,097  [DEBUG] [D][11:30:08][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:02:06,100  [DEBUG] [D][11:30:08][M2M ]m2m select fd[4]
 
2025-07-16 21:02:06,106  [DEBUG] [D][11:30:08][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:02:06,108  [DEBUG] [D][11:30:08][M2M ]tcpclient close[4]
 
2025-07-16 21:02:06,111  [DEBUG] [D][11:30:08][SAL ]socket[4] has closed
 
2025-07-16 21:02:06,116  [DEBUG] [D][11:30:08][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:02:06,123  [DEBUG] [D][11:30:08][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 2
 
2025-07-16 21:02:06,188  [DEBUG] [D][11:30:08][COMM]Main Task receive event:86
 
2025-07-16 21:02:06,194  [DEBUG] [D][11:30:08][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:02:06,629  [DEBUG] [D][11:30:08][PROT]CLEAN,SEND:8
 
2025-07-16 21:02:06,641  [DEBUG] [D][11:30:08][PROT]CLEAN:8
 
2025-07-16 21:02:06,654  [DEBUG] [D][11:30:08][PROT]index:0 1730201408
 
2025-07-16 21:02:06,656  [DEBUG] [D][11:30:08][PROT]is_send:0
 
2025-07-16 21:02:06,659  [DEBUG] [D][11:30:08][PROT]sequence_num:13
 
2025-07-16 21:02:06,662  [DEBUG] [D][11:30:08][PROT]retry_timeout:0
 
2025-07-16 21:02:06,665  [DEBUG] [D][11:30:08][PROT]retry_times:1
 
2025-07-16 21:02:06,667  [DEBUG] [D][11:30:08][PROT]send_path:0x2
 
2025-07-16 21:02:06,672  [DEBUG] [D][11:30:08][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:02:06,679  [DEBUG] [D][11:30:08][PROT]===========================================================
 
2025-07-16 21:02:06,684  [DEBUG] [W][11:30:08][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201408]
 
2025-07-16 21:02:06,693  [DEBUG] [D][11:30:08][PROT]===========================================================
 
2025-07-16 21:02:06,698  [DEBUG] [D][11:30:08][PROT]sending traceid [9999999999900006]
 
2025-07-16 21:02:06,701  [DEBUG] [D][11:30:08][PROT]Send_TO_M2M [1730201408]
 
2025-07-16 21:02:06,706  [DEBUG] [D][11:30:08][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:06,713  [DEBUG] [D][11:30:08][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:06,715  [DEBUG] [D][11:30:08][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:02:06,724  [DEBUG] [D][11:30:08][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:02:06,726  [DEBUG] [D][11:30:08][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:02:06,735  [DEBUG] [D][11:30:08][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:02:06,740  [DEBUG] [D][11:30:08][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:02:06,743  [DEBUG] [D][11:30:08][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:02:06,746  [DEBUG] [D][11:30:08][CAT1]at ops open socket[0]
 
2025-07-16 21:02:06,751  [DEBUG] [D][11:30:08][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:02:06,751  [DEBUG] 
 
2025-07-16 21:02:06,756  [DEBUG] [D][11:30:08][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:02:06,759  [DEBUG] [D][11:30:08][CAT1]<<< 
 
2025-07-16 21:02:06,760  [DEBUG] +CGATT: 1
 
2025-07-16 21:02:06,760  [DEBUG] 
 
2025-07-16 21:02:06,760  [DEBUG] OK
 
2025-07-16 21:02:06,761  [DEBUG] 
 
2025-07-16 21:02:06,761  [DEBUG] [D][11:30:08][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:02:06,763  [DEBUG] 
 
2025-07-16 21:02:06,766  [DEBUG] [D][11:30:08][CAT1]<<< 
 
2025-07-16 21:02:06,768  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:02:06,769  [DEBUG] 
 
2025-07-16 21:02:06,769  [DEBUG] OK
 
2025-07-16 21:02:06,769  [DEBUG] 
 
2025-07-16 21:02:06,771  [DEBUG] [D][11:30:08][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:02:06,772  [DEBUG] 
 
2025-07-16 21:02:06,774  [DEBUG] [D][11:30:08][CAT1]<<< 
 
2025-07-16 21:02:06,777  [DEBUG] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:02:06,777  [DEBUG] 
 
2025-07-16 21:02:06,778  [DEBUG] OK
 
2025-07-16 21:02:06,778  [DEBUG] 
 
2025-07-16 21:02:06,786  [DEBUG] [D][11:30:08][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:02:06,787  [DEBUG] 
 
2025-07-16 21:02:06,788  [DEBUG] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:06,789  [DEBUG] OK
 
2025-07-16 21:02:06,789  [DEBUG] 
 
2025-07-16 21:02:06,793  [DEBUG] [D][11:30:09][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:02:07,015  [DEBUG] [D][11:30:09][CAT1]opened : 0, 0
 
2025-07-16 21:02:07,017  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,023  [DEBUG] [D][11:30:09][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:02:07,029  [DEBUG] [D][11:30:09][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:02:07,034  [DEBUG] [D][11:30:09][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:02:07,039  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:07,042  [DEBUG] [D][11:30:09][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:07,045  [DEBUG] [D][11:30:09][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:07,052  [DEBUG] [D][11:30:09][M2M ]m2m send data len[70]
 
2025-07-16 21:02:07,053  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:07,062  [DEBUG] [D][11:30:09][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:02:07,068  [DEBUG] [D][11:30:09][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:07,071  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:07,076  [DEBUG] [D][11:30:09][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:02:07,076  [DEBUG] 
 
2025-07-16 21:02:07,081  [DEBUG] [D][11:30:09][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:02:07,088  [DEBUG] 0023B9F8113311331133113311331B88B397A3E22296667BCF0E7809D8DE4ABBDD5290
 
2025-07-16 21:02:07,089  [DEBUG] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:07,090  [DEBUG] SEND OK
 
2025-07-16 21:02:07,090  [DEBUG] 
 
2025-07-16 21:02:07,095  [DEBUG] [D][11:30:09][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:07,098  [DEBUG] [D][11:30:09][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:07,099  [DEBUG] 
 
2025-07-16 21:02:07,104  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,110  [DEBUG] [D][11:30:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:07,112  [DEBUG] [D][11:30:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:07,118  [DEBUG] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,121  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,126  [DEBUG] [D][11:30:09][PROT]M2M Send ok [1730201409]
 
2025-07-16 21:02:07,128  [DEBUG] [D][11:30:09][PROT]CLEAN:0
 
2025-07-16 21:02:07,131  [DEBUG] [D][11:30:09][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:07,138  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:07,143  [DEBUG] [D][11:30:09][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:02:07,146  [DEBUG] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,151  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,153  [DEBUG] [D][11:30:09][PROT]index:5 1730201409
 
2025-07-16 21:02:07,156  [DEBUG] [D][11:30:09][PROT]is_send:0
 
2025-07-16 21:02:07,160  [DEBUG] [D][11:30:09][PROT]sequence_num:14
 
2025-07-16 21:02:07,163  [DEBUG] [D][11:30:09][PROT]retry_timeout:0
 
2025-07-16 21:02:07,165  [DEBUG] [D][11:30:09][PROT]retry_times:1
 
2025-07-16 21:02:07,171  [DEBUG] [D][11:30:09][PROT]send_path:0x2
 
2025-07-16 21:02:07,173  [DEBUG] [D][11:30:09][PROT]min_index:5, type:0xC001, priority:0
 
2025-07-16 21:02:07,182  [DEBUG] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,188  [DEBUG] [W][11:30:09][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201409]
 
2025-07-16 21:02:07,194  [DEBUG] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,199  [DEBUG] [D][11:30:09][PROT]sending traceid [9999999999900008]
 
2025-07-16 21:02:07,205  [DEBUG] [D][11:30:09][PROT]Send_TO_M2M [1730201409]
 
2025-07-16 21:02:07,207  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:07,213  [DEBUG] [D][11:30:09][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:07,216  [DEBUG] [D][11:30:09][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:07,218  [DEBUG] [D][11:30:09][M2M ]m2m send data len[102]
 
2025-07-16 21:02:07,224  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:07,233  [DEBUG] [D][11:30:09][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x2005a010] format[0]
 
2025-07-16 21:02:07,238  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:07,241  [DEBUG] [D][11:30:09][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:07,246  [DEBUG] [D][11:30:09][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:02:07,247  [DEBUG] 
 
2025-07-16 21:02:07,251  [DEBUG] [D][11:30:09][CAT1]Send Data To Server[102][102] ... ->:
 
2025-07-16 21:02:07,261  [DEBUG] 0033B9FA113311331133113311331B88B222E772B83E7A622E479422819013E546AC238D81A241D7EA2752C2E98B58CBFB86D9
 
2025-07-16 21:02:07,262  [DEBUG] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:07,263  [DEBUG] SEND OK
 
2025-07-16 21:02:07,263  [DEBUG] 
 
2025-07-16 21:02:07,268  [DEBUG] [D][11:30:09][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:07,271  [DEBUG] [D][11:30:09][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:07,271  [DEBUG] 
 
2025-07-16 21:02:07,274  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,283  [DEBUG] [D][11:30:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:07,284  [DEBUG] [D][11:30:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:07,288  [DEBUG] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,294  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,299  [DEBUG] [D][11:30:09][PROT]M2M Send ok [1730201409]
 
2025-07-16 21:02:07,300  [DEBUG] [D][11:30:09][PROT]CLEAN:5
 
2025-07-16 21:02:07,305  [DEBUG] [D][11:30:09][PROT]index:1 1730201409
 
2025-07-16 21:02:07,307  [DEBUG] [D][11:30:09][PROT]is_send:0
 
2025-07-16 21:02:07,310  [DEBUG] [D][11:30:09][PROT]sequence_num:15
 
2025-07-16 21:02:07,313  [DEBUG] [D][11:30:09][PROT]retry_timeout:0
 
2025-07-16 21:02:07,316  [DEBUG] [D][11:30:09][PROT]retry_times:1
 
2025-07-16 21:02:07,318  [DEBUG] [D][11:30:09][PROT]send_path:0x2
 
2025-07-16 21:02:07,324  [DEBUG] [D][11:30:09][PROT]min_index:1, type:0x4205, priority:0
 
2025-07-16 21:02:07,330  [DEBUG] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,335  [DEBUG] [W][11:30:09][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201409]
 
2025-07-16 21:02:07,345  [DEBUG] [D][11:30:09][PROT]===========================================================
 
2025-07-16 21:02:07,350  [DEBUG] [D][11:30:09][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:02:07,352  [DEBUG] [D][11:30:09][PROT]Send_TO_M2M [1730201409]
 
2025-07-16 21:02:07,358  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:07,361  [DEBUG] [D][11:30:09][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:07,367  [DEBUG] [D][11:30:09][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:07,369  [DEBUG] [D][11:30:09][M2M ]m2m send data len[294]
 
2025-07-16 21:02:07,372  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:07,381  [DEBUG] [D][11:30:09][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x2005a010] format[0]
 
2025-07-16 21:02:07,386  [DEBUG] [D][11:30:09][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:07,388  [DEBUG] [D][11:30:09][CAT1]tx ret[17] >>> AT+QISEND=0,294
 
2025-07-16 21:02:07,389  [DEBUG] 
 
2025-07-16 21:02:07,394  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:07,399  [DEBUG] [D][11:30:09][CAT1]Send Data To Server[294][297] ... ->:
 
2025-07-16 21:02:07,428  [DEBUG] 0093B9FF113311331133113311331B88B15ED3C2A04C77B56FC8B55B15BF56DAE9B208F930F297BF7943547DF85F550FD509A11D770048BAFBA7B44A9EFCE9ED517FA37BDCA18B3EFE0D1AC0F64D0F5C9F3B7380433E4DF9A8844CCE10EE119FFCA007CF0A1201EE9FA86342D7DDE0A8E23904F51B46F8A7183C67D67707C62C3883E021ACB9FF4211FF6C56302CDC081575D8
 
2025-07-16 21:02:07,428  [DEBUG] [D][11:30:09][CAT1]<<< 
 
2025-07-16 21:02:07,429  [DEBUG] SEND OK
 
2025-07-16 21:02:07,429  [DEBUG] 
 
2025-07-16 21:02:07,433  [DEBUG] [D][11:30:09][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:07,435  [DEBUG] [D][11:30:09][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:07,435  [DEBUG] 
 
2025-07-16 21:02:07,441  [DEBUG] [D][11:30:09][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:07,448  [DEBUG] [D][11:30:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:07,449  [DEBUG] [D][11:30:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:07,456  [DEBUG] [D][11:30:09][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:07,462  [DEBUG] [D][11:30:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:07,465  [DEBUG] [D][11:30:09][PROT]M2M Send ok [1730201409]
 
2025-07-16 21:02:12,617  [DEBUG] [D][11:30:14][PROT]CLEAN,SEND:1
 
2025-07-16 21:02:12,628  [DEBUG] [D][11:30:14][PROT]CLEAN:1
 
2025-07-16 21:02:12,641  [DEBUG] [D][11:30:14][PROT]index:2 1730201414
 
2025-07-16 21:02:12,644  [DEBUG] [D][11:30:14][PROT]is_send:0
 
2025-07-16 21:02:12,647  [DEBUG] [D][11:30:14][PROT]sequence_num:16
 
2025-07-16 21:02:12,649  [DEBUG] [D][11:30:14][PROT]retry_timeout:0
 
2025-07-16 21:02:12,652  [DEBUG] [D][11:30:14][PROT]retry_times:1
 
2025-07-16 21:02:12,654  [DEBUG] [D][11:30:14][PROT]send_path:0x2
 
2025-07-16 21:02:12,660  [DEBUG] [D][11:30:14][PROT]min_index:2, type:0x4701, priority:0
 
2025-07-16 21:02:12,667  [DEBUG] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,672  [DEBUG] [W][11:30:14][PROT]SEND DATA TYPE:4701, SENDPATH:0x2 [1730201414]
 
2025-07-16 21:02:12,680  [DEBUG] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,686  [DEBUG] [D][11:30:14][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:02:12,689  [DEBUG] [D][11:30:14][PROT]Send_TO_M2M [1730201414]
 
2025-07-16 21:02:12,694  [DEBUG] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:12,697  [DEBUG] [D][11:30:14][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:12,702  [DEBUG] [D][11:30:14][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:12,705  [DEBUG] [D][11:30:14][M2M ]m2m send data len[198]
 
2025-07-16 21:02:12,708  [DEBUG] [D][11:30:14][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:12,716  [DEBUG] [D][11:30:14][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x2005a010] format[0]
 
2025-07-16 21:02:12,723  [DEBUG] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:12,727  [DEBUG] [D][11:30:14][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:12,730  [DEBUG] [D][11:30:14][CAT1]tx ret[17] >>> AT+QISEND=0,198
 
2025-07-16 21:02:12,731  [DEBUG] 
 
2025-07-16 21:02:12,735  [DEBUG] [D][11:30:14][CAT1]Send Data To Server[198][201] ... ->:
 
2025-07-16 21:02:12,754  [DEBUG] 0063B9F0113311331133113311331B88B1E3C692AE645C5E2128BCCB41926302A175EC9CB7ADD146BC5D850E3D638F33DA89119784A3678ED3237BD471C85592F740891E80D1903380B509D3B50B4C5C3E384B410F43EFEA087A71E9AD337228A3CF60
 
2025-07-16 21:02:12,754  [DEBUG] [D][11:30:14][CAT1]<<< 
 
2025-07-16 21:02:12,756  [DEBUG] SEND OK
 
2025-07-16 21:02:12,756  [DEBUG] 
 
2025-07-16 21:02:12,761  [DEBUG] [D][11:30:14][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:12,764  [DEBUG] [D][11:30:14][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:12,764  [DEBUG] 
 
2025-07-16 21:02:12,770  [DEBUG] [D][11:30:14][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:12,775  [DEBUG] [D][11:30:14][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:12,779  [DEBUG] [D][11:30:14][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:12,782  [DEBUG] [D][11:30:14][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:12,789  [DEBUG] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:12,792  [DEBUG] [D][11:30:14][PROT]M2M Send ok [1730201414]
 
2025-07-16 21:02:12,794  [DEBUG] [D][11:30:14][PROT]CLEAN:2
 
2025-07-16 21:02:12,796  [DEBUG] [D][11:30:14][PROT]index:3 1730201414
 
2025-07-16 21:02:12,799  [DEBUG] [D][11:30:14][PROT]is_send:0
 
2025-07-16 21:02:12,802  [DEBUG] [D][11:30:14][PROT]sequence_num:17
 
2025-07-16 21:02:12,804  [DEBUG] [D][11:30:14][PROT]retry_timeout:0
 
2025-07-16 21:02:12,808  [DEBUG] [D][11:30:14][PROT]retry_times:1
 
2025-07-16 21:02:12,811  [DEBUG] [D][11:30:14][PROT]send_path:0x2
 
2025-07-16 21:02:12,817  [DEBUG] [D][11:30:14][PROT]min_index:3, type:0x4705, priority:0
 
2025-07-16 21:02:12,826  [DEBUG] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,831  [DEBUG] [W][11:30:14][PROT]SEND DATA TYPE:4705, SENDPATH:0x2 [1730201414]
 
2025-07-16 21:02:12,836  [DEBUG] [D][11:30:14][PROT]===========================================================
 
2025-07-16 21:02:12,841  [DEBUG] [D][11:30:14][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:02:12,844  [DEBUG] [D][11:30:14][PROT]Send_TO_M2M [1730201414]
 
2025-07-16 21:02:12,850  [DEBUG] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:12,856  [DEBUG] [D][11:30:14][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:12,858  [DEBUG] [D][11:30:14][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:12,861  [DEBUG] [D][11:30:14][M2M ]m2m send data len[390]
 
2025-07-16 21:02:12,866  [DEBUG] [D][11:30:14][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:12,875  [DEBUG] [D][11:30:14][SAL ]cellular SEND socket id[0] type[1], len[390], data[0x2005a010] format[0]
 
2025-07-16 21:02:12,877  [DEBUG] [D][11:30:14][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:12,883  [DEBUG] [D][11:30:14][CAT1]tx ret[17] >>> AT+QISEND=0,390
 
2025-07-16 21:02:12,884  [DEBUG] 
 
2025-07-16 21:02:12,889  [DEBUG] [D][11:30:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:12,891  [DEBUG] [D][11:30:14][CAT1]Send Data To Server[390][390] ... ->:
 
2025-07-16 21:02:12,932  [DEBUG] 00C3B9FD113311331133113311331B88B16129A7D98F2BA09DDC043A25D96B36112CC426E8C9D5AF384D6A5F923ACD50026DF54112FA509C2F4E33D14EE4C400A477848748AFBA1981F7880C73A37C2E985975CC699AC7214364A1A4C4023D3EB4031176237A840A52AA100E37AB19F96342D6BA2A645ABC51B4C35E2BCA20A88A1BBCAD04E5C0B341A5260A35FE838156A73B84656223498B5A995232E498312DABE67451223F9674FB2202AF4FCF5FA1A290438E0C781EBC6418B69A442E2EF0F6D1
 
2025-07-16 21:02:12,933  [DEBUG] [D][11:30:15][CAT1]<<< 
 
2025-07-16 21:02:12,934  [DEBUG] SEND OK
 
2025-07-16 21:02:12,934  [DEBUG] 
 
2025-07-16 21:02:12,935  [DEBUG] [D][11:30:15][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:12,939  [DEBUG] [D][11:30:15][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:12,940  [DEBUG] 
 
2025-07-16 21:02:12,942  [DEBUG] [D][11:30:15][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:12,948  [DEBUG] [D][11:30:15][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:12,952  [DEBUG] [D][11:30:15][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:12,955  [DEBUG] [D][11:30:15][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:12,961  [DEBUG] [D][11:30:15][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:12,966  [DEBUG] [D][11:30:15][PROT]M2M Send ok [1730201415]
 
2025-07-16 21:02:13,871  [DEBUG] [D][11:30:16][GNSS]handler GSMGet Base timeout
 
2025-07-16 21:02:18,114  [DEBUG] [D][11:30:20][PROT]CLEAN,SEND:3
 
2025-07-16 21:02:18,125  [DEBUG] [D][11:30:20][PROT]CLEAN:3
 
2025-07-16 21:02:18,138  [DEBUG] [D][11:30:20][PROT]index:4 1730201420
 
2025-07-16 21:02:18,140  [DEBUG] [D][11:30:20][PROT]is_send:0
 
2025-07-16 21:02:18,143  [DEBUG] [D][11:30:20][PROT]sequence_num:18
 
2025-07-16 21:02:18,146  [DEBUG] [D][11:30:20][PROT]retry_timeout:0
 
2025-07-16 21:02:18,149  [DEBUG] [D][11:30:20][PROT]retry_times:1
 
2025-07-16 21:02:18,151  [DEBUG] [D][11:30:20][PROT]send_path:0x2
 
2025-07-16 21:02:18,157  [DEBUG] [D][11:30:20][PROT]min_index:4, type:0x5103, priority:0
 
2025-07-16 21:02:18,163  [DEBUG] [D][11:30:20][PROT]===========================================================
 
2025-07-16 21:02:18,169  [DEBUG] [W][11:30:20][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730201420]
 
2025-07-16 21:02:18,177  [DEBUG] [D][11:30:20][PROT]===========================================================
 
2025-07-16 21:02:18,180  [DEBUG] [D][11:30:20][COMM]PB encode data:87
 
2025-07-16 21:02:18,198  [DEBUG] 0A55080110001A120A0C464344373333363333453136120020431A120A0C453232453042463545374546120020791A120A0C413430304532463436324330120020791A130A0C4134303045324634363845311200209901
 
2025-07-16 21:02:18,200  [DEBUG] [D][11:30:20][PROT]sending traceid [999999999990000A]
 
2025-07-16 21:02:18,205  [DEBUG] [D][11:30:20][PROT]Send_TO_M2M [1730201420]
 
2025-07-16 21:02:18,207  [DEBUG] [D][11:30:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:18,214  [DEBUG] [D][11:30:20][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:18,216  [DEBUG] [D][11:30:20][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:18,222  [DEBUG] [D][11:30:20][M2M ]m2m send data len[230]
 
2025-07-16 21:02:18,224  [DEBUG] [D][11:30:20][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:18,233  [DEBUG] [D][11:30:20][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x2005a010] format[0]
 
2025-07-16 21:02:18,236  [DEBUG] [D][11:30:20][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:18,242  [DEBUG] [D][11:30:20][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:02:18,242  [DEBUG] 
 
2025-07-16 21:02:18,247  [DEBUG] [D][11:30:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:18,252  [DEBUG] [D][11:30:20][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:02:18,274  [DEBUG] 0073B6FC113311331133113311331B88BCA6E385F425614FFB9D58B45AEB2BAC6BD4A2DC5CE6C2ED8A7FC372BC48A62F7DC13BA27D8B42AD15D78C2942A2983DE9CCB2AA3884BF05B0E01058D6A55F6C279A85BF062025EF6A0B2ECED231BCD63FAADB143AC1FC236BAE5C9CCDFD56248CE0E9
 
2025-07-16 21:02:18,275  [DEBUG] [D][11:30:20][CAT1]<<< 
 
2025-07-16 21:02:18,275  [DEBUG] SEND OK
 
2025-07-16 21:02:18,275  [DEBUG] 
 
2025-07-16 21:02:18,280  [DEBUG] [D][11:30:20][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:18,282  [DEBUG] [D][11:30:20][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:18,283  [DEBUG] 
 
2025-07-16 21:02:18,288  [DEBUG] [D][11:30:20][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:18,294  [DEBUG] [D][11:30:20][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:18,296  [DEBUG] [D][11:30:20][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:18,303  [DEBUG] [D][11:30:20][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:18,305  [DEBUG] [D][11:30:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:18,310  [DEBUG] [D][11:30:20][PROT]M2M Send ok [1730201420]
 
2025-07-16 21:02:23,532  [DEBUG] [D][11:30:25][PROT]CLEAN,SEND:4
 
2025-07-16 21:02:23,542  [DEBUG] [D][11:30:25][PROT]CLEAN:4
 
2025-07-16 21:02:27,866  [DEBUG] [D][11:30:30][CAT1]closed : 0
 
2025-07-16 21:02:27,872  [DEBUG] [D][11:30:30][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:02:27,873  [DEBUG] [D][11:30:30][SAL ]socket closed id[0]
 
2025-07-16 21:02:27,879  [DEBUG] [D][11:30:30][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:02:27,884  [DEBUG] [D][11:30:30][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:02:27,887  [DEBUG] [D][11:30:30][M2M ]m2m select fd[4]
 
2025-07-16 21:02:27,893  [DEBUG] [D][11:30:30][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:02:27,896  [DEBUG] [D][11:30:30][M2M ]tcpclient close[4]
 
2025-07-16 21:02:27,898  [DEBUG] [D][11:30:30][SAL ]socket[4] has closed
 
2025-07-16 21:02:27,904  [DEBUG] [D][11:30:30][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:02:27,910  [DEBUG] [D][11:30:30][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 3
 
2025-07-16 21:02:27,954  [DEBUG] [D][11:30:30][COMM]Main Task receive event:86
 
2025-07-16 21:02:27,963  [DEBUG] [W][11:30:30][PROT]remove success[1730201430],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:02:27,972  [DEBUG] [D][11:30:30][HSDK][0] flush to flash addr:[0xE46500] --- write len --- [256]
 
2025-07-16 21:02:27,980  [DEBUG] [W][11:30:30][PROT]add success [1730201430],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:02:27,986  [DEBUG] [D][11:30:30][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:02:27,988  [DEBUG] [D][11:30:30][PROT]index:0 1730201430
 
2025-07-16 21:02:27,991  [DEBUG] [D][11:30:30][PROT]is_send:0
 
2025-07-16 21:02:27,994  [DEBUG] [D][11:30:30][PROT]sequence_num:19
 
2025-07-16 21:02:27,996  [DEBUG] [D][11:30:30][PROT]retry_timeout:0
 
2025-07-16 21:02:27,999  [DEBUG] [D][11:30:30][PROT]retry_times:1
 
2025-07-16 21:02:28,002  [DEBUG] [D][11:30:30][PROT]send_path:0x2
 
2025-07-16 21:02:28,007  [DEBUG] [D][11:30:30][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:02:28,016  [DEBUG] [D][11:30:30][PROT]===========================================================
 
2025-07-16 21:02:28,023  [DEBUG] [W][11:30:30][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201430]
 
2025-07-16 21:02:28,028  [DEBUG] [D][11:30:30][PROT]===========================================================
 
2025-07-16 21:02:28,033  [DEBUG] [D][11:30:30][PROT]sending traceid [999999999990000B]
 
2025-07-16 21:02:28,035  [DEBUG] [D][11:30:30][PROT]Send_TO_M2M [1730201430]
 
2025-07-16 21:02:28,041  [DEBUG] [D][11:30:30][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:28,046  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:28,049  [DEBUG] [D][11:30:30][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:02:28,059  [DEBUG] [D][11:30:30][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:02:28,061  [DEBUG] [D][11:30:30][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:02:28,069  [DEBUG] [D][11:30:30][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:02:28,075  [DEBUG] [D][11:30:30][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:02:28,077  [DEBUG] [D][11:30:30][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:02:28,083  [DEBUG] [D][11:30:30][CAT1]at ops open socket[0]
 
2025-07-16 21:02:28,086  [DEBUG] [D][11:30:30][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:02:28,087  [DEBUG] 
 
2025-07-16 21:02:28,091  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:02:28,093  [DEBUG] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,094  [DEBUG] +CGATT: 1
 
2025-07-16 21:02:28,094  [DEBUG] 
 
2025-07-16 21:02:28,095  [DEBUG] OK
 
2025-07-16 21:02:28,095  [DEBUG] 
 
2025-07-16 21:02:28,099  [DEBUG] [D][11:30:30][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:02:28,100  [DEBUG] 
 
2025-07-16 21:02:28,100  [DEBUG] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,102  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:02:28,103  [DEBUG] 
 
2025-07-16 21:02:28,103  [DEBUG] OK
 
2025-07-16 21:02:28,104  [DEBUG] 
 
2025-07-16 21:02:28,108  [DEBUG] [D][11:30:30][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:02:28,108  [DEBUG] 
 
2025-07-16 21:02:28,109  [DEBUG] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,110  [DEBUG] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:02:28,111  [DEBUG] 
 
2025-07-16 21:02:28,113  [DEBUG] OK
 
2025-07-16 21:02:28,113  [DEBUG] 
 
2025-07-16 21:02:28,119  [DEBUG] [D][11:30:30][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:02:28,120  [DEBUG] 
 
2025-07-16 21:02:28,121  [DEBUG] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,122  [DEBUG] OK
 
2025-07-16 21:02:28,122  [DEBUG] 
 
2025-07-16 21:02:28,127  [DEBUG] [D][11:30:30][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:02:28,217  [DEBUG] [D][11:30:30][CAT1]opened : 0, 0
 
2025-07-16 21:02:28,219  [DEBUG] [D][11:30:30][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:28,226  [DEBUG] [D][11:30:30][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:02:28,230  [DEBUG] [D][11:30:30][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:02:28,236  [DEBUG] [D][11:30:30][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:02:28,242  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:02:28,245  [DEBUG] [D][11:30:30][SAL ]sock send credit cnt[6]
 
2025-07-16 21:02:28,247  [DEBUG] [D][11:30:30][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:02:28,252  [DEBUG] [D][11:30:30][M2M ]m2m send data len[70]
 
2025-07-16 21:02:28,255  [DEBUG] [D][11:30:30][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:02:28,264  [DEBUG] [D][11:30:30][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:02:28,270  [DEBUG] [D][11:30:30][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:02:28,272  [DEBUG] [D][11:30:30][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:02:28,273  [DEBUG] 
 
2025-07-16 21:02:28,278  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:02:28,283  [DEBUG] [D][11:30:30][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:02:28,289  [DEBUG] 0023B9F1113311331133113311331B88BDA2F86023307D811A4F3516BD21EA985A350C
 
2025-07-16 21:02:28,291  [DEBUG] [D][11:30:30][CAT1]<<< 
 
2025-07-16 21:02:28,292  [DEBUG] SEND OK
 
2025-07-16 21:02:28,292  [DEBUG] 
 
2025-07-16 21:02:28,297  [DEBUG] [D][11:30:30][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:02:28,299  [DEBUG] [D][11:30:30][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:02:28,300  [DEBUG] 
 
2025-07-16 21:02:28,305  [DEBUG] [D][11:30:30][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:02:28,311  [DEBUG] [D][11:30:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:02:28,313  [DEBUG] [D][11:30:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:02:28,319  [DEBUG] [D][11:30:30][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:28,323  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:28,328  [DEBUG] [D][11:30:30][PROT]M2M Send ok [1730201430]
 
2025-07-16 21:02:28,330  [DEBUG] [D][11:30:30][PROT]CLEAN:0
 
2025-07-16 21:02:28,334  [DEBUG] [D][11:30:30][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:02:28,339  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:02:28,344  [DEBUG] [D][11:30:30][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:02:28,347  [DEBUG] [D][11:30:30][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:02:28,354  [DEBUG] [D][11:30:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:02:38,286  [DEBUG] [D][11:30:40][COMM]S->M yaw:INVALID
 
2025-07-16 21:02:39,576  [DEBUG] [D][11:30:41][COMM]M->S yaw:INVALID
 
2025-07-16 21:03:08,507  [DEBUG] [D][11:31:10][COMM]IMU: 273745 MEMS ERROR when cali 0
 
2025-07-16 21:03:08,827  [DEBUG] [D][11:31:11][CAT1]closed : 0
 
2025-07-16 21:03:08,832  [DEBUG] [D][11:31:11][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:03:08,835  [DEBUG] [D][11:31:11][SAL ]socket closed id[0]
 
2025-07-16 21:03:08,841  [DEBUG] [D][11:31:11][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:03:08,846  [DEBUG] [D][11:31:11][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:03:08,848  [DEBUG] [D][11:31:11][M2M ]m2m select fd[4]
 
2025-07-16 21:03:08,855  [DEBUG] [D][11:31:11][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:03:08,857  [DEBUG] [D][11:31:11][M2M ]tcpclient close[4]
 
2025-07-16 21:03:08,860  [DEBUG] [D][11:31:11][SAL ]socket[4] has closed
 
2025-07-16 21:03:08,866  [DEBUG] [D][11:31:11][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:03:08,871  [DEBUG] [D][11:31:11][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 4
 
2025-07-16 21:03:08,895  [DEBUG] [D][11:31:11][COMM]Main Task receive event:86
 
2025-07-16 21:03:08,902  [DEBUG] [W][11:31:11][PROT]remove success[1730201471],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:03:08,910  [DEBUG] [W][11:31:11][PROT]add success [1730201471],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:03:08,916  [DEBUG] [D][11:31:11][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:03:08,919  [DEBUG] [D][11:31:11][PROT]index:0 1730201471
 
2025-07-16 21:03:08,923  [DEBUG] [D][11:31:11][PROT]is_send:0
 
2025-07-16 21:03:08,928  [DEBUG] [D][11:31:11][PROT]sequence_num:20
 
2025-07-16 21:03:08,931  [DEBUG] [D][11:31:11][PROT]retry_timeout:0
 
2025-07-16 21:03:08,933  [DEBUG] [D][11:31:11][PROT]retry_times:1
 
2025-07-16 21:03:08,936  [DEBUG] [D][11:31:11][PROT]send_path:0x2
 
2025-07-16 21:03:08,941  [DEBUG] [D][11:31:11][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:03:08,948  [DEBUG] [D][11:31:11][PROT]===========================================================
 
2025-07-16 21:03:08,954  [DEBUG] [W][11:31:11][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201471]
 
2025-07-16 21:03:08,962  [DEBUG] [D][11:31:11][PROT]===========================================================
 
2025-07-16 21:03:08,964  [DEBUG] [D][11:31:11][PROT]sending traceid [999999999990000C]
 
2025-07-16 21:03:08,969  [DEBUG] [D][11:31:11][PROT]Send_TO_M2M [1730201471]
 
2025-07-16 21:03:08,975  [DEBUG] [D][11:31:11][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:08,978  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:08,983  [DEBUG] [D][11:31:11][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:03:08,990  [DEBUG] [D][11:31:11][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:03:08,995  [DEBUG] [D][11:31:11][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:03:09,001  [DEBUG] [D][11:31:11][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:03:09,009  [DEBUG] [D][11:31:11][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:03:09,011  [DEBUG] [D][11:31:11][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:03:09,014  [DEBUG] [D][11:31:11][CAT1]at ops open socket[0]
 
2025-07-16 21:03:09,020  [DEBUG] [D][11:31:11][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:03:09,020  [DEBUG] 
 
2025-07-16 21:03:09,026  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:03:09,027  [DEBUG] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,028  [DEBUG] +CGATT: 1
 
2025-07-16 21:03:09,028  [DEBUG] 
 
2025-07-16 21:03:09,028  [DEBUG] OK
 
2025-07-16 21:03:09,029  [DEBUG] 
 
2025-07-16 21:03:09,030  [DEBUG] [D][11:31:11][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:03:09,031  [DEBUG] 
 
2025-07-16 21:03:09,033  [DEBUG] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,034  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:03:09,035  [DEBUG] 
 
2025-07-16 21:03:09,036  [DEBUG] OK
 
2025-07-16 21:03:09,036  [DEBUG] 
 
2025-07-16 21:03:09,039  [DEBUG] [D][11:31:11][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:03:09,040  [DEBUG] 
 
2025-07-16 21:03:09,042  [DEBUG] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,044  [DEBUG] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:03:09,045  [DEBUG] 
 
2025-07-16 21:03:09,045  [DEBUG] OK
 
2025-07-16 21:03:09,046  [DEBUG] 
 
2025-07-16 21:03:09,053  [DEBUG] [D][11:31:11][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:03:09,054  [DEBUG] 
 
2025-07-16 21:03:09,055  [DEBUG] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,056  [DEBUG] OK
 
2025-07-16 21:03:09,057  [DEBUG] 
 
2025-07-16 21:03:09,060  [DEBUG] [D][11:31:11][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:03:09,165  [DEBUG] [D][11:31:11][CAT1]opened : 0, 0
 
2025-07-16 21:03:09,168  [DEBUG] [D][11:31:11][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:09,174  [DEBUG] [D][11:31:11][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:03:09,180  [DEBUG] [D][11:31:11][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:03:09,185  [DEBUG] [D][11:31:11][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:03:09,190  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:03:09,193  [DEBUG] [D][11:31:11][SAL ]sock send credit cnt[6]
 
2025-07-16 21:03:09,195  [DEBUG] [D][11:31:11][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:03:09,202  [DEBUG] [D][11:31:11][M2M ]m2m send data len[70]
 
2025-07-16 21:03:09,204  [DEBUG] [D][11:31:11][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:03:09,213  [DEBUG] [D][11:31:11][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:03:09,219  [DEBUG] [D][11:31:11][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:03:09,221  [DEBUG] [D][11:31:11][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:03:09,222  [DEBUG] 
 
2025-07-16 21:03:09,226  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:03:09,231  [DEBUG] [D][11:31:11][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:03:09,238  [DEBUG] 0023B9F2113311331133113311331B88B011E819F7C6E94D9730959F41121DA84AE768
 
2025-07-16 21:03:09,240  [DEBUG] [D][11:31:11][CAT1]<<< 
 
2025-07-16 21:03:09,241  [DEBUG] SEND OK
 
2025-07-16 21:03:09,241  [DEBUG] 
 
2025-07-16 21:03:09,246  [DEBUG] [D][11:31:11][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:03:09,248  [DEBUG] [D][11:31:11][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:03:09,250  [DEBUG] 
 
2025-07-16 21:03:09,254  [DEBUG] [D][11:31:11][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:09,261  [DEBUG] [D][11:31:11][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:03:09,263  [DEBUG] [D][11:31:11][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:03:09,269  [DEBUG] [D][11:31:11][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:09,272  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:03:09,277  [DEBUG] [D][11:31:11][PROT]M2M Send ok [1730201471]
 
2025-07-16 21:03:09,279  [DEBUG] [D][11:31:11][PROT]CLEAN:0
 
2025-07-16 21:03:09,282  [DEBUG] [D][11:31:11][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:09,288  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:09,294  [DEBUG] [D][11:31:11][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:03:09,296  [DEBUG] [D][11:31:11][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:09,303  [DEBUG] [D][11:31:11][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:03:49,786  [DEBUG] [D][11:31:52][CAT1]closed : 0
 
2025-07-16 21:03:49,791  [DEBUG] [D][11:31:52][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:03:49,794  [DEBUG] [D][11:31:52][SAL ]socket closed id[0]
 
2025-07-16 21:03:49,799  [DEBUG] [D][11:31:52][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:03:49,806  [DEBUG] [D][11:31:52][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:03:49,808  [DEBUG] [D][11:31:52][M2M ]m2m select fd[4]
 
2025-07-16 21:03:49,813  [DEBUG] [D][11:31:52][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:03:49,816  [DEBUG] [D][11:31:52][M2M ]tcpclient close[4]
 
2025-07-16 21:03:49,819  [DEBUG] [D][11:31:52][SAL ]socket[4] has closed
 
2025-07-16 21:03:49,824  [DEBUG] [D][11:31:52][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:03:49,831  [DEBUG] [D][11:31:52][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 5
 
2025-07-16 21:03:49,833  [DEBUG] [D][11:31:52][COMM]Main Task receive event:86
 
2025-07-16 21:03:49,841  [DEBUG] [W][11:31:52][PROT]remove success[1730201512],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:03:49,848  [DEBUG] [D][11:31:52][PROT]index:0 1730201512
 
2025-07-16 21:03:49,850  [DEBUG] [D][11:31:52][PROT]is_send:0
 
2025-07-16 21:03:49,853  [DEBUG] [D][11:31:52][PROT]sequence_num:21
 
2025-07-16 21:03:49,855  [DEBUG] [D][11:31:52][PROT]retry_timeout:0
 
2025-07-16 21:03:49,858  [DEBUG] [D][11:31:52][PROT]retry_times:1
 
2025-07-16 21:03:49,861  [DEBUG] [D][11:31:52][PROT]send_path:0x2
 
2025-07-16 21:03:49,866  [DEBUG] [D][11:31:52][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:03:49,871  [DEBUG] [D][11:31:52][PROT]===========================================================
 
2025-07-16 21:03:49,878  [DEBUG] [D][11:31:52][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:49,884  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:49,886  [DEBUG] [D][11:31:52][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:03:49,895  [DEBUG] [D][11:31:52][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:03:49,897  [DEBUG] [D][11:31:52][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:03:49,906  [DEBUG] [D][11:31:52][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:03:49,911  [DEBUG] [D][11:31:52][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:03:49,916  [DEBUG] [W][11:31:52][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201512]
 
2025-07-16 21:03:49,925  [DEBUG] [D][11:31:52][PROT]===========================================================
 
2025-07-16 21:03:49,927  [DEBUG] [D][11:31:52][PROT]sending traceid [999999999990000D]
 
2025-07-16 21:03:49,933  [DEBUG] [D][11:31:52][PROT]Send_TO_M2M [1730201512]
 
2025-07-16 21:03:49,941  [DEBUG] [W][11:31:52][PROT]add success [1730201512],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:03:49,947  [DEBUG] [D][11:31:52][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:03:49,950  [DEBUG] [D][11:31:52][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:03:49,955  [DEBUG] [D][11:31:52][CAT1]at ops open socket[0]
 
2025-07-16 21:03:49,958  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:03:49,963  [DEBUG] [D][11:31:52][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:03:49,964  [DEBUG] 
 
2025-07-16 21:03:49,966  [DEBUG] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,966  [DEBUG] +CGATT: 1
 
2025-07-16 21:03:49,966  [DEBUG] 
 
2025-07-16 21:03:49,966  [DEBUG] OK
 
2025-07-16 21:03:49,968  [DEBUG] 
 
2025-07-16 21:03:49,972  [DEBUG] [D][11:31:52][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:03:49,973  [DEBUG] 
 
2025-07-16 21:03:49,973  [DEBUG] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,974  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:03:49,975  [DEBUG] 
 
2025-07-16 21:03:49,975  [DEBUG] OK
 
2025-07-16 21:03:49,975  [DEBUG] 
 
2025-07-16 21:03:49,978  [DEBUG] [D][11:31:52][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:03:49,980  [DEBUG] 
 
2025-07-16 21:03:49,980  [DEBUG] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,983  [DEBUG] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:03:49,984  [DEBUG] 
 
2025-07-16 21:03:49,985  [DEBUG] OK
 
2025-07-16 21:03:49,985  [DEBUG] 
 
2025-07-16 21:03:49,992  [DEBUG] [D][11:31:52][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:03:49,993  [DEBUG] 
 
2025-07-16 21:03:49,994  [DEBUG] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:49,994  [DEBUG] OK
 
2025-07-16 21:03:49,995  [DEBUG] 
 
2025-07-16 21:03:49,999  [DEBUG] [D][11:31:52][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:03:50,075  [DEBUG] [D][11:31:52][CAT1]opened : 0, 0
 
2025-07-16 21:03:50,077  [DEBUG] [D][11:31:52][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:50,083  [DEBUG] [D][11:31:52][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:03:50,088  [DEBUG] [D][11:31:52][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:03:50,093  [DEBUG] [D][11:31:52][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:03:50,099  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:03:50,101  [DEBUG] [D][11:31:52][SAL ]sock send credit cnt[6]
 
2025-07-16 21:03:50,104  [DEBUG] [D][11:31:52][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:03:50,111  [DEBUG] [D][11:31:52][M2M ]m2m send data len[70]
 
2025-07-16 21:03:50,113  [DEBUG] [D][11:31:52][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:03:50,122  [DEBUG] [D][11:31:52][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:03:50,127  [DEBUG] [D][11:31:52][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:03:50,130  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:03:50,135  [DEBUG] [D][11:31:52][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:03:50,136  [DEBUG] 
 
2025-07-16 21:03:50,141  [DEBUG] [D][11:31:52][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:03:50,147  [DEBUG] 0023B9FE113311331133113311331B88BF0B481DA392E4B7C005C641C444A6CE7BB173
 
2025-07-16 21:03:50,149  [DEBUG] [D][11:31:52][CAT1]<<< 
 
2025-07-16 21:03:50,150  [DEBUG] SEND OK
 
2025-07-16 21:03:50,150  [DEBUG] 
 
2025-07-16 21:03:50,155  [DEBUG] [D][11:31:52][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:03:50,158  [DEBUG] [D][11:31:52][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:03:50,158  [DEBUG] 
 
2025-07-16 21:03:50,163  [DEBUG] [D][11:31:52][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:03:50,170  [DEBUG] [D][11:31:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:03:50,171  [DEBUG] [D][11:31:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:03:50,177  [DEBUG] [D][11:31:52][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:50,180  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:03:50,185  [DEBUG] [D][11:31:52][PROT]M2M Send ok [1730201512]
 
2025-07-16 21:03:50,188  [DEBUG] [D][11:31:52][PROT]CLEAN:0
 
2025-07-16 21:03:50,192  [DEBUG] [D][11:31:52][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:03:50,197  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:03:50,203  [DEBUG] [D][11:31:52][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:03:50,205  [DEBUG] [D][11:31:52][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:03:50,211  [DEBUG] [D][11:31:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:30,787  [DEBUG] [D][11:32:33][CAT1]closed : 0
 
2025-07-16 21:04:30,793  [DEBUG] [D][11:32:33][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:04:30,796  [DEBUG] [D][11:32:33][SAL ]socket closed id[0]
 
2025-07-16 21:04:30,802  [DEBUG] [D][11:32:33][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:04:30,806  [DEBUG] [D][11:32:33][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:04:30,809  [DEBUG] [D][11:32:33][M2M ]m2m select fd[4]
 
2025-07-16 21:04:30,815  [DEBUG] [D][11:32:33][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:04:30,818  [DEBUG] [D][11:32:33][M2M ]tcpclient close[4]
 
2025-07-16 21:04:30,821  [DEBUG] [D][11:32:33][SAL ]socket[4] has closed
 
2025-07-16 21:04:30,826  [DEBUG] [D][11:32:33][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:04:30,831  [DEBUG] [D][11:32:33][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 6
 
2025-07-16 21:04:30,869  [DEBUG] [D][11:32:33][COMM]Main Task receive event:86
 
2025-07-16 21:04:30,875  [DEBUG] [D][11:32:33][HSDK][0] flush to flash addr:[0xE46600] --- write len --- [256]
 
2025-07-16 21:04:30,884  [DEBUG] [W][11:32:33][PROT]remove success[1730201553],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:04:30,893  [DEBUG] [W][11:32:33][PROT]add success [1730201553],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:04:30,899  [DEBUG] [D][11:32:33][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:04:30,902  [DEBUG] [D][11:32:33][PROT]index:0 1730201553
 
2025-07-16 21:04:30,905  [DEBUG] [D][11:32:33][PROT]is_send:0
 
2025-07-16 21:04:30,907  [DEBUG] [D][11:32:33][PROT]sequence_num:22
 
2025-07-16 21:04:30,911  [DEBUG] [D][11:32:33][PROT]retry_timeout:0
 
2025-07-16 21:04:30,912  [DEBUG] [D][11:32:33][PROT]retry_times:1
 
2025-07-16 21:04:30,915  [DEBUG] [D][11:32:33][PROT]send_path:0x2
 
2025-07-16 21:04:30,921  [DEBUG] [D][11:32:33][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:04:30,930  [DEBUG] [D][11:32:33][PROT]===========================================================
 
2025-07-16 21:04:30,936  [DEBUG] [W][11:32:33][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201553]
 
2025-07-16 21:04:30,941  [DEBUG] [D][11:32:33][PROT]===========================================================
 
2025-07-16 21:04:30,946  [DEBUG] [D][11:32:33][PROT]sending traceid [999999999990000E]
 
2025-07-16 21:04:30,949  [DEBUG] [D][11:32:33][PROT]Send_TO_M2M [1730201553]
 
2025-07-16 21:04:30,955  [DEBUG] [D][11:32:33][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:04:30,960  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:04:30,962  [DEBUG] [D][11:32:33][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:04:30,972  [DEBUG] [D][11:32:33][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:04:30,974  [DEBUG] [D][11:32:33][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:04:30,984  [DEBUG] [D][11:32:33][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-16 21:04:30,989  [DEBUG] [D][11:32:33][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:04:30,991  [DEBUG] [D][11:32:33][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:04:30,997  [DEBUG] [D][11:32:33][CAT1]at ops open socket[0]
 
2025-07-16 21:04:30,999  [DEBUG] [D][11:32:33][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:04:31,000  [DEBUG] 
 
2025-07-16 21:04:31,005  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:04:31,007  [DEBUG] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,008  [DEBUG] +CGATT: 1
 
2025-07-16 21:04:31,008  [DEBUG] 
 
2025-07-16 21:04:31,008  [DEBUG] OK
 
2025-07-16 21:04:31,009  [DEBUG] 
 
2025-07-16 21:04:31,012  [DEBUG] [D][11:32:33][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:04:31,013  [DEBUG] 
 
2025-07-16 21:04:31,014  [DEBUG] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,015  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:04:31,016  [DEBUG] 
 
2025-07-16 21:04:31,017  [DEBUG] OK
 
2025-07-16 21:04:31,017  [DEBUG] 
 
2025-07-16 21:04:31,021  [DEBUG] [D][11:32:33][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:04:31,022  [DEBUG] 
 
2025-07-16 21:04:31,022  [DEBUG] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,024  [DEBUG] +QIACT: 1,1,1,"10.33.145.173"
 
2025-07-16 21:04:31,024  [DEBUG] 
 
2025-07-16 21:04:31,026  [DEBUG] OK
 
2025-07-16 21:04:31,027  [DEBUG] 
 
2025-07-16 21:04:31,033  [DEBUG] [D][11:32:33][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:04:31,035  [DEBUG] 
 
2025-07-16 21:04:31,037  [DEBUG] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,038  [DEBUG] OK
 
2025-07-16 21:04:31,039  [DEBUG] 
 
2025-07-16 21:04:31,040  [DEBUG] [D][11:32:33][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:04:31,127  [DEBUG] [D][11:32:33][CAT1]opened : 0, 0
 
2025-07-16 21:04:31,130  [DEBUG] [D][11:32:33][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:31,136  [DEBUG] [D][11:32:33][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:04:31,141  [DEBUG] [D][11:32:33][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:04:31,147  [DEBUG] [D][11:32:33][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:04:31,153  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:04:31,156  [DEBUG] [D][11:32:33][SAL ]sock send credit cnt[6]
 
2025-07-16 21:04:31,158  [DEBUG] [D][11:32:33][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:04:31,167  [DEBUG] [D][11:32:33][M2M ]m2m send data len[70]
 
2025-07-16 21:04:31,168  [DEBUG] [D][11:32:33][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:04:31,174  [DEBUG] [D][11:32:33][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-16 21:04:31,180  [DEBUG] [D][11:32:33][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:04:31,183  [DEBUG] [D][11:32:33][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:04:31,183  [DEBUG] 
 
2025-07-16 21:04:31,189  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:04:31,194  [DEBUG] [D][11:32:33][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:04:31,200  [DEBUG] 0023B9F3113311331133113311331B88BA4AF2E0ACD634682DB91A782CAEDFEF29DDB3
 
2025-07-16 21:04:31,202  [DEBUG] [D][11:32:33][CAT1]<<< 
 
2025-07-16 21:04:31,203  [DEBUG] SEND OK
 
2025-07-16 21:04:31,203  [DEBUG] 
 
2025-07-16 21:04:31,208  [DEBUG] [D][11:32:33][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:04:31,210  [DEBUG] [D][11:32:33][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:04:31,212  [DEBUG] 
 
2025-07-16 21:04:31,216  [DEBUG] [D][11:32:33][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:31,222  [DEBUG] [D][11:32:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:04:31,224  [DEBUG] [D][11:32:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:04:31,230  [DEBUG] [D][11:32:33][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:31,233  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:31,239  [DEBUG] [D][11:32:33][PROT]M2M Send ok [1730201553]
 
2025-07-16 21:04:31,241  [DEBUG] [D][11:32:33][PROT]CLEAN:0
 
2025-07-16 21:04:31,244  [DEBUG] [D][11:32:33][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:04:31,250  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:04:31,255  [DEBUG] [D][11:32:33][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:04:31,258  [DEBUG] [D][11:32:33][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:31,263  [DEBUG] [D][11:32:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:53,864  [DEBUG] [D][11:32:56][GNSS]location_callback:event=3
 
2025-07-16 21:04:54,573  [DEBUG] [D][11:32:56][COMM]Main Task receive event:14
 
2025-07-16 21:04:54,578  [DEBUG] [D][11:32:56][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:04:54,580  [DEBUG] [D][11:32:56][GNSS]stop event:3
 
2025-07-16 21:04:54,583  [DEBUG] [D][11:32:56][GNSS]GPS stop. ret=0
 
2025-07-16 21:04:54,586  [DEBUG] [D][11:32:56][COMM]Period location failed
 
2025-07-16 21:04:54,589  [DEBUG] [D][11:32:56][COMM]getLocatInfoStep2:1
 
2025-07-16 21:04:54,590  [DEBUG] 
 
2025-07-16 21:04:54,600  [DEBUG] [D][11:32:56][COMM]f:frm_park_cam_get_info. done reason:[1],type:[0],result:[0x04],err:[0xFF],angle:[0xFFFF],dist:[255]!!!
 
2025-07-16 21:04:54,606  [DEBUG] [D][11:32:56][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:04:54,608  [DEBUG] [D][11:32:56][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:04:54,611  [DEBUG] [W][11:32:56][COMM]get bat state1 error
 
2025-07-16 21:04:54,614  [DEBUG] [D][11:32:56][GNSS]5F01 soc:255
 
2025-07-16 21:04:54,620  [DEBUG] [W][11:32:56][COMM]get mc state information fail
 
2025-07-16 21:04:54,625  [DEBUG] [W][11:32:56][COMM]get mc speed information fail
 
2025-07-16 21:04:54,628  [DEBUG] [W][11:32:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 21:04:54,633  [DEBUG] [D][11:32:56][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:04:54,636  [DEBUG] [D][11:32:56][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:04:54,639  [DEBUG] [D][11:32:56][GNSS]nSatsInUse 0
 
2025-07-16 21:04:54,645  [DEBUG] [D][11:32:56][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:04:54,653  [DEBUG] [D][11:32:56][COMM]realtime_info.pitch_angle=25,realtime_info.roll_angle=0,realtime_info.nav_angle=32763
 
2025-07-16 21:04:54,664  [DEBUG] [W][11:32:56][COMM]5F04 LocFail:reason:0x01;diff:41576;LocUsedTime:0;LocStatus|Type:3|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:04:54,666  [DEBUG] [D][11:32:56][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:04:54,668  [DEBUG] 
 
2025-07-16 21:04:54,675  [DEBUG] [W][11:32:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:04:54,680  [DEBUG] [W][11:32:56][COMM]get mc power mode information fail
 
2025-07-16 21:04:54,683  [DEBUG] [D][11:32:56][COMM]can to 485 :254
 
2025-07-16 21:04:54,683  [DEBUG] 
 
2025-07-16 21:04:54,692  [DEBUG] [W][11:32:56][PROT]remove success[1730201576],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:04:54,700  [DEBUG] [W][11:32:56][PROT]add success [1730201576],send_path[2],type[5F04],priority[0],index[0],used[1]
 
2025-07-16 21:04:54,703  [DEBUG] [D][11:32:56][PROT]index:0 1730201576
 
2025-07-16 21:04:54,705  [DEBUG] [D][11:32:56][PROT]is_send:0
 
2025-07-16 21:04:54,708  [DEBUG] [D][11:32:56][PROT]sequence_num:23
 
2025-07-16 21:04:54,714  [DEBUG] [D][11:32:56][PROT]retry_timeout:0
 
2025-07-16 21:04:54,717  [DEBUG] [D][11:32:56][PROT]retry_times:1
 
2025-07-16 21:04:54,719  [DEBUG] [D][11:32:56][PROT]send_path:0x2
 
2025-07-16 21:04:54,722  [DEBUG] [D][11:32:56][PROT]min_index:0, type:0x5F04, priority:0
 
2025-07-16 21:04:54,732  [DEBUG] [D][11:32:56][PROT]===========================================================
 
2025-07-16 21:04:54,737  [DEBUG] [D][11:32:56][HSDK][0] flush to flash addr:[0xE46700] --- write len --- [256]
 
2025-07-16 21:04:54,742  [DEBUG] [W][11:32:56][PROT]SEND DATA TYPE:5F04, SENDPATH:0x2 [1730201576]
 
2025-07-16 21:04:54,751  [DEBUG] [D][11:32:56][PROT]===========================================================
 
2025-07-16 21:04:54,753  [DEBUG] [D][11:32:56][COMM]PB encode data:94
 
2025-07-16 21:04:54,771  [DEBUG] 0A5C0A1018FBFF01201928006080CB80B9066803200228003000380160FF0168FF0198010EA0010EA8010DB80107C00109C801FFFF01D80105E00100E80100F00101F80100800201880200900204AA02021800D802FE01E80200F002FE01
 
2025-07-16 21:04:54,776  [DEBUG] [D][11:32:56][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:04:54,778  [DEBUG] [D][11:32:56][PROT]Send_TO_M2M [1730201576]
 
2025-07-16 21:04:54,784  [DEBUG] [D][11:32:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:04:54,786  [DEBUG] [D][11:32:56][SAL ]sock send credit cnt[6]
 
2025-07-16 21:04:54,792  [DEBUG] [D][11:32:56][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:04:54,795  [DEBUG] [D][11:32:56][M2M ]m2m send data len[262]
 
2025-07-16 21:04:54,801  [DEBUG] [D][11:32:56][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:04:54,810  [DEBUG] [D][11:32:56][SAL ]cellular SEND socket id[0] type[1], len[262], data[0x2005a018] format[0]
 
2025-07-16 21:04:54,812  [DEBUG] [D][11:32:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:04:54,814  [DEBUG] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,815  [DEBUG] OK
 
2025-07-16 21:04:54,815  [DEBUG] 
 
2025-07-16 21:04:54,820  [DEBUG] [D][11:32:56][CAT1]tx ret[13] >>> AT+GPSRTK=0
 
2025-07-16 21:04:54,821  [DEBUG] 
 
2025-07-16 21:04:54,822  [DEBUG] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,823  [DEBUG] OK
 
2025-07-16 21:04:54,823  [DEBUG] 
 
2025-07-16 21:04:54,826  [DEBUG] [D][11:32:56][CAT1]tx ret[12] >>> AT+GPSDR=0
 
2025-07-16 21:04:54,826  [DEBUG] 
 
2025-07-16 21:04:54,828  [DEBUG] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,829  [DEBUG] OK
 
2025-07-16 21:04:54,829  [DEBUG] 
 
2025-07-16 21:04:54,834  [DEBUG] [D][11:32:56][CAT1]exec over: func id: 24, ret: 6
 
2025-07-16 21:04:54,836  [DEBUG] [D][11:32:56][CAT1]sub id: 24, ret: 6
 
2025-07-16 21:04:54,837  [DEBUG] 
 
2025-07-16 21:04:54,842  [DEBUG] [D][11:32:56][CAT1]gsm read msg sub id: 26
 
2025-07-16 21:04:54,844  [DEBUG] [D][11:32:56][CAT1]tx ret[18] >>> AT+WIFISCAN=8,45
 
2025-07-16 21:04:54,845  [DEBUG] 
 
2025-07-16 21:04:54,847  [DEBUG] [D][11:32:56][CAT1]<<< 
 
2025-07-16 21:04:54,848  [DEBUG] OK
 
2025-07-16 21:04:54,848  [DEBUG] 
 
2025-07-16 21:04:54,853  [DEBUG] [D][11:32:56][CAT1]exec over: func id: 26, ret: 6
 
2025-07-16 21:04:54,855  [DEBUG] [D][11:32:56][CAT1]gsm read msg sub id: 13
 
2025-07-16 21:04:54,860  [DEBUG] [D][11:32:56][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:04:54,861  [DEBUG] 
 
2025-07-16 21:04:54,872  [DEBUG] [D][11:32:57][GNSS]recv submsg id[1]
 
2025-07-16 21:04:54,875  [DEBUG] [D][11:32:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
 
2025-07-16 21:04:54,879  [DEBUG] [D][11:32:57][GNSS]location stop evt done evt
 
2025-07-16 21:04:55,245  [DEBUG] [D][11:32:57][CAT1]<<< 
 
2025-07-16 21:04:55,248  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:04:55,248  [DEBUG] 
 
2025-07-16 21:04:55,249  [DEBUG] OK
 
2025-07-16 21:04:55,251  [DEBUG] 
 
2025-07-16 21:04:55,254  [DEBUG] [D][11:32:57][CAT1]exec over: func id: 13, ret: 21
 
2025-07-16 21:04:55,256  [DEBUG] [D][11:32:57][M2M ]get csq[31]
 
2025-07-16 21:04:55,260  [DEBUG] [D][11:32:57][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:04:55,264  [DEBUG] [D][11:32:57][CAT1]tx ret[17] >>> AT+QISEND=0,262
 
2025-07-16 21:04:55,264  [DEBUG] 
 
2025-07-16 21:04:55,302  [DEBUG] [D][11:32:57][CAT1]Send Data To Server[262][265] ... ->:
 
2025-07-16 21:04:55,327  [DEBUG] 0083B6F5113311331133113311331B88B11278D909AC919600DB01534FEA0E8A89CC5525B4C45B6B9A3F2711A3D86767B4FCE2ABE4DFCBCE6C6F942F33C4EBEBDA80CF5797A88A350E513D636974EB7FD7267E6D03B08D509B9C3EC8C856CB137950C105460735717B98364905BB9344B7CCA92C529444AB476F33055D936A2BBAA660
 
2025-07-16 21:04:55,335  [DEBUG] [D][11:32:57][CAT1]<<< 
 
2025-07-16 21:04:55,337  [DEBUG] SEND OK
 
2025-07-16 21:04:55,337  [DEBUG] 
 
2025-07-16 21:04:55,340  [DEBUG] [D][11:32:57][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:04:55,342  [DEBUG] [D][11:32:57][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:04:55,345  [DEBUG] 
 
2025-07-16 21:04:55,349  [DEBUG] [D][11:32:57][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:55,355  [DEBUG] [D][11:32:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:04:55,359  [DEBUG] [D][11:32:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:04:55,361  [DEBUG] [D][11:32:57][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:55,368  [DEBUG] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:55,371  [DEBUG] [D][11:32:57][PROT]M2M Send ok [1730201577]
 
2025-07-16 21:04:55,373  [DEBUG] [D][11:32:57][PROT]CLEAN:0
 
2025-07-16 21:04:55,513  [DEBUG] [D][11:32:57][COMM]lastHelmetOrderIdV2:00
 
2025-07-16 21:04:55,516  [DEBUG] >>>>>RESEND ALLSTATE<<<<<
 
2025-07-16 21:04:55,524  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:04:55,532  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5006],priority[2],index[0],used[1]
 
2025-07-16 21:04:55,535  [DEBUG] [D][11:32:57][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:04:55,540  [DEBUG] [D][11:32:57][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:04:55,543  [DEBUG] [W][11:32:57][COMM]get soc error
 
2025-07-16 21:04:55,546  [DEBUG] [W][11:32:57][GNSS]stop locating
 
2025-07-16 21:04:55,549  [DEBUG] [D][11:32:57][GNSS]all continue location stop
 
2025-07-16 21:04:55,554  [DEBUG] [W][11:32:57][GNSS]sing locating running
 
2025-07-16 21:04:55,560  [DEBUG] [E][11:32:57][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:04:55,562  [DEBUG] [D][11:32:57][COMM]report elecbike, soc 0, reason 2
 
2025-07-16 21:04:55,574  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:04:55,582  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5D05],priority[3],index[1],used[1]
 
2025-07-16 21:04:55,585  [DEBUG] [D][11:32:57][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:04:55,590  [DEBUG] [D][11:32:57][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:04:55,592  [DEBUG] [D][11:32:57][PROT]index:1 1730201577
 
2025-07-16 21:04:55,595  [DEBUG] [D][11:32:57][PROT]is_send:0
 
2025-07-16 21:04:55,599  [DEBUG] [D][11:32:57][PROT]sequence_num:25
 
2025-07-16 21:04:55,601  [DEBUG] [D][11:32:57][PROT]retry_timeout:0
 
2025-07-16 21:04:55,604  [DEBUG] [D][11:32:57][PROT]retry_times:3
 
2025-07-16 21:04:55,607  [DEBUG] [D][11:32:57][PROT]send_path:0x2
 
2025-07-16 21:04:55,613  [DEBUG] [D][11:32:57][PROT]min_index:1, type:0x5D05, priority:3
 
2025-07-16 21:04:55,619  [DEBUG] [D][11:32:57][PROT]===========================================================
 
2025-07-16 21:04:55,625  [DEBUG] [W][11:32:57][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201577]
 
2025-07-16 21:04:55,633  [DEBUG] [D][11:32:57][PROT]===========================================================
 
2025-07-16 21:04:55,636  [DEBUG] [D][11:32:57][COMM]PB encode data:29
 
2025-07-16 21:04:55,642  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:04:55,646  [DEBUG] [D][11:32:57][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:04:55,649  [DEBUG] [D][11:32:57][PROT]Send_TO_M2M [1730201577]
 
2025-07-16 21:04:55,657  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:04:55,666  [DEBUG] [D][11:32:57][HSDK][0] flush to flash addr:[0xE46800] --- write len --- [256]
 
2025-07-16 21:04:55,669  [DEBUG] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:04:55,675  [DEBUG] [D][11:32:57][SAL ]sock send credit cnt[6]
 
2025-07-16 21:04:55,677  [DEBUG] [D][11:32:57][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:04:55,683  [DEBUG] [D][11:32:57][M2M ]m2m send data len[134]
 
2025-07-16 21:04:55,685  [DEBUG] [D][11:32:57][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:04:55,694  [DEBUG] [D][11:32:57][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:04:55,696  [DEBUG] [D][11:32:57][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:04:55,708  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[FF0E],priority[0],index[2],used[1]
 
2025-07-16 21:04:55,717  [DEBUG] [D][11:32:57][COMM]buried data a:10,b:2,c:255,d:0,f:16843008,k:5519,l:303,m:11,n:15,o:7,p:1673,q:2301,r:5519,z:665
 
2025-07-16 21:04:55,722  [DEBUG] [D][11:32:57][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:04:55,723  [DEBUG] 
 
2025-07-16 21:04:55,730  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:04:55,738  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 21:04:55,744  [DEBUG] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:04:55,746  [DEBUG] [D][11:32:57][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:04:55,753  [DEBUG] [D][11:32:57][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:04:55,755  [DEBUG] [D][11:32:57][GNSS]nSatsInUse 0
 
2025-07-16 21:04:55,767  [DEBUG] [W][11:32:57][COMM]5A07 LocFail:reason:0x01;diff:41577;LocUsedTime:0;LocStatus|Type:3|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:04:55,771  [DEBUG] [W][11:32:57][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:04:55,780  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:04:55,788  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5A07],priority[0],index[4],used[1]
 
2025-07-16 21:04:55,793  [DEBUG] [D][11:32:57][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:04:55,806  [DEBUG] 0043B6F6113311331133113311331B88B1B5151B374FAF1DD2611DFAECF23750E699619ECCB6BA59F2C4E791E7093D104876A053428D4C2BB72CD4F2A82C47333186E5
 
2025-07-16 21:04:55,811  [DEBUG] [D][11:32:57][COMM]Main Task receive event:14 finished processing
 
2025-07-16 21:04:55,817  [DEBUG] [D][11:32:57][COMM]Main Task receive event:54
 
2025-07-16 21:04:55,819  [DEBUG] [D][11:32:57][COMM][D301]:type:1, trace id:0
 
2025-07-16 21:04:55,821  [DEBUG] [D][11:32:57][COMM]get bat basic info err
 
2025-07-16 21:04:55,831  [DEBUG] [D][11:32:57][HSDK][0] flush to flash addr:[0xE46900] --- write len --- [256]
 
2025-07-16 21:04:55,839  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:04:55,846  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[D302],priority[0],index[5],used[1]
 
2025-07-16 21:04:55,852  [DEBUG] [D][11:32:57][COMM]Main Task receive event:54 finished processing
 
2025-07-16 21:04:55,854  [DEBUG] [D][11:32:57][CAT1]<<< 
 
2025-07-16 21:04:55,855  [DEBUG] SEND OK
 
2025-07-16 21:04:55,855  [DEBUG] 
 
2025-07-16 21:04:55,861  [DEBUG] [D][11:32:57][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:04:55,864  [DEBUG] [D][11:32:57][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:04:55,864  [DEBUG] 
 
2025-07-16 21:04:55,871  [DEBUG] [D][11:32:57][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:04:55,875  [DEBUG] [D][11:32:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:04:55,877  [DEBUG] [D][11:32:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:04:55,884  [DEBUG] [D][11:32:57][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:04:55,889  [DEBUG] [D][11:32:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:04:55,891  [DEBUG] [D][11:32:57][PROT]M2M Send ok [1730201577]
 
2025-07-16 21:05:00,918  [DEBUG] [D][11:33:03][PROT]CLEAN,SEND:1
 
2025-07-16 21:05:00,930  [DEBUG] [D][11:33:03][PROT]index:1 1730201583
 
2025-07-16 21:05:00,933  [DEBUG] [D][11:33:03][PROT]is_send:0
 
2025-07-16 21:05:00,936  [DEBUG] [D][11:33:03][PROT]sequence_num:25
 
2025-07-16 21:05:00,938  [DEBUG] [D][11:33:03][PROT]retry_timeout:0
 
2025-07-16 21:05:00,941  [DEBUG] [D][11:33:03][PROT]retry_times:2
 
2025-07-16 21:05:00,944  [DEBUG] [D][11:33:03][PROT]send_path:0x2
 
2025-07-16 21:05:00,949  [DEBUG] [D][11:33:03][PROT]min_index:1, type:0x5D05, priority:3
 
2025-07-16 21:05:00,955  [DEBUG] [D][11:33:03][PROT]===========================================================
 
2025-07-16 21:05:00,962  [DEBUG] [W][11:33:03][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201583]
 
2025-07-16 21:05:00,970  [DEBUG] [D][11:33:03][PROT]===========================================================
 
2025-07-16 21:05:00,972  [DEBUG] [D][11:33:03][COMM]PB encode data:29
 
2025-07-16 21:05:00,978  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:05:00,984  [DEBUG] [D][11:33:03][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:00,986  [DEBUG] [D][11:33:03][PROT]Send_TO_M2M [1730201583]
 
2025-07-16 21:05:00,992  [DEBUG] [D][11:33:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:00,995  [DEBUG] [D][11:33:03][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:00,999  [DEBUG] [D][11:33:03][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:01,002  [DEBUG] [D][11:33:03][M2M ]m2m send data len[134]
 
2025-07-16 21:05:01,007  [DEBUG] [D][11:33:03][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:01,014  [DEBUG] [D][11:33:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:05:01,019  [DEBUG] [D][11:33:03][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:01,021  [DEBUG] [D][11:33:03][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:05:01,023  [DEBUG] 
 
2025-07-16 21:05:01,028  [DEBUG] [D][11:33:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:01,033  [DEBUG] [D][11:33:03][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:05:01,046  [DEBUG] 0043B6F9113311331133113311331B88B1F5CF014126E6864C9DF772B21C1E00947763C833BA91D2CC2CA3EBD426C4E3629B50F48365DC642D6AF1A949A31C3043713C
 
2025-07-16 21:05:01,047  [DEBUG] [D][11:33:03][CAT1]<<< 
 
2025-07-16 21:05:01,048  [DEBUG] SEND OK
 
2025-07-16 21:05:01,048  [DEBUG] 
 
2025-07-16 21:05:01,053  [DEBUG] [D][11:33:03][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:01,056  [DEBUG] [D][11:33:03][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:01,057  [DEBUG] 
 
2025-07-16 21:05:01,061  [DEBUG] [D][11:33:03][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:01,068  [DEBUG] [D][11:33:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:01,072  [DEBUG] [D][11:33:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:01,075  [DEBUG] [D][11:33:03][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:01,081  [DEBUG] [D][11:33:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:01,083  [DEBUG] [D][11:33:03][PROT]M2M Send ok [1730201583]
 
2025-07-16 21:05:06,324  [DEBUG] [D][11:33:08][PROT]CLEAN,SEND:1
 
2025-07-16 21:05:06,336  [DEBUG] [D][11:33:08][PROT]index:1 1730201588
 
2025-07-16 21:05:06,338  [DEBUG] [D][11:33:08][PROT]is_send:0
 
2025-07-16 21:05:06,341  [DEBUG] [D][11:33:08][PROT]sequence_num:25
 
2025-07-16 21:05:06,344  [DEBUG] [D][11:33:08][PROT]retry_timeout:0
 
2025-07-16 21:05:06,347  [DEBUG] [D][11:33:08][PROT]retry_times:1
 
2025-07-16 21:05:06,350  [DEBUG] [D][11:33:08][PROT]send_path:0x2
 
2025-07-16 21:05:06,355  [DEBUG] [D][11:33:08][PROT]min_index:1, type:0x5D05, priority:3
 
2025-07-16 21:05:06,361  [DEBUG] [D][11:33:08][PROT]===========================================================
 
2025-07-16 21:05:06,367  [DEBUG] [W][11:33:08][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201588]
 
2025-07-16 21:05:06,375  [DEBUG] [D][11:33:08][PROT]===========================================================
 
2025-07-16 21:05:06,377  [DEBUG] [D][11:33:08][COMM]PB encode data:29
 
2025-07-16 21:05:06,383  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:05:06,389  [DEBUG] [D][11:33:08][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:06,392  [DEBUG] [D][11:33:08][PROT]Send_TO_M2M [1730201588]
 
2025-07-16 21:05:06,398  [DEBUG] [D][11:33:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:06,401  [DEBUG] [D][11:33:08][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:06,407  [DEBUG] [D][11:33:08][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:06,413  [DEBUG] [D][11:33:08][M2M ]m2m send data len[134]
 
2025-07-16 21:05:06,414  [DEBUG] [D][11:33:08][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:06,420  [DEBUG] [D][11:33:08][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:05:06,425  [DEBUG] [D][11:33:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:06,431  [DEBUG] [D][11:33:08][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:06,433  [DEBUG] [D][11:33:08][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:05:06,434  [DEBUG] 
 
2025-07-16 21:05:06,439  [DEBUG] [D][11:33:08][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:05:06,451  [DEBUG] 0043B6F7113311331133113311331B88B1B4035DFA2366F921D82D176C32CB1116AED5229C56CA0DD0A70F00B8C5179CA5DAA0250085FD670EA5EEB6632E2EB4525AC8
 
2025-07-16 21:05:06,453  [DEBUG] [D][11:33:08][CAT1]<<< 
 
2025-07-16 21:05:06,454  [DEBUG] SEND OK
 
2025-07-16 21:05:06,454  [DEBUG] 
 
2025-07-16 21:05:06,460  [DEBUG] [D][11:33:08][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:06,462  [DEBUG] [D][11:33:08][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:06,464  [DEBUG] 
 
2025-07-16 21:05:06,468  [DEBUG] [D][11:33:08][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:06,473  [DEBUG] [D][11:33:08][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:06,479  [DEBUG] [D][11:33:08][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:06,481  [DEBUG] [D][11:33:08][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:06,486  [DEBUG] [D][11:33:08][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:06,489  [DEBUG] [D][11:33:08][PROT]M2M Send ok [1730201588]
 
2025-07-16 21:05:06,673  [DEBUG] [D][11:33:08][CAT1]closed : 0
 
2025-07-16 21:05:06,678  [DEBUG] [D][11:33:08][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:05:06,680  [DEBUG] [D][11:33:08][SAL ]socket closed id[0]
 
2025-07-16 21:05:06,686  [DEBUG] [D][11:33:08][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:05:06,692  [DEBUG] [D][11:33:08][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:05:06,694  [DEBUG] [D][11:33:08][M2M ]m2m select fd[4]
 
2025-07-16 21:05:06,700  [DEBUG] [D][11:33:08][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:05:06,702  [DEBUG] [D][11:33:08][M2M ]tcpclient close[4]
 
2025-07-16 21:05:06,705  [DEBUG] [D][11:33:08][SAL ]socket[4] has closed
 
2025-07-16 21:05:06,711  [DEBUG] [D][11:33:08][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:05:06,717  [DEBUG] [D][11:33:08][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 7
 
2025-07-16 21:05:06,735  [DEBUG] [D][11:33:08][COMM]Main Task receive event:86
 
2025-07-16 21:05:06,743  [DEBUG] [W][11:33:08][PROT]remove success[1730201588],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:05:06,751  [DEBUG] [W][11:33:08][PROT]add success [1730201588],send_path[2],type[8301],priority[0],index[6],used[1]
 
2025-07-16 21:05:06,758  [DEBUG] [D][11:33:08][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:05:11,730  [DEBUG] [D][11:33:13][PROT]CLEAN,SEND:1
 
2025-07-16 21:05:11,741  [DEBUG] [D][11:33:13][PROT]CLEAN:1
 
2025-07-16 21:05:11,752  [DEBUG] [D][11:33:13][PROT]index:0 1730201593
 
2025-07-16 21:05:11,755  [DEBUG] [D][11:33:13][PROT]is_send:0
 
2025-07-16 21:05:11,758  [DEBUG] [D][11:33:13][PROT]sequence_num:24
 
2025-07-16 21:05:11,761  [DEBUG] [D][11:33:13][PROT]retry_timeout:0
 
2025-07-16 21:05:11,763  [DEBUG] [D][11:33:13][PROT]retry_times:1
 
2025-07-16 21:05:11,766  [DEBUG] [D][11:33:13][PROT]send_path:0x2
 
2025-07-16 21:05:11,772  [DEBUG] [D][11:33:13][PROT]min_index:0, type:0x5006, priority:2
 
2025-07-16 21:05:11,777  [DEBUG] [D][11:33:13][PROT]===========================================================
 
2025-07-16 21:05:11,782  [DEBUG] [W][11:33:13][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730201593]
 
2025-07-16 21:05:11,792  [DEBUG] [D][11:33:13][PROT]===========================================================
 
2025-07-16 21:05:11,794  [DEBUG] [D][11:33:13][COMM]PB encode data:38
 
2025-07-16 21:05:11,803  [DEBUG] 0A24080210841E18322000280030003800401F481860007001800194238A0106000000000000
 
2025-07-16 21:05:11,805  [DEBUG] [D][11:33:13][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:11,811  [DEBUG] [D][11:33:13][PROT]Send_TO_M2M [1730201593]
 
2025-07-16 21:05:11,813  [DEBUG] [D][11:33:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:11,819  [DEBUG] [E][11:33:13][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:05:11,826  [DEBUG] [E][11:33:13][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:05:11,828  [DEBUG] [D][11:33:13][M2M ]m2m send data len[-1]
 
2025-07-16 21:05:11,833  [DEBUG] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:11,839  [DEBUG] [E][11:33:14][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:05:11,841  [DEBUG] [E][11:33:14][PROT]M2M Send Fail [1730201594]
 
2025-07-16 21:05:11,844  [DEBUG] [D][11:33:14][PROT]CLEAN,SEND:0
 
2025-07-16 21:05:11,850  [DEBUG] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:05:11,853  [DEBUG] [D][11:33:14][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:05:11,858  [DEBUG] [D][11:33:14][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:11,859  [DEBUG] 
 
2025-07-16 21:05:11,859  [DEBUG] [D][11:33:14][PROT]CLEAN:0
 
2025-07-16 21:05:11,865  [DEBUG] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:05:11,867  [DEBUG] [D][11:33:14][CAT1]<<< 
 
2025-07-16 21:05:11,868  [DEBUG] +CGATT: 1
 
2025-07-16 21:05:11,869  [DEBUG] 
 
2025-07-16 21:05:11,870  [DEBUG] OK
 
2025-07-16 21:05:11,870  [DEBUG] 
 
2025-07-16 21:05:11,872  [DEBUG] [D][11:33:14][CAT1]tx ret[12] >>> AT+CGATT=0
 
2025-07-16 21:05:11,874  [DEBUG] 
 
2025-07-16 21:05:11,878  [DEBUG] [D][11:33:14][COMM]Main Task receive event:79
 
2025-07-16 21:05:11,881  [DEBUG] [D][11:33:14][COMM]Receive protocol ACK TIMEOUT event
 
2025-07-16 21:05:11,886  [DEBUG] [D][11:33:14][COMM]Main Task receive event:79 finished processing
 
2025-07-16 21:05:11,889  [DEBUG] +WIFISCAN:8,0,FCD733633E16,-31
 
2025-07-16 21:05:11,891  [DEBUG] +WIFISCAN:8,1,8ED733633E16,-32
 
2025-07-16 21:05:11,894  [DEBUG] +WIFISCAN:8,2,A400E2F462C0,-59
 
2025-07-16 21:05:11,897  [DEBUG] +WIFISCAN:8,3,E22E0BF5E7EF,-61
 
2025-07-16 21:05:11,900  [DEBUG] +WIFISCAN:8,4,A400E2F462C1,-61
 
2025-07-16 21:05:11,903  [DEBUG] +WIFISCAN:8,5,909838971A70,-66
 
2025-07-16 21:05:11,905  [DEBUG] +WIFISCAN:8,6,A400E2F468E2,-76
 
2025-07-16 21:05:11,908  [DEBUG] +WIFISCAN:8,7,A400E2F464E1,-77
 
2025-07-16 21:05:11,910  [DEBUG] 
 
2025-07-16 21:05:11,915  [DEBUG] [D][11:33:14][CAT1]wifi scan report total[8]
 
2025-07-16 21:05:11,920  [DEBUG] [D][11:33:14][CAT1]wifi scan result rpt len[256], retval[256]
 
2025-07-16 21:05:11,976  [DEBUG] [D][11:33:14][GNSS]recv submsg id[3]
 
2025-07-16 21:05:11,978  [DEBUG] [D][11:33:14][GNSS]handlerWifiScanDone
 
2025-07-16 21:05:11,985  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[0]mac:FCD733633E16 ssid: rssi:-31
 
2025-07-16 21:05:11,992  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[1]mac:8ED733633E16 ssid: rssi:-32
 
2025-07-16 21:05:11,999  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[2]mac:A400E2F462C0 ssid: rssi:-59
 
2025-07-16 21:05:12,007  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[3]mac:E22E0BF5E7EF ssid: rssi:-61
 
2025-07-16 21:05:12,012  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[4]mac:A400E2F462C1 ssid: rssi:-61
 
2025-07-16 21:05:12,021  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[5]mac:909838971A70 ssid: rssi:-66
 
2025-07-16 21:05:12,027  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[6]mac:A400E2F468E2 ssid: rssi:-76
 
2025-07-16 21:05:12,033  [DEBUG] [D][11:33:14][GNSS]frm_wifi_scan_callback:[7]mac:A400E2F464E1 ssid: rssi:-77
 
2025-07-16 21:05:12,042  [DEBUG] [D][11:33:14][HSDK][0] flush to flash addr:[0xE46A00] --- write len --- [256]
 
2025-07-16 21:05:12,049  [DEBUG] [W][11:33:14][PROT]remove success[1730201594],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:05:12,057  [DEBUG] [W][11:33:14][PROT]add success [1730201594],send_path[2],type[5103],priority[0],index[0],used[1]
 
2025-07-16 21:05:12,120  [DEBUG] [D][11:33:14][CAT1]<<< 
 
2025-07-16 21:05:12,121  [DEBUG] OK
 
2025-07-16 21:05:12,122  [DEBUG] 
 
2025-07-16 21:05:12,126  [DEBUG] [D][11:33:14][CAT1]exec over: func id: 10, ret: 6
 
2025-07-16 21:05:12,128  [DEBUG] [D][11:33:14][CAT1]sub id: 10, ret: 6
 
2025-07-16 21:05:12,128  [DEBUG] 
 
2025-07-16 21:05:12,134  [DEBUG] [D][11:33:14][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:12,141  [DEBUG] [D][11:33:14][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
 
2025-07-16 21:05:12,142  [DEBUG] [D][11:33:14][M2M ]m2m gsm shut done, ret[0]
 
2025-07-16 21:05:12,148  [DEBUG] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:12,153  [DEBUG] [D][11:33:14][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:05:12,159  [DEBUG] [D][11:33:14][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:05:12,165  [DEBUG] [D][11:33:14][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:05:12,171  [DEBUG] [D][11:33:14][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:05:12,176  [DEBUG] [D][11:33:14][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:05:12,182  [DEBUG] [D][11:33:14][CAT1]pdpdeact urc len[22]
 
2025-07-16 21:05:12,184  [DEBUG] [D][11:33:14][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:05:12,187  [DEBUG] [D][11:33:14][CAT1]at ops open socket[0]
 
2025-07-16 21:05:12,192  [DEBUG] [D][11:33:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:05:12,197  [DEBUG] [D][11:33:14][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:12,198  [DEBUG] 
 
2025-07-16 21:05:12,198  [DEBUG] [D][11:33:14][CAT1]<<< 
 
2025-07-16 21:05:12,200  [DEBUG] +CGATT: 0
 
2025-07-16 21:05:12,200  [DEBUG] 
 
2025-07-16 21:05:12,200  [DEBUG] OK
 
2025-07-16 21:05:12,202  [DEBUG] 
 
2025-07-16 21:05:12,204  [DEBUG] [D][11:33:14][CAT1]tx ret[12] >>> AT+CGATT=1
 
2025-07-16 21:05:12,204  [DEBUG] 
 
2025-07-16 21:05:12,506  [DEBUG] [D][11:33:14][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:05:12,557  [DEBUG] [D][11:33:14][COMM]f:frm_violent_loading_over_thr_process. is_first_over pitch:[127]
 
2025-07-16 21:05:12,558  [DEBUG] 
 
2025-07-16 21:05:13,505  [DEBUG] [D][11:33:15][COMM]398758 imu init OK
 
2025-07-16 21:05:13,596  [DEBUG] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,597  [DEBUG] OK
 
2025-07-16 21:05:13,605  [DEBUG] 
 
2025-07-16 21:05:13,609  [DEBUG] [D][11:33:15][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:13,609  [DEBUG] 
 
2025-07-16 21:05:13,638  [DEBUG] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,639  [DEBUG] +CGATT: 1
 
2025-07-16 21:05:13,639  [DEBUG] 
 
2025-07-16 21:05:13,640  [DEBUG] OK
 
2025-07-16 21:05:13,648  [DEBUG] 
 
2025-07-16 21:05:13,650  [DEBUG] [D][11:33:15][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:05:13,651  [DEBUG] 
 
2025-07-16 21:05:13,670  [DEBUG] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,672  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:05:13,673  [DEBUG] 
 
2025-07-16 21:05:13,673  [DEBUG] OK
 
2025-07-16 21:05:13,681  [DEBUG] 
 
2025-07-16 21:05:13,685  [DEBUG] [D][11:33:15][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:05:13,685  [DEBUG] 
 
2025-07-16 21:05:13,704  [DEBUG] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,706  [DEBUG] OK
 
2025-07-16 21:05:13,715  [DEBUG] 
 
2025-07-16 21:05:13,718  [DEBUG] [D][11:33:15][CAT1]tx ret[12] >>> AT+QIACT=1
 
2025-07-16 21:05:13,719  [DEBUG] 
 
2025-07-16 21:05:13,747  [DEBUG] [D][11:33:15][CAT1]<<< 
 
2025-07-16 21:05:13,748  [DEBUG] OK
 
2025-07-16 21:05:13,757  [DEBUG] 
 
2025-07-16 21:05:13,764  [DEBUG] [D][11:33:16][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:05:13,765  [DEBUG] 
 
2025-07-16 21:05:13,779  [DEBUG] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:13,779  [DEBUG] OK
 
2025-07-16 21:05:13,781  [DEBUG] 
 
2025-07-16 21:05:13,784  [DEBUG] [D][11:33:16][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:05:13,989  [DEBUG] [D][11:33:16][CAT1]opened : 0, 0
 
2025-07-16 21:05:13,991  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:13,997  [DEBUG] [D][11:33:16][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:05:14,003  [DEBUG] [D][11:33:16][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:05:14,009  [DEBUG] [D][11:33:16][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
 
2025-07-16 21:05:14,011  [DEBUG] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,017  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,020  [DEBUG] [D][11:33:16][PROT]index:2 1730201596
 
2025-07-16 21:05:14,022  [DEBUG] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,025  [DEBUG] [D][11:33:16][PROT]sequence_num:26
 
2025-07-16 21:05:14,027  [DEBUG] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,031  [DEBUG] [D][11:33:16][PROT]retry_times:1
 
2025-07-16 21:05:14,034  [DEBUG] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,039  [DEBUG] [D][11:33:16][PROT]min_index:2, type:0xFF0E, priority:0
 
2025-07-16 21:05:14,048  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,053  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,059  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,064  [DEBUG] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,067  [DEBUG] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,073  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,078  [DEBUG] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,081  [DEBUG] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,083  [DEBUG] [D][11:33:16][M2M ]m2m send data len[166]
 
2025-07-16 21:05:14,089  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,098  [DEBUG] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,100  [DEBUG] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,105  [DEBUG] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,166
 
2025-07-16 21:05:14,106  [DEBUG] 
 
2025-07-16 21:05:14,111  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,116  [DEBUG] [D][11:33:16][CAT1]Send Data To Server[166][169] ... ->:
 
2025-07-16 21:05:14,132  [DEBUG] 0053B9FB113311331133113311331B88B1EBFB1DB095A64A303BD12DB57B97520410891BEB5216DF71CD8452069F62517082FD3469F3FFCA648A25D070D883F8891824A076BB15CE66C55C6B802B8EEEB7D4C8
 
2025-07-16 21:05:14,134  [DEBUG] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,134  [DEBUG] SEND OK
 
2025-07-16 21:05:14,135  [DEBUG] 
 
2025-07-16 21:05:14,139  [DEBUG] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,142  [DEBUG] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,143  [DEBUG] 
 
2025-07-16 21:05:14,145  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,153  [DEBUG] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,155  [DEBUG] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,158  [DEBUG] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,165  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,166  [DEBUG] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:14,170  [DEBUG] [D][11:33:16][PROT]CLEAN:2
 
2025-07-16 21:05:14,174  [DEBUG] [D][11:33:16][PROT]index:3 1730201596
 
2025-07-16 21:05:14,178  [DEBUG] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,181  [DEBUG] [D][11:33:16][PROT]sequence_num:27
 
2025-07-16 21:05:14,183  [DEBUG] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,186  [DEBUG] [D][11:33:16][PROT]retry_times:1
 
2025-07-16 21:05:14,189  [DEBUG] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,194  [DEBUG] [D][11:33:16][PROT]min_index:3, type:0xC001, priority:0
 
2025-07-16 21:05:14,200  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,206  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,215  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,217  [DEBUG] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,222  [DEBUG] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,229  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,231  [DEBUG] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,234  [DEBUG] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,240  [DEBUG] [D][11:33:16][M2M ]m2m send data len[230]
 
2025-07-16 21:05:14,242  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,251  [DEBUG] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,256  [DEBUG] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,259  [DEBUG] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:05:14,260  [DEBUG] 
 
2025-07-16 21:05:14,264  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,270  [DEBUG] [D][11:33:16][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:05:14,291  [DEBUG] 0073B908113311331133113311331B88B10CCBEA11E5A9F42133A2D60D54907E4B7A8B794ED7712624C24CCA118F8D5BE51DD6EE662C342D16514DE55768D03164B3AF2C82F9DA2669FCB50941A9E9CAE97F41300ABB1240E2DD1CABAAA2E1E5F08E75C2386BD29E5F38355A4ACDF504C1582A
 
2025-07-16 21:05:14,291  [DEBUG] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,292  [DEBUG] SEND OK
 
2025-07-16 21:05:14,292  [DEBUG] 
 
2025-07-16 21:05:14,297  [DEBUG] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,301  [DEBUG] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,302  [DEBUG] 
 
2025-07-16 21:05:14,306  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,312  [DEBUG] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,314  [DEBUG] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,320  [DEBUG] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,323  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,329  [DEBUG] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:14,331  [DEBUG] [D][11:33:16][PROT]CLEAN:3
 
2025-07-16 21:05:14,333  [DEBUG] [D][11:33:16][PROT]index:4 1730201596
 
2025-07-16 21:05:14,336  [DEBUG] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,339  [DEBUG] [D][11:33:16][PROT]sequence_num:28
 
2025-07-16 21:05:14,343  [DEBUG] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,345  [DEBUG] [D][11:33:16][PROT]retry_times:1
 
2025-07-16 21:05:14,348  [DEBUG] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,352  [DEBUG] [D][11:33:16][PROT]min_index:4, type:0x5A07, priority:0
 
2025-07-16 21:05:14,362  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,368  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,373  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,375  [DEBUG] [D][11:33:16][COMM]PB encode data:35
 
2025-07-16 21:05:14,384  [DEBUG] 0A210A1F180020FBFF012880CB80B906300340016800A801B38E83B906F80100C00200
 
2025-07-16 21:05:14,386  [DEBUG] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,393  [DEBUG] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,395  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,401  [DEBUG] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,403  [DEBUG] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,409  [DEBUG] [D][11:33:16][M2M ]m2m send data len[134]
 
2025-07-16 21:05:14,412  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,421  [DEBUG] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,423  [DEBUG] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,429  [DEBUG] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,134
 
2025-07-16 21:05:14,430  [DEBUG] 
 
2025-07-16 21:05:14,435  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,439  [DEBUG] [D][11:33:16][CAT1]Send Data To Server[134][137] ... ->:
 
2025-07-16 21:05:14,452  [DEBUG] 0043B60A113311331133113311331B88B1BB07D1896932C9D95E259C7101F42D1B2A99D2642DAA383694E29DEDCAAE651DA8732A525C3E10557A664CCBACBD8511CDC1
 
2025-07-16 21:05:14,453  [DEBUG] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,454  [DEBUG] SEND OK
 
2025-07-16 21:05:14,454  [DEBUG] 
 
2025-07-16 21:05:14,459  [DEBUG] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,462  [DEBUG] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,462  [DEBUG] 
 
2025-07-16 21:05:14,467  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,474  [DEBUG] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,476  [DEBUG] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,482  [DEBUG] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,485  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,491  [DEBUG] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:14,493  [DEBUG] [D][11:33:16][PROT]CLEAN:4
 
2025-07-16 21:05:14,495  [DEBUG] [D][11:33:16][PROT]index:5 1730201596
 
2025-07-16 21:05:14,498  [DEBUG] [D][11:33:16][PROT]is_send:0
 
2025-07-16 21:05:14,501  [DEBUG] [D][11:33:16][PROT]sequence_num:29
 
2025-07-16 21:05:14,504  [DEBUG] [D][11:33:16][PROT]retry_timeout:0
 
2025-07-16 21:05:14,507  [DEBUG] [D][11:33:16][PROT]retry_times:3
 
2025-07-16 21:05:14,509  [DEBUG] [D][11:33:16][PROT]send_path:0x2
 
2025-07-16 21:05:14,514  [DEBUG] [D][11:33:16][PROT]min_index:5, type:0xD302, priority:0
 
2025-07-16 21:05:14,521  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,530  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201596]
 
2025-07-16 21:05:14,535  [DEBUG] [D][11:33:16][PROT]===========================================================
 
2025-07-16 21:05:14,538  [DEBUG] [D][11:33:16][COMM]PB encode data:11
 
2025-07-16 21:05:14,540  [DEBUG] 0A0908011A013030013A00
 
2025-07-16 21:05:14,546  [DEBUG] [D][11:33:16][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:14,548  [DEBUG] [D][11:33:16][PROT]Send_TO_M2M [1730201596]
 
2025-07-16 21:05:14,554  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:14,557  [DEBUG] [D][11:33:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:14,562  [DEBUG] [D][11:33:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:14,565  [DEBUG] [D][11:33:16][M2M ]m2m send data len[102]
 
2025-07-16 21:05:14,570  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:14,577  [DEBUG] [D][11:33:16][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:05:14,583  [DEBUG] [D][11:33:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:14,585  [DEBUG] [D][11:33:16][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:05:14,586  [DEBUG] 
 
2025-07-16 21:05:14,614  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:14,616  [DEBUG] [D][11:33:16][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:05:14,616  [DEBUG] 0033B60F113311331133113311331B88B127D41B2485247DDCA796E9C7D2A829E9BEA4B34646BC2935B9A01B24D8B126F00D29
 
2025-07-16 21:05:14,617  [DEBUG] [D][11:33:16][CAT1]<<< 
 
2025-07-16 21:05:14,617  [DEBUG] SEND OK
 
2025-07-16 21:05:14,618  [DEBUG] 
 
2025-07-16 21:05:14,618  [DEBUG] [D][11:33:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:14,619  [DEBUG] [D][11:33:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:14,619  [DEBUG] 
 
2025-07-16 21:05:14,621  [DEBUG] [D][11:33:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:14,627  [DEBUG] [D][11:33:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:14,632  [DEBUG] [D][11:33:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:14,634  [DEBUG] [D][11:33:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:14,640  [DEBUG] [D][11:33:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:14,643  [DEBUG] [D][11:33:16][PROT]M2M Send ok [1730201596]
 
2025-07-16 21:05:19,677  [DEBUG] [D][11:33:21][PROT]CLEAN,SEND:5
 
2025-07-16 21:05:19,690  [DEBUG] [D][11:33:21][PROT]index:5 1730201601
 
2025-07-16 21:05:19,693  [DEBUG] [D][11:33:21][PROT]is_send:0
 
2025-07-16 21:05:19,696  [DEBUG] [D][11:33:21][PROT]sequence_num:29
 
2025-07-16 21:05:19,699  [DEBUG] [D][11:33:21][PROT]retry_timeout:0
 
2025-07-16 21:05:19,702  [DEBUG] [D][11:33:21][PROT]retry_times:2
 
2025-07-16 21:05:19,705  [DEBUG] [D][11:33:21][PROT]send_path:0x2
 
2025-07-16 21:05:19,710  [DEBUG] [D][11:33:21][PROT]min_index:5, type:0xD302, priority:0
 
2025-07-16 21:05:19,716  [DEBUG] [D][11:33:21][PROT]===========================================================
 
2025-07-16 21:05:19,721  [DEBUG] [W][11:33:21][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201601]
 
2025-07-16 21:05:19,730  [DEBUG] [D][11:33:21][PROT]===========================================================
 
2025-07-16 21:05:19,732  [DEBUG] [D][11:33:21][COMM]PB encode data:11
 
2025-07-16 21:05:19,736  [DEBUG] 0A0908011A013030013A00
 
2025-07-16 21:05:19,741  [DEBUG] [D][11:33:21][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:19,743  [DEBUG] [D][11:33:21][PROT]Send_TO_M2M [1730201601]
 
2025-07-16 21:05:19,749  [DEBUG] [D][11:33:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:19,751  [DEBUG] [D][11:33:21][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:19,757  [DEBUG] [D][11:33:21][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:19,760  [DEBUG] [D][11:33:21][M2M ]m2m send data len[102]
 
2025-07-16 21:05:19,763  [DEBUG] [D][11:33:21][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:19,772  [DEBUG] [D][11:33:21][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:05:19,777  [DEBUG] [D][11:33:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:19,782  [DEBUG] [D][11:33:21][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:19,785  [DEBUG] [D][11:33:21][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:05:19,787  [DEBUG] 
 
2025-07-16 21:05:19,791  [DEBUG] [D][11:33:21][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:05:19,801  [DEBUG] 0033B600113311331133113311331B88B154091CAD635DECD3DEBDF7B1B9FF12883BC02FDC9D8F96DB892FD5A0F9F0E554A07A
 
2025-07-16 21:05:19,802  [DEBUG] [D][11:33:21][CAT1]<<< 
 
2025-07-16 21:05:19,803  [DEBUG] SEND OK
 
2025-07-16 21:05:19,803  [DEBUG] 
 
2025-07-16 21:05:19,808  [DEBUG] [D][11:33:21][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:19,810  [DEBUG] [D][11:33:21][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:19,811  [DEBUG] 
 
2025-07-16 21:05:19,816  [DEBUG] [D][11:33:21][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:19,821  [DEBUG] [D][11:33:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:19,824  [DEBUG] [D][11:33:22][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:19,830  [DEBUG] [D][11:33:22][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:19,835  [DEBUG] [D][11:33:22][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:19,838  [DEBUG] [D][11:33:22][PROT]M2M Send ok [1730201602]
 
2025-07-16 21:05:25,082  [DEBUG] [D][11:33:27][PROT]CLEAN,SEND:5
 
2025-07-16 21:05:25,094  [DEBUG] [D][11:33:27][PROT]index:5 1730201607
 
2025-07-16 21:05:25,098  [DEBUG] [D][11:33:27][PROT]is_send:0
 
2025-07-16 21:05:25,100  [DEBUG] [D][11:33:27][PROT]sequence_num:29
 
2025-07-16 21:05:25,103  [DEBUG] [D][11:33:27][PROT]retry_timeout:0
 
2025-07-16 21:05:25,105  [DEBUG] [D][11:33:27][PROT]retry_times:1
 
2025-07-16 21:05:25,108  [DEBUG] [D][11:33:27][PROT]send_path:0x2
 
2025-07-16 21:05:25,113  [DEBUG] [D][11:33:27][PROT]min_index:5, type:0xD302, priority:0
 
2025-07-16 21:05:25,120  [DEBUG] [D][11:33:27][PROT]===========================================================
 
2025-07-16 21:05:25,126  [DEBUG] [W][11:33:27][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201607]
 
2025-07-16 21:05:25,134  [DEBUG] [D][11:33:27][PROT]===========================================================
 
2025-07-16 21:05:25,136  [DEBUG] [D][11:33:27][COMM]PB encode data:11
 
2025-07-16 21:05:25,139  [DEBUG] 0A0908011A013030013A00
 
2025-07-16 21:05:25,145  [DEBUG] [D][11:33:27][PROT]sending traceid [9999999999900009]
 
2025-07-16 21:05:25,147  [DEBUG] [D][11:33:27][PROT]Send_TO_M2M [1730201607]
 
2025-07-16 21:05:25,153  [DEBUG] [D][11:33:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:25,155  [DEBUG] [D][11:33:27][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:25,162  [DEBUG] [D][11:33:27][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:25,164  [DEBUG] [D][11:33:27][M2M ]m2m send data len[102]
 
2025-07-16 21:05:25,167  [DEBUG] [D][11:33:27][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:25,175  [DEBUG] [D][11:33:27][SAL ]cellular SEND socket id[0] type[1], len[102], data[0x20059fd0] format[0]
 
2025-07-16 21:05:25,181  [DEBUG] [D][11:33:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:25,187  [DEBUG] [D][11:33:27][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:25,189  [DEBUG] [D][11:33:27][CAT1]tx ret[17] >>> AT+QISEND=0,102
 
2025-07-16 21:05:25,190  [DEBUG] 
 
2025-07-16 21:05:25,194  [DEBUG] [D][11:33:27][CAT1]Send Data To Server[102][105] ... ->:
 
2025-07-16 21:05:25,204  [DEBUG] 0033B60D113311331133113311331B88B1E8FA2FD3469ECA1643B8F85A7F674BAEABB12FF0B385A79B10B529DD84401911E1E3
 
2025-07-16 21:05:25,206  [DEBUG] [D][11:33:27][CAT1]<<< 
 
2025-07-16 21:05:25,206  [DEBUG] SEND OK
 
2025-07-16 21:05:25,206  [DEBUG] 
 
2025-07-16 21:05:25,212  [DEBUG] [D][11:33:27][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:25,214  [DEBUG] [D][11:33:27][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:25,215  [DEBUG] 
 
2025-07-16 21:05:25,220  [DEBUG] [D][11:33:27][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:25,227  [DEBUG] [D][11:33:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:25,228  [DEBUG] [D][11:33:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:25,235  [DEBUG] [D][11:33:27][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:25,240  [DEBUG] [D][11:33:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:25,242  [DEBUG] [D][11:33:27][PROT]M2M Send ok [1730201607]
 
2025-07-16 21:05:30,488  [DEBUG] [D][11:33:32][PROT]CLEAN,SEND:5
 
2025-07-16 21:05:30,499  [DEBUG] [D][11:33:32][PROT]CLEAN:5
 
2025-07-16 21:05:30,510  [DEBUG] [D][11:33:32][PROT]index:6 1730201612
 
2025-07-16 21:05:30,513  [DEBUG] [D][11:33:32][PROT]is_send:0
 
2025-07-16 21:05:30,516  [DEBUG] [D][11:33:32][PROT]sequence_num:30
 
2025-07-16 21:05:30,519  [DEBUG] [D][11:33:32][PROT]retry_timeout:0
 
2025-07-16 21:05:30,522  [DEBUG] [D][11:33:32][PROT]retry_times:1
 
2025-07-16 21:05:30,525  [DEBUG] [D][11:33:32][PROT]send_path:0x2
 
2025-07-16 21:05:30,530  [DEBUG] [D][11:33:32][PROT]min_index:6, type:0x8301, priority:0
 
2025-07-16 21:05:30,536  [DEBUG] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,542  [DEBUG] [W][11:33:32][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201612]
 
2025-07-16 21:05:30,551  [DEBUG] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,556  [DEBUG] [D][11:33:32][PROT]sending traceid [999999999990000F]
 
2025-07-16 21:05:30,558  [DEBUG] [D][11:33:32][PROT]Send_TO_M2M [1730201612]
 
2025-07-16 21:05:30,565  [DEBUG] [D][11:33:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:30,570  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:30,573  [DEBUG] [D][11:33:32][M2M ]socket has connect, gsm_send_status:1
 
2025-07-16 21:05:30,578  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:30,581  [DEBUG] [D][11:33:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:30,586  [DEBUG] [D][11:33:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:30,589  [DEBUG] [D][11:33:32][M2M ]m2m send data len[70]
 
2025-07-16 21:05:30,594  [DEBUG] [D][11:33:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:30,604  [DEBUG] [D][11:33:32][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:05:30,605  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:30,611  [DEBUG] [D][11:33:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:30,614  [DEBUG] [D][11:33:32][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:05:30,614  [DEBUG] 
 
2025-07-16 21:05:30,619  [DEBUG] [D][11:33:32][CAT1]Send Data To Server[70][73] ... ->:
 
2025-07-16 21:05:30,626  [DEBUG] 0023B90C113311331133113311331B88B86A998A58B2AEE933DD02A37C347E41B818F4
 
2025-07-16 21:05:30,628  [DEBUG] [D][11:33:32][CAT1]<<< 
 
2025-07-16 21:05:30,631  [DEBUG] SEND OK
 
2025-07-16 21:05:30,631  [DEBUG] 
 
2025-07-16 21:05:30,634  [DEBUG] [D][11:33:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:30,636  [DEBUG] [D][11:33:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:30,638  [DEBUG] 
 
2025-07-16 21:05:30,642  [DEBUG] [D][11:33:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:30,648  [DEBUG] [D][11:33:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:30,652  [DEBUG] [D][11:33:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:30,656  [DEBUG] [D][11:33:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:30,662  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:30,665  [DEBUG] [D][11:33:32][PROT]M2M Send ok [1730201612]
 
2025-07-16 21:05:30,667  [DEBUG] [D][11:33:32][PROT]CLEAN:6
 
2025-07-16 21:05:30,673  [DEBUG] [D][11:33:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:30,678  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:30,681  [DEBUG] [D][11:33:32][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:05:30,687  [DEBUG] [D][11:33:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:30,690  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:30,694  [DEBUG] [D][11:33:32][PROT]index:0 1730201612
 
2025-07-16 21:05:30,697  [DEBUG] [D][11:33:32][PROT]is_send:0
 
2025-07-16 21:05:30,702  [DEBUG] [D][11:33:32][PROT]sequence_num:31
 
2025-07-16 21:05:30,703  [DEBUG] [D][11:33:32][PROT]retry_timeout:0
 
2025-07-16 21:05:30,705  [DEBUG] [D][11:33:32][PROT]retry_times:1
 
2025-07-16 21:05:30,708  [DEBUG] [D][11:33:32][PROT]send_path:0x2
 
2025-07-16 21:05:30,714  [DEBUG] [D][11:33:32][PROT]min_index:0, type:0x5103, priority:0
 
2025-07-16 21:05:30,721  [DEBUG] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,726  [DEBUG] [W][11:33:32][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730201612]
 
2025-07-16 21:05:30,734  [DEBUG] [D][11:33:32][PROT]===========================================================
 
2025-07-16 21:05:30,736  [DEBUG] [D][11:33:32][COMM]PB encode data:86
 
2025-07-16 21:05:30,755  [DEBUG] 0A54080110001A120A0C4643443733333633334531361200203D1A120A0C3845443733333633334531361200203F1A120A0C413430304532463436324330120020751A120A0C45323245304246354537454612002079
 
2025-07-16 21:05:30,756  [DEBUG] [D][11:33:32][PROT]sending traceid [9999999999900010]
 
2025-07-16 21:05:30,761  [DEBUG] [D][11:33:32][PROT]Send_TO_M2M [1730201612]
 
2025-07-16 21:05:30,764  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:30,770  [DEBUG] [D][11:33:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:30,772  [DEBUG] [D][11:33:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:30,779  [DEBUG] [D][11:33:32][M2M ]m2m send data len[230]
 
2025-07-16 21:05:30,781  [DEBUG] [D][11:33:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:30,791  [DEBUG] [D][11:33:32][SAL ]cellular SEND socket id[0] type[1], len[230], data[0x20059fd0] format[0]
 
2025-07-16 21:05:30,796  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:30,798  [DEBUG] [D][11:33:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:30,804  [DEBUG] [D][11:33:32][CAT1]tx ret[17] >>> AT+QISEND=0,230
 
2025-07-16 21:05:30,805  [DEBUG] 
 
2025-07-16 21:05:30,808  [DEBUG] [D][11:33:32][CAT1]Send Data To Server[230][233] ... ->:
 
2025-07-16 21:05:30,831  [DEBUG] 0073B601113311331133113311331B884B48D418FBD78D8DE94899A3FA1B2BC648EE551D8989117C2843EE23F66BC4C32544F20D9A4DC98A55C2D004BDD08A64EF22789535F8DE38CF475AC36CA89B3503D25E19DC5452B6159DA9A87FBBC69DDE199BF03DAC759932E7F71DD42EE6E76A2AB6
 
2025-07-16 21:05:30,831  [DEBUG] [D][11:33:32][CAT1]<<< 
 
2025-07-16 21:05:30,832  [DEBUG] SEND OK
 
2025-07-16 21:05:30,832  [DEBUG] 
 
2025-07-16 21:05:30,837  [DEBUG] [D][11:33:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:30,840  [DEBUG] [D][11:33:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:30,840  [DEBUG] 
 
2025-07-16 21:05:30,842  [DEBUG] [D][11:33:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:30,851  [DEBUG] [D][11:33:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:30,853  [DEBUG] [D][11:33:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:30,859  [DEBUG] [D][11:33:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:30,861  [DEBUG] [D][11:33:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:30,866  [DEBUG] [D][11:33:32][PROT]M2M Send ok [1730201612]
 
2025-07-16 21:05:36,021  [DEBUG] [D][11:33:38][PROT]CLEAN,SEND:0
 
2025-07-16 21:05:36,031  [DEBUG] [D][11:33:38][PROT]CLEAN:0
 
2025-07-16 21:05:37,315  [DEBUG] [D][11:33:39][CAT1]closed : 0
 
2025-07-16 21:05:37,321  [DEBUG] [D][11:33:39][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:05:37,323  [DEBUG] [D][11:33:39][SAL ]socket closed id[0]
 
2025-07-16 21:05:37,329  [DEBUG] [D][11:33:39][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:05:37,335  [DEBUG] [D][11:33:39][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:05:37,337  [DEBUG] [D][11:33:39][M2M ]m2m select fd[4]
 
2025-07-16 21:05:37,342  [DEBUG] [D][11:33:39][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:05:37,345  [DEBUG] [D][11:33:39][M2M ]tcpclient close[4]
 
2025-07-16 21:05:37,348  [DEBUG] [D][11:33:39][SAL ]socket[4] has closed
 
2025-07-16 21:05:37,354  [DEBUG] [D][11:33:39][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:05:37,360  [DEBUG] [D][11:33:39][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 8
 
2025-07-16 21:05:37,362  [DEBUG] [D][11:33:39][COMM]Main Task receive event:86
 
2025-07-16 21:05:37,370  [DEBUG] [W][11:33:39][PROT]remove success[1730201619],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:05:37,379  [DEBUG] [D][11:33:39][HSDK][0] flush to flash addr:[0xE46B00] --- write len --- [256]
 
2025-07-16 21:05:37,381  [DEBUG] [D][11:33:39][PROT]index:0 1730201619
 
2025-07-16 21:05:37,384  [DEBUG] [D][11:33:39][PROT]is_send:0
 
2025-07-16 21:05:37,387  [DEBUG] [D][11:33:39][PROT]sequence_num:32
 
2025-07-16 21:05:37,389  [DEBUG] [D][11:33:39][PROT]retry_timeout:0
 
2025-07-16 21:05:37,393  [DEBUG] [D][11:33:39][PROT]retry_times:1
 
2025-07-16 21:05:37,396  [DEBUG] [D][11:33:39][PROT]send_path:0x2
 
2025-07-16 21:05:37,401  [DEBUG] [D][11:33:39][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:05:37,410  [DEBUG] [D][11:33:39][PROT]===========================================================
 
2025-07-16 21:05:37,416  [DEBUG] [D][11:33:39][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:37,418  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:37,423  [DEBUG] [D][11:33:39][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:05:37,429  [DEBUG] [D][11:33:39][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:05:37,435  [DEBUG] [D][11:33:39][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:05:37,440  [DEBUG] [D][11:33:39][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:05:37,449  [DEBUG] [D][11:33:39][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:05:37,454  [DEBUG] [W][11:33:39][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201619]
 
2025-07-16 21:05:37,460  [DEBUG] [D][11:33:39][PROT]===========================================================
 
2025-07-16 21:05:37,465  [DEBUG] [D][11:33:39][PROT]sending traceid [9999999999900011]
 
2025-07-16 21:05:37,469  [DEBUG] [D][11:33:39][PROT]Send_TO_M2M [1730201619]
 
2025-07-16 21:05:37,474  [DEBUG] [D][11:33:39][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:05:37,476  [DEBUG] [D][11:33:39][CAT1]at ops open socket[0]
 
2025-07-16 21:05:37,484  [DEBUG] [W][11:33:39][PROT]add success [1730201619],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:05:37,487  [DEBUG] [D][11:33:39][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:05:37,488  [DEBUG] 
 
2025-07-16 21:05:37,493  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:05:37,499  [DEBUG] [D][11:33:39][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:05:37,501  [DEBUG] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,502  [DEBUG] +CGATT: 1
 
2025-07-16 21:05:37,504  [DEBUG] 
 
2025-07-16 21:05:37,504  [DEBUG] OK
 
2025-07-16 21:05:37,506  [DEBUG] 
 
2025-07-16 21:05:37,508  [DEBUG] [D][11:33:39][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:05:37,509  [DEBUG] 
 
2025-07-16 21:05:37,510  [DEBUG] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,511  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:05:37,512  [DEBUG] 
 
2025-07-16 21:05:37,512  [DEBUG] OK
 
2025-07-16 21:05:37,512  [DEBUG] 
 
2025-07-16 21:05:37,516  [DEBUG] [D][11:33:39][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:05:37,517  [DEBUG] 
 
2025-07-16 21:05:37,519  [DEBUG] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,521  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:05:37,522  [DEBUG] 
 
2025-07-16 21:05:37,522  [DEBUG] OK
 
2025-07-16 21:05:37,522  [DEBUG] 
 
2025-07-16 21:05:37,530  [DEBUG] [D][11:33:39][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:05:37,532  [DEBUG] 
 
2025-07-16 21:05:37,532  [DEBUG] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,533  [DEBUG] OK
 
2025-07-16 21:05:37,533  [DEBUG] 
 
2025-07-16 21:05:37,536  [DEBUG] [D][11:33:39][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:05:37,587  [DEBUG] [D][11:33:39][CAT1]opened : 0, 0
 
2025-07-16 21:05:37,589  [DEBUG] [D][11:33:39][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:37,595  [DEBUG] [D][11:33:39][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:05:37,601  [DEBUG] [D][11:33:39][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:05:37,607  [DEBUG] [D][11:33:39][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:05:37,612  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:05:37,614  [DEBUG] [D][11:33:39][SAL ]sock send credit cnt[6]
 
2025-07-16 21:05:37,617  [DEBUG] [D][11:33:39][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:05:37,626  [DEBUG] [D][11:33:39][M2M ]m2m send data len[70]
 
2025-07-16 21:05:37,627  [DEBUG] [D][11:33:39][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:05:37,634  [DEBUG] [D][11:33:39][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:05:37,639  [DEBUG] [D][11:33:39][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:05:37,642  [DEBUG] [D][11:33:39][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:05:37,643  [DEBUG] 
 
2025-07-16 21:05:37,648  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:05:37,653  [DEBUG] [D][11:33:39][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:05:37,659  [DEBUG] 0023B902113311331133113311331B88447E531D77A18C1F90D3091A14BA0659C3C1E0
 
2025-07-16 21:05:37,661  [DEBUG] [D][11:33:39][CAT1]<<< 
 
2025-07-16 21:05:37,662  [DEBUG] SEND OK
 
2025-07-16 21:05:37,662  [DEBUG] 
 
2025-07-16 21:05:37,668  [DEBUG] [D][11:33:39][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:05:37,670  [DEBUG] [D][11:33:39][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:05:37,671  [DEBUG] 
 
2025-07-16 21:05:37,676  [DEBUG] [D][11:33:39][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:05:37,681  [DEBUG] [D][11:33:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:05:37,684  [DEBUG] [D][11:33:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:05:37,690  [DEBUG] [D][11:33:39][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:37,693  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:37,698  [DEBUG] [D][11:33:39][PROT]M2M Send ok [1730201619]
 
2025-07-16 21:05:37,701  [DEBUG] [D][11:33:39][PROT]CLEAN:0
 
2025-07-16 21:05:37,704  [DEBUG] [D][11:33:39][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:05:37,709  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:05:37,716  [DEBUG] [D][11:33:39][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:05:37,717  [DEBUG] [D][11:33:39][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:05:37,723  [DEBUG] [D][11:33:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:05:45,634  [DEBUG] [D][11:33:47][COMM]IMU: 430887 MEMS ERROR when cali 0
 
2025-07-16 21:05:56,835  [DEBUG] [D][11:33:59][COMM]IMU: 442091 MEMS ERROR when cali 0
 
2025-07-16 21:06:18,270  [DEBUG] [D][11:34:20][CAT1]closed : 0
 
2025-07-16 21:06:18,275  [DEBUG] [D][11:34:20][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:06:18,278  [DEBUG] [D][11:34:20][SAL ]socket closed id[0]
 
2025-07-16 21:06:18,285  [DEBUG] [D][11:34:20][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:06:18,290  [DEBUG] [D][11:34:20][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:06:18,292  [DEBUG] [D][11:34:20][M2M ]m2m select fd[4]
 
2025-07-16 21:06:18,297  [DEBUG] [D][11:34:20][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:06:18,301  [DEBUG] [D][11:34:20][M2M ]tcpclient close[4]
 
2025-07-16 21:06:18,303  [DEBUG] [D][11:34:20][SAL ]socket[4] has closed
 
2025-07-16 21:06:18,308  [DEBUG] [D][11:34:20][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:06:18,314  [DEBUG] [D][11:34:20][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 9
 
2025-07-16 21:06:18,321  [DEBUG] [D][11:34:20][COMM]Main Task receive event:86
 
2025-07-16 21:06:18,328  [DEBUG] [W][11:34:20][PROT]remove success[1730201660],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:06:18,337  [DEBUG] [W][11:34:20][PROT]add success [1730201660],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:06:18,344  [DEBUG] [D][11:34:20][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:06:18,345  [DEBUG] [D][11:34:20][PROT]index:0 1730201660
 
2025-07-16 21:06:18,349  [DEBUG] [D][11:34:20][PROT]is_send:0
 
2025-07-16 21:06:18,354  [DEBUG] [D][11:34:20][PROT]sequence_num:33
 
2025-07-16 21:06:18,356  [DEBUG] [D][11:34:20][PROT]retry_timeout:0
 
2025-07-16 21:06:18,359  [DEBUG] [D][11:34:20][PROT]retry_times:1
 
2025-07-16 21:06:18,362  [DEBUG] [D][11:34:20][PROT]send_path:0x2
 
2025-07-16 21:06:18,368  [DEBUG] [D][11:34:20][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:06:18,374  [DEBUG] [D][11:34:20][PROT]===========================================================
 
2025-07-16 21:06:18,379  [DEBUG] [W][11:34:20][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201660]
 
2025-07-16 21:06:18,388  [DEBUG] [D][11:34:20][PROT]===========================================================
 
2025-07-16 21:06:18,390  [DEBUG] [D][11:34:20][PROT]sending traceid [9999999999900012]
 
2025-07-16 21:06:18,396  [DEBUG] [D][11:34:20][PROT]Send_TO_M2M [1730201660]
 
2025-07-16 21:06:18,402  [DEBUG] [D][11:34:20][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:18,404  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:18,410  [DEBUG] [D][11:34:20][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:06:18,416  [DEBUG] [D][11:34:20][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:06:18,421  [DEBUG] [D][11:34:20][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:06:18,429  [DEBUG] [D][11:34:20][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:06:18,434  [DEBUG] [D][11:34:20][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:06:18,437  [DEBUG] [D][11:34:20][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:06:18,440  [DEBUG] [D][11:34:20][CAT1]at ops open socket[0]
 
2025-07-16 21:06:18,446  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:06:18,449  [DEBUG] [D][11:34:20][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:06:18,451  [DEBUG] 
 
2025-07-16 21:06:18,451  [DEBUG] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,454  [DEBUG] +CGATT: 1
 
2025-07-16 21:06:18,454  [DEBUG] 
 
2025-07-16 21:06:18,455  [DEBUG] OK
 
2025-07-16 21:06:18,455  [DEBUG] 
 
2025-07-16 21:06:18,457  [DEBUG] [D][11:34:20][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:06:18,457  [DEBUG] 
 
2025-07-16 21:06:18,459  [DEBUG] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,460  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:06:18,462  [DEBUG] 
 
2025-07-16 21:06:18,462  [DEBUG] OK
 
2025-07-16 21:06:18,463  [DEBUG] 
 
2025-07-16 21:06:18,465  [DEBUG] [D][11:34:20][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:06:18,466  [DEBUG] 
 
2025-07-16 21:06:18,468  [DEBUG] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,473  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:06:18,473  [DEBUG] 
 
2025-07-16 21:06:18,474  [DEBUG] OK
 
2025-07-16 21:06:18,474  [DEBUG] 
 
2025-07-16 21:06:18,480  [DEBUG] [D][11:34:20][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:06:18,481  [DEBUG] 
 
2025-07-16 21:06:18,482  [DEBUG] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,483  [DEBUG] OK
 
2025-07-16 21:06:18,484  [DEBUG] 
 
2025-07-16 21:06:18,487  [DEBUG] [D][11:34:20][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:06:18,591  [DEBUG] [D][11:34:20][CAT1]opened : 0, 0
 
2025-07-16 21:06:18,593  [DEBUG] [D][11:34:20][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:18,599  [DEBUG] [D][11:34:20][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:06:18,604  [DEBUG] [D][11:34:20][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:06:18,610  [DEBUG] [D][11:34:20][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:06:18,615  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:06:18,618  [DEBUG] [D][11:34:20][SAL ]sock send credit cnt[6]
 
2025-07-16 21:06:18,621  [DEBUG] [D][11:34:20][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:06:18,627  [DEBUG] [D][11:34:20][M2M ]m2m send data len[70]
 
2025-07-16 21:06:18,630  [DEBUG] [D][11:34:20][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:06:18,638  [DEBUG] [D][11:34:20][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:06:18,643  [DEBUG] [D][11:34:20][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:06:18,646  [DEBUG] [D][11:34:20][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:06:18,647  [DEBUG] 
 
2025-07-16 21:06:18,652  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:06:18,656  [DEBUG] [D][11:34:20][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:06:18,663  [DEBUG] 0023B90E113311331133113311331B8847FC44142F18BB4EBE2C6FA772E52F1749450D
 
2025-07-16 21:06:18,665  [DEBUG] [D][11:34:20][CAT1]<<< 
 
2025-07-16 21:06:18,665  [DEBUG] SEND OK
 
2025-07-16 21:06:18,665  [DEBUG] 
 
2025-07-16 21:06:18,671  [DEBUG] [D][11:34:20][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:06:18,674  [DEBUG] [D][11:34:20][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:06:18,675  [DEBUG] 
 
2025-07-16 21:06:18,680  [DEBUG] [D][11:34:20][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:18,685  [DEBUG] [D][11:34:20][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:06:18,687  [DEBUG] [D][11:34:20][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:06:18,693  [DEBUG] [D][11:34:20][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:18,695  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:06:18,702  [DEBUG] [D][11:34:20][PROT]M2M Send ok [1730201660]
 
2025-07-16 21:06:18,704  [DEBUG] [D][11:34:20][PROT]CLEAN:0
 
2025-07-16 21:06:18,707  [DEBUG] [D][11:34:20][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:18,714  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:18,719  [DEBUG] [D][11:34:20][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:06:18,721  [DEBUG] [D][11:34:20][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:18,727  [DEBUG] [D][11:34:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:06:51,644  [DEBUG] [D][11:34:53][COMM]IMU: 496907 MEMS ERROR when cali 0
 
2025-07-16 21:06:59,233  [DEBUG] [D][11:35:01][CAT1]closed : 0
 
2025-07-16 21:06:59,237  [DEBUG] [D][11:35:01][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:06:59,240  [DEBUG] [D][11:35:01][SAL ]socket closed id[0]
 
2025-07-16 21:06:59,245  [DEBUG] [D][11:35:01][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:06:59,251  [DEBUG] [D][11:35:01][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:06:59,254  [DEBUG] [D][11:35:01][M2M ]m2m select fd[4]
 
2025-07-16 21:06:59,259  [DEBUG] [D][11:35:01][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:06:59,262  [DEBUG] [D][11:35:01][M2M ]tcpclient close[4]
 
2025-07-16 21:06:59,265  [DEBUG] [D][11:35:01][SAL ]socket[4] has closed
 
2025-07-16 21:06:59,271  [DEBUG] [D][11:35:01][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:06:59,276  [DEBUG] [D][11:35:01][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 10
 
2025-07-16 21:06:59,279  [DEBUG] [D][11:35:01][COMM]Main Task receive event:86
 
2025-07-16 21:06:59,288  [DEBUG] [W][11:35:01][PROT]remove success[1730201701],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:06:59,292  [DEBUG] [D][11:35:01][PROT]index:0 1730201701
 
2025-07-16 21:06:59,295  [DEBUG] [D][11:35:01][PROT]is_send:0
 
2025-07-16 21:06:59,298  [DEBUG] [D][11:35:01][PROT]sequence_num:34
 
2025-07-16 21:06:59,301  [DEBUG] [D][11:35:01][PROT]retry_timeout:0
 
2025-07-16 21:06:59,303  [DEBUG] [D][11:35:01][PROT]retry_times:1
 
2025-07-16 21:06:59,306  [DEBUG] [D][11:35:01][PROT]send_path:0x2
 
2025-07-16 21:06:59,312  [DEBUG] [D][11:35:01][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:06:59,318  [DEBUG] [D][11:35:01][PROT]===========================================================
 
2025-07-16 21:06:59,324  [DEBUG] [W][11:35:01][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201701]
 
2025-07-16 21:06:59,332  [DEBUG] [D][11:35:01][PROT]===========================================================
 
2025-07-16 21:06:59,337  [DEBUG] [D][11:35:01][PROT]sending traceid [9999999999900013]
 
2025-07-16 21:06:59,340  [DEBUG] [D][11:35:01][PROT]Send_TO_M2M [1730201701]
 
2025-07-16 21:06:59,345  [DEBUG] [D][11:35:01][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:59,351  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:59,354  [DEBUG] [D][11:35:01][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:06:59,362  [DEBUG] [D][11:35:01][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:06:59,365  [DEBUG] [D][11:35:01][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:06:59,374  [DEBUG] [D][11:35:01][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:06:59,379  [DEBUG] [D][11:35:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:06:59,387  [DEBUG] [W][11:35:01][PROT]add success [1730201701],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:06:59,393  [DEBUG] [D][11:35:01][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:06:59,396  [DEBUG] [D][11:35:01][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:06:59,401  [DEBUG] [D][11:35:01][CAT1]at ops open socket[0]
 
2025-07-16 21:06:59,405  [DEBUG] [D][11:35:01][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:06:59,406  [DEBUG] 
 
2025-07-16 21:06:59,410  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:06:59,412  [DEBUG] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,412  [DEBUG] +CGATT: 1
 
2025-07-16 21:06:59,413  [DEBUG] 
 
2025-07-16 21:06:59,413  [DEBUG] OK
 
2025-07-16 21:06:59,413  [DEBUG] 
 
2025-07-16 21:06:59,417  [DEBUG] [D][11:35:01][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:06:59,419  [DEBUG] 
 
2025-07-16 21:06:59,419  [DEBUG] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,420  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:06:59,421  [DEBUG] 
 
2025-07-16 21:06:59,421  [DEBUG] OK
 
2025-07-16 21:06:59,422  [DEBUG] 
 
2025-07-16 21:06:59,424  [DEBUG] [D][11:35:01][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:06:59,427  [DEBUG] 
 
2025-07-16 21:06:59,427  [DEBUG] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,430  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:06:59,430  [DEBUG] 
 
2025-07-16 21:06:59,430  [DEBUG] OK
 
2025-07-16 21:06:59,432  [DEBUG] 
 
2025-07-16 21:06:59,439  [DEBUG] [D][11:35:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:06:59,440  [DEBUG] 
 
2025-07-16 21:06:59,441  [DEBUG] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,442  [DEBUG] OK
 
2025-07-16 21:06:59,442  [DEBUG] 
 
2025-07-16 21:06:59,446  [DEBUG] [D][11:35:01][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:06:59,506  [DEBUG] [D][11:35:01][CAT1]opened : 0, 0
 
2025-07-16 21:06:59,509  [DEBUG] [D][11:35:01][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:59,515  [DEBUG] [D][11:35:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:06:59,520  [DEBUG] [D][11:35:01][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:06:59,526  [DEBUG] [D][11:35:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:06:59,532  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:06:59,534  [DEBUG] [D][11:35:01][SAL ]sock send credit cnt[6]
 
2025-07-16 21:06:59,537  [DEBUG] [D][11:35:01][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:06:59,544  [DEBUG] [D][11:35:01][M2M ]m2m send data len[70]
 
2025-07-16 21:06:59,546  [DEBUG] [D][11:35:01][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:06:59,554  [DEBUG] [D][11:35:01][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:06:59,560  [DEBUG] [D][11:35:01][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:06:59,563  [DEBUG] [D][11:35:01][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:06:59,563  [DEBUG] 
 
2025-07-16 21:06:59,569  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:06:59,573  [DEBUG] [D][11:35:01][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:06:59,579  [DEBUG] 0023B903113311331133113311331B88494B04E413E7D37CA1067EBEE67F1A9CC40EE3
 
2025-07-16 21:06:59,581  [DEBUG] [D][11:35:01][CAT1]<<< 
 
2025-07-16 21:06:59,582  [DEBUG] SEND OK
 
2025-07-16 21:06:59,582  [DEBUG] 
 
2025-07-16 21:06:59,587  [DEBUG] [D][11:35:01][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:06:59,590  [DEBUG] [D][11:35:01][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:06:59,591  [DEBUG] 
 
2025-07-16 21:06:59,596  [DEBUG] [D][11:35:01][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:06:59,602  [DEBUG] [D][11:35:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:06:59,604  [DEBUG] [D][11:35:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:06:59,609  [DEBUG] [D][11:35:01][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:59,612  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:06:59,619  [DEBUG] [D][11:35:01][PROT]M2M Send ok [1730201701]
 
2025-07-16 21:06:59,621  [DEBUG] [D][11:35:01][PROT]CLEAN:0
 
2025-07-16 21:06:59,624  [DEBUG] [D][11:35:01][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:06:59,630  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:06:59,635  [DEBUG] [D][11:35:01][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:06:59,638  [DEBUG] [D][11:35:01][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:06:59,643  [DEBUG] [D][11:35:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:07:40,171  [DEBUG] [D][11:35:42][CAT1]closed : 0
 
2025-07-16 21:07:40,175  [DEBUG] [D][11:35:42][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:07:40,179  [DEBUG] [D][11:35:42][SAL ]socket closed id[0]
 
2025-07-16 21:07:40,184  [DEBUG] [D][11:35:42][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:07:40,190  [DEBUG] [D][11:35:42][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:07:40,192  [DEBUG] [D][11:35:42][M2M ]m2m select fd[4]
 
2025-07-16 21:07:40,199  [DEBUG] [D][11:35:42][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:07:40,201  [DEBUG] [D][11:35:42][M2M ]tcpclient close[4]
 
2025-07-16 21:07:40,203  [DEBUG] [D][11:35:42][SAL ]socket[4] has closed
 
2025-07-16 21:07:40,210  [DEBUG] [D][11:35:42][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:07:40,215  [DEBUG] [D][11:35:42][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 11
 
2025-07-16 21:07:40,218  [DEBUG] [D][11:35:42][COMM]Main Task receive event:86
 
2025-07-16 21:07:40,226  [DEBUG] [D][11:35:42][HSDK][0] flush to flash addr:[0xE46C00] --- write len --- [256]
 
2025-07-16 21:07:40,235  [DEBUG] [W][11:35:42][PROT]remove success[1730201742],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:07:40,237  [DEBUG] [D][11:35:42][PROT]index:0 1730201742
 
2025-07-16 21:07:40,240  [DEBUG] [D][11:35:42][PROT]is_send:0
 
2025-07-16 21:07:40,243  [DEBUG] [D][11:35:42][PROT]sequence_num:35
 
2025-07-16 21:07:40,245  [DEBUG] [D][11:35:42][PROT]retry_timeout:0
 
2025-07-16 21:07:40,248  [DEBUG] [D][11:35:42][PROT]retry_times:1
 
2025-07-16 21:07:40,251  [DEBUG] [D][11:35:42][PROT]send_path:0x2
 
2025-07-16 21:07:40,256  [DEBUG] [D][11:35:42][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:07:40,266  [DEBUG] [D][11:35:42][PROT]===========================================================
 
2025-07-16 21:07:40,270  [DEBUG] [W][11:35:42][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201742]
 
2025-07-16 21:07:40,276  [DEBUG] [D][11:35:42][PROT]===========================================================
 
2025-07-16 21:07:40,281  [DEBUG] [D][11:35:42][PROT]sending traceid [9999999999900014]
 
2025-07-16 21:07:40,284  [DEBUG] [D][11:35:42][PROT]Send_TO_M2M [1730201742]
 
2025-07-16 21:07:40,290  [DEBUG] [D][11:35:42][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:07:40,296  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:07:40,302  [DEBUG] [D][11:35:42][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:07:40,307  [DEBUG] [D][11:35:42][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:07:40,312  [DEBUG] [D][11:35:42][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:07:40,319  [DEBUG] [D][11:35:42][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:07:40,323  [DEBUG] [D][11:35:42][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:07:40,329  [DEBUG] [D][11:35:42][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:07:40,331  [DEBUG] [D][11:35:42][CAT1]at ops open socket[0]
 
2025-07-16 21:07:40,334  [DEBUG] [D][11:35:42][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:07:40,335  [DEBUG] 
 
2025-07-16 21:07:40,342  [DEBUG] [W][11:35:42][PROT]add success [1730201742],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:07:40,352  [DEBUG] [D][11:35:42][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:07:40,354  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:07:40,356  [DEBUG] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,359  [DEBUG] +CGATT: 1
 
2025-07-16 21:07:40,359  [DEBUG] 
 
2025-07-16 21:07:40,360  [DEBUG] OK
 
2025-07-16 21:07:40,360  [DEBUG] 
 
2025-07-16 21:07:40,363  [DEBUG] [D][11:35:42][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:07:40,363  [DEBUG] 
 
2025-07-16 21:07:40,365  [DEBUG] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,366  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:07:40,366  [DEBUG] 
 
2025-07-16 21:07:40,366  [DEBUG] OK
 
2025-07-16 21:07:40,367  [DEBUG] 
 
2025-07-16 21:07:40,373  [DEBUG] [D][11:35:42][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:07:40,374  [DEBUG] 
 
2025-07-16 21:07:40,374  [DEBUG] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,376  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:07:40,376  [DEBUG] 
 
2025-07-16 21:07:40,376  [DEBUG] OK
 
2025-07-16 21:07:40,378  [DEBUG] 
 
2025-07-16 21:07:40,385  [DEBUG] [D][11:35:42][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:07:40,386  [DEBUG] 
 
2025-07-16 21:07:40,387  [DEBUG] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,387  [DEBUG] OK
 
2025-07-16 21:07:40,388  [DEBUG] 
 
2025-07-16 21:07:40,391  [DEBUG] [D][11:35:42][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:07:40,424  [DEBUG] [D][11:35:42][CAT1]opened : 0, 0
 
2025-07-16 21:07:40,428  [DEBUG] [D][11:35:42][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:07:40,434  [DEBUG] [D][11:35:42][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:07:40,439  [DEBUG] [D][11:35:42][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:07:40,445  [DEBUG] [D][11:35:42][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:07:40,450  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:07:40,453  [DEBUG] [D][11:35:42][SAL ]sock send credit cnt[6]
 
2025-07-16 21:07:40,456  [DEBUG] [D][11:35:42][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:07:40,461  [DEBUG] [D][11:35:42][M2M ]m2m send data len[70]
 
2025-07-16 21:07:40,464  [DEBUG] [D][11:35:42][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:07:40,473  [DEBUG] [D][11:35:42][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:07:40,477  [DEBUG] [D][11:35:42][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:07:40,481  [DEBUG] [D][11:35:42][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:07:40,482  [DEBUG] 
 
2025-07-16 21:07:40,486  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:07:40,491  [DEBUG] [D][11:35:42][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:07:40,497  [DEBUG] 0023B905113311331133113311331B8846C26D84433FEAC2A266FEFB27AC9BD9F4697B
 
2025-07-16 21:07:40,500  [DEBUG] [D][11:35:42][CAT1]<<< 
 
2025-07-16 21:07:40,501  [DEBUG] SEND OK
 
2025-07-16 21:07:40,502  [DEBUG] 
 
2025-07-16 21:07:40,506  [DEBUG] [D][11:35:42][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:07:40,508  [DEBUG] [D][11:35:42][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:07:40,510  [DEBUG] 
 
2025-07-16 21:07:40,514  [DEBUG] [D][11:35:42][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:07:40,520  [DEBUG] [D][11:35:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:07:40,522  [DEBUG] [D][11:35:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:07:40,528  [DEBUG] [D][11:35:42][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:07:40,531  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:07:40,536  [DEBUG] [D][11:35:42][PROT]M2M Send ok [1730201742]
 
2025-07-16 21:07:40,538  [DEBUG] [D][11:35:42][PROT]CLEAN:0
 
2025-07-16 21:07:40,541  [DEBUG] [D][11:35:42][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:07:40,548  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:07:40,553  [DEBUG] [D][11:35:42][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:07:40,556  [DEBUG] [D][11:35:42][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:07:40,562  [DEBUG] [D][11:35:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:07:40,824  [DEBUG] [D][11:35:43][COMM]S->M yaw:INVALID
 
2025-07-16 21:07:41,882  [DEBUG] [D][11:35:44][COMM]M->S yaw:INVALID
 
2025-07-16 21:08:04,881  [DEBUG] [D][11:36:07][COMM]S->M yaw:INVALID
 
2025-07-16 21:08:06,158  [DEBUG] [D][11:36:08][COMM]M->S yaw:INVALID
 
2025-07-16 21:08:08,719  [DEBUG] [D][11:36:10][COMM]S->M yaw:INVALID
 
2025-07-16 21:08:10,115  [DEBUG] [D][11:36:12][COMM]M->S yaw:INVALID
 
2025-07-16 21:08:21,122  [DEBUG] [D][11:36:23][CAT1]closed : 0
 
2025-07-16 21:08:21,128  [DEBUG] [D][11:36:23][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:08:21,130  [DEBUG] [D][11:36:23][SAL ]socket closed id[0]
 
2025-07-16 21:08:21,135  [DEBUG] [D][11:36:23][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:08:21,142  [DEBUG] [D][11:36:23][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:08:21,144  [DEBUG] [D][11:36:23][M2M ]m2m select fd[4]
 
2025-07-16 21:08:21,149  [DEBUG] [D][11:36:23][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:08:21,152  [DEBUG] [D][11:36:23][M2M ]tcpclient close[4]
 
2025-07-16 21:08:21,155  [DEBUG] [D][11:36:23][SAL ]socket[4] has closed
 
2025-07-16 21:08:21,160  [DEBUG] [D][11:36:23][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:08:21,166  [DEBUG] [D][11:36:23][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 12
 
2025-07-16 21:08:21,199  [DEBUG] [D][11:36:23][COMM]Main Task receive event:86
 
2025-07-16 21:08:21,208  [DEBUG] [W][11:36:23][PROT]remove success[1730201783],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:08:21,217  [DEBUG] [W][11:36:23][PROT]add success [1730201783],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:08:21,222  [DEBUG] [D][11:36:23][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:08:21,224  [DEBUG] [D][11:36:23][PROT]index:0 1730201783
 
2025-07-16 21:08:21,227  [DEBUG] [D][11:36:23][PROT]is_send:0
 
2025-07-16 21:08:21,233  [DEBUG] [D][11:36:23][PROT]sequence_num:36
 
2025-07-16 21:08:21,234  [DEBUG] [D][11:36:23][PROT]retry_timeout:0
 
2025-07-16 21:08:21,238  [DEBUG] [D][11:36:23][PROT]retry_times:1
 
2025-07-16 21:08:21,241  [DEBUG] [D][11:36:23][PROT]send_path:0x2
 
2025-07-16 21:08:21,247  [DEBUG] [D][11:36:23][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:08:21,253  [DEBUG] [D][11:36:23][PROT]===========================================================
 
2025-07-16 21:08:21,258  [DEBUG] [W][11:36:23][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201783]
 
2025-07-16 21:08:21,266  [DEBUG] [D][11:36:23][PROT]===========================================================
 
2025-07-16 21:08:21,269  [DEBUG] [D][11:36:23][PROT]sending traceid [9999999999900015]
 
2025-07-16 21:08:21,275  [DEBUG] [D][11:36:23][PROT]Send_TO_M2M [1730201783]
 
2025-07-16 21:08:21,280  [DEBUG] [D][11:36:23][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:08:21,283  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:08:21,289  [DEBUG] [D][11:36:23][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:08:21,295  [DEBUG] [D][11:36:23][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:08:21,299  [DEBUG] [D][11:36:23][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:08:21,306  [DEBUG] [D][11:36:23][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:08:21,314  [DEBUG] [D][11:36:23][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:08:21,316  [DEBUG] [D][11:36:23][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:08:21,319  [DEBUG] [D][11:36:23][CAT1]at ops open socket[0]
 
2025-07-16 21:08:21,324  [DEBUG] [D][11:36:23][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:08:21,325  [DEBUG] 
 
2025-07-16 21:08:21,330  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:08:21,333  [DEBUG] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,333  [DEBUG] +CGATT: 1
 
2025-07-16 21:08:21,333  [DEBUG] 
 
2025-07-16 21:08:21,334  [DEBUG] OK
 
2025-07-16 21:08:21,334  [DEBUG] 
 
2025-07-16 21:08:21,336  [DEBUG] [D][11:36:23][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:08:21,337  [DEBUG] 
 
2025-07-16 21:08:21,338  [DEBUG] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,339  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:08:21,340  [DEBUG] 
 
2025-07-16 21:08:21,341  [DEBUG] OK
 
2025-07-16 21:08:21,342  [DEBUG] 
 
2025-07-16 21:08:21,345  [DEBUG] [D][11:36:23][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:08:21,346  [DEBUG] 
 
2025-07-16 21:08:21,348  [DEBUG] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,350  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:08:21,350  [DEBUG] 
 
2025-07-16 21:08:21,351  [DEBUG] OK
 
2025-07-16 21:08:21,351  [DEBUG] 
 
2025-07-16 21:08:21,359  [DEBUG] [D][11:36:23][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:08:21,360  [DEBUG] 
 
2025-07-16 21:08:21,361  [DEBUG] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,362  [DEBUG] OK
 
2025-07-16 21:08:21,363  [DEBUG] 
 
2025-07-16 21:08:21,365  [DEBUG] [D][11:36:23][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:08:21,442  [DEBUG] [D][11:36:23][CAT1]opened : 0, 0
 
2025-07-16 21:08:21,445  [DEBUG] [D][11:36:23][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:08:21,452  [DEBUG] [D][11:36:23][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:08:21,457  [DEBUG] [D][11:36:23][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:08:21,462  [DEBUG] [D][11:36:23][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:08:21,468  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:08:21,470  [DEBUG] [D][11:36:23][SAL ]sock send credit cnt[6]
 
2025-07-16 21:08:21,473  [DEBUG] [D][11:36:23][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:08:21,479  [DEBUG] [D][11:36:23][M2M ]m2m send data len[70]
 
2025-07-16 21:08:21,481  [DEBUG] [D][11:36:23][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:08:21,491  [DEBUG] [D][11:36:23][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:08:21,496  [DEBUG] [D][11:36:23][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:08:21,498  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:08:21,504  [DEBUG] [D][11:36:23][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:08:21,504  [DEBUG] 
 
2025-07-16 21:08:21,508  [DEBUG] [D][11:36:23][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:08:21,516  [DEBUG] 0023B906113311331133113311331B884541A058DC1BB1DDE0EA468B42D2014749EB27
 
2025-07-16 21:08:21,518  [DEBUG] [D][11:36:23][CAT1]<<< 
 
2025-07-16 21:08:21,519  [DEBUG] SEND OK
 
2025-07-16 21:08:21,519  [DEBUG] 
 
2025-07-16 21:08:21,523  [DEBUG] [D][11:36:23][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:08:21,526  [DEBUG] [D][11:36:23][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:08:21,527  [DEBUG] 
 
2025-07-16 21:08:21,532  [DEBUG] [D][11:36:23][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:08:21,538  [DEBUG] [D][11:36:23][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:08:21,541  [DEBUG] [D][11:36:23][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:08:21,546  [DEBUG] [D][11:36:23][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:08:21,549  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:08:21,554  [DEBUG] [D][11:36:23][PROT]M2M Send ok [1730201783]
 
2025-07-16 21:08:21,557  [DEBUG] [D][11:36:23][PROT]CLEAN:0
 
2025-07-16 21:08:21,561  [DEBUG] [D][11:36:23][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:08:21,566  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:08:21,572  [DEBUG] [D][11:36:23][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:08:21,574  [DEBUG] [D][11:36:23][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:08:21,580  [DEBUG] [D][11:36:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:02,099  [DEBUG] [D][11:37:04][CAT1]closed : 0
 
2025-07-16 21:09:02,103  [DEBUG] [D][11:37:04][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:09:02,105  [DEBUG] [D][11:37:04][SAL ]socket closed id[0]
 
2025-07-16 21:09:02,111  [DEBUG] [D][11:37:04][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:09:02,117  [DEBUG] [D][11:37:04][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:09:02,119  [DEBUG] [D][11:37:04][M2M ]m2m select fd[4]
 
2025-07-16 21:09:02,125  [DEBUG] [D][11:37:04][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:09:02,128  [DEBUG] [D][11:37:04][M2M ]tcpclient close[4]
 
2025-07-16 21:09:02,131  [DEBUG] [D][11:37:04][SAL ]socket[4] has closed
 
2025-07-16 21:09:02,136  [DEBUG] [D][11:37:04][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:09:02,143  [DEBUG] [D][11:37:04][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 13
 
2025-07-16 21:09:02,144  [DEBUG] [D][11:37:04][COMM]Main Task receive event:86
 
2025-07-16 21:09:02,154  [DEBUG] [W][11:37:04][PROT]remove success[1730201824],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:09:02,159  [DEBUG] [D][11:37:04][PROT]index:0 1730201824
 
2025-07-16 21:09:02,162  [DEBUG] [D][11:37:04][PROT]is_send:0
 
2025-07-16 21:09:02,164  [DEBUG] [D][11:37:04][PROT]sequence_num:37
 
2025-07-16 21:09:02,167  [DEBUG] [D][11:37:04][PROT]retry_timeout:0
 
2025-07-16 21:09:02,170  [DEBUG] [D][11:37:04][PROT]retry_times:1
 
2025-07-16 21:09:02,173  [DEBUG] [D][11:37:04][PROT]send_path:0x2
 
2025-07-16 21:09:02,177  [DEBUG] [D][11:37:04][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:09:02,184  [DEBUG] [D][11:37:04][PROT]===========================================================
 
2025-07-16 21:09:02,190  [DEBUG] [W][11:37:04][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201824]
 
2025-07-16 21:09:02,198  [DEBUG] [D][11:37:04][PROT]===========================================================
 
2025-07-16 21:09:02,204  [DEBUG] [D][11:37:04][PROT]sending traceid [9999999999900016]
 
2025-07-16 21:09:02,206  [DEBUG] [D][11:37:04][PROT]Send_TO_M2M [1730201824]
 
2025-07-16 21:09:02,212  [DEBUG] [D][11:37:04][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:02,217  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:02,219  [DEBUG] [D][11:37:04][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:09:02,229  [DEBUG] [D][11:37:04][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:09:02,231  [DEBUG] [D][11:37:04][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:09:02,240  [DEBUG] [D][11:37:04][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:09:02,245  [DEBUG] [D][11:37:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:09:02,248  [DEBUG] [D][11:37:04][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:09:02,250  [DEBUG] [D][11:37:04][CAT1]at ops open socket[0]
 
2025-07-16 21:09:02,257  [DEBUG] [D][11:37:04][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:09:02,257  [DEBUG] 
 
2025-07-16 21:09:02,264  [DEBUG] [W][11:37:04][PROT]add success [1730201824],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:09:02,271  [DEBUG] [D][11:37:04][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:09:02,276  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:09:02,278  [DEBUG] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,279  [DEBUG] +CGATT: 1
 
2025-07-16 21:09:02,279  [DEBUG] 
 
2025-07-16 21:09:02,279  [DEBUG] OK
 
2025-07-16 21:09:02,280  [DEBUG] 
 
2025-07-16 21:09:02,284  [DEBUG] [D][11:37:04][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:09:02,285  [DEBUG] 
 
2025-07-16 21:09:02,285  [DEBUG] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,286  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:09:02,287  [DEBUG] 
 
2025-07-16 21:09:02,287  [DEBUG] OK
 
2025-07-16 21:09:02,288  [DEBUG] 
 
2025-07-16 21:09:02,289  [DEBUG] [D][11:37:04][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:09:02,292  [DEBUG] 
 
2025-07-16 21:09:02,293  [DEBUG] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,295  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:09:02,295  [DEBUG] 
 
2025-07-16 21:09:02,295  [DEBUG] OK
 
2025-07-16 21:09:02,297  [DEBUG] 
 
2025-07-16 21:09:02,304  [DEBUG] [D][11:37:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:09:02,305  [DEBUG] 
 
2025-07-16 21:09:02,306  [DEBUG] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,306  [DEBUG] OK
 
2025-07-16 21:09:02,307  [DEBUG] 
 
2025-07-16 21:09:02,311  [DEBUG] [D][11:37:04][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:09:02,357  [DEBUG] [D][11:37:04][CAT1]opened : 0, 0
 
2025-07-16 21:09:02,360  [DEBUG] [D][11:37:04][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:02,365  [DEBUG] [D][11:37:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:09:02,370  [DEBUG] [D][11:37:04][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:09:02,377  [DEBUG] [D][11:37:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:09:02,381  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:09:02,384  [DEBUG] [D][11:37:04][SAL ]sock send credit cnt[6]
 
2025-07-16 21:09:02,387  [DEBUG] [D][11:37:04][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:09:02,393  [DEBUG] [D][11:37:04][M2M ]m2m send data len[70]
 
2025-07-16 21:09:02,395  [DEBUG] [D][11:37:04][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:09:02,404  [DEBUG] [D][11:37:04][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:09:02,410  [DEBUG] [D][11:37:04][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:09:02,413  [DEBUG] [D][11:37:04][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:09:02,413  [DEBUG] 
 
2025-07-16 21:09:02,418  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:09:02,423  [DEBUG] [D][11:37:04][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:09:02,429  [DEBUG] 0023B909113311331133113311331B884349476A4DED3FF21DD3BB8BB1C99DCB8A93C7
 
2025-07-16 21:09:02,432  [DEBUG] [D][11:37:04][CAT1]<<< 
 
2025-07-16 21:09:02,432  [DEBUG] SEND OK
 
2025-07-16 21:09:02,432  [DEBUG] 
 
2025-07-16 21:09:02,437  [DEBUG] [D][11:37:04][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:09:02,440  [DEBUG] [D][11:37:04][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:09:02,441  [DEBUG] 
 
2025-07-16 21:09:02,446  [DEBUG] [D][11:37:04][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:02,452  [DEBUG] [D][11:37:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:09:02,454  [DEBUG] [D][11:37:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:09:02,459  [DEBUG] [D][11:37:04][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:02,462  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:02,467  [DEBUG] [D][11:37:04][PROT]M2M Send ok [1730201824]
 
2025-07-16 21:09:02,470  [DEBUG] [D][11:37:04][PROT]CLEAN:0
 
2025-07-16 21:09:02,474  [DEBUG] [D][11:37:04][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:02,479  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:02,485  [DEBUG] [D][11:37:04][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:09:02,487  [DEBUG] [D][11:37:04][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:02,494  [DEBUG] [D][11:37:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:43,049  [DEBUG] [D][11:37:45][CAT1]closed : 0
 
2025-07-16 21:09:43,055  [DEBUG] [D][11:37:45][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:09:43,057  [DEBUG] [D][11:37:45][SAL ]socket closed id[0]
 
2025-07-16 21:09:43,063  [DEBUG] [D][11:37:45][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:09:43,069  [DEBUG] [D][11:37:45][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:09:43,071  [DEBUG] [D][11:37:45][M2M ]m2m select fd[4]
 
2025-07-16 21:09:43,077  [DEBUG] [D][11:37:45][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:09:43,079  [DEBUG] [D][11:37:45][M2M ]tcpclient close[4]
 
2025-07-16 21:09:43,082  [DEBUG] [D][11:37:45][SAL ]socket[4] has closed
 
2025-07-16 21:09:43,087  [DEBUG] [D][11:37:45][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:09:43,093  [DEBUG] [D][11:37:45][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 14
 
2025-07-16 21:09:43,120  [DEBUG] [D][11:37:45][COMM]Main Task receive event:86
 
2025-07-16 21:09:43,126  [DEBUG] [D][11:37:45][HSDK][0] flush to flash addr:[0xE46D00] --- write len --- [256]
 
2025-07-16 21:09:43,136  [DEBUG] [W][11:37:45][PROT]remove success[1730201865],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:09:43,144  [DEBUG] [W][11:37:45][PROT]add success [1730201865],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:09:43,151  [DEBUG] [D][11:37:45][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:09:43,153  [DEBUG] [D][11:37:45][PROT]index:0 1730201865
 
2025-07-16 21:09:43,156  [DEBUG] [D][11:37:45][PROT]is_send:0
 
2025-07-16 21:09:43,159  [DEBUG] [D][11:37:45][PROT]sequence_num:38
 
2025-07-16 21:09:43,161  [DEBUG] [D][11:37:45][PROT]retry_timeout:0
 
2025-07-16 21:09:43,164  [DEBUG] [D][11:37:45][PROT]retry_times:1
 
2025-07-16 21:09:43,167  [DEBUG] [D][11:37:45][PROT]send_path:0x2
 
2025-07-16 21:09:43,172  [DEBUG] [D][11:37:45][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:09:43,182  [DEBUG] [D][11:37:45][PROT]===========================================================
 
2025-07-16 21:09:43,187  [DEBUG] [W][11:37:45][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201865]
 
2025-07-16 21:09:43,192  [DEBUG] [D][11:37:45][PROT]===========================================================
 
2025-07-16 21:09:43,197  [DEBUG] [D][11:37:45][PROT]sending traceid [9999999999900017]
 
2025-07-16 21:09:43,199  [DEBUG] [D][11:37:45][PROT]Send_TO_M2M [1730201865]
 
2025-07-16 21:09:43,205  [DEBUG] [D][11:37:45][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:43,211  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:43,214  [DEBUG] [D][11:37:45][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:09:43,223  [DEBUG] [D][11:37:45][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:09:43,225  [DEBUG] [D][11:37:45][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:09:43,234  [DEBUG] [D][11:37:45][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:09:43,239  [DEBUG] [D][11:37:45][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:09:43,241  [DEBUG] [D][11:37:45][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:09:43,247  [DEBUG] [D][11:37:45][CAT1]at ops open socket[0]
 
2025-07-16 21:09:43,250  [DEBUG] [D][11:37:45][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:09:43,250  [DEBUG] 
 
2025-07-16 21:09:43,256  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:09:43,258  [DEBUG] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,259  [DEBUG] +CGATT: 1
 
2025-07-16 21:09:43,259  [DEBUG] 
 
2025-07-16 21:09:43,260  [DEBUG] OK
 
2025-07-16 21:09:43,260  [DEBUG] 
 
2025-07-16 21:09:43,264  [DEBUG] [D][11:37:45][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:09:43,265  [DEBUG] 
 
2025-07-16 21:09:43,265  [DEBUG] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,267  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:09:43,267  [DEBUG] 
 
2025-07-16 21:09:43,268  [DEBUG] OK
 
2025-07-16 21:09:43,268  [DEBUG] 
 
2025-07-16 21:09:43,274  [DEBUG] [D][11:37:45][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:09:43,278  [DEBUG] 
 
2025-07-16 21:09:43,278  [DEBUG] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,279  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:09:43,280  [DEBUG] 
 
2025-07-16 21:09:43,280  [DEBUG] OK
 
2025-07-16 21:09:43,281  [DEBUG] 
 
2025-07-16 21:09:43,284  [DEBUG] [D][11:37:45][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:09:43,284  [DEBUG] 
 
2025-07-16 21:09:43,286  [DEBUG] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,286  [DEBUG] OK
 
2025-07-16 21:09:43,287  [DEBUG] 
 
2025-07-16 21:09:43,291  [DEBUG] [D][11:37:45][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:09:43,371  [DEBUG] [D][11:37:45][CAT1]opened : 0, 0
 
2025-07-16 21:09:43,373  [DEBUG] [D][11:37:45][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:43,380  [DEBUG] [D][11:37:45][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:09:43,384  [DEBUG] [D][11:37:45][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:09:43,390  [DEBUG] [D][11:37:45][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:09:43,395  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:09:43,398  [DEBUG] [D][11:37:45][SAL ]sock send credit cnt[6]
 
2025-07-16 21:09:43,401  [DEBUG] [D][11:37:45][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:09:43,406  [DEBUG] [D][11:37:45][M2M ]m2m send data len[70]
 
2025-07-16 21:09:43,409  [DEBUG] [D][11:37:45][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:09:43,418  [DEBUG] [D][11:37:45][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:09:43,422  [DEBUG] [D][11:37:45][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:09:43,426  [DEBUG] [D][11:37:45][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:09:43,427  [DEBUG] 
 
2025-07-16 21:09:43,432  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:09:43,436  [DEBUG] [D][11:37:45][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:09:43,442  [DEBUG] 0023B907113311331133113311331B884EED2F8F53045FC58B9E7C8535F30601FC2133
 
2025-07-16 21:09:43,445  [DEBUG] [D][11:37:45][CAT1]<<< 
 
2025-07-16 21:09:43,446  [DEBUG] SEND OK
 
2025-07-16 21:09:43,446  [DEBUG] 
 
2025-07-16 21:09:43,450  [DEBUG] [D][11:37:45][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:09:43,454  [DEBUG] [D][11:37:45][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:09:43,455  [DEBUG] 
 
2025-07-16 21:09:43,459  [DEBUG] [D][11:37:45][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:09:43,465  [DEBUG] [D][11:37:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:09:43,467  [DEBUG] [D][11:37:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:09:43,473  [DEBUG] [D][11:37:45][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:43,476  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:09:43,481  [DEBUG] [D][11:37:45][PROT]M2M Send ok [1730201865]
 
2025-07-16 21:09:43,484  [DEBUG] [D][11:37:45][PROT]CLEAN:0
 
2025-07-16 21:09:43,487  [DEBUG] [D][11:37:45][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:09:43,492  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:09:43,499  [DEBUG] [D][11:37:45][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:09:43,502  [DEBUG] [D][11:37:45][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:09:43,507  [DEBUG] [D][11:37:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:10:24,000  [DEBUG] [D][11:38:26][CAT1]closed : 0
 
2025-07-16 21:10:24,006  [DEBUG] [D][11:38:26][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:10:24,008  [DEBUG] [D][11:38:26][SAL ]socket closed id[0]
 
2025-07-16 21:10:24,014  [DEBUG] [D][11:38:26][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:10:24,019  [DEBUG] [D][11:38:26][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:10:24,022  [DEBUG] [D][11:38:26][M2M ]m2m select fd[4]
 
2025-07-16 21:10:24,027  [DEBUG] [D][11:38:26][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:10:24,030  [DEBUG] [D][11:38:26][M2M ]tcpclient close[4]
 
2025-07-16 21:10:24,033  [DEBUG] [D][11:38:26][SAL ]socket[4] has closed
 
2025-07-16 21:10:24,039  [DEBUG] [D][11:38:26][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:10:24,044  [DEBUG] [D][11:38:26][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 15
 
2025-07-16 21:10:24,068  [DEBUG] [D][11:38:26][COMM]Main Task receive event:86
 
2025-07-16 21:10:24,076  [DEBUG] [W][11:38:26][PROT]remove success[1730201906],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:10:24,085  [DEBUG] [W][11:38:26][PROT]add success [1730201906],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:10:24,092  [DEBUG] [D][11:38:26][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:10:24,093  [DEBUG] [D][11:38:26][PROT]index:0 1730201906
 
2025-07-16 21:10:24,096  [DEBUG] [D][11:38:26][PROT]is_send:0
 
2025-07-16 21:10:24,102  [DEBUG] [D][11:38:26][PROT]sequence_num:39
 
2025-07-16 21:10:24,104  [DEBUG] [D][11:38:26][PROT]retry_timeout:0
 
2025-07-16 21:10:24,107  [DEBUG] [D][11:38:26][PROT]retry_times:1
 
2025-07-16 21:10:24,110  [DEBUG] [D][11:38:26][PROT]send_path:0x2
 
2025-07-16 21:10:24,116  [DEBUG] [D][11:38:26][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:10:24,122  [DEBUG] [D][11:38:26][PROT]===========================================================
 
2025-07-16 21:10:24,127  [DEBUG] [W][11:38:26][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201906]
 
2025-07-16 21:10:24,135  [DEBUG] [D][11:38:26][PROT]===========================================================
 
2025-07-16 21:10:24,138  [DEBUG] [D][11:38:26][PROT]sending traceid [9999999999900018]
 
2025-07-16 21:10:24,143  [DEBUG] [D][11:38:26][PROT]Send_TO_M2M [1730201906]
 
2025-07-16 21:10:24,149  [DEBUG] [D][11:38:26][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:10:24,152  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:10:24,158  [DEBUG] [D][11:38:26][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:10:24,163  [DEBUG] [D][11:38:26][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:10:24,168  [DEBUG] [D][11:38:26][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:10:24,175  [DEBUG] [D][11:38:26][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:10:24,183  [DEBUG] [D][11:38:26][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:10:24,185  [DEBUG] [D][11:38:26][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:10:24,188  [DEBUG] [D][11:38:26][CAT1]at ops open socket[0]
 
2025-07-16 21:10:24,193  [DEBUG] [D][11:38:26][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:10:24,194  [DEBUG] 
 
2025-07-16 21:10:24,199  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:10:24,200  [DEBUG] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,201  [DEBUG] +CGATT: 1
 
2025-07-16 21:10:24,203  [DEBUG] 
 
2025-07-16 21:10:24,203  [DEBUG] OK
 
2025-07-16 21:10:24,203  [DEBUG] 
 
2025-07-16 21:10:24,204  [DEBUG] [D][11:38:26][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:10:24,205  [DEBUG] 
 
2025-07-16 21:10:24,207  [DEBUG] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,208  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:10:24,208  [DEBUG] 
 
2025-07-16 21:10:24,209  [DEBUG] OK
 
2025-07-16 21:10:24,210  [DEBUG] 
 
2025-07-16 21:10:24,213  [DEBUG] [D][11:38:26][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:10:24,213  [DEBUG] 
 
2025-07-16 21:10:24,215  [DEBUG] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,218  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:10:24,219  [DEBUG] 
 
2025-07-16 21:10:24,219  [DEBUG] OK
 
2025-07-16 21:10:24,219  [DEBUG] 
 
2025-07-16 21:10:24,227  [DEBUG] [D][11:38:26][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:10:24,228  [DEBUG] 
 
2025-07-16 21:10:24,229  [DEBUG] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,230  [DEBUG] OK
 
2025-07-16 21:10:24,230  [DEBUG] 
 
2025-07-16 21:10:24,233  [DEBUG] [D][11:38:26][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:10:24,302  [DEBUG] [D][11:38:26][CAT1]opened : 0, 0
 
2025-07-16 21:10:24,305  [DEBUG] [D][11:38:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:10:24,311  [DEBUG] [D][11:38:26][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:10:24,316  [DEBUG] [D][11:38:26][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:10:24,321  [DEBUG] [D][11:38:26][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:10:24,327  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:10:24,330  [DEBUG] [D][11:38:26][SAL ]sock send credit cnt[6]
 
2025-07-16 21:10:24,333  [DEBUG] [D][11:38:26][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:10:24,338  [DEBUG] [D][11:38:26][M2M ]m2m send data len[70]
 
2025-07-16 21:10:24,341  [DEBUG] [D][11:38:26][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:10:24,349  [DEBUG] [D][11:38:26][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:10:24,354  [DEBUG] [D][11:38:26][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:10:24,357  [DEBUG] [D][11:38:26][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:10:24,359  [DEBUG] 
 
2025-07-16 21:10:24,363  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:10:24,368  [DEBUG] [D][11:38:26][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:10:24,375  [DEBUG] 0023B904113311331133113311331B88428F71A963829F327E6CF30F6AB0A145FA9E2A
 
2025-07-16 21:10:24,376  [DEBUG] [D][11:38:26][CAT1]<<< 
 
2025-07-16 21:10:24,377  [DEBUG] SEND OK
 
2025-07-16 21:10:24,378  [DEBUG] 
 
2025-07-16 21:10:24,382  [DEBUG] [D][11:38:26][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:10:24,385  [DEBUG] [D][11:38:26][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:10:24,385  [DEBUG] 
 
2025-07-16 21:10:24,391  [DEBUG] [D][11:38:26][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:10:24,397  [DEBUG] [D][11:38:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:10:24,399  [DEBUG] [D][11:38:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:10:24,405  [DEBUG] [D][11:38:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:10:24,408  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:10:24,413  [DEBUG] [D][11:38:26][PROT]M2M Send ok [1730201906]
 
2025-07-16 21:10:24,416  [DEBUG] [D][11:38:26][PROT]CLEAN:0
 
2025-07-16 21:10:24,418  [DEBUG] [D][11:38:26][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:10:24,425  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:10:24,430  [DEBUG] [D][11:38:26][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:10:24,433  [DEBUG] [D][11:38:26][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:10:24,439  [DEBUG] [D][11:38:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:04,942  [DEBUG] [D][11:39:07][CAT1]closed : 0
 
2025-07-16 21:11:04,948  [DEBUG] [D][11:39:07][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:11:04,951  [DEBUG] [D][11:39:07][SAL ]socket closed id[0]
 
2025-07-16 21:11:04,956  [DEBUG] [D][11:39:07][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:11:04,962  [DEBUG] [D][11:39:07][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:11:04,964  [DEBUG] [D][11:39:07][M2M ]m2m select fd[4]
 
2025-07-16 21:11:04,970  [DEBUG] [D][11:39:07][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:11:04,972  [DEBUG] [D][11:39:07][M2M ]tcpclient close[4]
 
2025-07-16 21:11:04,975  [DEBUG] [D][11:39:07][SAL ]socket[4] has closed
 
2025-07-16 21:11:04,981  [DEBUG] [D][11:39:07][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:11:04,986  [DEBUG] [D][11:39:07][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 16
 
2025-07-16 21:11:04,998  [DEBUG] [D][11:39:07][COMM]Main Task receive event:86
 
2025-07-16 21:11:05,007  [DEBUG] [W][11:39:07][PROT]remove success[1730201947],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:11:05,015  [DEBUG] [W][11:39:07][PROT]add success [1730201947],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:11:05,022  [DEBUG] [D][11:39:07][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:11:05,023  [DEBUG] [D][11:39:07][PROT]index:0 1730201947
 
2025-07-16 21:11:05,026  [DEBUG] [D][11:39:07][PROT]is_send:0
 
2025-07-16 21:11:05,031  [DEBUG] [D][11:39:07][PROT]sequence_num:40
 
2025-07-16 21:11:05,035  [DEBUG] [D][11:39:07][PROT]retry_timeout:0
 
2025-07-16 21:11:05,037  [DEBUG] [D][11:39:07][PROT]retry_times:1
 
2025-07-16 21:11:05,040  [DEBUG] [D][11:39:07][PROT]send_path:0x2
 
2025-07-16 21:11:05,045  [DEBUG] [D][11:39:07][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:11:05,051  [DEBUG] [D][11:39:07][PROT]===========================================================
 
2025-07-16 21:11:05,058  [DEBUG] [W][11:39:07][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201947]
 
2025-07-16 21:11:05,066  [DEBUG] [D][11:39:07][PROT]===========================================================
 
2025-07-16 21:11:05,068  [DEBUG] [D][11:39:07][PROT]sending traceid [9999999999900019]
 
2025-07-16 21:11:05,073  [DEBUG] [D][11:39:07][PROT]Send_TO_M2M [1730201947]
 
2025-07-16 21:11:05,079  [DEBUG] [D][11:39:07][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:05,082  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:05,088  [DEBUG] [D][11:39:07][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:11:05,094  [DEBUG] [D][11:39:07][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:11:05,099  [DEBUG] [D][11:39:07][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:11:05,105  [DEBUG] [D][11:39:07][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:11:05,114  [DEBUG] [D][11:39:07][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:11:05,116  [DEBUG] [D][11:39:07][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:11:05,118  [DEBUG] [D][11:39:07][CAT1]at ops open socket[0]
 
2025-07-16 21:11:05,124  [DEBUG] [D][11:39:07][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:11:05,125  [DEBUG] 
 
2025-07-16 21:11:05,129  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:11:05,130  [DEBUG] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,131  [DEBUG] +CGATT: 1
 
2025-07-16 21:11:05,132  [DEBUG] 
 
2025-07-16 21:11:05,132  [DEBUG] OK
 
2025-07-16 21:11:05,133  [DEBUG] 
 
2025-07-16 21:11:05,135  [DEBUG] [D][11:39:07][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:11:05,136  [DEBUG] 
 
2025-07-16 21:11:05,138  [DEBUG] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,139  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:11:05,140  [DEBUG] 
 
2025-07-16 21:11:05,140  [DEBUG] OK
 
2025-07-16 21:11:05,141  [DEBUG] 
 
2025-07-16 21:11:05,144  [DEBUG] [D][11:39:07][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:11:05,145  [DEBUG] 
 
2025-07-16 21:11:05,146  [DEBUG] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,150  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:11:05,150  [DEBUG] 
 
2025-07-16 21:11:05,150  [DEBUG] OK
 
2025-07-16 21:11:05,150  [DEBUG] 
 
2025-07-16 21:11:05,159  [DEBUG] [D][11:39:07][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:11:05,159  [DEBUG] 
 
2025-07-16 21:11:05,160  [DEBUG] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,161  [DEBUG] OK
 
2025-07-16 21:11:05,161  [DEBUG] 
 
2025-07-16 21:11:05,164  [DEBUG] [D][11:39:07][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:11:05,243  [DEBUG] [D][11:39:07][CAT1]opened : 0, 0
 
2025-07-16 21:11:05,246  [DEBUG] [D][11:39:07][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:05,251  [DEBUG] [D][11:39:07][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:11:05,256  [DEBUG] [D][11:39:07][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:11:05,262  [DEBUG] [D][11:39:07][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:11:05,267  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:11:05,270  [DEBUG] [D][11:39:07][SAL ]sock send credit cnt[6]
 
2025-07-16 21:11:05,272  [DEBUG] [D][11:39:07][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:11:05,278  [DEBUG] [D][11:39:07][M2M ]m2m send data len[70]
 
2025-07-16 21:11:05,281  [DEBUG] [D][11:39:07][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:11:05,290  [DEBUG] [D][11:39:07][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:11:05,295  [DEBUG] [D][11:39:07][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:11:05,298  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:11:05,303  [DEBUG] [D][11:39:07][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:11:05,303  [DEBUG] 
 
2025-07-16 21:11:05,309  [DEBUG] [D][11:39:07][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:11:05,315  [DEBUG] 0023B90B113311331133113311331B88417E7438FB91B91E8B606791CFB8E31776D14E
 
2025-07-16 21:11:05,317  [DEBUG] [D][11:39:07][CAT1]<<< 
 
2025-07-16 21:11:05,318  [DEBUG] SEND OK
 
2025-07-16 21:11:05,318  [DEBUG] 
 
2025-07-16 21:11:05,323  [DEBUG] [D][11:39:07][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:11:05,326  [DEBUG] [D][11:39:07][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:11:05,326  [DEBUG] 
 
2025-07-16 21:11:05,331  [DEBUG] [D][11:39:07][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:05,337  [DEBUG] [D][11:39:07][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:11:05,338  [DEBUG] [D][11:39:07][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:11:05,345  [DEBUG] [D][11:39:07][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:05,347  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:05,353  [DEBUG] [D][11:39:07][PROT]M2M Send ok [1730201947]
 
2025-07-16 21:11:05,356  [DEBUG] [D][11:39:07][PROT]CLEAN:0
 
2025-07-16 21:11:05,360  [DEBUG] [D][11:39:07][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:05,366  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:05,371  [DEBUG] [D][11:39:07][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:11:05,373  [DEBUG] [D][11:39:07][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:05,378  [DEBUG] [D][11:39:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:45,972  [DEBUG] [D][11:39:48][CAT1]closed : 0
 
2025-07-16 21:11:45,977  [DEBUG] [D][11:39:48][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:11:45,980  [DEBUG] [D][11:39:48][SAL ]socket closed id[0]
 
2025-07-16 21:11:45,985  [DEBUG] [D][11:39:48][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:11:45,991  [DEBUG] [D][11:39:48][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:11:45,993  [DEBUG] [D][11:39:48][M2M ]m2m select fd[4]
 
2025-07-16 21:11:45,999  [DEBUG] [D][11:39:48][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:11:46,001  [DEBUG] [D][11:39:48][M2M ]tcpclient close[4]
 
2025-07-16 21:11:46,004  [DEBUG] [D][11:39:48][SAL ]socket[4] has closed
 
2025-07-16 21:11:46,011  [DEBUG] [D][11:39:48][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:11:46,016  [DEBUG] [D][11:39:48][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 17
 
2025-07-16 21:11:46,028  [DEBUG] [D][11:39:48][COMM]Main Task receive event:86
 
2025-07-16 21:11:46,033  [DEBUG] [D][11:39:48][HSDK]need to erase for write: is[0x0] ie[0x5E00]
 
2025-07-16 21:11:46,044  [DEBUG] [D][11:39:48][HSDK][0] flush to flash addr:[0xE46E00] --- write len --- [256]
 
2025-07-16 21:11:46,052  [DEBUG] [W][11:39:48][PROT]remove success[1730201988],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:11:46,054  [DEBUG] [D][11:39:48][PROT]index:0 1730201988
 
2025-07-16 21:11:46,057  [DEBUG] [D][11:39:48][PROT]is_send:0
 
2025-07-16 21:11:46,060  [DEBUG] [D][11:39:48][PROT]sequence_num:41
 
2025-07-16 21:11:46,064  [DEBUG] [D][11:39:48][PROT]retry_timeout:0
 
2025-07-16 21:11:46,069  [DEBUG] [D][11:39:48][PROT]retry_times:1
 
2025-07-16 21:11:46,071  [DEBUG] [D][11:39:48][PROT]send_path:0x2
 
2025-07-16 21:11:46,074  [DEBUG] [D][11:39:48][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:11:46,083  [DEBUG] [D][11:39:48][PROT]===========================================================
 
2025-07-16 21:11:46,088  [DEBUG] [W][11:39:48][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201988]
 
2025-07-16 21:11:46,095  [DEBUG] [D][11:39:48][PROT]===========================================================
 
2025-07-16 21:11:46,100  [DEBUG] [D][11:39:48][PROT]sending traceid [999999999990001A]
 
2025-07-16 21:11:46,105  [DEBUG] [D][11:39:48][PROT]Send_TO_M2M [1730201988]
 
2025-07-16 21:11:46,108  [DEBUG] [D][11:39:48][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:46,113  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:46,119  [DEBUG] [D][11:39:48][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:11:46,125  [DEBUG] [D][11:39:48][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:11:46,130  [DEBUG] [D][11:39:48][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:11:46,136  [DEBUG] [D][11:39:48][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:11:46,142  [DEBUG] [D][11:39:48][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:11:46,149  [DEBUG] [W][11:39:48][PROT]add success [1730201988],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:11:46,158  [DEBUG] [D][11:39:48][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:11:46,160  [DEBUG] [D][11:39:48][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:11:46,163  [DEBUG] [D][11:39:48][CAT1]at ops open socket[0]
 
2025-07-16 21:11:46,169  [DEBUG] [D][11:39:48][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:11:46,169  [DEBUG] 
 
2025-07-16 21:11:46,175  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:11:46,175  [DEBUG] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,176  [DEBUG] +CGATT: 1
 
2025-07-16 21:11:46,177  [DEBUG] 
 
2025-07-16 21:11:46,177  [DEBUG] OK
 
2025-07-16 21:11:46,177  [DEBUG] 
 
2025-07-16 21:11:46,180  [DEBUG] [D][11:39:48][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:11:46,180  [DEBUG] 
 
2025-07-16 21:11:46,183  [DEBUG] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,184  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:11:46,185  [DEBUG] 
 
2025-07-16 21:11:46,185  [DEBUG] OK
 
2025-07-16 21:11:46,186  [DEBUG] 
 
2025-07-16 21:11:46,189  [DEBUG] [D][11:39:48][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:11:46,190  [DEBUG] 
 
2025-07-16 21:11:46,191  [DEBUG] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,194  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:11:46,195  [DEBUG] 
 
2025-07-16 21:11:46,195  [DEBUG] OK
 
2025-07-16 21:11:46,195  [DEBUG] 
 
2025-07-16 21:11:46,203  [DEBUG] [D][11:39:48][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:11:46,203  [DEBUG] 
 
2025-07-16 21:11:46,205  [DEBUG] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,205  [DEBUG] OK
 
2025-07-16 21:11:46,205  [DEBUG] 
 
2025-07-16 21:11:46,209  [DEBUG] [D][11:39:48][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:11:46,294  [DEBUG] [D][11:39:48][CAT1]opened : 0, 0
 
2025-07-16 21:11:46,297  [DEBUG] [D][11:39:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:46,302  [DEBUG] [D][11:39:48][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:11:46,307  [DEBUG] [D][11:39:48][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:11:46,313  [DEBUG] [D][11:39:48][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:11:46,318  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:11:46,321  [DEBUG] [D][11:39:48][SAL ]sock send credit cnt[6]
 
2025-07-16 21:11:46,324  [DEBUG] [D][11:39:48][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:11:46,330  [DEBUG] [D][11:39:48][M2M ]m2m send data len[70]
 
2025-07-16 21:11:46,333  [DEBUG] [D][11:39:48][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:11:46,342  [DEBUG] [D][11:39:48][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:11:46,346  [DEBUG] [D][11:39:48][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:11:46,349  [DEBUG] [D][11:39:48][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:11:46,351  [DEBUG] 
 
2025-07-16 21:11:46,355  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:11:46,360  [DEBUG] [D][11:39:48][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:11:46,366  [DEBUG] 0023B9D8113311331133113311331B884C85DA0101AB78FC51631E30F3C6AE279EF4E2
 
2025-07-16 21:11:46,369  [DEBUG] [D][11:39:48][CAT1]<<< 
 
2025-07-16 21:11:46,370  [DEBUG] SEND OK
 
2025-07-16 21:11:46,370  [DEBUG] 
 
2025-07-16 21:11:46,375  [DEBUG] [D][11:39:48][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:11:46,377  [DEBUG] [D][11:39:48][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:11:46,378  [DEBUG] 
 
2025-07-16 21:11:46,383  [DEBUG] [D][11:39:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:11:46,389  [DEBUG] [D][11:39:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:11:46,390  [DEBUG] [D][11:39:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:11:46,397  [DEBUG] [D][11:39:48][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:46,400  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:11:46,405  [DEBUG] [D][11:39:48][PROT]M2M Send ok [1730201988]
 
2025-07-16 21:11:46,407  [DEBUG] [D][11:39:48][PROT]CLEAN:0
 
2025-07-16 21:11:46,410  [DEBUG] [D][11:39:48][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:11:46,416  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:11:46,422  [DEBUG] [D][11:39:48][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:11:46,424  [DEBUG] [D][11:39:48][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:11:46,430  [DEBUG] [D][11:39:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:12:26,900  [DEBUG] [D][11:40:29][CAT1]closed : 0
 
2025-07-16 21:12:26,905  [DEBUG] [D][11:40:29][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:12:26,908  [DEBUG] [D][11:40:29][SAL ]socket closed id[0]
 
2025-07-16 21:12:26,913  [DEBUG] [D][11:40:29][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:12:26,919  [DEBUG] [D][11:40:29][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:12:26,922  [DEBUG] [D][11:40:29][M2M ]m2m select fd[4]
 
2025-07-16 21:12:26,928  [DEBUG] [D][11:40:29][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:12:26,930  [DEBUG] [D][11:40:29][M2M ]tcpclient close[4]
 
2025-07-16 21:12:26,933  [DEBUG] [D][11:40:29][SAL ]socket[4] has closed
 
2025-07-16 21:12:26,939  [DEBUG] [D][11:40:29][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:12:26,945  [DEBUG] [D][11:40:29][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 18
 
2025-07-16 21:12:26,948  [DEBUG] [D][11:40:29][COMM]Main Task receive event:86
 
2025-07-16 21:12:26,955  [DEBUG] [W][11:40:29][PROT]remove success[1730202029],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:12:26,966  [DEBUG] [W][11:40:29][PROT]add success [1730202029],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:12:26,973  [DEBUG] [D][11:40:29][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:12:26,975  [DEBUG] [D][11:40:29][PROT]index:0 1730202029
 
2025-07-16 21:12:26,977  [DEBUG] [D][11:40:29][PROT]is_send:0
 
2025-07-16 21:12:26,981  [DEBUG] [D][11:40:29][PROT]sequence_num:42
 
2025-07-16 21:12:26,983  [DEBUG] [D][11:40:29][PROT]retry_timeout:0
 
2025-07-16 21:12:26,986  [DEBUG] [D][11:40:29][PROT]retry_times:1
 
2025-07-16 21:12:26,989  [DEBUG] [D][11:40:29][PROT]send_path:0x2
 
2025-07-16 21:12:26,994  [DEBUG] [D][11:40:29][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:12:27,000  [DEBUG] [D][11:40:29][PROT]===========================================================
 
2025-07-16 21:12:27,009  [DEBUG] [W][11:40:29][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202029]
 
2025-07-16 21:12:27,015  [DEBUG] [D][11:40:29][PROT]===========================================================
 
2025-07-16 21:12:27,020  [DEBUG] [D][11:40:29][PROT]sending traceid [999999999990001B]
 
2025-07-16 21:12:27,022  [DEBUG] [D][11:40:29][PROT]Send_TO_M2M [1730202029]
 
2025-07-16 21:12:27,028  [DEBUG] [D][11:40:29][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:12:27,033  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:12:27,036  [DEBUG] [D][11:40:29][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:12:27,045  [DEBUG] [D][11:40:29][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:12:27,048  [DEBUG] [D][11:40:29][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:12:27,057  [DEBUG] [D][11:40:29][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:12:27,062  [DEBUG] [D][11:40:29][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:12:27,064  [DEBUG] [D][11:40:29][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:12:27,070  [DEBUG] [D][11:40:29][CAT1]at ops open socket[0]
 
2025-07-16 21:12:27,073  [DEBUG] [D][11:40:29][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:12:27,073  [DEBUG] 
 
2025-07-16 21:12:27,078  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:12:27,081  [DEBUG] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,082  [DEBUG] +CGATT: 1
 
2025-07-16 21:12:27,082  [DEBUG] 
 
2025-07-16 21:12:27,082  [DEBUG] OK
 
2025-07-16 21:12:27,082  [DEBUG] 
 
2025-07-16 21:12:27,086  [DEBUG] [D][11:40:29][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:12:27,087  [DEBUG] 
 
2025-07-16 21:12:27,087  [DEBUG] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,089  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:12:27,089  [DEBUG] 
 
2025-07-16 21:12:27,090  [DEBUG] OK
 
2025-07-16 21:12:27,090  [DEBUG] 
 
2025-07-16 21:12:27,092  [DEBUG] [D][11:40:29][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:12:27,094  [DEBUG] 
 
2025-07-16 21:12:27,095  [DEBUG] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,098  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:12:27,098  [DEBUG] 
 
2025-07-16 21:12:27,098  [DEBUG] OK
 
2025-07-16 21:12:27,100  [DEBUG] 
 
2025-07-16 21:12:27,107  [DEBUG] [D][11:40:29][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:12:27,107  [DEBUG] 
 
2025-07-16 21:12:27,108  [DEBUG] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,109  [DEBUG] OK
 
2025-07-16 21:12:27,109  [DEBUG] 
 
2025-07-16 21:12:27,113  [DEBUG] [D][11:40:29][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:12:27,221  [DEBUG] [D][11:40:29][CAT1]opened : 0, 0
 
2025-07-16 21:12:27,224  [DEBUG] [D][11:40:29][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:12:27,230  [DEBUG] [D][11:40:29][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:12:27,235  [DEBUG] [D][11:40:29][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:12:27,241  [DEBUG] [D][11:40:29][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:12:27,246  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:12:27,249  [DEBUG] [D][11:40:29][SAL ]sock send credit cnt[6]
 
2025-07-16 21:12:27,252  [DEBUG] [D][11:40:29][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:12:27,258  [DEBUG] [D][11:40:29][M2M ]m2m send data len[70]
 
2025-07-16 21:12:27,260  [DEBUG] [D][11:40:29][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:12:27,270  [DEBUG] [D][11:40:29][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:12:27,274  [DEBUG] [D][11:40:29][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:12:27,277  [DEBUG] [D][11:40:29][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:12:27,277  [DEBUG] 
 
2025-07-16 21:12:27,282  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:12:27,288  [DEBUG] [D][11:40:29][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:12:27,294  [DEBUG] 0023B9DA113311331133113311331B884D5367E36637DC2F86FF5738DC94525F182801
 
2025-07-16 21:12:27,296  [DEBUG] [D][11:40:29][CAT1]<<< 
 
2025-07-16 21:12:27,297  [DEBUG] SEND OK
 
2025-07-16 21:12:27,297  [DEBUG] 
 
2025-07-16 21:12:27,302  [DEBUG] [D][11:40:29][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:12:27,305  [DEBUG] [D][11:40:29][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:12:27,305  [DEBUG] 
 
2025-07-16 21:12:27,310  [DEBUG] [D][11:40:29][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:12:27,316  [DEBUG] [D][11:40:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:12:27,318  [DEBUG] [D][11:40:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:12:27,324  [DEBUG] [D][11:40:29][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:12:27,327  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:12:27,333  [DEBUG] [D][11:40:29][PROT]M2M Send ok [1730202029]
 
2025-07-16 21:12:27,336  [DEBUG] [D][11:40:29][PROT]CLEAN:0
 
2025-07-16 21:12:27,339  [DEBUG] [D][11:40:29][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:12:27,344  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:12:27,350  [DEBUG] [D][11:40:29][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:12:27,352  [DEBUG] [D][11:40:29][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:12:27,359  [DEBUG] [D][11:40:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:12:34,449  [DEBUG] [D][11:40:36][COMM]IMU: 839745 MEMS ERROR when cali 0
 
2025-07-16 21:13:07,829  [DEBUG] [D][11:41:10][CAT1]closed : 0
 
2025-07-16 21:13:07,835  [DEBUG] [D][11:41:10][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:13:07,837  [DEBUG] [D][11:41:10][SAL ]socket closed id[0]
 
2025-07-16 21:13:07,842  [DEBUG] [D][11:41:10][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:13:07,847  [DEBUG] [D][11:41:10][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:13:07,850  [DEBUG] [D][11:41:10][M2M ]m2m select fd[4]
 
2025-07-16 21:13:07,856  [DEBUG] [D][11:41:10][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:13:07,859  [DEBUG] [D][11:41:10][M2M ]tcpclient close[4]
 
2025-07-16 21:13:07,862  [DEBUG] [D][11:41:10][SAL ]socket[4] has closed
 
2025-07-16 21:13:07,867  [DEBUG] [D][11:41:10][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:13:07,873  [DEBUG] [D][11:41:10][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 19
 
2025-07-16 21:13:07,875  [DEBUG] [D][11:41:10][COMM]Main Task receive event:86
 
2025-07-16 21:13:07,883  [DEBUG] [W][11:41:10][PROT]remove success[1730202070],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:13:07,895  [DEBUG] [W][11:41:10][PROT]add success [1730202070],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:13:07,900  [DEBUG] [D][11:41:10][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:13:07,903  [DEBUG] [D][11:41:10][PROT]index:0 1730202070
 
2025-07-16 21:13:07,906  [DEBUG] [D][11:41:10][PROT]is_send:0
 
2025-07-16 21:13:07,909  [DEBUG] [D][11:41:10][PROT]sequence_num:43
 
2025-07-16 21:13:07,910  [DEBUG] [D][11:41:10][PROT]retry_timeout:0
 
2025-07-16 21:13:07,914  [DEBUG] [D][11:41:10][PROT]retry_times:1
 
2025-07-16 21:13:07,917  [DEBUG] [D][11:41:10][PROT]send_path:0x2
 
2025-07-16 21:13:07,923  [DEBUG] [D][11:41:10][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:13:07,929  [DEBUG] [D][11:41:10][PROT]===========================================================
 
2025-07-16 21:13:07,938  [DEBUG] [D][11:41:10][HSDK][0] flush to flash addr:[0xE46F00] --- write len --- [256]
 
2025-07-16 21:13:07,942  [DEBUG] [W][11:41:10][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202070]
 
2025-07-16 21:13:07,951  [DEBUG] [D][11:41:10][PROT]===========================================================
 
2025-07-16 21:13:07,953  [DEBUG] [D][11:41:10][PROT]sending traceid [999999999990001C]
 
2025-07-16 21:13:07,958  [DEBUG] [D][11:41:10][PROT]Send_TO_M2M [1730202070]
 
2025-07-16 21:13:07,962  [DEBUG] [D][11:41:10][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:07,967  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:07,973  [DEBUG] [D][11:41:10][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:13:07,978  [DEBUG] [D][11:41:10][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:13:07,984  [DEBUG] [D][11:41:10][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:13:07,990  [DEBUG] [D][11:41:10][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:13:07,995  [DEBUG] [D][11:41:10][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:13:08,001  [DEBUG] [D][11:41:10][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:13:08,003  [DEBUG] [D][11:41:10][CAT1]at ops open socket[0]
 
2025-07-16 21:13:08,007  [DEBUG] [D][11:41:10][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:13:08,009  [DEBUG] 
 
2025-07-16 21:13:08,012  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:13:08,014  [DEBUG] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,017  [DEBUG] +CGATT: 1
 
2025-07-16 21:13:08,017  [DEBUG] 
 
2025-07-16 21:13:08,017  [DEBUG] OK
 
2025-07-16 21:13:08,018  [DEBUG] 
 
2025-07-16 21:13:08,020  [DEBUG] [D][11:41:10][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:13:08,021  [DEBUG] 
 
2025-07-16 21:13:08,023  [DEBUG] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,023  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:13:08,024  [DEBUG] 
 
2025-07-16 21:13:08,024  [DEBUG] OK
 
2025-07-16 21:13:08,025  [DEBUG] 
 
2025-07-16 21:13:08,029  [DEBUG] [D][11:41:10][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:13:08,029  [DEBUG] 
 
2025-07-16 21:13:08,031  [DEBUG] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,034  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:13:08,035  [DEBUG] 
 
2025-07-16 21:13:08,035  [DEBUG] OK
 
2025-07-16 21:13:08,035  [DEBUG] 
 
2025-07-16 21:13:08,043  [DEBUG] [D][11:41:10][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:13:08,044  [DEBUG] 
 
2025-07-16 21:13:08,045  [DEBUG] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,046  [DEBUG] OK
 
2025-07-16 21:13:08,047  [DEBUG] 
 
2025-07-16 21:13:08,049  [DEBUG] [D][11:41:10][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:13:08,112  [DEBUG] [D][11:41:10][CAT1]opened : 0, 0
 
2025-07-16 21:13:08,115  [DEBUG] [D][11:41:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:08,120  [DEBUG] [D][11:41:10][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:13:08,126  [DEBUG] [D][11:41:10][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:13:08,131  [DEBUG] [D][11:41:10][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:13:08,137  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:13:08,140  [DEBUG] [D][11:41:10][SAL ]sock send credit cnt[6]
 
2025-07-16 21:13:08,143  [DEBUG] [D][11:41:10][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:13:08,148  [DEBUG] [D][11:41:10][M2M ]m2m send data len[70]
 
2025-07-16 21:13:08,151  [DEBUG] [D][11:41:10][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:13:08,160  [DEBUG] [D][11:41:10][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:13:08,165  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:13:08,168  [DEBUG] [D][11:41:10][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:13:08,173  [DEBUG] [D][11:41:10][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:13:08,173  [DEBUG] 
 
2025-07-16 21:13:08,178  [DEBUG] [D][11:41:10][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:13:08,185  [DEBUG] 0023B9DF113311331133113311331B8840D741505F3D0BB0831ADDFF9F2D601BCA62E6
 
2025-07-16 21:13:08,187  [DEBUG] [D][11:41:10][CAT1]<<< 
 
2025-07-16 21:13:08,190  [DEBUG] SEND OK
 
2025-07-16 21:13:08,190  [DEBUG] 
 
2025-07-16 21:13:08,193  [DEBUG] [D][11:41:10][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:13:08,196  [DEBUG] [D][11:41:10][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:13:08,198  [DEBUG] 
 
2025-07-16 21:13:08,201  [DEBUG] [D][11:41:10][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:08,207  [DEBUG] [D][11:41:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:13:08,212  [DEBUG] [D][11:41:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:13:08,215  [DEBUG] [D][11:41:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:08,221  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:13:08,224  [DEBUG] [D][11:41:10][PROT]M2M Send ok [1730202070]
 
2025-07-16 21:13:08,227  [DEBUG] [D][11:41:10][PROT]CLEAN:0
 
2025-07-16 21:13:08,232  [DEBUG] [D][11:41:10][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:08,237  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:08,241  [DEBUG] [D][11:41:10][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:13:08,246  [DEBUG] [D][11:41:10][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:08,250  [DEBUG] [D][11:41:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:13:48,866  [DEBUG] [D][11:41:51][CAT1]closed : 0
 
2025-07-16 21:13:48,870  [DEBUG] [D][11:41:51][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:13:48,873  [DEBUG] [D][11:41:51][SAL ]socket closed id[0]
 
2025-07-16 21:13:48,879  [DEBUG] [D][11:41:51][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:13:48,885  [DEBUG] [D][11:41:51][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:13:48,887  [DEBUG] [D][11:41:51][M2M ]m2m select fd[4]
 
2025-07-16 21:13:48,892  [DEBUG] [D][11:41:51][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:13:48,895  [DEBUG] [D][11:41:51][M2M ]tcpclient close[4]
 
2025-07-16 21:13:48,898  [DEBUG] [D][11:41:51][SAL ]socket[4] has closed
 
2025-07-16 21:13:48,904  [DEBUG] [D][11:41:51][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:13:48,909  [DEBUG] [D][11:41:51][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 20
 
2025-07-16 21:13:48,977  [DEBUG] [D][11:41:51][COMM]Main Task receive event:86
 
2025-07-16 21:13:48,984  [DEBUG] [W][11:41:51][PROT]remove success[1730202111],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:13:48,992  [DEBUG] [W][11:41:51][PROT]add success [1730202111],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:13:48,998  [DEBUG] [D][11:41:51][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:13:49,001  [DEBUG] [D][11:41:51][PROT]index:0 1730202111
 
2025-07-16 21:13:49,004  [DEBUG] [D][11:41:51][PROT]is_send:0
 
2025-07-16 21:13:49,009  [DEBUG] [D][11:41:51][PROT]sequence_num:44
 
2025-07-16 21:13:49,012  [DEBUG] [D][11:41:51][PROT]retry_timeout:0
 
2025-07-16 21:13:49,015  [DEBUG] [D][11:41:51][PROT]retry_times:1
 
2025-07-16 21:13:49,018  [DEBUG] [D][11:41:51][PROT]send_path:0x2
 
2025-07-16 21:13:49,023  [DEBUG] [D][11:41:51][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:13:49,029  [DEBUG] [D][11:41:51][PROT]===========================================================
 
2025-07-16 21:13:49,037  [DEBUG] [W][11:41:51][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202111]
 
2025-07-16 21:13:49,044  [DEBUG] [D][11:41:51][PROT]===========================================================
 
2025-07-16 21:13:49,046  [DEBUG] [D][11:41:51][PROT]sending traceid [999999999990001D]
 
2025-07-16 21:13:49,051  [DEBUG] [D][11:41:51][PROT]Send_TO_M2M [1730202111]
 
2025-07-16 21:13:49,057  [DEBUG] [D][11:41:51][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:49,060  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:49,065  [DEBUG] [D][11:41:51][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:13:49,071  [DEBUG] [D][11:41:51][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:13:49,076  [DEBUG] [D][11:41:51][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:13:49,082  [DEBUG] [D][11:41:51][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:13:49,091  [DEBUG] [D][11:41:51][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:13:49,093  [DEBUG] [D][11:41:51][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:13:49,096  [DEBUG] [D][11:41:51][CAT1]at ops open socket[0]
 
2025-07-16 21:13:49,102  [DEBUG] [D][11:41:51][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:13:49,102  [DEBUG] 
 
2025-07-16 21:13:49,107  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:13:49,108  [DEBUG] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,109  [DEBUG] +CGATT: 1
 
2025-07-16 21:13:49,109  [DEBUG] 
 
2025-07-16 21:13:49,110  [DEBUG] OK
 
2025-07-16 21:13:49,110  [DEBUG] 
 
2025-07-16 21:13:49,112  [DEBUG] [D][11:41:51][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:13:49,113  [DEBUG] 
 
2025-07-16 21:13:49,115  [DEBUG] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,116  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:13:49,117  [DEBUG] 
 
2025-07-16 21:13:49,117  [DEBUG] OK
 
2025-07-16 21:13:49,118  [DEBUG] 
 
2025-07-16 21:13:49,120  [DEBUG] [D][11:41:51][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:13:49,121  [DEBUG] 
 
2025-07-16 21:13:49,123  [DEBUG] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,126  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:13:49,126  [DEBUG] 
 
2025-07-16 21:13:49,127  [DEBUG] OK
 
2025-07-16 21:13:49,129  [DEBUG] 
 
2025-07-16 21:13:49,136  [DEBUG] [D][11:41:51][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:13:49,137  [DEBUG] 
 
2025-07-16 21:13:49,137  [DEBUG] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,138  [DEBUG] OK
 
2025-07-16 21:13:49,138  [DEBUG] 
 
2025-07-16 21:13:49,142  [DEBUG] [D][11:41:51][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:13:49,217  [DEBUG] [D][11:41:51][CAT1]opened : 0, 0
 
2025-07-16 21:13:49,219  [DEBUG] [D][11:41:51][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:49,225  [DEBUG] [D][11:41:51][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:13:49,231  [DEBUG] [D][11:41:51][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:13:49,236  [DEBUG] [D][11:41:51][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:13:49,242  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:13:49,244  [DEBUG] [D][11:41:51][SAL ]sock send credit cnt[6]
 
2025-07-16 21:13:49,248  [DEBUG] [D][11:41:51][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:13:49,252  [DEBUG] [D][11:41:51][M2M ]m2m send data len[70]
 
2025-07-16 21:13:49,256  [DEBUG] [D][11:41:51][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:13:49,264  [DEBUG] [D][11:41:51][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:13:49,269  [DEBUG] [D][11:41:51][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:13:49,272  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:13:49,278  [DEBUG] [D][11:41:51][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:13:49,279  [DEBUG] 
 
2025-07-16 21:13:49,283  [DEBUG] [D][11:41:51][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:13:49,289  [DEBUG] 0023B9D0113311331133113311331B884F682EE6C640A6192331486DB1E8C73BC0A40C
 
2025-07-16 21:13:49,292  [DEBUG] [D][11:41:51][CAT1]<<< 
 
2025-07-16 21:13:49,293  [DEBUG] SEND OK
 
2025-07-16 21:13:49,293  [DEBUG] 
 
2025-07-16 21:13:49,297  [DEBUG] [D][11:41:51][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:13:49,300  [DEBUG] [D][11:41:51][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:13:49,300  [DEBUG] 
 
2025-07-16 21:13:49,306  [DEBUG] [D][11:41:51][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:13:49,312  [DEBUG] [D][11:41:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:13:49,314  [DEBUG] [D][11:41:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:13:49,319  [DEBUG] [D][11:41:51][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:49,323  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:13:49,328  [DEBUG] [D][11:41:51][PROT]M2M Send ok [1730202111]
 
2025-07-16 21:13:49,330  [DEBUG] [D][11:41:51][PROT]CLEAN:0
 
2025-07-16 21:13:49,334  [DEBUG] [D][11:41:51][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:13:49,339  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:13:49,345  [DEBUG] [D][11:41:51][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:13:49,347  [DEBUG] [D][11:41:51][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:13:49,354  [DEBUG] [D][11:41:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:14:29,752  [DEBUG] [D][11:42:32][CAT1]closed : 0
 
2025-07-16 21:14:29,757  [DEBUG] [D][11:42:32][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:14:29,759  [DEBUG] [D][11:42:32][SAL ]socket closed id[0]
 
2025-07-16 21:14:29,766  [DEBUG] [D][11:42:32][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:14:29,771  [DEBUG] [D][11:42:32][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:14:29,774  [DEBUG] [D][11:42:32][M2M ]m2m select fd[4]
 
2025-07-16 21:14:29,779  [DEBUG] [D][11:42:32][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:14:29,782  [DEBUG] [D][11:42:32][M2M ]tcpclient close[4]
 
2025-07-16 21:14:29,785  [DEBUG] [D][11:42:32][SAL ]socket[4] has closed
 
2025-07-16 21:14:29,791  [DEBUG] [D][11:42:32][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:14:29,797  [DEBUG] [D][11:42:32][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 21
 
2025-07-16 21:14:29,810  [DEBUG] [D][11:42:32][COMM]Main Task receive event:86
 
2025-07-16 21:14:29,819  [DEBUG] [W][11:42:32][PROT]remove success[1730202152],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:14:29,827  [DEBUG] [W][11:42:32][PROT]add success [1730202152],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:14:29,833  [DEBUG] [D][11:42:32][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:14:29,836  [DEBUG] [D][11:42:32][PROT]index:0 1730202152
 
2025-07-16 21:14:29,839  [DEBUG] [D][11:42:32][PROT]is_send:0
 
2025-07-16 21:14:29,844  [DEBUG] [D][11:42:32][PROT]sequence_num:45
 
2025-07-16 21:14:29,847  [DEBUG] [D][11:42:32][PROT]retry_timeout:0
 
2025-07-16 21:14:29,850  [DEBUG] [D][11:42:32][PROT]retry_times:1
 
2025-07-16 21:14:29,853  [DEBUG] [D][11:42:32][PROT]send_path:0x2
 
2025-07-16 21:14:29,857  [DEBUG] [D][11:42:32][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:14:29,864  [DEBUG] [D][11:42:32][PROT]===========================================================
 
2025-07-16 21:14:29,870  [DEBUG] [W][11:42:32][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202152]
 
2025-07-16 21:14:29,878  [DEBUG] [D][11:42:32][PROT]===========================================================
 
2025-07-16 21:14:29,881  [DEBUG] [D][11:42:32][PROT]sending traceid [999999999990001E]
 
2025-07-16 21:14:29,886  [DEBUG] [D][11:42:32][PROT]Send_TO_M2M [1730202152]
 
2025-07-16 21:14:29,891  [DEBUG] [D][11:42:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:14:29,894  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:14:29,899  [DEBUG] [D][11:42:32][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:14:29,906  [DEBUG] [D][11:42:32][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:14:29,911  [DEBUG] [D][11:42:32][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:14:29,917  [DEBUG] [D][11:42:32][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:14:29,926  [DEBUG] [D][11:42:32][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:14:29,928  [DEBUG] [D][11:42:32][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:14:29,931  [DEBUG] [D][11:42:32][CAT1]at ops open socket[0]
 
2025-07-16 21:14:29,936  [DEBUG] [D][11:42:32][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:14:29,936  [DEBUG] 
 
2025-07-16 21:14:29,941  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:14:29,942  [DEBUG] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,944  [DEBUG] +CGATT: 1
 
2025-07-16 21:14:29,944  [DEBUG] 
 
2025-07-16 21:14:29,944  [DEBUG] OK
 
2025-07-16 21:14:29,945  [DEBUG] 
 
2025-07-16 21:14:29,948  [DEBUG] [D][11:42:32][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:14:29,948  [DEBUG] 
 
2025-07-16 21:14:29,950  [DEBUG] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,951  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:14:29,951  [DEBUG] 
 
2025-07-16 21:14:29,952  [DEBUG] OK
 
2025-07-16 21:14:29,953  [DEBUG] 
 
2025-07-16 21:14:29,956  [DEBUG] [D][11:42:32][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:14:29,956  [DEBUG] 
 
2025-07-16 21:14:29,958  [DEBUG] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,961  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:14:29,961  [DEBUG] 
 
2025-07-16 21:14:29,962  [DEBUG] OK
 
2025-07-16 21:14:29,963  [DEBUG] 
 
2025-07-16 21:14:29,970  [DEBUG] [D][11:42:32][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:14:29,970  [DEBUG] 
 
2025-07-16 21:14:29,972  [DEBUG] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:29,972  [DEBUG] OK
 
2025-07-16 21:14:29,972  [DEBUG] 
 
2025-07-16 21:14:29,977  [DEBUG] [D][11:42:32][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:14:30,044  [DEBUG] [D][11:42:32][CAT1]opened : 0, 0
 
2025-07-16 21:14:30,047  [DEBUG] [D][11:42:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:14:30,053  [DEBUG] [D][11:42:32][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:14:30,058  [DEBUG] [D][11:42:32][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:14:30,064  [DEBUG] [D][11:42:32][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:14:30,070  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:14:30,072  [DEBUG] [D][11:42:32][SAL ]sock send credit cnt[6]
 
2025-07-16 21:14:30,075  [DEBUG] [D][11:42:32][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:14:30,080  [DEBUG] [D][11:42:32][M2M ]m2m send data len[70]
 
2025-07-16 21:14:30,083  [DEBUG] [D][11:42:32][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:14:30,092  [DEBUG] [D][11:42:32][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:14:30,097  [DEBUG] [D][11:42:32][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:14:30,100  [DEBUG] [D][11:42:32][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:14:30,100  [DEBUG] 
 
2025-07-16 21:14:30,106  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:14:30,111  [DEBUG] [D][11:42:32][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:14:30,117  [DEBUG] 0023B9DD113311331133113311331B884AB0493A00D6EB7C0D641AB04A925367576D89
 
2025-07-16 21:14:30,119  [DEBUG] [D][11:42:32][CAT1]<<< 
 
2025-07-16 21:14:30,119  [DEBUG] SEND OK
 
2025-07-16 21:14:30,120  [DEBUG] 
 
2025-07-16 21:14:30,126  [DEBUG] [D][11:42:32][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:14:30,127  [DEBUG] [D][11:42:32][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:14:30,127  [DEBUG] 
 
2025-07-16 21:14:30,133  [DEBUG] [D][11:42:32][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:14:30,139  [DEBUG] [D][11:42:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:14:30,141  [DEBUG] [D][11:42:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:14:30,148  [DEBUG] [D][11:42:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:14:30,151  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:14:30,156  [DEBUG] [D][11:42:32][PROT]M2M Send ok [1730202152]
 
2025-07-16 21:14:30,159  [DEBUG] [D][11:42:32][PROT]CLEAN:0
 
2025-07-16 21:14:30,162  [DEBUG] [D][11:42:32][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:14:30,167  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:14:30,172  [DEBUG] [D][11:42:32][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:14:30,175  [DEBUG] [D][11:42:32][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:14:30,181  [DEBUG] [D][11:42:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:10,701  [DEBUG] [D][11:43:13][CAT1]closed : 0
 
2025-07-16 21:15:10,706  [DEBUG] [D][11:43:13][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:15:10,708  [DEBUG] [D][11:43:13][SAL ]socket closed id[0]
 
2025-07-16 21:15:10,714  [DEBUG] [D][11:43:13][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:15:10,720  [DEBUG] [D][11:43:13][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:15:10,722  [DEBUG] [D][11:43:13][M2M ]m2m select fd[4]
 
2025-07-16 21:15:10,727  [DEBUG] [D][11:43:13][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:15:10,730  [DEBUG] [D][11:43:13][M2M ]tcpclient close[4]
 
2025-07-16 21:15:10,733  [DEBUG] [D][11:43:13][SAL ]socket[4] has closed
 
2025-07-16 21:15:10,739  [DEBUG] [D][11:43:13][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:15:10,746  [DEBUG] [D][11:43:13][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 22
 
2025-07-16 21:15:10,747  [DEBUG] [D][11:43:13][COMM]Main Task receive event:86
 
2025-07-16 21:15:10,756  [DEBUG] [W][11:43:13][PROT]remove success[1730202193],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:15:10,765  [DEBUG] [D][11:43:13][HSDK][0] flush to flash addr:[0xE47000] --- write len --- [256]
 
2025-07-16 21:15:10,767  [DEBUG] [D][11:43:13][PROT]index:0 1730202193
 
2025-07-16 21:15:10,769  [DEBUG] [D][11:43:13][PROT]is_send:0
 
2025-07-16 21:15:10,772  [DEBUG] [D][11:43:13][PROT]sequence_num:46
 
2025-07-16 21:15:10,776  [DEBUG] [D][11:43:13][PROT]retry_timeout:0
 
2025-07-16 21:15:10,778  [DEBUG] [D][11:43:13][PROT]retry_times:1
 
2025-07-16 21:15:10,781  [DEBUG] [D][11:43:13][PROT]send_path:0x2
 
2025-07-16 21:15:10,786  [DEBUG] [D][11:43:13][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:15:10,796  [DEBUG] [D][11:43:13][PROT]===========================================================
 
2025-07-16 21:15:10,800  [DEBUG] [D][11:43:13][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:10,803  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:10,809  [DEBUG] [D][11:43:13][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:15:10,815  [DEBUG] [D][11:43:13][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:15:10,822  [DEBUG] [D][11:43:13][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:15:10,827  [DEBUG] [D][11:43:13][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:15:10,835  [DEBUG] [D][11:43:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:15:10,840  [DEBUG] [W][11:43:13][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202193]
 
2025-07-16 21:15:10,845  [DEBUG] [D][11:43:13][PROT]===========================================================
 
2025-07-16 21:15:10,850  [DEBUG] [D][11:43:13][PROT]sending traceid [999999999990001F]
 
2025-07-16 21:15:10,853  [DEBUG] [D][11:43:13][PROT]Send_TO_M2M [1730202193]
 
2025-07-16 21:15:10,859  [DEBUG] [D][11:43:13][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:15:10,862  [DEBUG] [D][11:43:13][CAT1]at ops open socket[0]
 
2025-07-16 21:15:10,870  [DEBUG] [W][11:43:13][PROT]add success [1730202193],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:15:10,876  [DEBUG] [D][11:43:13][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:15:10,878  [DEBUG] [D][11:43:13][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:15:10,880  [DEBUG] 
 
2025-07-16 21:15:10,885  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:15:10,886  [DEBUG] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,888  [DEBUG] +CGATT: 1
 
2025-07-16 21:15:10,889  [DEBUG] 
 
2025-07-16 21:15:10,889  [DEBUG] OK
 
2025-07-16 21:15:10,889  [DEBUG] 
 
2025-07-16 21:15:10,892  [DEBUG] [D][11:43:13][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:15:10,892  [DEBUG] 
 
2025-07-16 21:15:10,894  [DEBUG] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,896  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:15:10,897  [DEBUG] 
 
2025-07-16 21:15:10,898  [DEBUG] OK
 
2025-07-16 21:15:10,899  [DEBUG] 
 
2025-07-16 21:15:10,901  [DEBUG] [D][11:43:13][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:15:10,902  [DEBUG] 
 
2025-07-16 21:15:10,904  [DEBUG] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,906  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:15:10,907  [DEBUG] 
 
2025-07-16 21:15:10,908  [DEBUG] OK
 
2025-07-16 21:15:10,909  [DEBUG] 
 
2025-07-16 21:15:10,915  [DEBUG] [D][11:43:13][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:15:10,915  [DEBUG] 
 
2025-07-16 21:15:10,917  [DEBUG] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:10,918  [DEBUG] OK
 
2025-07-16 21:15:10,918  [DEBUG] 
 
2025-07-16 21:15:10,922  [DEBUG] [D][11:43:13][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:15:10,971  [DEBUG] [D][11:43:13][CAT1]opened : 0, 0
 
2025-07-16 21:15:10,973  [DEBUG] [D][11:43:13][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:10,979  [DEBUG] [D][11:43:13][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:15:10,984  [DEBUG] [D][11:43:13][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:15:10,990  [DEBUG] [D][11:43:13][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:15:10,996  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:15:10,998  [DEBUG] [D][11:43:13][SAL ]sock send credit cnt[6]
 
2025-07-16 21:15:11,001  [DEBUG] [D][11:43:13][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:15:11,007  [DEBUG] [D][11:43:13][M2M ]m2m send data len[70]
 
2025-07-16 21:15:11,009  [DEBUG] [D][11:43:13][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:15:11,018  [DEBUG] [D][11:43:13][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:15:11,023  [DEBUG] [D][11:43:13][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:15:11,026  [DEBUG] [D][11:43:13][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:15:11,026  [DEBUG] 
 
2025-07-16 21:15:11,032  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:15:11,037  [DEBUG] [D][11:43:13][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:15:11,043  [DEBUG] 0023B9DC113311331133113311331B88485570E4CD69CA006BFF6365E0C9861E2CE57D
 
2025-07-16 21:15:11,046  [DEBUG] [D][11:43:13][CAT1]<<< 
 
2025-07-16 21:15:11,046  [DEBUG] SEND OK
 
2025-07-16 21:15:11,046  [DEBUG] 
 
2025-07-16 21:15:11,051  [DEBUG] [D][11:43:13][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:15:11,054  [DEBUG] [D][11:43:13][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:15:11,054  [DEBUG] 
 
2025-07-16 21:15:11,060  [DEBUG] [D][11:43:13][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:11,065  [DEBUG] [D][11:43:13][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:15:11,067  [DEBUG] [D][11:43:13][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:15:11,074  [DEBUG] [D][11:43:13][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:11,076  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:11,083  [DEBUG] [D][11:43:13][PROT]M2M Send ok [1730202193]
 
2025-07-16 21:15:11,084  [DEBUG] [D][11:43:13][PROT]CLEAN:0
 
2025-07-16 21:15:11,088  [DEBUG] [D][11:43:13][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:11,094  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:11,099  [DEBUG] [D][11:43:13][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:15:11,102  [DEBUG] [D][11:43:13][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:11,107  [DEBUG] [D][11:43:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:48,511  [DEBUG] [D][11:43:50][COMM]IMU: 1033828 MEMS ERROR when cali 0
 
2025-07-16 21:15:51,653  [DEBUG] [D][11:43:53][CAT1]closed : 0
 
2025-07-16 21:15:51,658  [DEBUG] [D][11:43:53][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:15:51,661  [DEBUG] [D][11:43:53][SAL ]socket closed id[0]
 
2025-07-16 21:15:51,667  [DEBUG] [D][11:43:53][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:15:51,671  [DEBUG] [D][11:43:53][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:15:51,674  [DEBUG] [D][11:43:53][M2M ]m2m select fd[4]
 
2025-07-16 21:15:51,680  [DEBUG] [D][11:43:53][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:15:51,682  [DEBUG] [D][11:43:53][M2M ]tcpclient close[4]
 
2025-07-16 21:15:51,685  [DEBUG] [D][11:43:53][SAL ]socket[4] has closed
 
2025-07-16 21:15:51,691  [DEBUG] [D][11:43:53][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:15:51,697  [DEBUG] [D][11:43:53][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 23
 
2025-07-16 21:15:51,700  [DEBUG] [D][11:43:53][COMM]Main Task receive event:86
 
2025-07-16 21:15:51,707  [DEBUG] [W][11:43:53][PROT]remove success[1730202233],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:15:51,713  [DEBUG] [D][11:43:53][PROT]index:0 1730202233
 
2025-07-16 21:15:51,716  [DEBUG] [D][11:43:53][PROT]is_send:0
 
2025-07-16 21:15:51,719  [DEBUG] [D][11:43:53][PROT]sequence_num:47
 
2025-07-16 21:15:51,721  [DEBUG] [D][11:43:53][PROT]retry_timeout:0
 
2025-07-16 21:15:51,725  [DEBUG] [D][11:43:53][PROT]retry_times:1
 
2025-07-16 21:15:51,727  [DEBUG] [D][11:43:53][PROT]send_path:0x2
 
2025-07-16 21:15:51,732  [DEBUG] [D][11:43:53][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:15:51,739  [DEBUG] [D][11:43:53][PROT]===========================================================
 
2025-07-16 21:15:51,744  [DEBUG] [W][11:43:53][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202233]
 
2025-07-16 21:15:51,752  [DEBUG] [D][11:43:53][PROT]===========================================================
 
2025-07-16 21:15:51,758  [DEBUG] [D][11:43:53][PROT]sending traceid [9999999999900020]
 
2025-07-16 21:15:51,760  [DEBUG] [D][11:43:53][PROT]Send_TO_M2M [1730202233]
 
2025-07-16 21:15:51,766  [DEBUG] [D][11:43:53][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:51,772  [DEBUG] [D][11:43:53][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:51,775  [DEBUG] [D][11:43:53][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:15:51,783  [DEBUG] [D][11:43:53][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:15:51,785  [DEBUG] [D][11:43:53][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:15:51,794  [DEBUG] [D][11:43:53][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:15:51,800  [DEBUG] [D][11:43:54][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:15:51,803  [DEBUG] [D][11:43:54][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:15:51,805  [DEBUG] [D][11:43:54][CAT1]at ops open socket[0]
 
2025-07-16 21:15:51,816  [DEBUG] [W][11:43:53][PROT]add success [1730202233],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:15:51,822  [DEBUG] [D][11:43:54][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:15:51,825  [DEBUG] [D][11:43:54][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:15:51,825  [DEBUG] 
 
2025-07-16 21:15:51,831  [DEBUG] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:15:51,833  [DEBUG] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,834  [DEBUG] +CGATT: 1
 
2025-07-16 21:15:51,834  [DEBUG] 
 
2025-07-16 21:15:51,834  [DEBUG] OK
 
2025-07-16 21:15:51,834  [DEBUG] 
 
2025-07-16 21:15:51,839  [DEBUG] [D][11:43:54][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:15:51,839  [DEBUG] 
 
2025-07-16 21:15:51,840  [DEBUG] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,841  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:15:51,841  [DEBUG] 
 
2025-07-16 21:15:51,842  [DEBUG] OK
 
2025-07-16 21:15:51,842  [DEBUG] 
 
2025-07-16 21:15:51,844  [DEBUG] [D][11:43:54][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:15:51,846  [DEBUG] 
 
2025-07-16 21:15:51,846  [DEBUG] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,849  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:15:51,849  [DEBUG] 
 
2025-07-16 21:15:51,851  [DEBUG] OK
 
2025-07-16 21:15:51,852  [DEBUG] 
 
2025-07-16 21:15:51,860  [DEBUG] [D][11:43:54][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:15:51,861  [DEBUG] 
 
2025-07-16 21:15:51,861  [DEBUG] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,862  [DEBUG] OK
 
2025-07-16 21:15:51,862  [DEBUG] 
 
2025-07-16 21:15:51,866  [DEBUG] [D][11:43:54][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:15:51,914  [DEBUG] [D][11:43:54][CAT1]opened : 0, 0
 
2025-07-16 21:15:51,917  [DEBUG] [D][11:43:54][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:51,923  [DEBUG] [D][11:43:54][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:15:51,927  [DEBUG] [D][11:43:54][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:15:51,932  [DEBUG] [D][11:43:54][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:15:51,938  [DEBUG] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:15:51,941  [DEBUG] [D][11:43:54][SAL ]sock send credit cnt[6]
 
2025-07-16 21:15:51,944  [DEBUG] [D][11:43:54][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:15:51,949  [DEBUG] [D][11:43:54][M2M ]m2m send data len[70]
 
2025-07-16 21:15:51,952  [DEBUG] [D][11:43:54][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:15:51,961  [DEBUG] [D][11:43:54][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:15:51,966  [DEBUG] [D][11:43:54][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:15:51,969  [DEBUG] [D][11:43:54][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:15:51,969  [DEBUG] 
 
2025-07-16 21:15:51,975  [DEBUG] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:15:51,980  [DEBUG] [D][11:43:54][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:15:51,986  [DEBUG] 0023B9D1113311331133113311331B887BFC7772C024EA25C5D92CC180F405746DD570
 
2025-07-16 21:15:51,988  [DEBUG] [D][11:43:54][CAT1]<<< 
 
2025-07-16 21:15:51,989  [DEBUG] SEND OK
 
2025-07-16 21:15:51,989  [DEBUG] 
 
2025-07-16 21:15:51,994  [DEBUG] [D][11:43:54][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:15:51,997  [DEBUG] [D][11:43:54][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:15:51,997  [DEBUG] 
 
2025-07-16 21:15:52,002  [DEBUG] [D][11:43:54][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:15:52,008  [DEBUG] [D][11:43:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:15:52,010  [DEBUG] [D][11:43:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:15:52,016  [DEBUG] [D][11:43:54][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:52,019  [DEBUG] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:15:52,025  [DEBUG] [D][11:43:54][PROT]M2M Send ok [1730202234]
 
2025-07-16 21:15:52,027  [DEBUG] [D][11:43:54][PROT]CLEAN:0
 
2025-07-16 21:15:52,030  [DEBUG] [D][11:43:54][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:15:52,036  [DEBUG] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:15:52,041  [DEBUG] [D][11:43:54][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:15:52,044  [DEBUG] [D][11:43:54][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:15:52,049  [DEBUG] [D][11:43:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:16:32,620  [DEBUG] [D][11:44:34][CAT1]closed : 0
 
2025-07-16 21:16:32,625  [DEBUG] [D][11:44:34][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:16:32,629  [DEBUG] [D][11:44:34][SAL ]socket closed id[0]
 
2025-07-16 21:16:32,634  [DEBUG] [D][11:44:34][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:16:32,641  [DEBUG] [D][11:44:34][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:16:32,642  [DEBUG] [D][11:44:34][M2M ]m2m select fd[4]
 
2025-07-16 21:16:32,647  [DEBUG] [D][11:44:34][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:16:32,650  [DEBUG] [D][11:44:34][M2M ]tcpclient close[4]
 
2025-07-16 21:16:32,652  [DEBUG] [D][11:44:34][SAL ]socket[4] has closed
 
2025-07-16 21:16:32,659  [DEBUG] [D][11:44:34][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:16:32,664  [DEBUG] [D][11:44:34][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 24
 
2025-07-16 21:16:32,704  [DEBUG] [D][11:44:35][COMM]Main Task receive event:86
 
2025-07-16 21:16:32,713  [DEBUG] [W][11:44:35][PROT]remove success[1730202275],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:16:32,715  [DEBUG] [D][11:44:35][PROT]index:0 1730202275
 
2025-07-16 21:16:32,717  [DEBUG] [D][11:44:35][PROT]is_send:0
 
2025-07-16 21:16:32,720  [DEBUG] [D][11:44:35][PROT]sequence_num:48
 
2025-07-16 21:16:32,723  [DEBUG] [D][11:44:35][PROT]retry_timeout:0
 
2025-07-16 21:16:32,730  [DEBUG] [D][11:44:35][PROT]retry_times:1
 
2025-07-16 21:16:32,732  [DEBUG] [D][11:44:35][PROT]send_path:0x2
 
2025-07-16 21:16:32,735  [DEBUG] [D][11:44:35][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:16:32,743  [DEBUG] [D][11:44:35][PROT]===========================================================
 
2025-07-16 21:16:32,749  [DEBUG] [W][11:44:35][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202275]
 
2025-07-16 21:16:32,755  [DEBUG] [D][11:44:35][PROT]===========================================================
 
2025-07-16 21:16:32,760  [DEBUG] [D][11:44:35][PROT]sending traceid [9999999999900021]
 
2025-07-16 21:16:32,767  [DEBUG] [D][11:44:35][PROT]Send_TO_M2M [1730202275]
 
2025-07-16 21:16:32,768  [DEBUG] [D][11:44:35][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:16:32,774  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:16:32,779  [DEBUG] [D][11:44:35][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:16:32,785  [DEBUG] [D][11:44:35][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:16:32,791  [DEBUG] [D][11:44:35][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:16:32,796  [DEBUG] [D][11:44:35][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:16:32,802  [DEBUG] [D][11:44:35][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:16:32,807  [DEBUG] [D][11:44:35][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:16:32,810  [DEBUG] [D][11:44:35][CAT1]at ops open socket[0]
 
2025-07-16 21:16:32,813  [DEBUG] [D][11:44:35][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:16:32,815  [DEBUG] 
 
2025-07-16 21:16:32,824  [DEBUG] [W][11:44:35][PROT]add success [1730202275],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:16:32,830  [DEBUG] [D][11:44:35][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:16:32,835  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:16:32,835  [DEBUG] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,836  [DEBUG] +CGATT: 1
 
2025-07-16 21:16:32,838  [DEBUG] 
 
2025-07-16 21:16:32,838  [DEBUG] OK
 
2025-07-16 21:16:32,839  [DEBUG] 
 
2025-07-16 21:16:32,840  [DEBUG] [D][11:44:35][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:16:32,841  [DEBUG] 
 
2025-07-16 21:16:32,843  [DEBUG] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,844  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:16:32,845  [DEBUG] 
 
2025-07-16 21:16:32,846  [DEBUG] OK
 
2025-07-16 21:16:32,847  [DEBUG] 
 
2025-07-16 21:16:32,849  [DEBUG] [D][11:44:35][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:16:32,850  [DEBUG] 
 
2025-07-16 21:16:32,851  [DEBUG] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,854  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:16:32,855  [DEBUG] 
 
2025-07-16 21:16:32,855  [DEBUG] OK
 
2025-07-16 21:16:32,855  [DEBUG] 
 
2025-07-16 21:16:32,862  [DEBUG] [D][11:44:35][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:16:32,863  [DEBUG] 
 
2025-07-16 21:16:32,864  [DEBUG] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:32,865  [DEBUG] OK
 
2025-07-16 21:16:32,865  [DEBUG] 
 
2025-07-16 21:16:32,869  [DEBUG] [D][11:44:35][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:16:32,931  [DEBUG] [D][11:44:35][CAT1]opened : 0, 0
 
2025-07-16 21:16:32,934  [DEBUG] [D][11:44:35][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:16:32,940  [DEBUG] [D][11:44:35][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:16:32,945  [DEBUG] [D][11:44:35][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:16:32,951  [DEBUG] [D][11:44:35][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:16:32,956  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:16:32,959  [DEBUG] [D][11:44:35][SAL ]sock send credit cnt[6]
 
2025-07-16 21:16:32,962  [DEBUG] [D][11:44:35][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:16:32,968  [DEBUG] [D][11:44:35][M2M ]m2m send data len[70]
 
2025-07-16 21:16:32,970  [DEBUG] [D][11:44:35][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:16:32,980  [DEBUG] [D][11:44:35][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:16:32,984  [DEBUG] [D][11:44:35][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:16:32,987  [DEBUG] [D][11:44:35][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:16:32,988  [DEBUG] 
 
2025-07-16 21:16:32,994  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:16:32,998  [DEBUG] [D][11:44:35][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:16:33,005  [DEBUG] 0023B9D2113311331133113311331B8874F0AF8392C29442FF78461E21A6DCB6F2D770
 
2025-07-16 21:16:33,007  [DEBUG] [D][11:44:35][CAT1]<<< 
 
2025-07-16 21:16:33,007  [DEBUG] SEND OK
 
2025-07-16 21:16:33,008  [DEBUG] 
 
2025-07-16 21:16:33,013  [DEBUG] [D][11:44:35][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:16:33,015  [DEBUG] [D][11:44:35][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:16:33,015  [DEBUG] 
 
2025-07-16 21:16:33,020  [DEBUG] [D][11:44:35][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:16:33,027  [DEBUG] [D][11:44:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:16:33,029  [DEBUG] [D][11:44:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:16:33,034  [DEBUG] [D][11:44:35][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:16:33,037  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:16:33,042  [DEBUG] [D][11:44:35][PROT]M2M Send ok [1730202275]
 
2025-07-16 21:16:33,046  [DEBUG] [D][11:44:35][PROT]CLEAN:0
 
2025-07-16 21:16:33,049  [DEBUG] [D][11:44:35][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:16:33,054  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:16:33,060  [DEBUG] [D][11:44:35][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:16:33,062  [DEBUG] [D][11:44:35][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:16:33,068  [DEBUG] [D][11:44:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:17:13,658  [DEBUG] [D][11:45:15][CAT1]closed : 0
 
2025-07-16 21:17:13,663  [DEBUG] [D][11:45:15][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:17:13,666  [DEBUG] [D][11:45:15][SAL ]socket closed id[0]
 
2025-07-16 21:17:13,671  [DEBUG] [D][11:45:15][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:17:13,677  [DEBUG] [D][11:45:15][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:17:13,680  [DEBUG] [D][11:45:15][M2M ]m2m select fd[4]
 
2025-07-16 21:17:13,686  [DEBUG] [D][11:45:15][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:17:13,688  [DEBUG] [D][11:45:15][M2M ]tcpclient close[4]
 
2025-07-16 21:17:13,691  [DEBUG] [D][11:45:15][SAL ]socket[4] has closed
 
2025-07-16 21:17:13,697  [DEBUG] [D][11:45:15][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:17:13,702  [DEBUG] [D][11:45:15][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 25
 
2025-07-16 21:17:13,720  [DEBUG] [D][11:45:16][COMM]Main Task receive event:86
 
2025-07-16 21:17:13,728  [DEBUG] [W][11:45:16][PROT]remove success[1730202316],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:17:13,737  [DEBUG] [D][11:45:16][HSDK][0] flush to flash addr:[0xE47100] --- write len --- [256]
 
2025-07-16 21:17:13,739  [DEBUG] [D][11:45:16][PROT]index:0 1730202316
 
2025-07-16 21:17:13,741  [DEBUG] [D][11:45:16][PROT]is_send:0
 
2025-07-16 21:17:13,744  [DEBUG] [D][11:45:16][PROT]sequence_num:49
 
2025-07-16 21:17:13,747  [DEBUG] [D][11:45:16][PROT]retry_timeout:0
 
2025-07-16 21:17:13,750  [DEBUG] [D][11:45:16][PROT]retry_times:1
 
2025-07-16 21:17:13,753  [DEBUG] [D][11:45:16][PROT]send_path:0x2
 
2025-07-16 21:17:13,759  [DEBUG] [D][11:45:16][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-16 21:17:13,764  [DEBUG] [D][11:45:16][PROT]===========================================================
 
2025-07-16 21:17:13,770  [DEBUG] [D][11:45:16][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:17:13,776  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:17:13,778  [DEBUG] [D][11:45:16][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 21:17:13,787  [DEBUG] [D][11:45:16][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 21:17:13,789  [DEBUG] [D][11:45:16][SAL ]Cellular task submsg id[8]
 
2025-07-16 21:17:13,799  [DEBUG] [D][11:45:16][SAL ]cellular OPEN socket size[144], msg->data[0x20059fb0], socket[0]
 
2025-07-16 21:17:13,805  [DEBUG] [D][11:45:16][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 21:17:13,808  [DEBUG] [W][11:45:16][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202316]
 
2025-07-16 21:17:13,817  [DEBUG] [D][11:45:16][PROT]===========================================================
 
2025-07-16 21:17:13,820  [DEBUG] [D][11:45:16][PROT]sending traceid [9999999999900022]
 
2025-07-16 21:17:13,826  [DEBUG] [D][11:45:16][PROT]Send_TO_M2M [1730202316]
 
2025-07-16 21:17:13,828  [DEBUG] [D][11:45:16][CAT1]gsm read msg sub id: 8
 
2025-07-16 21:17:13,834  [DEBUG] [D][11:45:16][CAT1]at ops open socket[0]
 
2025-07-16 21:17:13,836  [DEBUG] [D][11:45:16][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:17:13,837  [DEBUG] 
 
2025-07-16 21:17:13,842  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 21:17:13,849  [DEBUG] [W][11:45:16][PROT]add success [1730202316],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:17:13,857  [DEBUG] [D][11:45:16][COMM]Main Task receive event:86 finished processing
 
2025-07-16 21:17:13,859  [DEBUG] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,859  [DEBUG] +CGATT: 1
 
2025-07-16 21:17:13,860  [DEBUG] 
 
2025-07-16 21:17:13,860  [DEBUG] OK
 
2025-07-16 21:17:13,860  [DEBUG] 
 
2025-07-16 21:17:13,864  [DEBUG] [D][11:45:16][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:17:13,865  [DEBUG] 
 
2025-07-16 21:17:13,865  [DEBUG] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,867  [DEBUG] +CSQ: 31,99
 
2025-07-16 21:17:13,868  [DEBUG] 
 
2025-07-16 21:17:13,868  [DEBUG] OK
 
2025-07-16 21:17:13,869  [DEBUG] 
 
2025-07-16 21:17:13,873  [DEBUG] [D][11:45:16][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 21:17:13,873  [DEBUG] 
 
2025-07-16 21:17:13,874  [DEBUG] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,875  [DEBUG] +QIACT: 1,1,1,"10.16.103.238"
 
2025-07-16 21:17:13,875  [DEBUG] 
 
2025-07-16 21:17:13,878  [DEBUG] OK
 
2025-07-16 21:17:13,878  [DEBUG] 
 
2025-07-16 21:17:13,884  [DEBUG] [D][11:45:16][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 21:17:13,885  [DEBUG] 
 
2025-07-16 21:17:13,886  [DEBUG] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:13,887  [DEBUG] OK
 
2025-07-16 21:17:13,887  [DEBUG] 
 
2025-07-16 21:17:13,891  [DEBUG] [D][11:45:16][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 21:17:13,959  [DEBUG] [D][11:45:16][CAT1]opened : 0, 0
 
2025-07-16 21:17:13,962  [DEBUG] [D][11:45:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:17:13,968  [DEBUG] [D][11:45:16][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 21:17:13,973  [DEBUG] [D][11:45:16][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 21:17:13,978  [DEBUG] [D][11:45:16][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-16 21:17:13,985  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:17:13,987  [DEBUG] [D][11:45:16][SAL ]sock send credit cnt[6]
 
2025-07-16 21:17:13,990  [DEBUG] [D][11:45:16][SAL ]sock send ind credit cnt[6]
 
2025-07-16 21:17:13,995  [DEBUG] [D][11:45:16][M2M ]m2m send data len[70]
 
2025-07-16 21:17:13,998  [DEBUG] [D][11:45:16][SAL ]Cellular task submsg id[10]
 
2025-07-16 21:17:14,007  [DEBUG] [D][11:45:16][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x20059fd0] format[0]
 
2025-07-16 21:17:14,012  [DEBUG] [D][11:45:16][CAT1]gsm read msg sub id: 15
 
2025-07-16 21:17:14,016  [DEBUG] [D][11:45:16][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-16 21:17:14,016  [DEBUG] 
 
2025-07-16 21:17:14,020  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:17:14,025  [DEBUG] [D][11:45:16][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-16 21:17:14,032  [DEBUG] 0023B9DE113311331133113311331B8877096CCFBF55FFDBA63D3419FDFA87F1E216EB
 
2025-07-16 21:17:14,034  [DEBUG] [D][11:45:16][CAT1]<<< 
 
2025-07-16 21:17:14,035  [DEBUG] SEND OK
 
2025-07-16 21:17:14,035  [DEBUG] 
 
2025-07-16 21:17:14,040  [DEBUG] [D][11:45:16][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 21:17:14,043  [DEBUG] [D][11:45:16][CAT1]sub id: 15, ret: 11
 
2025-07-16 21:17:14,043  [DEBUG] 
 
2025-07-16 21:17:14,049  [DEBUG] [D][11:45:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:17:14,054  [DEBUG] [D][11:45:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 21:17:14,056  [DEBUG] [D][11:45:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 21:17:14,063  [DEBUG] [D][11:45:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:17:14,066  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:17:14,071  [DEBUG] [D][11:45:16][PROT]M2M Send ok [1730202316]
 
2025-07-16 21:17:14,073  [DEBUG] [D][11:45:16][PROT]CLEAN:0
 
2025-07-16 21:17:14,076  [DEBUG] [D][11:45:16][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-16 21:17:14,082  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 21:17:14,087  [DEBUG] [D][11:45:16][M2M ]socket has connect, gsm_send_status:0
 
2025-07-16 21:17:14,089  [DEBUG] [D][11:45:16][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:17:14,097  [DEBUG] [D][11:45:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:17:54,531  [DEBUG] [D][11:45:56][CAT1]closed : 0
 
2025-07-16 21:17:54,536  [DEBUG] [D][11:45:56][SAL ]Cellular task submsg id[67]
 
2025-07-16 21:17:54,539  [DEBUG] [D][11:45:56][SAL ]socket closed id[0]
 
2025-07-16 21:17:54,545  [DEBUG] [D][11:45:56][SAL ]socket remote close ind. id[4]
 
2025-07-16 21:17:54,549  [DEBUG] [D][11:45:56][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-16 21:17:54,553  [DEBUG] [D][11:45:56][M2M ]m2m select fd[4]
 
2025-07-16 21:17:54,558  [DEBUG] [D][11:45:56][M2M ]socket[4] Link is disconnected
 
2025-07-16 21:17:54,561  [DEBUG] [D][11:45:56][M2M ]tcpclient close[4]
 
2025-07-16 21:17:54,564  [DEBUG] [D][11:45:56][SAL ]socket[4] has closed
 
2025-07-16 21:17:54,568  [DEBUG] [D][11:45:56][PROT]recv_protocol_data_from_gprs ok
 
2025-07-16 21:18:14,506  [DEBUG] [D][11:46:16][COMM]Main Task receive event:21
 
2025-07-16 21:18:14,596  [DEBUG] [D][11:46:16][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:18:14,601  [DEBUG] [D][11:46:16][HSDK]hexlog index save 0 25088 39 @ 1536 : 0
 
2025-07-16 21:18:14,605  [DEBUG] [D][11:46:16][HSDK]write save hexlog index [0]
 
2025-07-16 21:18:15,268  [DEBUG] [W][11:46:17][COMM]Power Off
 
2025-07-16 21:18:15,274  [DEBUG] [D][11:46:17][COMM]Ready to power off, please pull out the cable
 
2025-07-16 21:18:15,281  [DEBUG] [D][11:46:17][COMM]Main Task receive event:21 finished processing
 
2025-07-16 21:18:27,966  [DEBUG] [D][11:46:30][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:28,175  [DEBUG] [D][11:46:30][COMM]appComBuriedPack:module=9,type=2,localtime=1730202390, cnt=2
 
2025-07-16 21:18:28,180  [DEBUG] [D][11:46:30][COMM]Main Task receive event:102
 
2025-07-16 21:18:28,185  [DEBUG] [D][11:46:30][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:18:28,964  [DEBUG] [D][11:46:31][COMM]1194299 imu init OK
 
2025-07-16 21:18:28,976  [DEBUG] [D][11:46:31][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:29,975  [DEBUG] [D][11:46:32][COMM]1195310 imu init OK
 
2025-07-16 21:18:29,987  [DEBUG] [D][11:46:32][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:30,986  [DEBUG] [D][11:46:33][COMM]1196321 imu init OK
 
2025-07-16 21:18:30,998  [DEBUG] [D][11:46:33][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:32,001  [DEBUG] [D][11:46:34][COMM]1197333 imu init OK
 
2025-07-16 21:18:32,010  [DEBUG] [D][11:46:34][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:33,010  [DEBUG] [D][11:46:35][COMM]1198344 imu init OK
 
2025-07-16 21:18:33,021  [DEBUG] [D][11:46:35][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:34,020  [DEBUG] [D][11:46:36][COMM]1199355 imu init OK
 
2025-07-16 21:18:34,032  [DEBUG] [D][11:46:36][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:35,032  [DEBUG] [D][11:46:37][COMM]1200367 imu init OK
 
2025-07-16 21:18:35,044  [DEBUG] [D][11:46:37][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:36,043  [DEBUG] [D][11:46:38][COMM]1201378 imu init OK
 
2025-07-16 21:18:36,055  [DEBUG] [D][11:46:38][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:37,055  [DEBUG] [D][11:46:39][COMM]1202389 imu init OK
 
2025-07-16 21:18:37,066  [DEBUG] [D][11:46:39][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:38,066  [DEBUG] [D][11:46:40][COMM]1203401 imu init OK
 
2025-07-16 21:18:38,076  [DEBUG] [D][11:46:40][COMM]imu work error:[-1]. goto init
 
2025-07-16 21:18:39,076  [DEBUG] [D][11:46:41][COMM]imu error,enter wait
 
2025-07-16 21:19:28,972  [DEBUG] [D][11:47:31][COMM]appComBuriedPack:module=9,type=2,localtime=1730202451, cnt=3
 
2025-07-16 21:19:29,016  [DEBUG] [D][11:47:31][COMM]Main Task receive event:102
 
2025-07-16 21:19:29,022  [DEBUG] [D][11:47:31][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:31:41,410  [DEBUG] [D][11:59:43][COMM]Main Task receive event:99
 
2025-07-16 21:31:41,415  [DEBUG] [D][11:59:43][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:31:41,424  [DEBUG] [D][11:59:43][COMM]handlerPeriodWakeup, g_elecBatMissedCount = 2, time = 1730203183, allstateRepSeconds = 1730201400
 
2025-07-16 21:31:41,429  [DEBUG] [D][11:59:43][COMM]Main Task receive event:99 finished processing
 
2025-07-16 21:31:41,434  [DEBUG] [D][11:59:43][COMM]Main Task receive event:146
 
2025-07-16 21:31:41,437  [DEBUG] [D][11:59:43][COMM]a:13,b:0,c:0,d:0,e:0
 
2025-07-16 21:31:41,446  [DEBUG] [W][11:59:43][PROT]remove success[1730203183],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:31:41,454  [DEBUG] [W][11:59:43][PROT]add success [1730203183],send_path[2],type[C001],priority[0],index[0],used[1]
 
2025-07-16 21:31:41,459  [DEBUG] [D][11:59:43][COMM]Main Task receive event:146 finished processing
 
2025-07-16 21:31:41,465  [DEBUG] [D][11:59:43][PROT]index:0 1730203183
 
2025-07-16 21:31:41,468  [DEBUG] [D][11:59:43][PROT]is_send:0
 
2025-07-16 21:31:41,471  [DEBUG] [D][11:59:43][PROT]sequence_num:50
 
2025-07-16 21:31:41,473  [DEBUG] [D][11:59:43][PROT]retry_timeout:0
 
2025-07-16 21:31:41,476  [DEBUG] [D][11:59:43][PROT]retry_times:1
 
2025-07-16 21:31:41,479  [DEBUG] [D][11:59:43][PROT]send_path:0x2
 
2025-07-16 21:31:41,484  [DEBUG] [D][11:59:43][PROT]min_index:0, type:0xC001, priority:0
 
2025-07-16 21:31:41,491  [DEBUG] [D][11:59:43][PROT]===========================================================
 
2025-07-16 21:31:41,496  [DEBUG] [W][11:59:43][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730203183]
 
2025-07-16 21:31:41,504  [DEBUG] [D][11:59:43][PROT]===========================================================
 
2025-07-16 21:31:41,509  [DEBUG] [D][11:59:43][PROT]sending traceid [9999999999900026]
 
2025-07-16 21:31:41,512  [DEBUG] [D][11:59:43][PROT]Send_TO_M2M [1730203183]
 
2025-07-16 21:31:41,519  [DEBUG] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:31:41,521  [DEBUG] [E][11:59:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:31:41,527  [DEBUG] [E][11:59:43][M2M ]m2m send data len err[-1,102]
 
2025-07-16 21:31:41,529  [DEBUG] [D][11:59:43][M2M ]m2m send data len[-1]
 
2025-07-16 21:31:41,535  [DEBUG] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:31:41,540  [DEBUG] [E][11:59:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:31:41,543  [DEBUG] [E][11:59:43][PROT]M2M Send Fail [1730203183]
 
2025-07-16 21:31:41,546  [DEBUG] [D][11:59:43][PROT]CLEAN,SEND:0
 
2025-07-16 21:31:41,552  [DEBUG] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:31:41,554  [DEBUG] [D][11:59:43][PROT]CLEAN:0
 
2025-07-16 21:31:41,557  [DEBUG] [D][11:59:43][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:31:41,563  [DEBUG] [D][11:59:43][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:31:41,563  [DEBUG] 
 
2025-07-16 21:31:41,567  [DEBUG] [D][11:59:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:31:49,473  [DEBUG] [D][11:59:51][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:31:49,473  [DEBUG] 
 
2025-07-16 21:31:53,454  [DEBUG] [D][11:59:55][COMM]Main Task receive event:7
 
2025-07-16 21:31:53,459  [DEBUG] [D][11:59:55][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:31:53,468  [DEBUG] [D][11:59:55][COMM]handlerPeriodRep, g_elecBatMissedCount = 2, time = 1730203195, allstateRepSeconds = 1730201400
 
2025-07-16 21:31:53,470  [DEBUG] [D][11:59:55][COMM]Open GPS Module...
 
2025-07-16 21:31:53,473  [DEBUG] [D][11:59:55][GNSS]start event:3
 
2025-07-16 21:31:53,479  [DEBUG] [D][11:59:55][GNSS]GPS start. ret=0
 
2025-07-16 21:31:53,481  [DEBUG] [W][11:59:55][GNSS]start sing locating
 
2025-07-16 21:31:53,487  [DEBUG] [D][11:59:55][GNSS]gps single mode only, do wifi scan.
 
2025-07-16 21:31:53,490  [DEBUG] [D][11:59:55][COMM]index:0,power_mode:0xFF
 
2025-07-16 21:31:53,493  [DEBUG] [D][11:59:55][COMM]index:1,sound_mode:0xFF
 
2025-07-16 21:31:53,498  [DEBUG] [D][11:59:55][COMM]index:2,gsensor_mode:0xFF
 
2025-07-16 21:31:53,501  [DEBUG] [D][11:59:55][COMM]index:3,report_freq_mode:0xFF
 
2025-07-16 21:31:53,507  [DEBUG] [D][11:59:55][COMM]index:4,report_period:0xFF
 
2025-07-16 21:31:53,509  [DEBUG] [D][11:59:55][COMM]index:5,normal_reset_mode:0xFF
 
2025-07-16 21:31:53,514  [DEBUG] [D][11:59:55][COMM]index:6,normal_reset_period:0xFF
 
2025-07-16 21:31:53,521  [DEBUG] [D][11:59:55][COMM]index:7,spock_over_speed:0xFF
 
2025-07-16 21:31:53,523  [DEBUG] [D][11:59:55][COMM]index:8,spock_limit_speed:0xFF
 
2025-07-16 21:31:53,528  [DEBUG] [D][11:59:55][COMM]index:9,spock_report_period_unlock:0xFF
 
2025-07-16 21:31:53,535  [DEBUG] [D][11:59:55][COMM]index:10,spock_report_period_unlock_unit:0xFF
 
2025-07-16 21:31:53,540  [DEBUG] [D][11:59:55][COMM]index:11,ble_scan_mode:0xFF
 
2025-07-16 21:31:53,543  [DEBUG] [D][11:59:55][COMM]index:12,ble_adv_mode:0xFF
 
2025-07-16 21:31:53,548  [DEBUG] [D][11:59:55][COMM]index:13,spock_audio_volumn:0xFF
 
2025-07-16 21:31:53,554  [DEBUG] [D][11:59:55][COMM]index:14,spock_low_bat_alarm_soc:0xFF
 
2025-07-16 21:31:53,556  [DEBUG] [D][11:59:55][COMM]index:15,bat_auth_mode:0xFF
 
2025-07-16 21:31:53,562  [DEBUG] [D][11:59:55][COMM]index:16,imu_config_params:0xFF
 
2025-07-16 21:31:53,565  [DEBUG] [D][11:59:55][COMM]index:17,long_connect_params:0xFF
 
2025-07-16 21:31:53,570  [DEBUG] [D][11:59:55][COMM]index:18,detain_mark:0xFF
 
2025-07-16 21:31:53,576  [DEBUG] [D][11:59:55][COMM]index:19,lock_pos_report_count:0xFF
 
2025-07-16 21:31:53,579  [DEBUG] [D][11:59:55][COMM]index:20,lock_pos_report_interval:0xFF
 
2025-07-16 21:31:53,585  [DEBUG] [D][11:59:55][COMM]index:21,mc_mode:0xFF
 
2025-07-16 21:31:53,587  [DEBUG] [D][11:59:55][COMM]index:22,S_mode:0xFF
 
2025-07-16 21:31:53,590  [DEBUG] [D][11:59:55][COMM]index:23,overweight:0xFF
 
2025-07-16 21:31:53,595  [DEBUG] [D][11:59:55][COMM]index:24,standstill_mode:0xFF
 
2025-07-16 21:31:53,598  [DEBUG] [D][11:59:55][COMM]index:25,night_mode:0xFF
 
2025-07-16 21:31:53,603  [DEBUG] [D][11:59:55][COMM]index:26,experiment1:0xFF
 
2025-07-16 21:31:53,607  [DEBUG] [D][11:59:55][COMM]index:27,experiment2:0xFF
 
2025-07-16 21:31:53,613  [DEBUG] [D][11:59:55][COMM]index:28,experiment3:0xFF
 
2025-07-16 21:31:53,615  [DEBUG] [D][11:59:55][COMM]index:29,experiment4:0xFF
 
2025-07-16 21:31:53,621  [DEBUG] [D][11:59:55][COMM]index:30,night_mode_start:0xFF
 
2025-07-16 21:31:53,624  [DEBUG] [D][11:59:55][COMM]index:31,night_mode_end:0xFF
 
2025-07-16 21:31:53,629  [DEBUG] [D][11:59:55][COMM]index:33,park_report_minutes:0xFF
 
2025-07-16 21:31:53,634  [DEBUG] [D][11:59:55][COMM]index:34,park_report_mode:0xFF
 
2025-07-16 21:31:53,637  [DEBUG] [D][11:59:55][COMM]index:35,mc_undervoltage_protection:0xFF
 
2025-07-16 21:31:53,643  [DEBUG] [D][11:59:55][COMM]index:38,charge_battery_para: FF
 
2025-07-16 21:31:53,649  [DEBUG] [D][11:59:55][COMM]index:39,multirider_mode:0xFF
 
2025-07-16 21:31:53,651  [DEBUG] [D][11:59:55][COMM]index:40,mc_launch_mode:0xFF
 
2025-07-16 21:31:53,657  [DEBUG] [D][11:59:55][COMM]index:41,head_light_enable_mode:0xFF
 
2025-07-16 21:31:53,663  [DEBUG] [D][11:59:55][COMM]index:42,set_time_ble_mode_begin_min:0xFF
 
2025-07-16 21:31:53,668  [DEBUG] [D][11:59:55][COMM]index:43,set_time_ble_mode_end_min:0xFF
 
2025-07-16 21:31:53,673  [DEBUG] [D][11:59:55][COMM]index:44,riding_duration_config:0xFF
 
2025-07-16 21:31:53,676  [DEBUG] [D][11:59:55][COMM]index:45,camera_park_angle_cfg:0xFF
 
2025-07-16 21:31:53,682  [DEBUG] [D][11:59:55][COMM]index:46,camera_park_type_cfg:0xFF
 
2025-07-16 21:31:53,687  [DEBUG] [D][11:59:55][COMM]index:47,bat_info_rep_cfg:0xFF
 
2025-07-16 21:31:53,690  [DEBUG] [D][11:59:55][COMM]index:48,shlmt_sensor_en:0xFF
 
2025-07-16 21:31:53,696  [DEBUG] [D][11:59:55][COMM]index:49,mc_load_startup:0xFF
 
2025-07-16 21:31:53,699  [DEBUG] [D][11:59:55][COMM]index:50,mc_tcs_mode:0xFF
 
2025-07-16 21:31:53,705  [DEBUG] [D][11:59:55][COMM]index:51,traffic_audio_play:0xFF
 
2025-07-16 21:31:53,707  [DEBUG] [D][11:59:55][COMM]index:52,traffic_mode:0xFF
 
2025-07-16 21:31:53,712  [DEBUG] [D][11:59:55][COMM]index:53,traffic_info_collect_freq:0xFF
 
2025-07-16 21:31:53,719  [DEBUG] [D][11:59:55][COMM]index:54,traffic_security_model_cycle:0xFF
 
2025-07-16 21:31:53,724  [DEBUG] [D][11:59:55][COMM]index:55,wheel_alarm_play_switch:255
 
2025-07-16 21:31:53,730  [DEBUG] [D][11:59:55][COMM]index:57,traffic_sens_cycle:0xFF
 
2025-07-16 21:31:53,732  [DEBUG] [D][11:59:55][COMM]index:58,traffic_light_threshold:0xFF
 
2025-07-16 21:31:53,738  [DEBUG] [D][11:59:55][COMM]index:59,traffic_retrograde_threshold:0xFF
 
2025-07-16 21:31:53,743  [DEBUG] [D][11:59:55][COMM]index:60,traffic_road_threshold:0xFF
 
2025-07-16 21:31:53,749  [DEBUG] [D][11:59:55][COMM]index:61,traffic_sens_threshold:0xFF
 
2025-07-16 21:31:53,752  [DEBUG] [D][11:59:55][COMM]index:63,experiment5:0xFF
 
2025-07-16 21:31:53,758  [DEBUG] [D][11:59:55][COMM]index:64,camera_park_markline_cfg:0xFF
 
2025-07-16 21:31:53,763  [DEBUG] [D][11:59:55][COMM]index:65,camera_park_fenceline_cfg:0xFF
 
2025-07-16 21:31:53,768  [DEBUG] [D][11:59:55][COMM]index:66,camera_park_distance_cfg:0xFF
 
2025-07-16 21:31:53,773  [DEBUG] [D][11:59:55][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
 
2025-07-16 21:31:53,776  [DEBUG] [D][11:59:55][COMM]index:68,camera_park_ps_cfg:0xFFFF
 
2025-07-16 21:31:53,783  [DEBUG] [D][11:59:55][COMM]index:70,camera_park_light_cfg:0xFF
 
2025-07-16 21:31:53,788  [DEBUG] [D][11:59:55][COMM]index:71,camera_park_self_check_cfg:0xFF
 
2025-07-16 21:31:53,794  [DEBUG] [D][11:59:55][COMM]index:72,experiment6:0xFF
 
2025-07-16 21:31:53,796  [DEBUG] [D][11:59:55][COMM]index:73,experiment7:0xFF
 
2025-07-16 21:31:53,802  [DEBUG] [D][11:59:55][COMM]index:74,load_messurement_cfg:0xff
 
2025-07-16 21:31:53,805  [DEBUG] [D][11:59:55][COMM]index:75,zero_value_from_server:-1
 
2025-07-16 21:31:53,809  [DEBUG] [D][11:59:55][COMM]index:76,multirider_threshold:255
 
2025-07-16 21:31:53,816  [DEBUG] [D][11:59:55][COMM]index:77,experiment8:255
 
2025-07-16 21:31:53,821  [DEBUG] [D][11:59:55][COMM]index:78,temp_park_audio_play_duration:255
 
2025-07-16 21:31:53,827  [DEBUG] [D][11:59:55][COMM]index:79,temp_park_tail_light_twinkle_duration:255
 
2025-07-16 21:31:53,832  [DEBUG] [D][11:59:55][COMM]index:80,temp_park_reminder_timeout_duration:255
 
2025-07-16 21:31:53,838  [DEBUG] [D][11:59:55][COMM]index:82,loc_report_low_speed_thr:255
 
2025-07-16 21:31:53,841  [DEBUG] [D][11:59:55][COMM]index:83,loc_report_interval:255
 
2025-07-16 21:31:53,846  [DEBUG] [D][11:59:55][COMM]index:84,multirider_threshold_p2:255
 
2025-07-16 21:31:53,851  [DEBUG] [D][11:59:55][COMM]index:85,multirider_strategy:255
 
2025-07-16 21:31:53,858  [DEBUG] [D][11:59:55][COMM]index:81,camera_park_similar_thr_cfg:0xFF
 
2025-07-16 21:31:53,863  [DEBUG] [D][11:59:55][COMM]index:86,camera_park_self_check_period_cfg:0xFF
 
2025-07-16 21:31:53,866  [DEBUG] [D][11:59:55][COMM]index:96,rope_sensor_cfg:0xFF
 
2025-07-16 21:31:53,871  [DEBUG] [D][11:59:55][COMM]index:90,weight_param:0xFF
 
2025-07-16 21:31:53,877  [DEBUG] [D][11:59:55][COMM]index:93,lock_anti_theft_mode:0xFF
 
2025-07-16 21:31:53,879  [DEBUG] [D][11:59:55][COMM]index:94,high_temp_alarm_count:0xFF
 
2025-07-16 21:31:53,885  [DEBUG] [D][11:59:55][COMM]index:95,current_limit:0xFF
 
2025-07-16 21:31:53,891  [DEBUG] [D][11:59:55][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
 
2025-07-16 21:31:53,897  [DEBUG] [D][11:59:55][COMM]index:104,brake_release_protect:0xFF
 
2025-07-16 21:31:53,898  [DEBUG] 
 
2025-07-16 21:31:53,902  [DEBUG] [D][11:59:55][COMM]index:100,location_mode:0xFF
 
2025-07-16 21:31:53,905  [DEBUG] [D][11:59:55][COMM]index:110,display_speed_limit:255
 
2025-07-16 21:31:53,910  [DEBUG] [D][11:59:55][COMM]index:105,vo_unload_thr:0xFFFF
 
2025-07-16 21:31:53,916  [DEBUG] [D][11:59:55][COMM]index:107,vo_loading_angle_thr:0xFF
 
2025-07-16 21:31:53,918  [DEBUG] [D][11:59:55][COMM]index:108,vo_func_switch:0xFF
 
2025-07-16 21:31:53,927  [DEBUG] [W][11:59:55][PROT]remove success[1730203195],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:31:53,936  [DEBUG] [W][11:59:55][PROT]add success [1730203195],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 21:31:53,947  [DEBUG] [D][11:59:55][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:31:53,950  [DEBUG] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:31:53,952  [DEBUG] [D][11:59:55][COMM]report park limit config
 
2025-07-16 21:31:53,963  [DEBUG] [D][11:59:55][COMM]report park limit config:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
 
2025-07-16 21:31:53,967  [DEBUG] FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 
2025-07-16 21:31:53,969  [DEBUG] [D][11:59:55][COMM]
 
2025-07-16 21:31:53,977  [DEBUG] [W][11:59:55][PROT]remove success[1730203195],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:31:53,987  [DEBUG] [W][11:59:55][PROT]add success [1730203195],send_path[2],type[4701],priority[0],index[1],used[1]
 
2025-07-16 21:31:53,994  [DEBUG] [W][11:59:55][PROT]remove success[1730203195],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:31:54,003  [DEBUG] [W][11:59:55][PROT]add success [1730203195],send_path[2],type[4705],priority[0],index[2],used[1]
 
2025-07-16 21:31:54,009  [DEBUG] [D][11:59:55][COMM]bledev scan is invalid, will return
 
2025-07-16 21:31:54,018  [DEBUG] [D][11:59:55][COMM]------>period, report file manifest, waiting for Verify or count 1 less
 
2025-07-16 21:31:54,021  [DEBUG] [D][11:59:55][COMM]Main Task receive event:7 finished processing
 
2025-07-16 21:31:57,494  [DEBUG] [D][11:59:59][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:31:57,494  [DEBUG] 
 
2025-07-16 21:32:04,006  [DEBUG] [E][12:00:06][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:04,008  [DEBUG] [D][12:00:06][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:04,012  [DEBUG] [D][12:00:06][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:05,510  [DEBUG] [D][12:00:07][CAT1]exec over: func id: 10, ret: -43
 
2025-07-16 21:32:05,513  [DEBUG] [D][12:00:07][CAT1]sub id: 10, ret: -43
 
2025-07-16 21:32:05,513  [DEBUG] 
 
2025-07-16 21:32:05,518  [DEBUG] [D][12:00:07][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:32:05,524  [DEBUG] [D][12:00:07][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
 
2025-07-16 21:32:05,529  [DEBUG] [D][12:00:07][M2M ]m2m gsm shut done, ret[1]
 
2025-07-16 21:32:05,532  [DEBUG] [D][12:00:07][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:32:05,537  [DEBUG] [D][12:00:07][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:05,537  [DEBUG] 
 
2025-07-16 21:32:05,542  [DEBUG] [D][12:00:07][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
 
2025-07-16 21:32:05,546  [DEBUG] [D][12:00:07][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET_ACK
 
2025-07-16 21:32:05,622  [DEBUG] [D][12:00:08][COMM]Main Task receive event:98
 
2025-07-16 21:32:05,625  [DEBUG] [D][12:00:08][COMM]Err Event 2 Cnt
 
2025-07-16 21:32:05,631  [DEBUG] [D][12:00:08][COMM]Main Task receive event:98 finished processing
 
2025-07-16 21:32:07,539  [DEBUG] [D][12:00:09][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:07,540  [DEBUG] 
 
2025-07-16 21:32:09,555  [DEBUG] [D][12:00:11][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:09,555  [DEBUG] 
 
2025-07-16 21:32:11,570  [DEBUG] [D][12:00:13][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:11,571  [DEBUG] 
 
2025-07-16 21:32:13,591  [DEBUG] [D][12:00:15][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:13,592  [DEBUG] 
 
2025-07-16 21:32:14,041  [DEBUG] [E][12:00:16][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:14,044  [DEBUG] [D][12:00:16][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:14,050  [DEBUG] [D][12:00:16][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:14,053  [DEBUG] [D][12:00:16][GNSS]handler GSMGet Base timeout
 
2025-07-16 21:32:15,600  [DEBUG] [D][12:00:18][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:32:15,602  [DEBUG] [D][12:00:18][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:32:15,604  [DEBUG] 
 
2025-07-16 21:32:15,610  [DEBUG] [D][12:00:18][CAT1]gsm read msg sub id: 4
 
2025-07-16 21:32:15,613  [DEBUG] [W][12:00:18][CAT1]gsm_module_reboot
 
2025-07-16 21:32:15,613  [DEBUG] 
 
2025-07-16 21:32:16,048  [DEBUG] [D][12:00:18][GNSS]recv submsg id[1]
 
2025-07-16 21:32:16,051  [DEBUG] [D][12:00:18][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:32:16,055  [DEBUG] [D][12:00:18][GNSS]start gps fail
 
2025-07-16 21:32:23,971  [DEBUG] [D][12:00:26][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:23,972  [DEBUG] 
 
2025-07-16 21:32:24,079  [DEBUG] [E][12:00:26][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:24,081  [DEBUG] [D][12:00:26][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:24,086  [DEBUG] [D][12:00:26][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:24,982  [DEBUG] [D][12:00:27][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:24,983  [DEBUG] 
 
2025-07-16 21:32:25,999  [DEBUG] [D][12:00:28][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:26,000  [DEBUG] 
 
2025-07-16 21:32:27,015  [DEBUG] [D][12:00:29][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:27,016  [DEBUG] 
 
2025-07-16 21:32:28,033  [DEBUG] [D][12:00:30][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:28,034  [DEBUG] 
 
2025-07-16 21:32:29,047  [DEBUG] [D][12:00:31][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:29,047  [DEBUG] 
 
2025-07-16 21:32:30,067  [DEBUG] [D][12:00:32][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:30,068  [DEBUG] 
 
2025-07-16 21:32:31,087  [DEBUG] [D][12:00:33][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:31,088  [DEBUG] 
 
2025-07-16 21:32:32,107  [DEBUG] [D][12:00:34][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:32,107  [DEBUG] 
 
2025-07-16 21:32:33,121  [DEBUG] [D][12:00:35][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:33,123  [DEBUG] 
 
2025-07-16 21:32:34,116  [DEBUG] [E][12:00:36][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:34,118  [DEBUG] [D][12:00:36][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:34,122  [DEBUG] [D][12:00:36][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:34,135  [DEBUG] [D][12:00:36][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:34,136  [DEBUG] 
 
2025-07-16 21:32:35,150  [DEBUG] [D][12:00:37][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:35,151  [DEBUG] 
 
2025-07-16 21:32:36,164  [DEBUG] [D][12:00:38][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:36,165  [DEBUG] 
 
2025-07-16 21:32:37,180  [DEBUG] [D][12:00:39][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:37,181  [DEBUG] 
 
2025-07-16 21:32:38,197  [DEBUG] [D][12:00:40][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:38,199  [DEBUG] 
 
2025-07-16 21:32:39,209  [DEBUG] [D][12:00:41][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:39,211  [DEBUG] 
 
2025-07-16 21:32:40,224  [DEBUG] [D][12:00:42][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:40,225  [DEBUG] 
 
2025-07-16 21:32:41,239  [DEBUG] [D][12:00:43][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:41,240  [DEBUG] 
 
2025-07-16 21:32:42,253  [DEBUG] [D][12:00:44][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:42,254  [DEBUG] 
 
2025-07-16 21:32:42,436  [DEBUG] [D][12:00:44][COMM]Main Task receive event:21
 
2025-07-16 21:32:42,526  [DEBUG] [D][12:00:44][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:32:42,532  [DEBUG] [D][12:00:44][HSDK]hexlog index save 0 25088 39 @ 1536 : 0
 
2025-07-16 21:32:42,534  [DEBUG] [D][12:00:44][HSDK]write save hexlog index [0]
 
2025-07-16 21:32:43,144  [DEBUG] [W][12:00:45][COMM]Power Off
 
2025-07-16 21:32:43,150  [DEBUG] [D][12:00:45][COMM]Ready to power off, please pull out the cable
 
2025-07-16 21:32:43,157  [DEBUG] [D][12:00:45][COMM]Main Task receive event:21 finished processing
 
2025-07-16 21:32:43,273  [DEBUG] [D][12:00:45][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:32:43,274  [DEBUG] 
 
2025-07-16 21:32:44,162  [DEBUG] [E][12:00:46][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:44,165  [DEBUG] [D][12:00:46][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:44,169  [DEBUG] [D][12:00:46][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:44,290  [DEBUG] [D][12:00:46][CAT1]exec over: func id: 4, ret: -32
 
2025-07-16 21:32:44,293  [DEBUG] [D][12:00:46][CAT1]sub id: 4, ret: -32
 
2025-07-16 21:32:44,293  [DEBUG] 
 
2025-07-16 21:32:44,298  [DEBUG] [D][12:00:46][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:32:44,303  [DEBUG] [D][12:00:46][SAL ]handle subcmd ack sub_id[4], socket[0], result[-32]
 
2025-07-16 21:32:44,310  [DEBUG] [D][12:00:46][M2M ]m2m gsm reset done, ret[255]
 
2025-07-16 21:32:44,312  [DEBUG] [D][12:00:46][M2M ]reset OK, ret err
 
2025-07-16 21:32:44,315  [DEBUG] [D][12:00:46][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:32:44,320  [DEBUG] [D][12:00:46][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:32:44,320  [DEBUG] 
 
2025-07-16 21:32:44,323  [DEBUG] [D][12:00:46][M2M ]m2m switch to: M2M_GSM_PWR_ON
 
2025-07-16 21:32:44,328  [DEBUG] [D][12:00:46][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
 
2025-07-16 21:32:49,321  [DEBUG] [D][12:00:51][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:32:49,321  [DEBUG] 
 
2025-07-16 21:32:54,216  [DEBUG] [E][12:00:56][GNSS]GPS module no nmea data!
 
2025-07-16 21:32:54,219  [DEBUG] [D][12:00:56][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:32:54,224  [DEBUG] [D][12:00:56][GNSS]GPS reload start. ret=0
 
2025-07-16 21:32:54,229  [DEBUG] [D][12:00:56][GNSS]frm_wifi_scan_callback: scan timeout
 
2025-07-16 21:32:54,331  [DEBUG] [D][12:00:56][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:32:54,333  [DEBUG] [D][12:00:56][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:32:54,334  [DEBUG] 
 
2025-07-16 21:32:54,341  [DEBUG] [D][12:00:56][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:32:54,345  [DEBUG] [D][12:00:56][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:54,346  [DEBUG] 
 
2025-07-16 21:32:55,220  [DEBUG] [D][12:00:57][GNSS]recv submsg id[1]
 
2025-07-16 21:32:55,224  [DEBUG] [D][12:00:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:32:55,227  [DEBUG] [D][12:00:57][GNSS]stop gps fail
 
2025-07-16 21:32:56,359  [DEBUG] [D][12:00:58][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:56,360  [DEBUG] 
 
2025-07-16 21:32:58,378  [DEBUG] [D][12:01:00][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:32:58,378  [DEBUG] 
 
2025-07-16 21:33:00,389  [DEBUG] [D][12:01:02][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:00,389  [DEBUG] 
 
2025-07-16 21:33:02,408  [DEBUG] [D][12:01:04][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:02,410  [DEBUG] 
 
2025-07-16 21:33:04,268  [DEBUG] [E][12:01:06][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:04,274  [DEBUG] [D][12:01:06][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:04,275  [DEBUG] [D][12:01:06][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:04,429  [DEBUG] [D][12:01:06][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:33:04,431  [DEBUG] [D][12:01:06][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:33:04,431  [DEBUG] 
 
2025-07-16 21:33:04,440  [DEBUG] [D][12:01:06][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:33:04,443  [DEBUG] [D][12:01:06][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:04,443  [DEBUG] 
 
2025-07-16 21:33:05,273  [DEBUG] [D][12:01:07][GNSS]recv submsg id[1]
 
2025-07-16 21:33:05,276  [DEBUG] [D][12:01:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:33:05,280  [DEBUG] [D][12:01:07][GNSS]start gps fail
 
2025-07-16 21:33:09,456  [DEBUG] [D][12:01:11][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:09,457  [DEBUG] 
 
2025-07-16 21:33:14,318  [DEBUG] [E][12:01:16][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:14,320  [DEBUG] [D][12:01:16][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:14,325  [DEBUG] [D][12:01:16][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:14,466  [DEBUG] [D][12:01:16][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:33:14,470  [DEBUG] [D][12:01:16][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:33:14,471  [DEBUG] 
 
2025-07-16 21:33:14,478  [DEBUG] [D][12:01:16][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:33:14,483  [DEBUG] [D][12:01:16][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:14,483  [DEBUG] 
 
2025-07-16 21:33:15,320  [DEBUG] [D][12:01:17][GNSS]recv submsg id[1]
 
2025-07-16 21:33:15,324  [DEBUG] [D][12:01:17][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:33:15,328  [DEBUG] [D][12:01:17][GNSS]stop gps fail
 
2025-07-16 21:33:16,497  [DEBUG] [D][12:01:18][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:16,498  [DEBUG] 
 
2025-07-16 21:33:18,517  [DEBUG] [D][12:01:20][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:18,517  [DEBUG] 
 
2025-07-16 21:33:20,534  [DEBUG] [D][12:01:22][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:20,535  [DEBUG] 
 
2025-07-16 21:33:22,553  [DEBUG] [D][12:01:24][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:22,554  [DEBUG] 
 
2025-07-16 21:33:24,356  [DEBUG] [E][12:01:26][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:24,358  [DEBUG] [D][12:01:26][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:24,363  [DEBUG] [D][12:01:26][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:24,569  [DEBUG] [D][12:01:26][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:33:24,571  [DEBUG] [D][12:01:26][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:33:24,572  [DEBUG] 
 
2025-07-16 21:33:24,580  [DEBUG] [D][12:01:26][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:33:24,583  [DEBUG] [D][12:01:26][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:24,585  [DEBUG] 
 
2025-07-16 21:33:25,359  [DEBUG] [D][12:01:27][GNSS]recv submsg id[1]
 
2025-07-16 21:33:25,362  [DEBUG] [D][12:01:27][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:33:25,365  [DEBUG] [D][12:01:27][GNSS]start gps fail
 
2025-07-16 21:33:29,602  [DEBUG] [D][12:01:32][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:29,603  [DEBUG] 
 
2025-07-16 21:33:34,394  [DEBUG] [E][12:01:36][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:34,397  [DEBUG] [D][12:01:36][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:34,401  [DEBUG] [D][12:01:36][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:34,618  [DEBUG] [D][12:01:37][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:33:34,620  [DEBUG] [D][12:01:37][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:33:34,622  [DEBUG] 
 
2025-07-16 21:33:34,629  [DEBUG] [D][12:01:37][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:33:34,633  [DEBUG] [D][12:01:37][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:34,634  [DEBUG] 
 
2025-07-16 21:33:35,397  [DEBUG] [D][12:01:37][GNSS]recv submsg id[1]
 
2025-07-16 21:33:35,400  [DEBUG] [D][12:01:37][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:33:35,404  [DEBUG] [D][12:01:37][GNSS]stop gps fail
 
2025-07-16 21:33:36,649  [DEBUG] [D][12:01:39][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:36,649  [DEBUG] 
 
2025-07-16 21:33:38,668  [DEBUG] [D][12:01:41][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:38,668  [DEBUG] 
 
2025-07-16 21:33:40,685  [DEBUG] [D][12:01:43][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:40,686  [DEBUG] 
 
2025-07-16 21:33:42,697  [DEBUG] [D][12:01:45][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:42,698  [DEBUG] 
 
2025-07-16 21:33:44,431  [DEBUG] [E][12:01:46][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:44,432  [DEBUG] [D][12:01:46][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:44,438  [DEBUG] [D][12:01:46][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:44,707  [DEBUG] [D][12:01:47][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:33:44,709  [DEBUG] [D][12:01:47][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:33:44,711  [DEBUG] 
 
2025-07-16 21:33:44,719  [DEBUG] [D][12:01:47][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:33:44,722  [DEBUG] [D][12:01:47][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:44,723  [DEBUG] 
 
2025-07-16 21:33:45,432  [DEBUG] [D][12:01:47][GNSS]recv submsg id[1]
 
2025-07-16 21:33:45,436  [DEBUG] [D][12:01:47][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:33:45,440  [DEBUG] [D][12:01:47][GNSS]start gps fail
 
2025-07-16 21:33:49,736  [DEBUG] [D][12:01:52][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:33:49,739  [DEBUG] 
 
2025-07-16 21:33:54,469  [DEBUG] [E][12:01:56][GNSS]GPS module no nmea data!
 
2025-07-16 21:33:54,471  [DEBUG] [D][12:01:56][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:33:54,475  [DEBUG] [D][12:01:56][GNSS]GPS reload start. ret=0
 
2025-07-16 21:33:54,757  [DEBUG] [D][12:01:57][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:33:54,759  [DEBUG] [D][12:01:57][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:33:54,760  [DEBUG] 
 
2025-07-16 21:33:54,767  [DEBUG] [D][12:01:57][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:33:54,772  [DEBUG] [D][12:01:57][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:54,773  [DEBUG] 
 
2025-07-16 21:33:55,471  [DEBUG] [D][12:01:57][GNSS]recv submsg id[1]
 
2025-07-16 21:33:55,474  [DEBUG] [D][12:01:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:33:55,478  [DEBUG] [D][12:01:57][GNSS]stop gps fail
 
2025-07-16 21:33:56,791  [DEBUG] [D][12:01:59][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:56,791  [DEBUG] 
 
2025-07-16 21:33:58,804  [DEBUG] [D][12:02:01][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:33:58,806  [DEBUG] 
 
2025-07-16 21:34:00,823  [DEBUG] [D][12:02:03][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:34:00,825  [DEBUG] 
 
2025-07-16 21:34:02,839  [DEBUG] [D][12:02:05][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:34:02,840  [DEBUG] 
 
2025-07-16 21:34:04,508  [DEBUG] [E][12:02:06][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:04,511  [DEBUG] [D][12:02:06][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:04,515  [DEBUG] [D][12:02:06][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:04,851  [DEBUG] [D][12:02:07][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:34:04,852  [DEBUG] [D][12:02:07][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:34:04,854  [DEBUG] 
 
2025-07-16 21:34:04,860  [DEBUG] [D][12:02:07][CAT1]gsm read msg sub id: 1
 
2025-07-16 21:34:04,862  [DEBUG] [D][12:02:07][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:04,863  [DEBUG] 
 
2025-07-16 21:34:05,511  [DEBUG] [D][12:02:07][GNSS]recv submsg id[1]
 
2025-07-16 21:34:05,514  [DEBUG] [D][12:02:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:34:05,518  [DEBUG] [D][12:02:07][GNSS]start gps fail
 
2025-07-16 21:34:06,742  [DEBUG] [D][12:02:09][COMM]appComBuriedPack:module=9,type=2,localtime=1730203329, cnt=4
 
2025-07-16 21:34:06,784  [DEBUG] [D][12:02:09][COMM]Main Task receive event:102
 
2025-07-16 21:34:06,789  [DEBUG] [D][12:02:09][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:34:14,550  [DEBUG] [E][12:02:16][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:14,553  [DEBUG] [D][12:02:16][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:14,557  [DEBUG] [D][12:02:16][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:14,741  [DEBUG] [D][12:02:17][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:14,742  [DEBUG] 
 
2025-07-16 21:34:15,758  [DEBUG] [D][12:02:18][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:15,758  [DEBUG] 
 
2025-07-16 21:34:16,771  [DEBUG] [D][12:02:19][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:16,772  [DEBUG] 
 
2025-07-16 21:34:17,782  [DEBUG] [D][12:02:20][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:17,783  [DEBUG] 
 
2025-07-16 21:34:18,800  [DEBUG] [D][12:02:21][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:18,801  [DEBUG] 
 
2025-07-16 21:34:19,815  [DEBUG] [D][12:02:22][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:19,817  [DEBUG] 
 
2025-07-16 21:34:20,831  [DEBUG] [D][12:02:23][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:20,832  [DEBUG] 
 
2025-07-16 21:34:21,845  [DEBUG] [D][12:02:24][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:21,847  [DEBUG] 
 
2025-07-16 21:34:22,860  [DEBUG] [D][12:02:25][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:22,862  [DEBUG] 
 
2025-07-16 21:34:23,876  [DEBUG] [D][12:02:26][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:23,876  [DEBUG] 
 
2025-07-16 21:34:24,593  [DEBUG] [E][12:02:27][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:24,596  [DEBUG] [D][12:02:27][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:24,600  [DEBUG] [D][12:02:27][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:24,891  [DEBUG] [D][12:02:27][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:24,892  [DEBUG] 
 
2025-07-16 21:34:25,902  [DEBUG] [D][12:02:28][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:25,903  [DEBUG] 
 
2025-07-16 21:34:26,920  [DEBUG] [D][12:02:29][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:26,921  [DEBUG] 
 
2025-07-16 21:34:27,932  [DEBUG] [D][12:02:30][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:27,934  [DEBUG] 
 
2025-07-16 21:34:28,950  [DEBUG] [D][12:02:31][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:28,951  [DEBUG] 
 
2025-07-16 21:34:29,966  [DEBUG] [D][12:02:32][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:29,967  [DEBUG] 
 
2025-07-16 21:34:30,980  [DEBUG] [D][12:02:33][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:30,980  [DEBUG] 
 
2025-07-16 21:34:31,995  [DEBUG] [D][12:02:34][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:31,996  [DEBUG] 
 
2025-07-16 21:34:33,008  [DEBUG] [D][12:02:35][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:33,010  [DEBUG] 
 
2025-07-16 21:34:34,025  [DEBUG] [D][12:02:36][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:34,026  [DEBUG] 
 
2025-07-16 21:34:34,633  [DEBUG] [E][12:02:37][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:34,637  [DEBUG] [D][12:02:37][GNSS]GPS reload stop. ret=-3
 
2025-07-16 21:34:34,642  [DEBUG] [D][12:02:37][GNSS]GPS reload start. ret=-3
 
2025-07-16 21:34:35,043  [DEBUG] [D][12:02:37][CAT1]exec over: func id: 1, ret: -14
 
2025-07-16 21:34:35,046  [DEBUG] [D][12:02:37][CAT1]sub id: 1, ret: -14
 
2025-07-16 21:34:35,046  [DEBUG] 
 
2025-07-16 21:34:35,051  [DEBUG] [D][12:02:37][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:34:35,057  [DEBUG] [D][12:02:37][SAL ]handle subcmd ack sub_id[1], socket[0], result[-14]
 
2025-07-16 21:34:35,062  [DEBUG] [D][12:02:37][SAL ]gsm power on ind rst[-14]
 
2025-07-16 21:34:35,065  [DEBUG] [D][12:02:37][M2M ]m2m gsm power on, ret[255]
 
2025-07-16 21:34:35,068  [DEBUG] [E][12:02:37][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 21:34:35,074  [DEBUG] [D][12:02:37][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:34:35,076  [DEBUG] [D][12:02:37][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:34:35,076  [DEBUG] 
 
2025-07-16 21:34:35,082  [DEBUG] [D][12:02:37][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
 
2025-07-16 21:34:35,088  [DEBUG] [D][12:02:37][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET_ACK
 
2025-07-16 21:34:35,093  [DEBUG] [D][12:02:37][COMM]Main Task receive event:98
 
2025-07-16 21:34:35,097  [DEBUG] [D][12:02:37][COMM]Err Event 2 Cnt
 
2025-07-16 21:34:35,102  [DEBUG] [D][12:02:37][COMM]Main Task receive event:98 finished processing
 
2025-07-16 21:34:40,072  [DEBUG] [D][12:02:42][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:34:40,073  [DEBUG] 
 
2025-07-16 21:34:44,673  [DEBUG] [E][12:02:47][GNSS]GPS module no nmea data!
 
2025-07-16 21:34:44,675  [DEBUG] [D][12:02:47][GNSS]GPS reload stop. ret=0
 
2025-07-16 21:34:44,679  [DEBUG] [D][12:02:47][GNSS]GPS reload start. ret=0
 
2025-07-16 21:34:45,090  [DEBUG] [D][12:02:47][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:34:45,091  [DEBUG] [D][12:02:47][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:34:45,092  [DEBUG] 
 
2025-07-16 21:34:45,100  [DEBUG] [D][12:02:47][CAT1]gsm read msg sub id: 4
 
2025-07-16 21:34:45,102  [DEBUG] [W][12:02:47][CAT1]gsm_module_reboot
 
2025-07-16 21:34:45,103  [DEBUG] 
 
2025-07-16 21:34:45,677  [DEBUG] [D][12:02:48][GNSS]recv submsg id[1]
 
2025-07-16 21:34:45,679  [DEBUG] [D][12:02:48][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:34:45,682  [DEBUG] [D][12:02:48][GNSS]stop gps fail
 
2025-07-16 21:34:53,471  [DEBUG] [D][12:02:55][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:53,472  [DEBUG] 
 
2025-07-16 21:34:53,706  [DEBUG] [D][12:02:56][GNSS]location_callback:event=3
 
2025-07-16 21:34:54,367  [DEBUG] [D][12:02:56][COMM]Main Task receive event:14
 
2025-07-16 21:34:54,373  [DEBUG] [D][12:02:56][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:34:54,375  [DEBUG] [D][12:02:56][GNSS]stop event:3
 
2025-07-16 21:34:54,378  [DEBUG] [D][12:02:56][GNSS]GPS stop. ret=0
 
2025-07-16 21:34:54,382  [DEBUG] [D][12:02:56][COMM]Period location failed
 
2025-07-16 21:34:54,384  [DEBUG] [D][12:02:56][COMM]getLocatInfoStep2:1
 
2025-07-16 21:34:54,384  [DEBUG] 
 
2025-07-16 21:34:54,397  [DEBUG] [D][12:02:56][COMM]f:frm_park_cam_get_info. done reason:[1],type:[0],result:[0x04],err:[0xFF],angle:[0xFFFF],dist:[255]!!!
 
2025-07-16 21:34:54,401  [DEBUG] [D][12:02:56][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:34:54,404  [DEBUG] [D][12:02:56][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:34:54,407  [DEBUG] [W][12:02:56][COMM]get bat state1 error
 
2025-07-16 21:34:54,409  [DEBUG] [D][12:02:56][GNSS]5F01 soc:255
 
2025-07-16 21:34:54,415  [DEBUG] [W][12:02:56][COMM]get mc state information fail
 
2025-07-16 21:34:54,421  [DEBUG] [W][12:02:56][COMM]get mc speed information fail
 
2025-07-16 21:34:54,424  [DEBUG] [W][12:02:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 21:34:54,429  [DEBUG] [D][12:02:56][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:34:54,431  [DEBUG] [D][12:02:56][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:34:54,434  [DEBUG] [D][12:02:56][GNSS]nSatsInUse 0
 
2025-07-16 21:34:54,447  [DEBUG] [D][12:02:56][COMM]realtime_info.pitch_angle=127,realtime_info.roll_angle=32767,realtime_info.nav_angle=32763
 
2025-07-16 21:34:54,458  [DEBUG] [W][12:02:56][COMM]5F04 LocFail:reason:0x01;diff:43376;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:34:54,462  [DEBUG] [W][12:02:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:34:54,468  [DEBUG] [W][12:02:56][COMM]get mc power mode information fail
 
2025-07-16 21:34:54,470  [DEBUG] [D][12:02:56][COMM]can to 485 :254
 
2025-07-16 21:34:54,470  [DEBUG] 
 
2025-07-16 21:34:54,478  [DEBUG] [W][12:02:56][PROT]remove success[1730203376],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:34:54,487  [DEBUG] [W][12:02:56][PROT]add success [1730203376],send_path[2],type[5F04],priority[0],index[3],used[1]
 
2025-07-16 21:34:54,493  [DEBUG] [D][12:02:56][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
 
2025-07-16 21:34:54,499  [DEBUG] [D][12:02:56][M2M ]m2m_task: gpc:[0],gpo:[0]
 
2025-07-16 21:34:54,502  [DEBUG] [D][12:02:56][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:54,502  [DEBUG] 
 
2025-07-16 21:34:55,500  [DEBUG] [D][12:02:57][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:55,501  [DEBUG] 
 
2025-07-16 21:34:56,424  [DEBUG] [D][12:02:58][M2M ]get csq[7]
 
2025-07-16 21:34:56,517  [DEBUG] [D][12:02:58][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:56,517  [DEBUG] 
 
2025-07-16 21:34:56,678  [DEBUG] [D][12:02:59][COMM]lastHelmetOrderIdV2:00
 
2025-07-16 21:34:56,679  [DEBUG] >>>>>RESEND ALLSTATE<<<<<
 
2025-07-16 21:34:56,688  [DEBUG] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:34:56,696  [DEBUG] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 21:34:56,698  [DEBUG] [D][12:02:59][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:34:56,704  [DEBUG] [D][12:02:59][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:34:56,706  [DEBUG] [W][12:02:59][COMM]get soc error
 
2025-07-16 21:34:56,710  [DEBUG] [W][12:02:59][GNSS]stop locating
 
2025-07-16 21:34:56,713  [DEBUG] [D][12:02:59][GNSS]all continue location stop
 
2025-07-16 21:34:56,718  [DEBUG] [W][12:02:59][GNSS]sing locating running
 
2025-07-16 21:34:56,724  [DEBUG] [E][12:02:59][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:34:56,727  [DEBUG] [D][12:02:59][COMM]report elecbike, soc 0, reason 2
 
2025-07-16 21:34:56,737  [DEBUG] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:34:56,745  [DEBUG] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 21:34:56,749  [DEBUG] [D][12:02:59][COMM]BAT CAN get state1 Fail 204
 
2025-07-16 21:34:56,754  [DEBUG] [D][12:02:59][COMM]BAT CAN get soc Fail, 204
 
2025-07-16 21:34:56,762  [DEBUG] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:34:56,771  [DEBUG] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 21:34:56,780  [DEBUG] [D][12:02:59][COMM]buried data a:10,b:2,c:255,d:0,f:16843008,k:4770,l:404,m:11,n:13,o:13,p:1052,q:1972,r:4812,z:665
 
2025-07-16 21:34:56,790  [DEBUG] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 21:34:56,800  [DEBUG] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 21:34:56,802  [DEBUG] [D][12:02:59][GNSS]nTotalNumSatsInView = 0
 
2025-07-16 21:34:56,807  [DEBUG] [D][12:02:59][GNSS]nSatsAvgSNR 0, nSatsSNROver35 0
 
2025-07-16 21:34:56,810  [DEBUG] [D][12:02:59][GNSS]nSatsInUse 0
 
2025-07-16 21:34:56,823  [DEBUG] [W][12:02:59][COMM]5A07 LocFail:reason:0x01;diff:43379;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 21:34:56,826  [DEBUG] [W][12:02:59][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 21:34:56,835  [DEBUG] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 21:34:56,844  [DEBUG] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[5A07],priority[0],index[8],used[1]
 
2025-07-16 21:34:56,850  [DEBUG] [D][12:02:59][COMM]Main Task receive event:14 finished processing
 
2025-07-16 21:34:56,852  [DEBUG] [D][12:02:59][COMM]Main Task receive event:54
 
2025-07-16 21:34:56,858  [DEBUG] [D][12:02:59][COMM][D301]:type:1, trace id:0
 
2025-07-16 21:34:56,861  [DEBUG] [D][12:02:59][COMM]get bat basic info err
 
2025-07-16 21:34:56,868  [DEBUG] [W][12:02:59][PROT]remove success[1730203379],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-16 21:34:56,877  [DEBUG] [W][12:02:59][PROT]add success [1730203379],send_path[2],type[D302],priority[0],index[9],used[1]
 
2025-07-16 21:34:56,884  [DEBUG] [D][12:02:59][COMM]Main Task receive event:54 finished processing
 
2025-07-16 21:34:57,529  [DEBUG] [D][12:02:59][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:57,531  [DEBUG] 
 
2025-07-16 21:34:58,547  [DEBUG] [D][12:03:00][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:58,547  [DEBUG] 
 
2025-07-16 21:34:59,559  [DEBUG] [D][12:03:01][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:34:59,560  [DEBUG] 
 
2025-07-16 21:35:00,576  [DEBUG] [D][12:03:02][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:00,576  [DEBUG] 
 
2025-07-16 21:35:01,589  [DEBUG] [D][12:03:04][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:01,590  [DEBUG] 
 
2025-07-16 21:35:02,604  [DEBUG] [D][12:03:05][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:02,605  [DEBUG] 
 
2025-07-16 21:35:03,620  [DEBUG] [D][12:03:06][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:03,621  [DEBUG] 
 
2025-07-16 21:35:04,634  [DEBUG] [D][12:03:07][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:04,634  [DEBUG] 
 
2025-07-16 21:35:05,648  [DEBUG] [D][12:03:08][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:05,649  [DEBUG] 
 
2025-07-16 21:35:06,665  [DEBUG] [D][12:03:09][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:06,665  [DEBUG] 
 
2025-07-16 21:35:07,678  [DEBUG] [D][12:03:10][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:07,679  [DEBUG] 
 
2025-07-16 21:35:08,488  [DEBUG] [D][12:03:10][COMM]appComBuriedPack:module=9,type=2,localtime=1730203390, cnt=5
 
2025-07-16 21:35:08,521  [DEBUG] [D][12:03:10][COMM]Main Task receive event:102
 
2025-07-16 21:35:08,528  [DEBUG] [D][12:03:10][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:35:08,693  [DEBUG] [D][12:03:11][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:08,694  [DEBUG] 
 
2025-07-16 21:35:09,707  [DEBUG] [D][12:03:12][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:09,708  [DEBUG] 
 
2025-07-16 21:35:10,724  [DEBUG] [D][12:03:13][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:10,725  [DEBUG] 
 
2025-07-16 21:35:11,738  [DEBUG] [D][12:03:14][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:11,739  [DEBUG] 
 
2025-07-16 21:35:12,754  [DEBUG] [D][12:03:15][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:12,755  [DEBUG] 
 
2025-07-16 21:35:13,765  [DEBUG] [D][12:03:16][CAT1]exec over: func id: 4, ret: -32
 
2025-07-16 21:35:13,768  [DEBUG] [D][12:03:16][CAT1]sub id: 4, ret: -32
 
2025-07-16 21:35:13,769  [DEBUG] 
 
2025-07-16 21:35:13,774  [DEBUG] [D][12:03:16][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:35:13,779  [DEBUG] [D][12:03:16][SAL ]handle subcmd ack sub_id[4], socket[0], result[-32]
 
2025-07-16 21:35:13,784  [DEBUG] [D][12:03:16][M2M ]m2m gsm reset done, ret[255]
 
2025-07-16 21:35:13,787  [DEBUG] [D][12:03:16][M2M ]reset OK, ret err
 
2025-07-16 21:35:13,790  [DEBUG] [D][12:03:16][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:35:13,795  [DEBUG] [D][12:03:16][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:13,796  [DEBUG] 
 
2025-07-16 21:35:13,798  [DEBUG] [D][12:03:16][M2M ]m2m switch to: M2M_GSM_PWR_ON
 
2025-07-16 21:35:13,804  [DEBUG] [D][12:03:16][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
 
2025-07-16 21:35:15,447  [DEBUG] [D][12:03:17][COMM]Main Task receive event:21
 
2025-07-16 21:35:15,537  [DEBUG] [D][12:03:17][COMM]main task tmp_sleep_event = 40
 
2025-07-16 21:35:15,544  [DEBUG] [D][12:03:17][HSDK]hexlog index save 0 25088 39 @ 1536 : 0
 
2025-07-16 21:35:15,546  [DEBUG] [D][12:03:17][HSDK]write save hexlog index [0]
 
2025-07-16 21:35:16,157  [DEBUG] [W][12:03:18][COMM]Power Off
 
2025-07-16 21:35:16,163  [DEBUG] [D][12:03:18][COMM]Ready to power off, please pull out the cable
 
2025-07-16 21:35:16,170  [DEBUG] [D][12:03:18][COMM]Main Task receive event:21 finished processing
 
2025-07-16 21:35:18,796  [DEBUG] [D][12:03:21][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:18,797  [DEBUG] 
 
2025-07-16 21:35:23,815  [DEBUG] [D][12:03:26][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:35:23,817  [DEBUG] [D][12:03:26][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:35:23,818  [DEBUG] 
 
2025-07-16 21:35:23,826  [DEBUG] [D][12:03:26][CAT1]gsm read msg sub id: 23
 
2025-07-16 21:35:23,829  [DEBUG] [D][12:03:26][GNSS]recv submsg id[1]
 
2025-07-16 21:35:23,835  [DEBUG] [D][12:03:26][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:35:23,840  [DEBUG] [D][12:03:26][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:23,841  [DEBUG] 
 
2025-07-16 21:35:23,842  [DEBUG] [D][12:03:26][GNSS]stop gps fail
 
2025-07-16 21:35:25,846  [DEBUG] [D][12:03:28][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:25,847  [DEBUG] 
 
2025-07-16 21:35:27,866  [DEBUG] [D][12:03:30][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:27,867  [DEBUG] 
 
2025-07-16 21:35:29,882  [DEBUG] [D][12:03:32][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:29,883  [DEBUG] 
 
2025-07-16 21:35:31,897  [DEBUG] [D][12:03:34][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 21:35:31,897  [DEBUG] 
 
2025-07-16 21:35:33,908  [DEBUG] [D][12:03:36][CAT1]exec over: func id: 23, ret: -151
 
2025-07-16 21:35:33,910  [DEBUG] [D][12:03:36][CAT1]sub id: 23, ret: -151
 
2025-07-16 21:35:33,911  [DEBUG] 
 
2025-07-16 21:35:33,919  [DEBUG] [D][12:03:36][CAT1]gsm read msg sub id: 24
 
2025-07-16 21:35:33,922  [DEBUG] [D][12:03:36][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:33,923  [DEBUG] 
 
2025-07-16 21:35:34,868  [DEBUG] [D][12:03:37][GNSS]recv submsg id[1]
 
2025-07-16 21:35:34,871  [DEBUG] [D][12:03:37][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
 
2025-07-16 21:35:34,875  [DEBUG] [D][12:03:37][GNSS]start gps fail
 
2025-07-16 21:35:38,937  [DEBUG] [D][12:03:41][CAT1]tx ret[13] >>> AT+GPSPWR=0
 
2025-07-16 21:35:38,938  [DEBUG] 
 
2025-07-16 21:35:43,956  [DEBUG] [D][12:03:46][CAT1]exec over: func id: 24, ret: -181
 
2025-07-16 21:35:43,957  [DEBUG] [D][12:03:46][CAT1]sub id: 24, ret: -181
 
2025-07-16 21:35:43,960  [DEBUG] 
 
2025-07-16 21:35:43,967  [DEBUG] [D][12:03:46][CAT1]gsm read msg sub id: 26
 
2025-07-16 21:35:43,969  [DEBUG] [D][12:03:46][CAT1]tx ret[18] >>> AT+WIFISCAN=8,45
 
2025-07-16 21:35:43,970  [DEBUG] 
 
2025-07-16 21:35:44,904  [DEBUG] [D][12:03:47][GNSS]recv submsg id[1]
 
2025-07-16 21:35:44,908  [DEBUG] [D][12:03:47][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
 
2025-07-16 21:35:44,911  [DEBUG] [D][12:03:47][GNSS]stop gps fail
 
2025-07-16 21:35:44,979  [DEBUG] [D][12:03:47][CAT1]exec over: func id: 26, ret: -231
 
2025-07-16 21:35:44,991  [DEBUG] [D][12:03:47][CAT1]gsm read msg sub id: 13
 
2025-07-16 21:35:44,992  [DEBUG] [D][12:03:47][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:44,993  [DEBUG] 
 
2025-07-16 21:35:45,503  [DEBUG] [D][12:03:47][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:45,503  [DEBUG] 
 
2025-07-16 21:35:46,016  [DEBUG] [D][12:03:48][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:46,017  [DEBUG] 
 
2025-07-16 21:35:46,528  [DEBUG] [D][12:03:48][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:46,528  [DEBUG] 
 
2025-07-16 21:35:47,051  [DEBUG] [D][12:03:49][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:47,054  [DEBUG] 
 
2025-07-16 21:35:47,562  [DEBUG] [D][12:03:49][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:47,564  [DEBUG] 
 
2025-07-16 21:35:48,076  [DEBUG] [D][12:03:50][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:48,077  [DEBUG] 
 
2025-07-16 21:35:48,588  [DEBUG] [D][12:03:51][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:48,589  [DEBUG] 
 
2025-07-16 21:35:49,101  [DEBUG] [D][12:03:51][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:49,101  [DEBUG] 
 
2025-07-16 21:35:49,613  [DEBUG] [D][12:03:52][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 21:35:49,613  [DEBUG] 
 
2025-07-16 21:35:49,924  [DEBUG] [D][12:03:52][GNSS]frm_wifi_scan_callback: scan timeout
 
2025-07-16 21:35:50,126  [DEBUG] [D][12:03:52][CAT1]exec over: func id: 13, ret: -131
 
2025-07-16 21:35:50,136  [DEBUG] [D][12:03:52][CAT1]gsm read msg sub id: 1
 
2025-07-16 21:35:50,138  [DEBUG] [D][12:03:52][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:35:50,139  [DEBUG] 
 
2025-07-16 21:36:00,012  [DEBUG] [D][12:04:02][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:00,014  [DEBUG] 
 
2025-07-16 21:36:01,026  [DEBUG] [D][12:04:03][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:01,027  [DEBUG] 
 
2025-07-16 21:36:02,044  [DEBUG] [D][12:04:04][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:02,045  [DEBUG] 
 
2025-07-16 21:36:03,067  [DEBUG] [D][12:04:05][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:03,067  [DEBUG] 
 
2025-07-16 21:36:04,079  [DEBUG] [D][12:04:06][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:04,079  [DEBUG] 
 
2025-07-16 21:36:05,095  [DEBUG] [D][12:04:07][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:05,096  [DEBUG] 
 
2025-07-16 21:36:06,110  [DEBUG] [D][12:04:08][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:06,111  [DEBUG] 
 
2025-07-16 21:36:07,125  [DEBUG] [D][12:04:09][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:07,126  [DEBUG] 
 
2025-07-16 21:36:08,141  [DEBUG] [D][12:04:10][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:08,141  [DEBUG] 
 
2025-07-16 21:36:08,936  [DEBUG] [D][12:04:11][COMM]appComBuriedPack:module=9,type=2,localtime=1730203451, cnt=6
 
2025-07-16 21:36:08,964  [DEBUG] [D][12:04:11][COMM]Main Task receive event:102
 
2025-07-16 21:36:08,970  [DEBUG] [D][12:04:11][COMM]Main Task receive event:102 finished processing
 
2025-07-16 21:36:09,155  [DEBUG] [D][12:04:11][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:09,155  [DEBUG] 
 
2025-07-16 21:36:10,169  [DEBUG] [D][12:04:12][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:10,170  [DEBUG] 
 
2025-07-16 21:36:11,185  [DEBUG] [D][12:04:13][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:11,186  [DEBUG] 
 
2025-07-16 21:36:12,199  [DEBUG] [D][12:04:14][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:12,199  [DEBUG] 
 
2025-07-16 21:36:13,212  [DEBUG] [D][12:04:15][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:13,213  [DEBUG] 
 
2025-07-16 21:36:14,229  [DEBUG] [D][12:04:16][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:14,231  [DEBUG] 
 
2025-07-16 21:36:15,241  [DEBUG] [D][12:04:17][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:15,242  [DEBUG] 
 
2025-07-16 21:36:16,256  [DEBUG] [D][12:04:18][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:16,258  [DEBUG] 
 
2025-07-16 21:36:17,273  [DEBUG] [D][12:04:19][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:17,273  [DEBUG] 
 
2025-07-16 21:36:18,284  [DEBUG] [D][12:04:20][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:18,284  [DEBUG] 
 
2025-07-16 21:36:19,301  [DEBUG] [D][12:04:21][CAT1]tx ret[4] >>> AT
 
2025-07-16 21:36:19,302  [DEBUG] 
 
2025-07-16 21:36:20,319  [DEBUG] [D][12:04:22][CAT1]exec over: func id: 1, ret: -14
 
2025-07-16 21:36:20,321  [DEBUG] [D][12:04:22][CAT1]sub id: 1, ret: -14
 
2025-07-16 21:36:20,322  [DEBUG] 
 
2025-07-16 21:36:20,328  [DEBUG] [D][12:04:22][SAL ]Cellular task submsg id[68]
 
2025-07-16 21:36:20,333  [DEBUG] [D][12:04:22][SAL ]handle subcmd ack sub_id[1], socket[0], result[-14]
 
2025-07-16 21:36:20,338  [DEBUG] [D][12:04:22][SAL ]gsm power on ind rst[-14]
 
2025-07-16 21:36:20,341  [DEBUG] [D][12:04:22][M2M ]m2m gsm power on, ret[255]
 
2025-07-16 21:36:20,347  [DEBUG] [E][12:04:22][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-16 21:36:20,349  [DEBUG] [E][12:04:22][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 21:36:20,356  [DEBUG] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
 
2025-07-16 21:36:20,360  [DEBUG] [E][12:04:22][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-16 21:36:20,363  [DEBUG] [D][12:04:22][M2M ]g_m2m_is_idle become 1
 
2025-07-16 21:36:20,368  [DEBUG] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 21:36:20,371  [DEBUG] [D][12:04:22][PROT]index:5 1730203462
 
2025-07-16 21:36:20,374  [DEBUG] [D][12:04:22][PROT]is_send:0
 
2025-07-16 21:36:20,378  [DEBUG] [D][12:04:22][PROT]sequence_num:56
 
2025-07-16 21:36:20,380  [DEBUG] [D][12:04:22][PROT]retry_timeout:0
 
2025-07-16 21:36:20,383  [DEBUG] [D][12:04:22][PROT]retry_times:3
 
2025-07-16 21:36:20,386  [DEBUG] [D][12:04:22][PROT]send_path:0x2
 
2025-07-16 21:36:20,391  [DEBUG] [D][12:04:22][PROT]min_index:5, type:0x5D05, priority:3
 
2025-07-16 21:36:20,399  [DEBUG] [D][12:04:22][PROT]===========================================================
 
2025-07-16 21:36:20,405  [DEBUG] [W][12:04:22][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203462]
 
2025-07-16 21:36:20,411  [DEBUG] [D][12:04:22][PROT]===========================================================
 
2025-07-16 21:36:20,415  [DEBUG] [D][12:04:22][COMM]PB encode data:29
 
2025-07-16 21:36:20,419  [DEBUG] 0A1B1219080230013801400048005005A80104D00100D801FF01880202
 
2025-07-16 21:36:20,425  [DEBUG] [D][12:04:22][PROT]sending traceid [9999999999900027]
 
2025-07-16 21:36:20,430  [DEBUG] [D][12:04:22][PROT]Send_TO_M2M [1730203462]
 
2025-07-16 21:36:20,435  [DEBUG] [D][12:04:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
 
2025-07-16 21:36:20,438  [DEBUG] [D][12:04:22][M2M ]m2m_task: gpc:[0],gpo:[0]
 
2025-07-16 21:36:20,444  [DEBUG] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 21:36:20,450  [DEBUG] [E][12:04:22][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:36:20,452  [DEBUG] [E][12:04:22][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:36:20,455  [DEBUG] [D][12:04:22][M2M ]m2m send data len[-1]
 
2025-07-16 21:36:20,462  [DEBUG] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 21:36:20,466  [DEBUG] [E][12:04:22][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:36:20,469  [DEBUG] [E][12:04:22][PROT]M2M Send Fail [1730203462]
 
2025-07-16 21:36:20,471  [DEBUG] [D][12:04:22][PROT]CLEAN,SEND:5
 
2025-07-16 21:36:20,477  [DEBUG] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
 
2025-07-16 21:36:20,483  [DEBUG] [D][12:04:22][CAT1]gsm read msg sub id: 10
 
2025-07-16 21:36:20,485  [DEBUG] [D][12:04:22][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 21:36:20,486  [DEBUG] 
 
2025-07-16 21:36:20,490  [DEBUG] [D][12:04:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
 
2025-07-16 21:36:20,494  [DEBUG] [D][12:04:22][COMM]Main Task receive event:2
 
2025-07-16 21:36:20,504  [DEBUG] [D][12:04:22][FCTY]==========REBOOT E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 21:36:20,507  [DEBUG] [D][12:04:22][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 21:36:22,812  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 21:36:22,814  [DEBUG] flash is 24bit address mode

 
2025-07-16 21:36:22,817  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 21:36:22,820  [DEBUG] HW SW version: 5340 109

 
2025-07-16 21:36:22,824  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 21:36:22,826  [DEBUG] get_boot_mode a5a5
 
2025-07-16 21:36:22,826  [DEBUG] is_app_complete 1
 
2025-07-16 21:36:23,026  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 21:36:23,027  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 21:36:23,029  [DEBUG] [ADC]init adc success.

 
2025-07-16 21:36:23,660  [DEBUG] para ret:306,valid:aa

 
2025-07-16 21:36:23,674  [DEBUG] [W][12:04:24][COMM]BKP RESET_MODE[a5a5], reason[1-1]
 
2025-07-16 21:36:23,677  [DEBUG] [E][12:04:24][COMM]RESETREAS:0x00000008
 
2025-07-16 21:36:23,682  [DEBUG] [E][12:04:24][COMM]Multirider mode not support: 255
 
2025-07-16 21:36:23,688  [DEBUG] [W][12:04:24][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 21:36:23,692  [DEBUG] [W][12:04:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 21:36:23,695  [DEBUG] [W][12:04:24][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 21:36:23,701  [DEBUG] [W][12:04:24][FCTY]DeviceID    = 460130020284403
 
2025-07-16 21:36:23,705  [DEBUG] [W][12:04:24][FCTY]HardwareID  = 868667086862221
 
2025-07-16 21:36:23,710  [DEBUG] [W][12:04:24][FCTY]MoBikeID    = 9999999999
 
2025-07-16 21:36:23,713  [DEBUG] [W][12:04:24][FCTY]LockID      = F050821689
 
2025-07-16 21:36:23,715  [DEBUG] [W][12:04:24][FCTY]BLEFWVersion= 105
 
2025-07-16 21:36:23,722  [DEBUG] [W][12:04:24][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-16 21:36:23,723  [DEBUG] [W][12:04:24][FCTY]Bat         = 0 mv
 
2025-07-16 21:36:23,727  [DEBUG] [W][12:04:24][FCTY]Current     = 0 ma
 
2025-07-16 21:36:23,732  [DEBUG] [W][12:04:24][FCTY]VBUS        = 0 mv
 
2025-07-16 21:36:23,738  [DEBUG] [W][12:04:24][FCTY]TEMP= 0,BATID= 662484,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 21:36:23,741  [DEBUG] [W][12:04:24][FCTY]Ext battery vol = 1, adc = 75
 
2025-07-16 21:36:23,746  [DEBUG] [W][12:04:24][FCTY]Bike Type flag is invalied
 
2025-07-16 21:36:23,749  [DEBUG] [W][12:04:24][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 21:36:23,752  [DEBUG] [W][12:04:24][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 21:36:23,758  [DEBUG] [W][12:04:24][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 21:36:23,760  [DEBUG] [W][12:04:24][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 21:36:23,763  [DEBUG] [W][12:04:24][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 21:36:23,766  [DEBUG] [W][12:04:24][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 21:36:23,770  [DEBUG] [W][12:04:24][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 21:36:23,824  [DEBUG] [W][12:04:24][GNSS]start sing locating
 
2025-07-16 21:36:24,227  [DEBUG] [E][12:04:25][COMM]1x1 rx timeout
 
2025-07-16 21:36:24,630  [DEBUG] [E][12:04:25][COMM]1x1 rx timeout
 
2025-07-16 21:36:24,634  [DEBUG] [E][12:04:25][COMM]1x1 tp timeout
 
2025-07-16 21:36:24,636  [DEBUG] [E][12:04:25][COMM]1x1 error -3.
 
2025-07-16 21:36:24,639  [DEBUG] [W][12:04:25][COMM]Bat auth off fail, error:-1
 
2025-07-16 21:36:24,645  [DEBUG] [E][12:04:25][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 21:36:24,648  [DEBUG] [W][12:04:25][COMM]Init MC LOCK_STATE 2
 
2025-07-16 21:36:24,658  [DEBUG] [W][12:04:25][PROT]remove success[1730203465],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:36:24,665  [DEBUG] [W][12:04:25][PROT]add success [1730203465],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 21:36:25,749  [DEBUG] [W][12:04:26][PROT]remove success[1730203466],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:36:25,756  [DEBUG] [W][12:04:26][PROT]add success [1730203466],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 21:36:35,908  [DEBUG] [W][12:04:36][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730203476]
 
2025-07-16 21:36:41,767  [DEBUG] [W][12:04:42][PROT]remove success[1730203482],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:36:41,775  [DEBUG] [W][12:04:42][PROT]add success [1730203482],send_path[2],type[5103],priority[0],index[2],used[1]
 
2025-07-16 21:36:42,968  [DEBUG] [W][12:04:43][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730203483]
 
2025-07-16 21:36:48,371  [DEBUG] [W][12:04:49][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730203489]
 
2025-07-16 21:36:53,773  [DEBUG] [W][12:04:54][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730203494]
 
2025-07-16 21:36:59,182  [DEBUG] [W][12:05:00][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730203500]
 
2025-07-16 21:37:40,530  [DEBUG] [W][12:05:41][PROT]remove success[1730203541],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:37:40,538  [DEBUG] [W][12:05:41][PROT]add success [1730203541],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 21:37:40,543  [DEBUG] [W][12:05:41][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730203541]
 
2025-07-16 21:38:09,046  [DEBUG] [W][12:06:10][GNSS][RTK]enough, report now.
 
2025-07-16 21:38:09,479  [DEBUG] [E][12:06:10][COMM]1x1 rx timeout
 
2025-07-16 21:38:09,885  [DEBUG] [E][12:06:10][COMM]1x1 rx timeout
 
2025-07-16 21:38:09,887  [DEBUG] [E][12:06:10][COMM]1x1 tp timeout
 
2025-07-16 21:38:09,890  [DEBUG] [E][12:06:10][COMM]1x1 error -3.
 
2025-07-16 21:38:09,893  [DEBUG] [E][12:06:10][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:38:09,902  [DEBUG] [W][12:06:10][PROT]remove success[1730203570],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:38:09,908  [DEBUG] [W][12:06:10][PROT]add success [1730203570],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 21:38:09,922  [DEBUG] [W][12:06:10][COMM]5A07 LocFail:reason:0x07;diff:5518;LocUsedTime:76;LocStatus|Type:3|000;HDOP:01;SatsView:19;SatsSNR35:02
 
2025-07-16 21:38:09,930  [DEBUG] [W][12:06:10][COMM]5A07 LocFail:GpsSpeed:00;alt:0056;lon:114340371   lat:23012020
 
2025-07-16 21:38:09,937  [DEBUG] [W][12:06:10][PROT]remove success[1730203570],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:38:09,944  [DEBUG] [W][12:06:10][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203570]
 
2025-07-16 21:38:09,952  [DEBUG] [W][12:06:10][PROT]add success [1730203570],send_path[2],type[5A07],priority[0],index[1],used[1]
 
2025-07-16 21:38:15,311  [DEBUG] [W][12:06:16][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203576]
 
2025-07-16 21:38:20,716  [DEBUG] [W][12:06:21][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203581]
 
2025-07-16 21:38:26,120  [DEBUG] [W][12:06:27][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203587]
 
2025-07-16 21:38:31,528  [DEBUG] [W][12:06:32][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203592]
 
2025-07-16 21:38:36,930  [DEBUG] [W][12:06:37][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203597]
 
2025-07-16 21:38:42,339  [DEBUG] [W][12:06:43][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203603]
 
2025-07-16 21:38:47,743  [DEBUG] [W][12:06:48][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203608]
 
2025-07-16 21:38:53,149  [DEBUG] [W][12:06:54][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203614]
 
2025-07-16 21:38:58,555  [DEBUG] [W][12:06:59][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730203619]
 
2025-07-16 21:39:03,974  [DEBUG] [W][12:07:04][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730203624]
 
2025-07-16 21:39:11,103  [DEBUG] [E][12:07:12][COMM]1x1 rx timeout
 
2025-07-16 21:39:11,506  [DEBUG] [E][12:07:12][COMM]1x1 rx timeout
 
2025-07-16 21:39:11,509  [DEBUG] [E][12:07:12][COMM]1x1 tp timeout
 
2025-07-16 21:39:11,511  [DEBUG] [E][12:07:12][COMM]1x1 error -3.
 
2025-07-16 21:39:11,514  [DEBUG] [E][12:07:12][COMM]frm_mc_open_mos failed.
 
2025-07-16 21:39:11,524  [DEBUG] [E][12:07:12][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 21:39:11,531  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:39:11,539  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[5E01],priority[3],index[0],used[1]
 
2025-07-16 21:39:11,548  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 21:39:11,556  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[C001],priority[0],index[1],used[1]
 
2025-07-16 21:39:11,562  [DEBUG] [W][12:07:12][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730203632]
 
2025-07-16 21:39:11,810  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 21:39:11,819  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[5006],priority[2],index[2],used[1]
 
2025-07-16 21:39:11,821  [DEBUG] [W][12:07:12][COMM]get soc error
 
2025-07-16 21:39:11,824  [DEBUG] [W][12:07:12][GNSS]stop locating
 
2025-07-16 21:39:11,827  [DEBUG] [W][12:07:12][GNSS]sing locating running
 
2025-07-16 21:39:11,833  [DEBUG] [E][12:07:12][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 21:39:11,841  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 21:39:11,849  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[5D05],priority[3],index[3],used[1]
 
2025-07-16 21:39:11,860  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 21:39:11,868  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[FF0E],priority[0],index[4],used[1]
 
2025-07-16 21:39:11,876  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 21:39:11,885  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[C001],priority[0],index[5],used[1]
 
2025-07-16 21:39:11,894  [DEBUG] [W][12:07:12][PROT]remove success[1730203632],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 21:39:11,902  [DEBUG] [W][12:07:12][PROT]add success [1730203632],send_path[2],type[D302],priority[0],index[6],used[1]
 
2025-07-16 21:39:16,966  [DEBUG] [W][12:07:17][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730203637]
 
2025-07-16 21:39:22,371  [DEBUG] [W][12:07:23][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730203643]
 
2025-07-16 21:39:27,791  [DEBUG] [W][12:07:28][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203648]
 
2025-07-16 21:39:33,196  [DEBUG] [W][12:07:34][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203654]
 
2025-07-16 21:39:35,995  [DEBUG] [W][12:07:36][PROT]remove success[1730203656],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:39:36,003  [DEBUG] [W][12:07:36][PROT]add success [1730203656],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:39:38,604  [DEBUG] [W][12:07:39][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730203659]
 
2025-07-16 21:39:38,607  [DEBUG] [E][12:07:39][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 21:39:38,612  [DEBUG] [E][12:07:39][M2M ]m2m send data len err[-1,134]
 
2025-07-16 21:39:38,617  [DEBUG] [E][12:07:39][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 21:39:38,623  [DEBUG] [E][12:07:39][PROT]M2M Send Fail [1730203659]
 
2025-07-16 21:39:40,702  [DEBUG] [W][12:07:41][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730203661]
 
2025-07-16 21:39:46,121  [DEBUG] [W][12:07:47][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:46,217  [DEBUG] [W][12:07:47][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:46,312  [DEBUG] [W][12:07:47][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:46,397  [DEBUG] [W][12:07:47][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730203667]
 
2025-07-16 21:39:51,805  [DEBUG] [W][12:07:52][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730203672]
 
2025-07-16 21:39:57,210  [DEBUG] [W][12:07:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730203678]
 
2025-07-16 21:40:02,627  [DEBUG] [W][12:08:03][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203683]
 
2025-07-16 21:40:49,300  [DEBUG] [W][12:08:50][PROT]remove success[1730203730],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:40:49,305  [DEBUG] [W][12:08:50][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203730]
 
2025-07-16 21:40:49,313  [DEBUG] [W][12:08:50][PROT]add success [1730203730],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:41:30,234  [DEBUG] [W][12:09:31][PROT]remove success[1730203771],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:41:30,242  [DEBUG] [W][12:09:31][PROT]add success [1730203771],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:41:30,247  [DEBUG] [W][12:09:31][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203771]
 
2025-07-16 21:42:11,253  [DEBUG] [W][12:10:12][PROT]remove success[1730203812],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:42:11,259  [DEBUG] [W][12:10:12][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203812]
 
2025-07-16 21:42:11,266  [DEBUG] [W][12:10:12][PROT]add success [1730203812],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:42:52,202  [DEBUG] [W][12:10:53][PROT]remove success[1730203853],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:42:52,210  [DEBUG] [W][12:10:53][PROT]add success [1730203853],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:42:52,216  [DEBUG] [W][12:10:53][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203853]
 
2025-07-16 21:43:33,135  [DEBUG] [W][12:11:34][PROT]remove success[1730203894],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:43:33,143  [DEBUG] [W][12:11:34][PROT]add success [1730203894],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:43:33,148  [DEBUG] [W][12:11:34][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203894]
 
2025-07-16 21:44:14,052  [DEBUG] [W][12:12:15][PROT]remove success[1730203935],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:44:14,058  [DEBUG] [W][12:12:15][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203935]
 
2025-07-16 21:44:14,065  [DEBUG] [W][12:12:15][PROT]add success [1730203935],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:44:54,977  [DEBUG] [W][12:12:55][PROT]remove success[1730203975],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:44:54,985  [DEBUG] [W][12:12:55][PROT]add success [1730203975],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:44:54,991  [DEBUG] [W][12:12:55][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730203975]
 
2025-07-16 21:45:36,010  [DEBUG] [W][12:13:37][PROT]remove success[1730204017],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:45:36,019  [DEBUG] [W][12:13:37][PROT]add success [1730204017],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:45:36,024  [DEBUG] [W][12:13:37][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204017]
 
2025-07-16 21:46:16,945  [DEBUG] [W][12:14:17][PROT]remove success[1730204057],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:46:16,959  [DEBUG] [W][12:14:17][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204057]
 
2025-07-16 21:46:16,965  [DEBUG] [W][12:14:17][PROT]add success [1730204057],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:46:57,858  [DEBUG] [W][12:14:58][PROT]remove success[1730204098],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:46:57,866  [DEBUG] [W][12:14:58][PROT]add success [1730204098],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:46:57,871  [DEBUG] [W][12:14:58][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204098]
 
2025-07-16 21:47:38,861  [DEBUG] [W][12:15:39][PROT]remove success[1730204139],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:47:38,869  [DEBUG] [W][12:15:39][PROT]add success [1730204139],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:47:38,874  [DEBUG] [W][12:15:39][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204139]
 
2025-07-16 21:48:19,803  [DEBUG] [W][12:16:20][PROT]remove success[1730204180],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:48:19,811  [DEBUG] [W][12:16:20][PROT]add success [1730204180],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:48:19,816  [DEBUG] [W][12:16:20][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204180]
 
2025-07-16 21:49:00,828  [DEBUG] [W][12:17:01][PROT]remove success[1730204221],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:49:00,836  [DEBUG] [W][12:17:01][PROT]add success [1730204221],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:49:00,840  [DEBUG] [W][12:17:01][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204221]
 
2025-07-16 21:49:43,078  [DEBUG] [W][12:17:44][PROT]remove success[1730204264],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:49:43,086  [DEBUG] [W][12:17:44][PROT]add success [1730204264],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:49:43,091  [DEBUG] [W][12:17:44][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204264]
 
2025-07-16 21:50:23,992  [DEBUG] [W][12:18:25][PROT]remove success[1730204305],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:50:23,997  [DEBUG] [W][12:18:25][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204305]
 
2025-07-16 21:50:24,006  [DEBUG] [W][12:18:25][PROT]add success [1730204305],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:51:04,909  [DEBUG] [W][12:19:05][PROT]remove success[1730204345],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:51:04,918  [DEBUG] [W][12:19:05][PROT]add success [1730204345],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:51:04,923  [DEBUG] [W][12:19:05][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204345]
 
2025-07-16 21:51:45,929  [DEBUG] [W][12:19:46][PROT]remove success[1730204386],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:51:45,935  [DEBUG] [W][12:19:46][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204386]
 
2025-07-16 21:51:45,943  [DEBUG] [W][12:19:46][PROT]add success [1730204386],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:52:26,836  [DEBUG] [W][12:20:27][PROT]remove success[1730204427],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:52:26,842  [DEBUG] [W][12:20:27][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204427]
 
2025-07-16 21:52:26,848  [DEBUG] [W][12:20:27][PROT]add success [1730204427],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:53:07,845  [DEBUG] [W][12:21:08][PROT]remove success[1730204468],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:53:07,853  [DEBUG] [W][12:21:08][PROT]add success [1730204468],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:53:07,859  [DEBUG] [W][12:21:08][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204468]
 
2025-07-16 21:53:48,757  [DEBUG] [W][12:21:49][PROT]remove success[1730204509],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:53:48,766  [DEBUG] [W][12:21:49][PROT]add success [1730204509],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:53:48,771  [DEBUG] [W][12:21:49][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204509]
 
2025-07-16 21:54:29,769  [DEBUG] [W][12:22:30][PROT]remove success[1730204550],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:54:29,777  [DEBUG] [W][12:22:30][PROT]add success [1730204550],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:54:29,783  [DEBUG] [W][12:22:30][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204550]
 
2025-07-16 21:55:10,697  [DEBUG] [W][12:23:11][PROT]remove success[1730204591],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:55:10,703  [DEBUG] [W][12:23:11][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204591]
 
2025-07-16 21:55:10,709  [DEBUG] [W][12:23:11][PROT]add success [1730204591],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:55:51,603  [DEBUG] [W][12:23:52][PROT]remove success[1730204632],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:55:51,612  [DEBUG] [W][12:23:52][PROT]add success [1730204632],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:55:51,618  [DEBUG] [W][12:23:52][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204632]
 
2025-07-16 21:56:32,617  [DEBUG] [W][12:24:33][PROT]remove success[1730204673],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 21:56:32,623  [DEBUG] [W][12:24:33][PROT]add success [1730204673],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 21:56:32,629  [DEBUG] [W][12:24:33][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730204673]
 
2025-07-16 21:57:34,356  [DEBUG] [W][12:25:35][COMM]Power Off
 
