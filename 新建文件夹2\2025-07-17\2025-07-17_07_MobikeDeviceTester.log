2025-07-17 07:00:16,728  [DEBUG] [W][19:40:35][PROT]remove success[1730230835],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:00:16,736  [DEBUG] [W][19:40:35][PROT]add success [1730230835],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:00:16,741  [DEBUG] [W][19:40:35][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730230835]
 
2025-07-17 07:00:57,643  [DEBUG] [W][19:41:16][PROT]remove success[1730230876],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:00:57,655  [DEBUG] [W][19:41:16][PROT]add success [1730230876],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:00:57,656  [DEBUG] [W][19:41:16][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730230876]
 
2025-07-17 07:01:38,682  [DEBUG] [W][19:41:57][PROT]remove success[1730230917],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:01:38,691  [DEBUG] [W][19:41:57][PROT]add success [1730230917],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:01:38,696  [DEBUG] [W][19:41:57][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730230917]
 
2025-07-17 07:02:19,619  [DEBUG] [W][19:42:38][PROT]remove success[1730230958],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:02:19,625  [DEBUG] [W][19:42:38][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730230958]
 
2025-07-17 07:02:19,632  [DEBUG] [W][19:42:38][PROT]add success [1730230958],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:03:00,549  [DEBUG] [W][19:43:19][PROT]remove success[1730230999],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:03:00,557  [DEBUG] [W][19:43:19][PROT]add success [1730230999],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:03:00,563  [DEBUG] [W][19:43:19][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730230999]
 
2025-07-17 07:03:41,573  [DEBUG] [W][19:44:00][PROT]remove success[1730231040],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:03:41,581  [DEBUG] [W][19:44:00][PROT]add success [1730231040],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:03:41,587  [DEBUG] [W][19:44:00][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231040]
 
2025-07-17 07:04:22,494  [DEBUG] [W][19:44:41][PROT]remove success[1730231081],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:04:22,500  [DEBUG] [W][19:44:41][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231081]
 
2025-07-17 07:04:22,507  [DEBUG] [W][19:44:41][PROT]add success [1730231081],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:05:03,431  [DEBUG] [W][19:45:22][PROT]remove success[1730231122],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:05:03,440  [DEBUG] [W][19:45:22][PROT]add success [1730231122],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:05:03,445  [DEBUG] [W][19:45:22][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231122]
 
2025-07-17 07:05:44,437  [DEBUG] [W][19:46:03][PROT]remove success[1730231163],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:05:44,445  [DEBUG] [W][19:46:03][PROT]add success [1730231163],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:05:44,456  [DEBUG] [W][19:46:03][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231163]
 
2025-07-17 07:06:25,355  [DEBUG] [W][19:46:44][PROT]remove success[1730231204],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:06:25,363  [DEBUG] [W][19:46:44][PROT]add success [1730231204],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:06:25,369  [DEBUG] [W][19:46:44][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231204]
 
2025-07-17 07:07:06,279  [DEBUG] [W][19:47:25][PROT]remove success[1730231245],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:07:06,288  [DEBUG] [W][19:47:25][PROT]add success [1730231245],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:07:06,293  [DEBUG] [W][19:47:25][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231245]
 
2025-07-17 07:07:47,301  [DEBUG] [W][19:48:06][PROT]remove success[1730231286],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:07:47,309  [DEBUG] [W][19:48:06][PROT]add success [1730231286],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:07:47,315  [DEBUG] [W][19:48:06][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231286]
 
2025-07-17 07:08:28,212  [DEBUG] [W][19:48:47][PROT]remove success[1730231327],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:08:28,220  [DEBUG] [W][19:48:47][PROT]add success [1730231327],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:08:28,225  [DEBUG] [W][19:48:47][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231327]
 
2025-07-17 07:09:09,158  [DEBUG] [W][19:49:28][PROT]remove success[1730231368],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:09:09,166  [DEBUG] [W][19:49:28][PROT]add success [1730231368],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:09:09,172  [DEBUG] [W][19:49:28][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231368]
 
2025-07-17 07:09:50,089  [DEBUG] [W][19:50:09][PROT]remove success[1730231409],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:09:50,097  [DEBUG] [W][19:50:09][PROT]add success [1730231409],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:09:50,102  [DEBUG] [W][19:50:09][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231409]
 
2025-07-17 07:10:31,122  [DEBUG] [W][19:50:50][PROT]remove success[1730231450],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:10:31,130  [DEBUG] [W][19:50:50][PROT]add success [1730231450],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:10:31,135  [DEBUG] [W][19:50:50][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231450]
 
2025-07-17 07:11:12,020  [DEBUG] [W][19:51:31][PROT]remove success[1730231491],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:11:12,026  [DEBUG] [W][19:51:31][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231491]
 
2025-07-17 07:11:12,034  [DEBUG] [W][19:51:31][PROT]add success [1730231491],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:11:53,041  [DEBUG] [W][19:52:12][PROT]remove success[1730231532],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:11:53,050  [DEBUG] [W][19:52:12][PROT]add success [1730231532],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:11:53,055  [DEBUG] [W][19:52:12][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231532]
 
2025-07-17 07:12:33,967  [DEBUG] [W][19:52:53][PROT]remove success[1730231573],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:12:33,975  [DEBUG] [W][19:52:53][PROT]add success [1730231573],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:12:33,980  [DEBUG] [W][19:52:53][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231573]
 
2025-07-17 07:13:14,897  [DEBUG] [W][19:53:34][PROT]remove success[1730231614],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:13:14,905  [DEBUG] [W][19:53:34][PROT]add success [1730231614],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:13:14,910  [DEBUG] [W][19:53:34][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730231614]
 
2025-07-17 07:14:16,306  [DEBUG] [W][19:54:35][COMM]Power Off
 
2025-07-17 07:19:24,546  [DEBUG] [W][19:59:43][PROT]remove success[1730231983],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:19:24,554  [DEBUG] [W][19:59:43][PROT]add success [1730231983],send_path[2],type[C001],priority[0],index[0],used[1]
 
2025-07-17 07:19:24,560  [DEBUG] [W][19:59:43][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730231983]
 
2025-07-17 07:19:24,566  [DEBUG] [E][19:59:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-17 07:19:24,568  [DEBUG] [E][19:59:43][M2M ]m2m send data len err[-1,102]
 
2025-07-17 07:19:24,575  [DEBUG] [E][19:59:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-17 07:19:24,578  [DEBUG] [E][19:59:43][PROT]M2M Send Fail [1730231983]
 
2025-07-17 07:19:36,590  [DEBUG] [W][19:59:55][GNSS]start sing locating
 
2025-07-17 07:19:36,598  [DEBUG] [W][19:59:55][PROT]remove success[1730231995],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:19:36,607  [DEBUG] [W][19:59:55][PROT]add success [1730231995],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:19:36,615  [DEBUG] [W][19:59:55][PROT]remove success[1730231995],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:19:36,624  [DEBUG] [W][19:59:55][PROT]add success [1730231995],send_path[2],type[4701],priority[0],index[1],used[1]
 
2025-07-17 07:19:36,632  [DEBUG] [W][19:59:55][PROT]remove success[1730231995],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-17 07:19:36,640  [DEBUG] [W][19:59:55][PROT]add success [1730231995],send_path[2],type[4705],priority[0],index[2],used[1]
 
2025-07-17 07:19:47,172  [DEBUG] [E][20:00:06][GNSS]GPS module no nmea data!
 
2025-07-17 07:19:57,189  [DEBUG] [E][20:00:16][GNSS]GPS module no nmea data!
 
2025-07-17 07:19:58,718  [DEBUG] [W][20:00:17][CAT1]gsm_module_reboot
 
2025-07-17 07:19:58,719  [DEBUG] 
 
2025-07-17 07:20:07,215  [DEBUG] [E][20:00:26][GNSS]GPS module no nmea data!
 
2025-07-17 07:20:17,236  [DEBUG] [E][20:00:36][GNSS]GPS module no nmea data!
 
2025-07-17 07:20:26,333  [DEBUG] [W][20:00:45][COMM]Power Off
 
2025-07-17 07:20:27,257  [DEBUG] [E][20:00:46][GNSS]GPS module no nmea data!
 
2025-07-17 07:20:37,291  [DEBUG] [E][20:00:56][GNSS]GPS module no nmea data!
 
2025-07-17 07:20:47,334  [DEBUG] [E][20:01:06][GNSS]GPS module no nmea data!
 
2025-07-17 07:20:57,380  [DEBUG] [E][20:01:16][GNSS]GPS module no nmea data!
 
2025-07-17 07:21:07,412  [DEBUG] [E][20:01:26][GNSS]GPS module no nmea data!
 
2025-07-17 07:21:17,440  [DEBUG] [E][20:01:36][GNSS]GPS module no nmea data!
 
2025-07-17 07:21:27,456  [DEBUG] [E][20:01:46][GNSS]GPS module no nmea data!
 
2025-07-17 07:21:37,474  [DEBUG] [E][20:01:56][GNSS]GPS module no nmea data!
 
2025-07-17 07:21:47,488  [DEBUG] [E][20:02:06][GNSS]GPS module no nmea data!
 
2025-07-17 07:21:57,514  [DEBUG] [E][20:02:16][GNSS]GPS module no nmea data!
 
2025-07-17 07:22:07,552  [DEBUG] [E][20:02:26][GNSS]GPS module no nmea data!
 
2025-07-17 07:22:17,600  [DEBUG] [E][20:02:36][GNSS]GPS module no nmea data!
 
2025-07-17 07:22:18,121  [DEBUG] [E][20:02:37][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-17 07:22:27,627  [DEBUG] [E][20:02:46][GNSS]GPS module no nmea data!
 
2025-07-17 07:22:28,158  [DEBUG] [W][20:02:47][CAT1]gsm_module_reboot
 
2025-07-17 07:22:28,159  [DEBUG] 
 
2025-07-17 07:22:37,549  [DEBUG] [W][20:02:56][COMM]get bat state1 error
 
2025-07-17 07:22:37,551  [DEBUG] [W][20:02:56][COMM]get mc state information fail
 
2025-07-17 07:22:37,557  [DEBUG] [W][20:02:56][COMM]get mc speed information fail
 
2025-07-17 07:22:37,563  [DEBUG] [W][20:02:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-17 07:22:37,574  [DEBUG] [W][20:02:56][COMM]5F04 LocFail:reason:0x01;diff:72176;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-17 07:22:37,579  [DEBUG] [W][20:02:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-17 07:22:37,585  [DEBUG] [W][20:02:56][COMM]get mc power mode information fail
 
2025-07-17 07:22:37,593  [DEBUG] [W][20:02:56][PROT]remove success[1730232176],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-17 07:22:37,600  [DEBUG] [W][20:02:56][PROT]add success [1730232176],send_path[2],type[5F04],priority[0],index[3],used[1]
 
2025-07-17 07:22:39,859  [DEBUG] [W][20:02:59][PROT]remove success[1730232179],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-17 07:22:39,867  [DEBUG] [W][20:02:59][PROT]add success [1730232179],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-17 07:22:39,870  [DEBUG] [W][20:02:59][COMM]get soc error
 
2025-07-17 07:22:39,873  [DEBUG] [W][20:02:59][GNSS]stop locating
 
2025-07-17 07:22:39,876  [DEBUG] [W][20:02:59][GNSS]sing locating running
 
2025-07-17 07:22:39,882  [DEBUG] [E][20:02:59][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-17 07:22:39,890  [DEBUG] [W][20:02:59][PROT]remove success[1730232179],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-17 07:22:39,899  [DEBUG] [W][20:02:59][PROT]add success [1730232179],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-17 07:22:39,909  [DEBUG] [W][20:02:59][PROT]remove success[1730232179],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-17 07:22:39,918  [DEBUG] [W][20:02:59][PROT]add success [1730232179],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-17 07:22:39,926  [DEBUG] [W][20:02:59][PROT]remove success[1730232179],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-17 07:22:39,934  [DEBUG] [W][20:02:59][PROT]add success [1730232179],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-17 07:22:39,946  [DEBUG] [W][20:02:59][COMM]5A07 LocFail:reason:0x01;diff:72179;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-17 07:22:39,951  [DEBUG] [W][20:02:59][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-17 07:22:39,961  [DEBUG] [W][20:02:59][PROT]remove success[1730232179],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-17 07:22:39,968  [DEBUG] [W][20:02:59][PROT]add success [1730232179],send_path[2],type[5A07],priority[0],index[8],used[1]
 
2025-07-17 07:22:39,976  [DEBUG] [W][20:02:59][PROT]remove success[1730232179],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-17 07:22:39,984  [DEBUG] [W][20:02:59][PROT]add success [1730232179],send_path[2],type[D302],priority[0],index[9],used[1]
 
2025-07-17 07:22:59,275  [DEBUG] [W][20:03:18][COMM]Power Off
 
2025-07-17 07:24:03,467  [DEBUG] [E][20:04:22][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-17 07:24:03,472  [DEBUG] [E][20:04:22][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-17 07:24:03,481  [DEBUG] [E][20:04:22][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-17 07:24:03,504  [DEBUG] [W][20:04:22][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730232262]
 
2025-07-17 07:24:03,506  [DEBUG] [E][20:04:22][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-17 07:24:03,511  [DEBUG] [E][20:04:22][M2M ]m2m send data len err[-1,134]
 
2025-07-17 07:24:03,516  [DEBUG] [E][20:04:22][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-17 07:24:03,522  [DEBUG] [E][20:04:22][PROT]M2M Send Fail [1730232262]
 
2025-07-17 07:24:05,935  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:24:05,937  [DEBUG] flash is 24bit address mode

 
2025-07-17 07:24:05,940  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-17 07:24:05,943  [DEBUG] HW SW version: 5340 109

 
2025-07-17 07:24:05,947  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-17 07:24:05,949  [DEBUG] get_boot_mode a5a5
 
2025-07-17 07:24:05,949  [DEBUG] is_app_complete 1
 
2025-07-17 07:24:06,149  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:24:06,151  [DEBUG] [ADC]Timer status: enabled

 
2025-07-17 07:24:06,153  [DEBUG] [ADC]init adc success.

 
2025-07-17 07:24:06,780  [DEBUG] para ret:306,valid:aa

 
2025-07-17 07:24:06,798  [DEBUG] [W][20:04:24][COMM]BKP RESET_MODE[a5a5], reason[1-1]
 
2025-07-17 07:24:06,801  [DEBUG] [E][20:04:24][COMM]RESETREAS:0x00000008
 
2025-07-17 07:24:06,807  [DEBUG] [E][20:04:24][COMM]Multirider mode not support: 255
 
2025-07-17 07:24:06,815  [DEBUG] [W][20:04:24][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:24:06,821  [DEBUG] [W][20:04:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:24:06,824  [DEBUG] [W][20:04:24][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:24:06,829  [DEBUG] [W][20:04:24][FCTY]DeviceID    = 460130020284403
 
2025-07-17 07:24:06,832  [DEBUG] [W][20:04:24][FCTY]HardwareID  = 868667086862221
 
2025-07-17 07:24:06,837  [DEBUG] [W][20:04:24][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:24:06,840  [DEBUG] [W][20:04:24][FCTY]LockID      = F050821689
 
2025-07-17 07:24:06,843  [DEBUG] [W][20:04:24][FCTY]BLEFWVersion= 105
 
2025-07-17 07:24:06,848  [DEBUG] [W][20:04:24][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-17 07:24:06,851  [DEBUG] [W][20:04:24][FCTY]Bat         = 0 mv
 
2025-07-17 07:24:06,854  [DEBUG] [W][20:04:24][FCTY]Current     = 0 ma
 
2025-07-17 07:24:06,860  [DEBUG] [W][20:04:24][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:24:06,865  [DEBUG] [W][20:04:24][FCTY]TEMP= 0,BATID= 662484,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:24:06,868  [DEBUG] [W][20:04:24][FCTY]Ext battery vol = 2, adc = 96
 
2025-07-17 07:24:06,874  [DEBUG] [W][20:04:24][FCTY]Bike Type flag is invalied
 
2025-07-17 07:24:06,876  [DEBUG] [W][20:04:24][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:24:06,879  [DEBUG] [W][20:04:24][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:24:06,885  [DEBUG] [W][20:04:24][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:24:06,888  [DEBUG] [W][20:04:24][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:24:06,891  [DEBUG] [W][20:04:24][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:24:06,894  [DEBUG] [W][20:04:24][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:24:06,897  [DEBUG] [W][20:04:24][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:24:06,954  [DEBUG] [W][20:04:24][GNSS]start sing locating
 
2025-07-17 07:24:07,355  [DEBUG] [E][20:04:25][COMM]1x1 rx timeout
 
2025-07-17 07:24:07,761  [DEBUG] [E][20:04:25][COMM]1x1 rx timeout
 
2025-07-17 07:24:07,764  [DEBUG] [E][20:04:25][COMM]1x1 tp timeout
 
2025-07-17 07:24:07,767  [DEBUG] [E][20:04:25][COMM]1x1 error -3.
 
2025-07-17 07:24:07,770  [DEBUG] [W][20:04:25][COMM]Bat auth off fail, error:-1
 
2025-07-17 07:24:07,775  [DEBUG] [E][20:04:25][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-17 07:24:07,777  [DEBUG] [W][20:04:25][COMM]Init MC LOCK_STATE 2
 
2025-07-17 07:24:07,788  [DEBUG] [W][20:04:25][PROT]remove success[1730232265],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:24:07,796  [DEBUG] [W][20:04:25][PROT]add success [1730232265],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:24:08,883  [DEBUG] [W][20:04:26][PROT]remove success[1730232266],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:24:08,890  [DEBUG] [W][20:04:26][PROT]add success [1730232266],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-17 07:24:20,579  [DEBUG] [W][20:04:38][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730232278]
 
2025-07-17 07:24:25,883  [DEBUG] [W][20:04:43][PROT]remove success[1730232283],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-17 07:24:25,891  [DEBUG] [W][20:04:43][PROT]add success [1730232283],send_path[2],type[5103],priority[0],index[2],used[1]
 
2025-07-17 07:24:26,067  [DEBUG] [W][20:04:43][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730232283]
 
2025-07-17 07:24:31,474  [DEBUG] [W][20:04:49][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730232289]
 
2025-07-17 07:24:36,876  [DEBUG] [W][20:04:54][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730232294]
 
2025-07-17 07:24:42,275  [DEBUG] [W][20:05:00][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730232300]
 
2025-07-17 07:25:04,933  [DEBUG] [W][20:05:22][PROT]remove success[1730232322],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:25:04,942  [DEBUG] [W][20:05:22][PROT]add success [1730232322],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:25:04,947  [DEBUG] [W][20:05:22][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730232322]
 
2025-07-17 07:25:16,419  [DEBUG] [W][20:05:34][GNSS][RTK]found position idx 26.
 
2025-07-17 07:25:16,833  [DEBUG] [E][20:05:34][COMM]1x1 rx timeout
 
2025-07-17 07:25:17,238  [DEBUG] [E][20:05:35][COMM]1x1 rx timeout
 
2025-07-17 07:25:17,241  [DEBUG] [E][20:05:35][COMM]1x1 tp timeout
 
2025-07-17 07:25:17,243  [DEBUG] [E][20:05:35][COMM]1x1 error -3.
 
2025-07-17 07:25:17,247  [DEBUG] [E][20:05:35][COMM]frm_mc_open_mos failed.
 
2025-07-17 07:25:17,259  [DEBUG] [W][20:05:35][PROT]remove success[1730232335],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:25:17,268  [DEBUG] [W][20:05:35][PROT]add success [1730232335],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-17 07:25:17,279  [DEBUG] [W][20:05:35][COMM]5A07 LocFail:reason:0x07;diff:11981;LocUsedTime:57;LocStatus|Type:3|000;HDOP:01;SatsView:16;SatsSNR35:00
 
2025-07-17 07:25:17,284  [DEBUG] [W][20:05:35][COMM]5A07 LocFail:GpsSpeed:00;alt:0062;lon:114340366   lat:23012010
 
2025-07-17 07:25:17,292  [DEBUG] [W][20:05:35][PROT]remove success[1730232335],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:25:17,301  [DEBUG] [W][20:05:35][PROT]add success [1730232335],send_path[2],type[5A07],priority[0],index[1],used[1]
 
2025-07-17 07:25:17,308  [DEBUG] [W][20:05:35][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232335]
 
2025-07-17 07:25:22,661  [DEBUG] [W][20:05:40][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232340]
 
2025-07-17 07:25:28,053  [DEBUG] [W][20:05:45][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232345]
 
2025-07-17 07:25:33,449  [DEBUG] [W][20:05:51][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232351]
 
2025-07-17 07:25:38,842  [DEBUG] [W][20:05:56][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232356]
 
2025-07-17 07:25:44,237  [DEBUG] [W][20:06:02][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232362]
 
2025-07-17 07:25:49,637  [DEBUG] [W][20:06:07][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232367]
 
2025-07-17 07:25:55,036  [DEBUG] [W][20:06:12][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232372]
 
2025-07-17 07:26:00,441  [DEBUG] [W][20:06:18][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232378]
 
2025-07-17 07:26:05,847  [DEBUG] [W][20:06:23][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730232383]
 
2025-07-17 07:26:07,422  [DEBUG] [W][20:06:25][PROT]remove success[1730232385],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-17 07:26:07,429  [DEBUG] [W][20:06:25][PROT]add success [1730232385],send_path[2],type[8301],priority[0],index[2],used[1]
 
2025-07-17 07:26:11,266  [DEBUG] [W][20:06:29][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730232389]
 
2025-07-17 07:26:11,274  [DEBUG] [E][20:06:29][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-17 07:26:11,279  [DEBUG] [E][20:06:29][M2M ]m2m send data len err[-1,422]
 
2025-07-17 07:26:11,285  [DEBUG] [E][20:06:29][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-17 07:26:11,290  [DEBUG] [E][20:06:29][PROT]M2M Send Fail [1730232389]
 
2025-07-17 07:26:13,623  [DEBUG] [W][20:06:31][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232391]
 
2025-07-17 07:26:18,302  [DEBUG] [E][20:06:36][COMM]1x1 rx timeout
 
2025-07-17 07:26:18,708  [DEBUG] [E][20:06:36][COMM]1x1 rx timeout
 
2025-07-17 07:26:18,710  [DEBUG] [E][20:06:36][COMM]1x1 tp timeout
 
2025-07-17 07:26:18,713  [DEBUG] [E][20:06:36][COMM]1x1 error -3.
 
2025-07-17 07:26:18,716  [DEBUG] [E][20:06:36][COMM]frm_mc_open_mos failed.
 
2025-07-17 07:26:18,724  [DEBUG] [E][20:06:36][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-17 07:26:18,733  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:26:18,741  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[5E01],priority[3],index[0],used[1]
 
2025-07-17 07:26:18,749  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:26:18,755  [DEBUG] [W][20:06:36][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730232396]
 
2025-07-17 07:26:18,764  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[C001],priority[0],index[1],used[1]
 
2025-07-17 07:26:19,014  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-17 07:26:19,022  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[5006],priority[2],index[2],used[1]
 
2025-07-17 07:26:19,025  [DEBUG] [W][20:06:36][COMM]get soc error
 
2025-07-17 07:26:19,028  [DEBUG] [W][20:06:36][GNSS]stop locating
 
2025-07-17 07:26:19,031  [DEBUG] [W][20:06:36][GNSS]sing locating running
 
2025-07-17 07:26:19,036  [DEBUG] [E][20:06:36][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-17 07:26:19,044  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-17 07:26:19,053  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[5D05],priority[3],index[3],used[1]
 
2025-07-17 07:26:19,064  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-17 07:26:19,073  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[FF0E],priority[0],index[4],used[1]
 
2025-07-17 07:26:19,080  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-17 07:26:19,089  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[C001],priority[0],index[5],used[1]
 
2025-07-17 07:26:19,097  [DEBUG] [W][20:06:36][PROT]remove success[1730232396],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-17 07:26:19,105  [DEBUG] [W][20:06:36][PROT]add success [1730232396],send_path[2],type[D302],priority[0],index[6],used[1]
 
2025-07-17 07:26:24,156  [DEBUG] [W][20:06:42][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730232402]
 
2025-07-17 07:26:29,563  [DEBUG] [W][20:06:47][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730232407]
 
2025-07-17 07:26:34,978  [DEBUG] [W][20:06:52][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730232412]
 
2025-07-17 07:26:40,389  [DEBUG] [W][20:06:58][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730232418]
 
2025-07-17 07:26:45,790  [DEBUG] [W][20:07:03][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730232423]
 
2025-07-17 07:26:51,194  [DEBUG] [W][20:07:09][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730232429]
 
2025-07-17 07:26:56,610  [DEBUG] [W][20:07:14][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730232434]
 
2025-07-17 07:26:56,706  [DEBUG] [W][20:07:14][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730232434]
 
2025-07-17 07:26:56,801  [DEBUG] [W][20:07:14][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730232434]
 
2025-07-17 07:26:56,887  [DEBUG] [W][20:07:14][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730232434]
 
2025-07-17 07:27:02,281  [DEBUG] [W][20:07:20][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730232440]
 
2025-07-17 07:27:07,689  [DEBUG] [W][20:07:25][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730232445]
 
2025-07-17 07:27:49,158  [DEBUG] [W][20:08:07][PROT]remove success[1730232487],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:27:49,166  [DEBUG] [W][20:08:07][PROT]add success [1730232487],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:27:49,172  [DEBUG] [W][20:08:07][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232487]
 
2025-07-17 07:28:30,100  [DEBUG] [W][20:08:47][PROT]remove success[1730232527],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:28:30,107  [DEBUG] [W][20:08:47][PROT]add success [1730232527],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:28:30,113  [DEBUG] [W][20:08:47][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232527]
 
2025-07-17 07:29:11,029  [DEBUG] [W][20:09:28][PROT]remove success[1730232568],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:29:11,037  [DEBUG] [W][20:09:28][PROT]add success [1730232568],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:29:11,042  [DEBUG] [W][20:09:28][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232568]
 
2025-07-17 07:29:52,036  [DEBUG] [W][20:10:09][PROT]remove success[1730232609],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:29:52,043  [DEBUG] [W][20:10:09][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232609]
 
2025-07-17 07:29:52,050  [DEBUG] [W][20:10:09][PROT]add success [1730232609],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:30:32,958  [DEBUG] [W][20:10:50][PROT]remove success[1730232650],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:30:32,967  [DEBUG] [W][20:10:50][PROT]add success [1730232650],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:30:32,972  [DEBUG] [W][20:10:50][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232650]
 
2025-07-17 07:31:13,976  [DEBUG] [W][20:11:31][PROT]remove success[1730232691],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:31:13,984  [DEBUG] [W][20:11:31][PROT]add success [1730232691],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:31:13,990  [DEBUG] [W][20:11:31][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232691]
 
2025-07-17 07:31:54,917  [DEBUG] [W][20:12:12][PROT]remove success[1730232732],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:31:54,933  [DEBUG] [W][20:12:12][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232732]
 
2025-07-17 07:31:54,941  [DEBUG] [W][20:12:12][PROT]add success [1730232732],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:32:35,846  [DEBUG] [W][20:12:53][PROT]remove success[1730232773],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:32:35,856  [DEBUG] [W][20:12:53][PROT]add success [1730232773],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:32:35,861  [DEBUG] [W][20:12:53][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232773]
 
2025-07-17 07:33:16,799  [DEBUG] [W][20:13:34][PROT]remove success[1730232814],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:33:16,807  [DEBUG] [W][20:13:34][PROT]add success [1730232814],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:33:16,812  [DEBUG] [W][20:13:34][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232814]
 
2025-07-17 07:33:57,812  [DEBUG] [W][20:14:15][PROT]remove success[1730232855],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:33:57,820  [DEBUG] [W][20:14:15][PROT]add success [1730232855],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:33:57,826  [DEBUG] [W][20:14:15][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232855]
 
2025-07-17 07:34:38,746  [DEBUG] [W][20:14:56][PROT]remove success[1730232896],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:34:38,754  [DEBUG] [W][20:14:56][PROT]add success [1730232896],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:34:38,759  [DEBUG] [W][20:14:56][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232896]
 
2025-07-17 07:35:19,660  [DEBUG] [W][20:15:37][PROT]remove success[1730232937],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:35:19,667  [DEBUG] [W][20:15:37][PROT]add success [1730232937],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:35:19,672  [DEBUG] [W][20:15:37][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232937]
 
2025-07-17 07:36:00,697  [DEBUG] [W][20:16:18][PROT]remove success[1730232978],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:36:00,704  [DEBUG] [W][20:16:18][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730232978]
 
2025-07-17 07:36:00,711  [DEBUG] [W][20:16:18][PROT]add success [1730232978],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:36:41,623  [DEBUG] [W][20:16:59][PROT]remove success[1730233019],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:36:41,630  [DEBUG] [W][20:16:59][PROT]add success [1730233019],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:36:41,635  [DEBUG] [W][20:16:59][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233019]
 
2025-07-17 07:37:22,557  [DEBUG] [W][20:17:40][PROT]remove success[1730233060],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:37:22,565  [DEBUG] [W][20:17:40][PROT]add success [1730233060],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:37:22,570  [DEBUG] [W][20:17:40][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233060]
 
2025-07-17 07:38:03,468  [DEBUG] [W][20:18:21][PROT]remove success[1730233101],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:38:03,477  [DEBUG] [W][20:18:21][PROT]add success [1730233101],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:38:03,482  [DEBUG] [W][20:18:21][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233101]
 
2025-07-17 07:38:44,481  [DEBUG] [W][20:19:02][PROT]remove success[1730233142],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:38:44,489  [DEBUG] [W][20:19:02][PROT]add success [1730233142],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:38:44,494  [DEBUG] [W][20:19:02][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233142]
 
2025-07-17 07:39:25,408  [DEBUG] [W][20:19:43][PROT]remove success[1730233183],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:39:25,416  [DEBUG] [W][20:19:43][PROT]add success [1730233183],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:39:25,421  [DEBUG] [W][20:19:43][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233183]
 
2025-07-17 07:40:06,338  [DEBUG] [W][20:20:24][PROT]remove success[1730233224],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:40:06,346  [DEBUG] [W][20:20:24][PROT]add success [1730233224],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:40:06,352  [DEBUG] [W][20:20:24][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233224]
 
2025-07-17 07:40:47,355  [DEBUG] [W][20:21:05][PROT]remove success[1730233265],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:40:47,364  [DEBUG] [W][20:21:05][PROT]add success [1730233265],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:40:47,369  [DEBUG] [W][20:21:05][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233265]
 
2025-07-17 07:41:28,387  [DEBUG] [W][20:21:46][PROT]remove success[1730233306],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:41:28,395  [DEBUG] [W][20:21:46][PROT]add success [1730233306],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:41:28,400  [DEBUG] [W][20:21:46][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233306]
 
2025-07-17 07:42:09,320  [DEBUG] [W][20:22:27][PROT]remove success[1730233347],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:42:09,328  [DEBUG] [W][20:22:27][PROT]add success [1730233347],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:42:09,334  [DEBUG] [W][20:22:27][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233347]
 
2025-07-17 07:42:50,239  [DEBUG] [W][20:23:08][PROT]remove success[1730233388],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:42:50,248  [DEBUG] [W][20:23:08][PROT]add success [1730233388],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:42:50,253  [DEBUG] [W][20:23:08][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233388]
 
2025-07-17 07:43:31,173  [DEBUG] [W][20:23:49][PROT]remove success[1730233429],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:43:31,179  [DEBUG] [W][20:23:49][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730233429]
 
2025-07-17 07:43:31,186  [DEBUG] [W][20:23:49][PROT]add success [1730233429],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:44:32,511  [DEBUG] [W][20:24:50][COMM]Power Off
 
2025-07-17 07:49:25,759  [DEBUG] [W][20:29:43][PROT]remove success[1730233783],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:49:25,767  [DEBUG] [W][20:29:43][PROT]add success [1730233783],send_path[2],type[C001],priority[0],index[0],used[1]
 
2025-07-17 07:49:25,773  [DEBUG] [W][20:29:43][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730233783]
 
2025-07-17 07:49:25,779  [DEBUG] [E][20:29:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-17 07:49:25,781  [DEBUG] [E][20:29:43][M2M ]m2m send data len err[-1,102]
 
2025-07-17 07:49:25,787  [DEBUG] [E][20:29:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-17 07:49:25,790  [DEBUG] [E][20:29:43][PROT]M2M Send Fail [1730233783]
 
2025-07-17 07:49:37,797  [DEBUG] [W][20:29:55][GNSS]start sing locating
 
2025-07-17 07:49:37,805  [DEBUG] [W][20:29:55][PROT]remove success[1730233795],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:49:37,813  [DEBUG] [W][20:29:55][PROT]add success [1730233795],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:49:37,821  [DEBUG] [W][20:29:55][PROT]remove success[1730233795],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:49:37,829  [DEBUG] [W][20:29:55][PROT]add success [1730233795],send_path[2],type[4701],priority[0],index[1],used[1]
 
2025-07-17 07:49:37,844  [DEBUG] [W][20:29:55][PROT]remove success[1730233795],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-17 07:49:37,848  [DEBUG] [W][20:29:55][PROT]add success [1730233795],send_path[2],type[4705],priority[0],index[2],used[1]
 
2025-07-17 07:49:47,976  [DEBUG] [E][20:30:05][GNSS]GPS module no nmea data!
 
2025-07-17 07:49:57,999  [DEBUG] [E][20:30:15][GNSS]GPS module no nmea data!
 
2025-07-17 07:49:59,926  [DEBUG] [W][20:30:17][CAT1]gsm_module_reboot
 
2025-07-17 07:49:59,927  [DEBUG] 
 
2025-07-17 07:50:08,014  [DEBUG] [E][20:30:26][GNSS]GPS module no nmea data!
 
2025-07-17 07:50:18,044  [DEBUG] [E][20:30:36][GNSS]GPS module no nmea data!
 
2025-07-17 07:50:27,448  [DEBUG] [W][20:30:45][COMM]Power Off
 
2025-07-17 07:50:28,098  [DEBUG] [E][20:30:46][GNSS]GPS module no nmea data!
 
2025-07-17 07:50:38,102  [DEBUG] [E][20:30:56][GNSS]GPS module no nmea data!
 
2025-07-17 07:50:48,148  [DEBUG] [E][20:31:06][GNSS]GPS module no nmea data!
 
2025-07-17 07:50:58,174  [DEBUG] [E][20:31:16][GNSS]GPS module no nmea data!
 
2025-07-17 07:51:08,203  [DEBUG] [E][20:31:26][GNSS]GPS module no nmea data!
 
2025-07-17 07:51:18,229  [DEBUG] [E][20:31:36][GNSS]GPS module no nmea data!
 
2025-07-17 07:51:28,266  [DEBUG] [E][20:31:46][GNSS]GPS module no nmea data!
 
2025-07-17 07:51:38,311  [DEBUG] [E][20:31:56][GNSS]GPS module no nmea data!
 
2025-07-17 07:51:48,336  [DEBUG] [E][20:32:06][GNSS]GPS module no nmea data!
 
2025-07-17 07:51:58,391  [DEBUG] [E][20:32:16][GNSS]GPS module no nmea data!
 
2025-07-17 07:52:08,434  [DEBUG] [E][20:32:26][GNSS]GPS module no nmea data!
 
2025-07-17 07:52:18,459  [DEBUG] [E][20:32:36][GNSS]GPS module no nmea data!
 
2025-07-17 07:52:19,341  [DEBUG] [E][20:32:37][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-17 07:52:28,495  [DEBUG] [E][20:32:46][GNSS]GPS module no nmea data!
 
2025-07-17 07:52:29,376  [DEBUG] [W][20:32:47][CAT1]gsm_module_reboot
 
2025-07-17 07:52:29,376  [DEBUG] 
 
2025-07-17 07:52:38,527  [DEBUG] [E][20:32:56][GNSS]GPS module no nmea data!
 
2025-07-17 07:52:38,792  [DEBUG] [W][20:32:56][COMM]get bat state1 error
 
2025-07-17 07:52:38,793  [DEBUG] [W][20:32:56][COMM]get mc state information fail
 
2025-07-17 07:52:38,798  [DEBUG] [W][20:32:56][COMM]get mc speed information fail
 
2025-07-17 07:52:38,803  [DEBUG] [W][20:32:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-17 07:52:38,815  [DEBUG] [W][20:32:56][COMM]5F04 LocFail:reason:0x01;diff:73976;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-17 07:52:38,821  [DEBUG] [W][20:32:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-17 07:52:38,826  [DEBUG] [W][20:32:56][COMM]get mc power mode information fail
 
2025-07-17 07:52:38,833  [DEBUG] [W][20:32:56][PROT]remove success[1730233976],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-17 07:52:38,842  [DEBUG] [W][20:32:56][PROT]add success [1730233976],send_path[2],type[5F04],priority[0],index[3],used[1]
 
2025-07-17 07:52:41,100  [DEBUG] [W][20:32:59][PROT]remove success[1730233979],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-17 07:52:41,108  [DEBUG] [W][20:32:59][PROT]add success [1730233979],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-17 07:52:41,111  [DEBUG] [W][20:32:59][COMM]get soc error
 
2025-07-17 07:52:41,114  [DEBUG] [W][20:32:59][GNSS]stop locating
 
2025-07-17 07:52:41,116  [DEBUG] [W][20:32:59][GNSS]sing locating running
 
2025-07-17 07:52:41,123  [DEBUG] [E][20:32:59][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-17 07:52:41,131  [DEBUG] [W][20:32:59][PROT]remove success[1730233979],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-17 07:52:41,139  [DEBUG] [W][20:32:59][PROT]add success [1730233979],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-17 07:52:41,150  [DEBUG] [W][20:32:59][PROT]remove success[1730233979],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-17 07:52:41,159  [DEBUG] [W][20:32:59][PROT]add success [1730233979],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-17 07:52:41,167  [DEBUG] [W][20:32:59][PROT]remove success[1730233979],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-17 07:52:41,176  [DEBUG] [W][20:32:59][PROT]add success [1730233979],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-17 07:52:41,187  [DEBUG] [W][20:32:59][COMM]5A07 LocFail:reason:0x01;diff:73979;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-17 07:52:41,192  [DEBUG] [W][20:32:59][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-17 07:52:41,201  [DEBUG] [W][20:32:59][PROT]remove success[1730233979],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-17 07:52:41,210  [DEBUG] [W][20:32:59][PROT]add success [1730233979],send_path[2],type[5A07],priority[0],index[8],used[1]
 
2025-07-17 07:52:41,217  [DEBUG] [W][20:32:59][PROT]remove success[1730233979],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-17 07:52:41,226  [DEBUG] [W][20:32:59][PROT]add success [1730233979],send_path[2],type[D302],priority[0],index[9],used[1]
 
2025-07-17 07:53:00,504  [DEBUG] [W][20:33:18][COMM]Power Off
 
2025-07-17 07:54:24,775  [DEBUG] [E][20:34:42][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-17 07:54:24,779  [DEBUG] [E][20:34:42][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-17 07:54:24,787  [DEBUG] [E][20:34:42][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-17 07:54:24,799  [DEBUG] [W][20:34:42][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730234082]
 
2025-07-17 07:54:24,809  [DEBUG] [E][20:34:42][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-17 07:54:24,813  [DEBUG] [E][20:34:42][M2M ]m2m send data len err[-1,134]
 
2025-07-17 07:54:24,819  [DEBUG] [E][20:34:42][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-17 07:54:24,823  [DEBUG] [E][20:34:42][PROT]M2M Send Fail [1730234082]
 
2025-07-17 07:54:27,231  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:54:27,234  [DEBUG] flash is 24bit address mode

 
2025-07-17 07:54:27,236  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-17 07:54:27,239  [DEBUG] HW SW version: 5340 109

 
2025-07-17 07:54:27,242  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-17 07:54:27,246  [DEBUG] get_boot_mode a5a5
 
2025-07-17 07:54:27,246  [DEBUG] is_app_complete 1
 
2025-07-17 07:54:27,445  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:54:27,449  [DEBUG] [ADC]Timer status: enabled

 
2025-07-17 07:54:27,449  [DEBUG] [ADC]init adc success.

 
2025-07-17 07:54:28,078  [DEBUG] para ret:306,valid:aa

 
2025-07-17 07:54:28,094  [DEBUG] [W][20:34:44][COMM]BKP RESET_MODE[a5a5], reason[1-1]
 
2025-07-17 07:54:28,098  [DEBUG] [E][20:34:44][COMM]RESETREAS:0x00000008
 
2025-07-17 07:54:28,104  [DEBUG] [E][20:34:44][COMM]Multirider mode not support: 255
 
2025-07-17 07:54:28,109  [DEBUG] [W][20:34:44][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:54:28,112  [DEBUG] [W][20:34:44][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:54:28,117  [DEBUG] [W][20:34:44][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:54:28,120  [DEBUG] [W][20:34:44][FCTY]DeviceID    = 460130020284403
 
2025-07-17 07:54:28,126  [DEBUG] [W][20:34:44][FCTY]HardwareID  = 868667086862221
 
2025-07-17 07:54:28,128  [DEBUG] [W][20:34:44][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:54:28,134  [DEBUG] [W][20:34:44][FCTY]LockID      = F050821689
 
2025-07-17 07:54:28,137  [DEBUG] [W][20:34:44][FCTY]BLEFWVersion= 105
 
2025-07-17 07:54:28,142  [DEBUG] [W][20:34:44][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-17 07:54:28,145  [DEBUG] [W][20:34:44][FCTY]Bat         = 0 mv
 
2025-07-17 07:54:28,148  [DEBUG] [W][20:34:44][FCTY]Current     = 0 ma
 
2025-07-17 07:54:28,150  [DEBUG] [W][20:34:44][FCTY]VBUS        = 0 mv
 
2025-07-17 07:54:28,157  [DEBUG] [W][20:34:44][FCTY]TEMP= 0,BATID= 663732,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:54:28,162  [DEBUG] [W][20:34:44][FCTY]Ext battery vol = 2, adc = 87
 
2025-07-17 07:54:28,165  [DEBUG] [W][20:34:44][FCTY]Bike Type flag is invalied
 
2025-07-17 07:54:28,171  [DEBUG] [W][20:34:44][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:54:28,173  [DEBUG] [W][20:34:44][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:54:28,176  [DEBUG] [W][20:34:44][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:54:28,179  [DEBUG] [W][20:34:44][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:54:28,184  [DEBUG] [W][20:34:44][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:54:28,187  [DEBUG] [W][20:34:44][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:54:28,191  [DEBUG] [W][20:34:44][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:54:28,251  [DEBUG] [W][20:34:44][GNSS]start sing locating
 
2025-07-17 07:54:28,651  [DEBUG] [E][20:34:45][COMM]1x1 rx timeout
 
2025-07-17 07:54:29,055  [DEBUG] [E][20:34:45][COMM]1x1 rx timeout
 
2025-07-17 07:54:29,057  [DEBUG] [E][20:34:45][COMM]1x1 tp timeout
 
2025-07-17 07:54:29,060  [DEBUG] [E][20:34:45][COMM]1x1 error -3.
 
2025-07-17 07:54:29,063  [DEBUG] [W][20:34:45][COMM]Bat auth off fail, error:-1
 
2025-07-17 07:54:29,069  [DEBUG] [E][20:34:45][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-17 07:54:29,071  [DEBUG] [W][20:34:45][COMM]Init MC LOCK_STATE 2
 
2025-07-17 07:54:29,083  [DEBUG] [W][20:34:45][PROT]remove success[1730234085],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:54:29,090  [DEBUG] [W][20:34:45][PROT]add success [1730234085],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:54:30,172  [DEBUG] [W][20:34:46][PROT]remove success[1730234086],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:54:30,179  [DEBUG] [W][20:34:46][PROT]add success [1730234086],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-17 07:54:40,146  [DEBUG] [W][20:34:56][PROT]remove success[1730234096],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-17 07:54:40,153  [DEBUG] [W][20:34:56][PROT]add success [1730234096],send_path[2],type[5103],priority[0],index[2],used[1]
 
2025-07-17 07:54:42,429  [DEBUG] [W][20:34:58][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730234098]
 
2025-07-17 07:54:47,842  [DEBUG] [W][20:35:04][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730234104]
 
2025-07-17 07:54:53,243  [DEBUG] [W][20:35:09][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730234109]
 
2025-07-17 07:54:58,648  [DEBUG] [W][20:35:15][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730234115]
 
2025-07-17 07:55:04,054  [DEBUG] [W][20:35:20][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730234120]
 
2025-07-17 07:56:04,952  [DEBUG] [W][20:36:21][COMM]Power Off
 
2025-07-17 07:56:45,890  [DEBUG] [E][20:37:02][GNSS]GPS module no nmea data!
 
2025-07-17 07:56:55,915  [DEBUG] [E][20:37:12][GNSS]GPS module no nmea data!
 
2025-07-17 07:57:05,934  [DEBUG] [E][20:37:22][GNSS]GPS module no nmea data!
 
2025-07-17 07:57:15,952  [DEBUG] [E][20:37:32][GNSS]GPS module no nmea data!
 
2025-07-17 07:57:25,970  [DEBUG] [E][20:37:42][GNSS]GPS module no nmea data!
 
2025-07-17 07:57:29,526  [DEBUG] [E][20:37:46][COMM]1x1 rx timeout
 
2025-07-17 07:57:29,932  [DEBUG] [E][20:37:46][COMM]1x1 rx timeout
 
2025-07-17 07:57:29,934  [DEBUG] [E][20:37:46][COMM]1x1 tp timeout
 
2025-07-17 07:57:29,936  [DEBUG] [E][20:37:46][COMM]1x1 error -3.
 
2025-07-17 07:57:29,939  [DEBUG] [E][20:37:46][COMM]frm_mc_open_mos failed.
 
2025-07-17 07:57:29,947  [DEBUG] [W][20:37:46][PROT]remove success[1730234266],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:57:29,955  [DEBUG] [W][20:37:46][PROT]add success [1730234266],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-17 07:57:29,962  [DEBUG] [W][20:37:46][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730234266]
 
2025-07-17 07:57:29,973  [DEBUG] [W][20:37:46][COMM]5A07 LocFail:reason:0x07;diff:74266;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-17 07:57:29,978  [DEBUG] [W][20:37:46][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-17 07:57:29,988  [DEBUG] [W][20:37:46][PROT]remove success[1730234266],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:57:29,996  [DEBUG] [W][20:37:46][PROT]add success [1730234266],send_path[2],type[5A07],priority[0],index[1],used[1]
 
2025-07-17 07:57:54,917  [DEBUG] ??????????*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:57:54,920  [DEBUG] flash is 24bit address mode

 
2025-07-17 07:57:54,922  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-17 07:57:54,925  [DEBUG] HW SW version: 5340 109

 
2025-07-17 07:57:54,927  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-17 07:57:54,931  [DEBUG] get_boot_mode 0
 
2025-07-17 07:57:54,931  [DEBUG] is_app_complete 0
 
2025-07-17 07:57:55,130  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:57:55,133  [DEBUG] [ADC]Timer status: enabled

 
2025-07-17 07:57:55,134  [DEBUG] [ADC]init adc success.

 
2025-07-17 07:57:55,765  [DEBUG] para ret:306,valid:aa

 
2025-07-17 07:57:55,844  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-17 07:57:55,848  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-17 07:57:55,853  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-17 07:57:55,858  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:57:55,863  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:57:55,866  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:57:55,871  [DEBUG] [W][11:26:37][FCTY]DeviceID    = *************59
 
2025-07-17 07:57:55,874  [DEBUG] [W][11:26:37][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:57:55,880  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:57:55,883  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:57:55,886  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-17 07:57:55,891  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:57:55,894  [DEBUG] [W][11:26:37][FCTY]Bat         = 3844 mv
 
2025-07-17 07:57:55,897  [DEBUG] [W][11:26:37][FCTY]Current     = 350 ma
 
2025-07-17 07:57:55,902  [DEBUG] [W][11:26:37][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:57:55,908  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:57:55,912  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 8
 
2025-07-17 07:57:55,916  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-17 07:57:55,919  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:57:55,925  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:57:55,927  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:57:55,930  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:57:55,933  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:57:55,936  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:57:55,942  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:57:55,944  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-17 07:57:56,297  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-17 07:57:56,702  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-17 07:57:56,704  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-17 07:57:56,707  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-17 07:57:56,710  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-17 07:57:56,716  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-17 07:57:56,719  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-17 07:57:56,730  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:57:56,737  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:57:57,925  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:57:57,932  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-17 07:57:59,824  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-17 07:57:59,832  [DEBUG] [D][11:26:41][COMM]Password OK
 
2025-07-17 07:57:59,835  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44B00] --- write len --- [256]
 
2025-07-17 07:57:59,837  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:57:59,845  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:57:59,851  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:57:59,854  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:57:59,859  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:57:59,865  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:57:59,867  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************59
 
2025-07-17 07:57:59,873  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:57:59,876  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:57:59,881  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:57:59,888  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44C00] --- write len --- [256]
 
2025-07-17 07:57:59,889  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:57:59,897  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:57:59,899  [DEBUG] [W][11:26:41][FCTY]Bat         = 3824 mv
 
2025-07-17 07:57:59,901  [DEBUG] [W][11:26:41][FCTY]Current     = 50 ma
 
2025-07-17 07:57:59,906  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:57:59,912  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:57:59,914  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 24
 
2025-07-17 07:57:59,920  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:57:59,926  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:57:59,928  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:57:59,932  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:57:59,937  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:57:59,940  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:57:59,945  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:57:59,946  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:57:59,949  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:57:59,957  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:57:59,963  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:57:59,968  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:57:59,973  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:57:59,980  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:57:59,982  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:57:59,991  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44D00] --- write len --- [256]
 
2025-07-17 07:57:59,995  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:57:59,998  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:00,003  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************59
 
2025-07-17 07:58:00,006  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:58:00,012  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:00,015  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:00,017  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:00,023  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:58:00,026  [DEBUG] [W][11:26:41][FCTY]Bat         = 3824 mv
 
2025-07-17 07:58:00,032  [DEBUG] [D][11:26:41][HSDK]need to erase for write: is[0x0] ie[0x3E00]
 
2025-07-17 07:58:00,040  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44E00] --- write len --- [256]
 
2025-07-17 07:58:00,042  [DEBUG] [W][11:26:41][FCTY]Current     = 50 ma
 
2025-07-17 07:58:00,045  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:00,051  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:00,057  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 22
 
2025-07-17 07:58:00,063  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 50 mv
 
2025-07-17 07:58:00,065  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:00,071  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:00,073  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:00,076  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:00,079  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:00,085  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:00,088  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:00,090  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:00,099  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,102  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,107  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:00,115  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,121  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,123  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:00,128  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:00,136  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44F00] --- write len --- [256]
 
2025-07-17 07:58:00,140  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:00,143  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************59
 
2025-07-17 07:58:00,149  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:58:00,151  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:00,157  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:00,159  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:00,162  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:58:00,168  [DEBUG] [W][11:26:41][FCTY]Bat         = 3824 mv
 
2025-07-17 07:58:00,170  [DEBUG] [W][11:26:41][FCTY]Current     = 50 ma
 
2025-07-17 07:58:00,173  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:00,183  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45000] --- write len --- [256]
 
2025-07-17 07:58:00,188  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:00,193  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 19
 
2025-07-17 07:58:00,199  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 75 mv
 
2025-07-17 07:58:00,202  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:00,204  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:00,210  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:00,213  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:00,216  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:00,220  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:00,222  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:00,227  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:00,233  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,238  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,243  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:00,249  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,255  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,260  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:00,263  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:00,268  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:00,275  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45100] --- write len --- [256]
 
2025-07-17 07:58:00,279  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************59
 
2025-07-17 07:58:00,285  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:58:00,287  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:00,290  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:00,298  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:00,300  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:58:00,302  [DEBUG] [W][11:26:41][FCTY]Bat         = 3824 mv
 
2025-07-17 07:58:00,307  [DEBUG] [W][11:26:41][FCTY]Current     = 50 ma
 
2025-07-17 07:58:00,310  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:00,316  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 668764,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:00,321  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 25
 
2025-07-17 07:58:00,328  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 50 mv
 
2025-07-17 07:58:00,330  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:00,333  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:00,341  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45200] --- write len --- [256]
 
2025-07-17 07:58:00,344  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:00,347  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:00,352  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:00,355  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:00,357  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:00,361  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:00,370  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,374  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,377  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:00,385  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,392  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,394  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:00,399  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:00,404  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:00,408  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************59
 
2025-07-17 07:58:00,416  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45300] --- write len --- [256]
 
2025-07-17 07:58:00,419  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:58:00,424  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:00,427  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:00,429  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:00,435  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:58:00,438  [DEBUG] [W][11:26:41][FCTY]Bat         = 3844 mv
 
2025-07-17 07:58:00,441  [DEBUG] [W][11:26:41][FCTY]Current     = 150 ma
 
2025-07-17 07:58:00,446  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:00,452  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:00,455  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-17 07:58:00,461  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 75 mv
 
2025-07-17 07:58:00,466  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:00,469  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:00,471  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:00,474  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:00,481  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:00,543  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:00,546  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:00,554  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45400] --- write len --- [256]
 
2025-07-17 07:58:00,557  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:00,565  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,570  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,576  [DEBUG] [D][11:26:41][COMM]msg 02AC loss. last_tick:0. cur_tick:5001. period:500
 
2025-07-17 07:58:00,577  [DEBUG] 
 
2025-07-17 07:58:00,584  [DEBUG] [D][11:26:41][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5002. period:500. j,i:6 59
 
2025-07-17 07:58:00,588  [DEBUG] 
 
2025-07-17 07:58:00,590  [DEBUG] [D][11:26:41][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5003. period:500. j,i:8 61
 
2025-07-17 07:58:00,592  [DEBUG] 
 
2025-07-17 07:58:00,599  [DEBUG] [D][11:26:41][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5003. period:500. j,i:9 62
 
2025-07-17 07:58:00,599  [DEBUG] 
 
2025-07-17 07:58:00,607  [DEBUG] [D][11:26:41][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5003. period:500. j,i:10 63
 
2025-07-17 07:58:00,607  [DEBUG] 
 
2025-07-17 07:58:00,616  [DEBUG] [D][11:26:41][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5004. period:500. j,i:11 64
 
2025-07-17 07:58:00,616  [DEBUG] 
 
2025-07-17 07:58:00,624  [DEBUG] [D][11:26:41][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5004. period:500. j,i:12 65
 
2025-07-17 07:58:00,624  [DEBUG] 
 
2025-07-17 07:58:00,654  [DEBUG] [D][11:26:41][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5005. period:500. j,i:21 74
 
2025-07-17 07:58:00,655  [DEBUG] 
 
2025-07-17 07:58:00,659  [DEBUG] [D][11:26:41][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5005. period:500. j,i:22 75
 
2025-07-17 07:58:00,660  [DEBUG] 
 
2025-07-17 07:58:00,667  [DEBUG] [D][11:26:41][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:23 76
 
2025-07-17 07:58:00,668  [DEBUG] 
 
2025-07-17 07:58:00,676  [DEBUG] [D][11:26:41][COMM]bat msg 025B loss. last_tick:0. cur_tick:5006. period:500. j,i:25 78
 
2025-07-17 07:58:00,676  [DEBUG] 
 
2025-07-17 07:58:00,684  [DEBUG] [D][11:26:41][COMM]bat msg 025C loss. last_tick:0. cur_tick:5007. period:500. j,i:26 79
 
2025-07-17 07:58:00,684  [DEBUG] 
 
2025-07-17 07:58:00,692  [DEBUG] [D][11:26:41][COMM]sd485 msg 025C loss. last_tick:0. cur_tick:5007. period:500. j,i:0 79
 
2025-07-17 07:58:00,701  [DEBUG] [D][11:26:41][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008E00C71E22217 5008
 
2025-07-17 07:58:00,706  [DEBUG] [D][11:26:41][COMM]CAN message bat fault change: 0x000600BC->0x06E61FFC 5008
 
2025-07-17 07:58:00,714  [DEBUG] [D][11:26:41][COMM]CAN message sd485 fault change: 0x0000000E->0x0000000F 5009
 
2025-07-17 07:58:00,720  [DEBUG] [D][11:26:41][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010E05 5009
 
2025-07-17 07:58:00,725  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:00,735  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,736  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,741  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:00,744  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:00,749  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:00,755  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************59
 
2025-07-17 07:58:00,758  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:58:00,763  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:00,770  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45500] --- write len --- [256]
 
2025-07-17 07:58:00,772  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:00,777  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:00,780  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:58:00,783  [DEBUG] [W][11:26:42][FCTY]Bat         = 3844 mv
 
2025-07-17 07:58:00,789  [DEBUG] [W][11:26:42][FCTY]Current     = 150 ma
 
2025-07-17 07:58:00,791  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:00,797  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:00,802  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-17 07:58:00,809  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 151 mv
 
2025-07-17 07:58:00,812  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:00,817  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:00,820  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:00,823  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:00,825  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:00,828  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:00,834  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:00,836  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:00,845  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,848  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,854  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+LOCKID=F050821690<<<<<
 
2025-07-17 07:58:00,859  [DEBUG] [D][11:26:42][COMM]dual backup valid_state=0x11
 
2025-07-17 07:58:00,861  [DEBUG] [D][11:26:42][COMM]dual bank write result:0
 
2025-07-17 07:58:00,867  [DEBUG] [D][11:26:42][COMM]set lockid successfully
 
2025-07-17 07:58:00,870  [DEBUG] [D][11:26:42][COMM]5689 imu init OK
 
2025-07-17 07:58:00,875  [DEBUG] [D][11:26:42][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:00,894  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45600] --- write len --- [256]
 
2025-07-17 07:58:00,900  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:00,904  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:00,910  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:00,915  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:00,918  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:00,923  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:00,926  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************59
 
2025-07-17 07:58:00,932  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087132087
 
2025-07-17 07:58:00,935  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:00,941  [DEBUG] [W][11:26:42][FCTY]LockID      = F050821690
 
2025-07-17 07:58:01,016  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45700] --- write len --- [256]
 
2025-07-17 07:58:01,017  [DEBUG] [D][11:26:42][CAT1]power_urc_cb ret[5]
 
2025-07-17 07:58:01,018  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:01,019  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = C036C948D048
 
2025-07-17 07:58:01,020  [DEBUG] [W][11:26:42][FCTY]Bat         = 3844 mv
 
2025-07-17 07:58:01,021  [DEBUG] [W][11:26:42][FCTY]Current     = 150 ma
 
2025-07-17 07:58:01,021  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:01,022  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:01,025  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 29
 
2025-07-17 07:58:01,025  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 75 mv
 
2025-07-17 07:58:01,026  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:01,026  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:01,028  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:01,028  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:01,029  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:01,030  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:01,030  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:01,030  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:01,031  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:01,031  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:01,036  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-17 07:58:01,124  [DEBUG] [D][11:26:42][COMM]dual backup valid_state=0x11
 
2025-07-17 07:58:01,259  [DEBUG] [D][11:26:43][COMM]dual bank write result:0
 
2025-07-17 07:58:01,262  [DEBUG] [D][11:26:43][COMM]set lockname successfully
 
2025-07-17 07:58:01,826  [DEBUG] [D][11:26:43][COMM]6699 imu init OK
 
2025-07-17 07:58:01,837  [DEBUG] [D][11:26:43][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:02,837  [DEBUG] [D][11:26:44][COMM]7710 imu init OK
 
2025-07-17 07:58:02,849  [DEBUG] [D][11:26:44][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:07,177  [DEBUG]   ???????????*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:58:07,179  [DEBUG] flash is 24bit address mode

 
2025-07-17 07:58:07,182  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-17 07:58:07,184  [DEBUG] HW SW version: 5340 109

 
2025-07-17 07:58:07,187  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-17 07:58:07,190  [DEBUG] get_boot_mode 0
 
2025-07-17 07:58:07,190  [DEBUG] is_app_complete 0
 
2025-07-17 07:58:07,390  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:58:07,393  [DEBUG] [ADC]Timer status: enabled

 
2025-07-17 07:58:07,394  [DEBUG] [ADC]init adc success.

 
2025-07-17 07:58:08,023  [DEBUG] para ret:306,valid:aa

 
2025-07-17 07:58:08,103  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-17 07:58:08,105  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-17 07:58:08,112  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-17 07:58:08,117  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:08,123  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:08,125  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:08,131  [DEBUG] [W][11:26:37][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:08,134  [DEBUG] [W][11:26:37][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:08,139  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:08,142  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:08,145  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:08,150  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:08,153  [DEBUG] [W][11:26:37][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:08,156  [DEBUG] [W][11:26:37][FCTY]Current     = 350 ma
 
2025-07-17 07:58:08,161  [DEBUG] [W][11:26:37][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:08,172  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:08,175  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 8
 
2025-07-17 07:58:08,181  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:08,184  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:08,186  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:08,189  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:08,195  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:08,198  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:08,200  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:08,202  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:08,207  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-17 07:58:08,577  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-17 07:58:08,983  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-17 07:58:08,985  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-17 07:58:08,987  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-17 07:58:08,990  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-17 07:58:08,996  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-17 07:58:08,998  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-17 07:58:09,009  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:58:09,017  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:58:10,201  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:58:10,208  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-17 07:58:12,345  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-17 07:58:12,351  [DEBUG] [D][11:26:41][COMM]Password OK
 
2025-07-17 07:58:12,356  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45000] --- write len --- [256]
 
2025-07-17 07:58:12,359  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:12,367  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,373  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,375  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:12,381  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:12,387  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:12,389  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:12,395  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:12,398  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:12,403  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:12,409  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45100] --- write len --- [256]
 
2025-07-17 07:58:12,411  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:12,417  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:12,420  [DEBUG] [W][11:26:41][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:12,423  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-17 07:58:12,428  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:12,434  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 663732,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:12,437  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 21
 
2025-07-17 07:58:12,443  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5445 mv, Acckey2 vol = 126 mv
 
2025-07-17 07:58:12,448  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:12,451  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:12,453  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:12,459  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:12,462  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:12,465  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:12,467  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:12,473  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:12,480  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,485  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,490  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:12,495  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,501  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,506  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:12,512  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45200] --- write len --- [256]
 
2025-07-17 07:58:12,517  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:12,520  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:12,526  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:12,528  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:12,534  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:12,537  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:12,539  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:12,546  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:12,548  [DEBUG] [W][11:26:41][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:12,558  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45300] --- write len --- [256]
 
2025-07-17 07:58:12,559  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-17 07:58:12,562  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:12,571  [DEBUG] [D][11:26:42][COMM]msg 02AC loss. last_tick:0. cur_tick:5037. period:500
 
2025-07-17 07:58:12,572  [DEBUG] 
 
2025-07-17 07:58:12,577  [DEBUG] [D][11:26:42][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5037. period:500. j,i:6 59
 
2025-07-17 07:58:12,577  [DEBUG] 
 
2025-07-17 07:58:12,585  [DEBUG] [D][11:26:42][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5038. period:500. j,i:8 61
 
2025-07-17 07:58:12,585  [DEBUG] 
 
2025-07-17 07:58:12,594  [DEBUG] [D][11:26:42][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5039. period:500. j,i:9 62
 
2025-07-17 07:58:12,594  [DEBUG] 
 
2025-07-17 07:58:12,602  [DEBUG] [D][11:26:42][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5039. period:500. j,i:10 63
 
2025-07-17 07:58:12,602  [DEBUG] 
 
2025-07-17 07:58:12,611  [DEBUG] [D][11:26:42][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5039. period:500. j,i:11 64
 
2025-07-17 07:58:12,611  [DEBUG] 
 
2025-07-17 07:58:12,621  [DEBUG] [D][11:26:42][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5040. period:500. j,i:12 65
 
2025-07-17 07:58:12,622  [DEBUG] 
 
2025-07-17 07:58:12,625  [DEBUG] [D][11:26:42][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5040. period:500. j,i:21 74
 
2025-07-17 07:58:12,625  [DEBUG] 
 
2025-07-17 07:58:12,633  [DEBUG] [D][11:26:42][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5041. period:500. j,i:22 75
 
2025-07-17 07:58:12,634  [DEBUG] 
 
2025-07-17 07:58:12,641  [DEBUG] [D][11:26:42][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5041. period:500. j,i:23 76
 
2025-07-17 07:58:12,642  [DEBUG] 
 
2025-07-17 07:58:12,650  [DEBUG] [D][11:26:42][COMM]bat msg 025B loss. last_tick:0. cur_tick:5042. period:500. j,i:25 78
 
2025-07-17 07:58:12,651  [DEBUG] 
 
2025-07-17 07:58:12,657  [DEBUG] [D][11:26:42][COMM]bat msg 025C loss. last_tick:0. cur_tick:5042. period:500. j,i:26 79
 
2025-07-17 07:58:12,658  [DEBUG] 
 
2025-07-17 07:58:12,666  [DEBUG] [D][11:26:42][COMM]sd485 msg 025C loss. last_tick:0. cur_tick:5043. period:500. j,i:0 79
 
2025-07-17 07:58:12,671  [DEBUG] [D][11:26:42][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008E00C71E22217 5043
 
2025-07-17 07:58:12,680  [DEBUG] [D][11:26:42][COMM]CAN message bat fault change: 0x000600BC->0x06E61FFC 5044
 
2025-07-17 07:58:12,686  [DEBUG] [D][11:26:42][COMM]CAN message sd485 fault change: 0x0000000E->0x0000000F 5044
 
2025-07-17 07:58:12,693  [DEBUG] [D][11:26:42][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010E05 5045
 
2025-07-17 07:58:12,698  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:12,704  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 24
 
2025-07-17 07:58:12,711  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5443 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:12,713  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:12,716  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:12,721  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:12,723  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:12,727  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:12,730  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:12,735  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:12,738  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:12,745  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,750  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,755  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:12,762  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,766  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,771  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:12,774  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:12,782  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45400] --- write len --- [256]
 
2025-07-17 07:58:12,788  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:12,790  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:12,796  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:12,799  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:12,804  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:12,807  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:12,809  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:12,816  [DEBUG] [W][11:26:42][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:12,818  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-17 07:58:12,821  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:12,829  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45500] --- write len --- [256]
 
2025-07-17 07:58:12,835  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:12,838  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 15
 
2025-07-17 07:58:12,844  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5445 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:12,850  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:12,852  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:12,854  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:12,860  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:12,863  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:12,866  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:12,868  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:12,871  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:12,880  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,887  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,893  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:12,896  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:12,902  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:12,904  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:12,910  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:12,915  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:12,922  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45600] --- write len --- [256]
 
2025-07-17 07:58:12,926  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:12,929  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:12,934  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:12,938  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:12,941  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:12,946  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:12,949  [DEBUG] [W][11:26:42][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:12,952  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-17 07:58:12,957  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:12,963  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:12,968  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 19
 
2025-07-17 07:58:12,975  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5452 mv, Acckey2 vol = 25 mv
 
2025-07-17 07:58:12,978  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:12,981  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:12,988  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45700] --- write len --- [256]
 
2025-07-17 07:58:12,991  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:12,994  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:12,996  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:13,003  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:13,005  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:13,008  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:13,016  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,020  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,025  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:13,033  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,039  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,041  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:13,047  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:13,049  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:13,130  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:13,137  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45800] --- write len --- [256]
 
2025-07-17 07:58:13,141  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:13,143  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:13,151  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:13,152  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:13,155  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:13,161  [DEBUG] [W][11:26:42][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:13,163  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-17 07:58:13,166  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:13,172  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:13,177  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 19
 
2025-07-17 07:58:13,183  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5465 mv, Acckey2 vol = 101 mv
 
2025-07-17 07:58:13,186  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:13,191  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:13,194  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:13,196  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:13,199  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:13,205  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:13,208  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:13,213  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45900] --- write len --- [256]
 
2025-07-17 07:58:13,241  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:13,247  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,253  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,259  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:13,264  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,271  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,274  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:13,277  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:13,283  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:13,286  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:13,291  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:13,294  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:13,303  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45A00] --- write len --- [256]
 
2025-07-17 07:58:13,305  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:13,308  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:13,314  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:13,317  [DEBUG] [W][11:26:42][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:13,322  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-17 07:58:13,325  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:13,330  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:13,336  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 30
 
2025-07-17 07:58:13,342  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5450 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:13,345  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:13,347  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:13,352  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:13,356  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:13,360  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:13,361  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:13,364  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:13,369  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:13,375  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,381  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,386  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+LOCKID=F050821691<<<<<
 
2025-07-17 07:58:13,391  [DEBUG] [D][11:26:42][COMM]dual backup valid_state=0x11
 
2025-07-17 07:58:13,395  [DEBUG] [D][11:26:42][CAT1]power_urc_cb ret[5]
 
2025-07-17 07:58:13,398  [DEBUG] [D][11:26:42][COMM]dual bank write result:0
 
2025-07-17 07:58:13,403  [DEBUG] [D][11:26:42][COMM]set lockid successfully
 
2025-07-17 07:58:13,406  [DEBUG] [D][11:26:42][COMM]5883 imu init OK
 
2025-07-17 07:58:13,409  [DEBUG] [D][11:26:42][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:13,428  [DEBUG] [D][11:26:43][HSDK][0] flush to flash addr:[0xE45B00] --- write len --- [256]
 
2025-07-17 07:58:13,434  [DEBUG] [W][11:26:43][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:13,438  [DEBUG] [D][11:26:43][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,444  [DEBUG] [D][11:26:43][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,450  [DEBUG] [W][11:26:43][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:13,452  [DEBUG] [W][11:26:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:13,458  [DEBUG] [W][11:26:43][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:13,461  [DEBUG] [W][11:26:43][FCTY]DeviceID    = *************05
 
2025-07-17 07:58:13,466  [DEBUG] [W][11:26:43][FCTY]HardwareID  = 868667087268428
 
2025-07-17 07:58:13,469  [DEBUG] [W][11:26:43][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:13,474  [DEBUG] [W][11:26:43][FCTY]LockID      = F050821691
 
2025-07-17 07:58:13,523  [DEBUG] [D][11:26:43][HSDK][0] flush to flash addr:[0xE45C00] --- write len --- [256]
 
2025-07-17 07:58:13,524  [DEBUG] [W][11:26:43][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:13,525  [DEBUG] [W][11:26:43][FCTY]BLEMacAddr   = DBE65C0C5BF6
 
2025-07-17 07:58:13,529  [DEBUG] [W][11:26:43][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:13,530  [DEBUG] [W][11:26:43][FCTY]Current     = 150 ma
 
2025-07-17 07:58:13,531  [DEBUG] [W][11:26:43][FCTY]VBUS        = 4500 mv
 
2025-07-17 07:58:13,533  [DEBUG] [W][11:26:43][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:13,534  [DEBUG] [W][11:26:43][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-17 07:58:13,537  [DEBUG] [D][11:26:43][FCTY]Acckey1 vol = 5449 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:13,537  [DEBUG] [W][11:26:43][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:13,538  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:13,539  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:13,539  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:13,540  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:13,540  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:13,541  [DEBUG] [W][11:26:43][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:13,544  [DEBUG] [W][11:26:43][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:13,551  [DEBUG] [D][11:26:43][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:13,556  [DEBUG] [D][11:26:43][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:13,637  [DEBUG] [W][11:26:43][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-17 07:58:13,642  [DEBUG] [D][11:26:43][COMM]dual backup valid_state=0x11
 
2025-07-17 07:58:13,773  [DEBUG] [D][11:26:43][COMM]dual bank write result:0
 
2025-07-17 07:58:13,775  [DEBUG] [D][11:26:43][COMM]set lockname successfully
 
2025-07-17 07:58:14,283  [DEBUG] [D][11:26:43][COMM]6895 imu init OK
 
2025-07-17 07:58:14,293  [DEBUG] [D][11:26:43][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:17,117  [DEBUG]  *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:58:17,120  [DEBUG] flash is 24bit address mode

 
2025-07-17 07:58:17,123  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-17 07:58:17,126  [DEBUG] HW SW version: 5340 109

 
2025-07-17 07:58:17,129  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-17 07:58:17,131  [DEBUG] get_boot_mode 0
 
2025-07-17 07:58:17,131  [DEBUG] is_app_complete 0
 
2025-07-17 07:58:17,331  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-17 07:58:17,333  [DEBUG] [ADC]Timer status: enabled

 
2025-07-17 07:58:17,335  [DEBUG] [ADC]init adc success.

 
2025-07-17 07:58:17,967  [DEBUG] para ret:306,valid:aa

 
2025-07-17 07:58:18,043  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-17 07:58:18,046  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-17 07:58:18,052  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-17 07:58:18,058  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:18,064  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:18,066  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:18,072  [DEBUG] [W][11:26:37][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:18,075  [DEBUG] [W][11:26:37][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:18,080  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:18,083  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:18,085  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:18,092  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:18,095  [DEBUG] [W][11:26:37][FCTY]Bat         = 3884 mv
 
2025-07-17 07:58:18,097  [DEBUG] [W][11:26:37][FCTY]Current     = 400 ma
 
2025-07-17 07:58:18,103  [DEBUG] [W][11:26:37][FCTY]VBUS        = 2600 mv
 
2025-07-17 07:58:18,108  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:18,114  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 3
 
2025-07-17 07:58:18,117  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:18,120  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:18,125  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:18,127  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:18,130  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:18,133  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:18,137  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:18,142  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:18,145  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-17 07:58:18,509  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-17 07:58:18,914  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-17 07:58:18,916  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-17 07:58:18,919  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-17 07:58:18,922  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-17 07:58:18,927  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-17 07:58:18,931  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-17 07:58:18,941  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:58:18,949  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-17 07:58:20,132  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-17 07:58:20,139  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-17 07:58:22,089  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-17 07:58:22,090  [DEBUG] [D][11:26:41][COMM]Password OK
 
2025-07-17 07:58:22,099  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44B00] --- write len --- [256]
 
2025-07-17 07:58:22,101  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:22,109  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,115  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,117  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:22,122  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:22,128  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:22,131  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:22,137  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:22,139  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:22,145  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:22,151  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44C00] --- write len --- [256]
 
2025-07-17 07:58:22,153  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:22,159  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:22,162  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:22,165  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-17 07:58:22,170  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:22,175  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:22,178  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 24
 
2025-07-17 07:58:22,184  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:22,190  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:22,193  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:22,196  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:22,201  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:22,204  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:22,207  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:22,209  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:22,214  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:22,222  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,227  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,232  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:22,237  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,243  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,246  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:22,255  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44D00] --- write len --- [256]
 
2025-07-17 07:58:22,259  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:22,262  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:22,268  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:22,270  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:22,276  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:22,279  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:22,281  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:22,286  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:22,289  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:22,296  [DEBUG] [D][11:26:41][HSDK]need to erase for write: is[0x0] ie[0x3E00]
 
2025-07-17 07:58:22,304  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44E00] --- write len --- [256]
 
2025-07-17 07:58:22,306  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-17 07:58:22,309  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:22,318  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:22,321  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 18
 
2025-07-17 07:58:22,327  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5489 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:22,329  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:22,335  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:22,338  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:22,340  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:22,343  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:22,349  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:22,351  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:22,354  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:22,363  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,366  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,371  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:22,379  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,385  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,387  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:22,393  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:22,399  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE44F00] --- write len --- [256]
 
2025-07-17 07:58:22,404  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:22,407  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:22,412  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:22,415  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:22,420  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:22,423  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:22,426  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:22,431  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:22,435  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-17 07:58:22,438  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:22,446  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45000] --- write len --- [256]
 
2025-07-17 07:58:22,452  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 663732,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:22,457  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 24
 
2025-07-17 07:58:22,463  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 126 mv
 
2025-07-17 07:58:22,466  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:22,468  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:22,474  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:22,476  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:22,479  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:22,483  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:22,488  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:22,491  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:22,498  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,502  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,507  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:22,512  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,518  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,523  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:22,526  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:22,532  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:22,539  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45100] --- write len --- [256]
 
2025-07-17 07:58:22,543  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:22,550  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:22,551  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:22,554  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:22,559  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:22,562  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:22,565  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:22,571  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-17 07:58:22,574  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:22,579  [DEBUG] [W][11:26:41][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:22,585  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-17 07:58:22,591  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5487 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:22,594  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:22,596  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:22,606  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45200] --- write len --- [256]
 
2025-07-17 07:58:22,608  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:22,610  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:22,616  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:22,618  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:22,621  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:22,624  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:22,634  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,638  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,641  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:22,649  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,655  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,657  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:22,663  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:22,668  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:22,671  [DEBUG] [W][11:26:41][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:22,680  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE45300] --- write len --- [256]
 
2025-07-17 07:58:22,682  [DEBUG] [W][11:26:41][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:22,688  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:22,691  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:22,696  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:22,699  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:22,702  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:22,705  [DEBUG] [W][11:26:41][FCTY]Current     = 150 ma
 
2025-07-17 07:58:22,710  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:22,729  [DEBUG] [D][11:26:41][COMM]msg 02AC loss. last_tick:0. cur_tick:5004. period:500
 
2025-07-17 07:58:22,730  [DEBUG] 
 
2025-07-17 07:58:22,737  [DEBUG] [D][11:26:41][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
 
2025-07-17 07:58:22,738  [DEBUG] 
 
2025-07-17 07:58:22,746  [DEBUG] [D][11:26:41][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:8 61
 
2025-07-17 07:58:22,746  [DEBUG] 
 
2025-07-17 07:58:22,753  [DEBUG] [D][11:26:41][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5006. period:500. j,i:9 62
 
2025-07-17 07:58:22,754  [DEBUG] 
 
2025-07-17 07:58:22,762  [DEBUG] [D][11:26:41][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:10 63
 
2025-07-17 07:58:22,763  [DEBUG] 
 
2025-07-17 07:58:22,770  [DEBUG] [D][11:26:41][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:11 64
 
2025-07-17 07:58:22,771  [DEBUG] 
 
2025-07-17 07:58:22,776  [DEBUG] [D][11:26:41][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5007. period:500. j,i:12 65
 
2025-07-17 07:58:22,777  [DEBUG] 
 
2025-07-17 07:58:22,785  [DEBUG] [D][11:26:41][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:21 74
 
2025-07-17 07:58:22,785  [DEBUG] 
 
2025-07-17 07:58:22,792  [DEBUG] [D][11:26:41][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5008. period:500. j,i:22 75
 
2025-07-17 07:58:22,794  [DEBUG] 
 
2025-07-17 07:58:22,801  [DEBUG] [D][11:26:41][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
 
2025-07-17 07:58:22,802  [DEBUG] 
 
2025-07-17 07:58:22,809  [DEBUG] [D][11:26:41][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:25 78
 
2025-07-17 07:58:22,809  [DEBUG] 
 
2025-07-17 07:58:22,841  [DEBUG] [D][11:26:41][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:26 79
 
2025-07-17 07:58:22,842  [DEBUG] 
 
2025-07-17 07:58:22,848  [DEBUG] [D][11:26:41][COMM]sd485 msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:0 79
 
2025-07-17 07:58:22,854  [DEBUG] [D][11:26:41][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008E00C71E22217 5011
 
2025-07-17 07:58:22,861  [DEBUG] [D][11:26:41][COMM]CAN message bat fault change: 0x000600BC->0x06E61FFC 5011
 
2025-07-17 07:58:22,868  [DEBUG] [D][11:26:41][COMM]CAN message sd485 fault change: 0x0000000E->0x0000000F 5012
 
2025-07-17 07:58:22,876  [DEBUG] [D][11:26:41][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010E05 5012
 
2025-07-17 07:58:22,880  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:22,886  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 19
 
2025-07-17 07:58:22,892  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:22,895  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:22,897  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:22,903  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:22,905  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:22,908  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:22,910  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:22,914  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:22,923  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45400] --- write len --- [256]
 
2025-07-17 07:58:22,925  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:22,934  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,939  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,943  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:22,950  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:22,956  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:22,958  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:22,964  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:22,967  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:22,973  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:22,977  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:22,980  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:22,988  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45500] --- write len --- [256]
 
2025-07-17 07:58:22,992  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-17 07:58:22,994  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:23,000  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:23,003  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:23,005  [DEBUG] [W][11:26:42][FCTY]Current     = 150 ma
 
2025-07-17 07:58:23,008  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:23,016  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 664984,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:23,019  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 19
 
2025-07-17 07:58:23,027  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5487 mv, Acckey2 vol = 0 mv
 
2025-07-17 07:58:23,032  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:23,034  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:23,037  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:23,040  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:23,046  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:23,050  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:23,051  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:23,054  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:23,062  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:23,068  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:23,074  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+LOCKID=F050821692<<<<<
 
2025-07-17 07:58:23,076  [DEBUG] [D][11:26:42][COMM]dual backup valid_state=0x11
 
2025-07-17 07:58:23,081  [DEBUG] [D][11:26:42][COMM]dual bank write result:0
 
2025-07-17 07:58:23,084  [DEBUG] [D][11:26:42][COMM]set lockid successfully
 
2025-07-17 07:58:23,087  [DEBUG] [D][11:26:42][COMM]5690 imu init OK
 
2025-07-17 07:58:23,093  [DEBUG] [D][11:26:42][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:23,112  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45600] --- write len --- [256]
 
2025-07-17 07:58:23,117  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-17 07:58:23,122  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:23,128  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:23,133  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-17 07:58:23,136  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-17 07:58:23,141  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-17 07:58:23,144  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************17
 
2025-07-17 07:58:23,149  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667086751622
 
2025-07-17 07:58:23,153  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-17 07:58:23,158  [DEBUG] [W][11:26:42][FCTY]LockID      = F050821692
 
2025-07-17 07:58:23,205  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45700] --- write len --- [256]
 
2025-07-17 07:58:23,206  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-17 07:58:23,207  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = DD4EDD9F13DC
 
2025-07-17 07:58:23,208  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-17 07:58:23,209  [DEBUG] [W][11:26:42][FCTY]Current     = 150 ma
 
2025-07-17 07:58:23,210  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-17 07:58:23,210  [DEBUG] [D][11:26:42][CAT1]power_urc_cb ret[5]
 
2025-07-17 07:58:23,211  [DEBUG] [W][11:26:42][FCTY]TEMP= 27,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-17 07:58:23,212  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 24
 
2025-07-17 07:58:23,215  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5489 mv, Acckey2 vol = 50 mv
 
2025-07-17 07:58:23,216  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-17 07:58:23,217  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-17 07:58:23,217  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-17 07:58:23,218  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-17 07:58:23,220  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-17 07:58:23,222  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-17 07:58:23,228  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-17 07:58:23,231  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-17 07:58:23,240  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-17 07:58:23,243  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-17 07:58:23,312  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-17 07:58:23,318  [DEBUG] [D][11:26:42][COMM]dual backup valid_state=0x11
 
2025-07-17 07:58:23,447  [DEBUG] [D][11:26:43][COMM]dual bank write result:0
 
2025-07-17 07:58:23,450  [DEBUG] [D][11:26:43][COMM]set lockname successfully
 
2025-07-17 07:58:24,029  [DEBUG] [D][11:26:43][COMM]6701 imu init OK
 
2025-07-17 07:58:24,042  [DEBUG] [D][11:26:43][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:25,040  [DEBUG] [D][11:26:44][COMM]7713 imu init OK
 
2025-07-17 07:58:25,053  [DEBUG] [D][11:26:44][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:26,053  [DEBUG] [D][11:26:45][COMM]8725 imu init OK
 
2025-07-17 07:58:26,067  [DEBUG] [D][11:26:45][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:26,142  [DEBUG] [D][11:26:45][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 0.
 
2025-07-17 07:58:26,180  [DEBUG] [D][11:26:45][CAT1]power_urc_cb ret[76]
 
2025-07-17 07:58:27,065  [DEBUG] [D][11:26:46][COMM]9738 imu init OK
 
2025-07-17 07:58:27,078  [DEBUG] [D][11:26:46][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:27,334  [DEBUG] [D][11:26:46][COMM]msg 0223 loss. last_tick:0. cur_tick:10002. period:1000
 
2025-07-17 07:58:27,336  [DEBUG] 
 
2025-07-17 07:58:27,340  [DEBUG] [D][11:26:46][COMM]msg 0225 loss. last_tick:0. cur_tick:10002. period:1000
 
2025-07-17 07:58:27,341  [DEBUG] 
 
2025-07-17 07:58:27,348  [DEBUG] [D][11:26:46][COMM]msg 0229 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-17 07:58:27,349  [DEBUG] 
 
2025-07-17 07:58:27,354  [DEBUG] [D][11:26:46][COMM]msg 0601 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-17 07:58:27,355  [DEBUG] 
 
2025-07-17 07:58:27,361  [DEBUG] [D][11:26:46][COMM]CAN message fault change: 0x0008E00C71E22217->0x0008F80C71E2223F 10004
 
2025-07-17 07:58:27,369  [DEBUG] [D][11:26:46][COMM]CAN fault change: 0x0000000300010E05->0x0000000300010F05 10005
 
2025-07-17 07:58:28,078  [DEBUG] [D][11:26:47][COMM]10749 imu init OK
 
2025-07-17 07:58:28,091  [DEBUG] [D][11:26:47][COMM]imu work error:[-1]. goto init
 
2025-07-17 07:58:29,016  [DEBUG] [D][11:26:48][CAT1]tx ret[4] >>> AT
 
2025-07-17 07:58:29,017  [DEBUG] 
 
2025-07-17 07:58:29,045  [DEBUG] [D][11:26:48][CAT1]<<< 

 
2025-07-17 07:58:29,046  [DEBUG] OK
 
2025-07-17 07:58:29,056  [DEBUG] 
 
2025-07-17 07:58:29,059  [DEBUG] [D][11:26:48][CAT1]tx ret[6] >>> ATE0
 
2025-07-17 07:58:29,061  [DEBUG] 
 
2025-07-17 07:58:29,090  [DEBUG] [D][11:26:48][COMM]imu error,enter wait
 
2025-07-17 07:58:29,092  [DEBUG] [D][11:26:48][CAT1]<<< 

 
2025-07-17 07:58:29,093  [DEBUG] OK
 
2025-07-17 07:58:29,093  [DEBUG] 
 
2025-07-17 07:58:29,105  [DEBUG] [D][11:26:48][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-17 07:58:29,105  [DEBUG] 
 
2025-07-17 07:58:29,122  [DEBUG] [D][11:26:48][CAT1]<<< 
 
2025-07-17 07:58:29,132  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"
 
2025-07-17 07:58:29,132  [DEBUG] 
 
2025-07-17 07:58:29,133  [DEBUG] OK
 
2025-07-17 07:58:29,133  [DEBUG] 
 
2025-07-17 07:58:29,136  [DEBUG] [D][11:26:48][CAT1]tx ret[10] >>> AT+CFUN?
 
2025-07-17 07:58:29,138  [DEBUG] 
 
2025-07-17 07:58:29,156  [DEBUG] [D][11:26:48][CAT1]<<< 
 
2025-07-17 07:58:29,158  [DEBUG] +CFUN: 1
 
2025-07-17 07:58:29,158  [DEBUG] 
 
2025-07-17 07:58:29,159  [DEBUG] OK
 
2025-07-17 07:58:29,161  [DEBUG] 
 
2025-07-17 07:58:29,162  [DEBUG] [D][11:26:48][CAT1]exec over: func id: 1, ret: 18
 
2025-07-17 07:58:29,167  [DEBUG] [D][11:26:48][CAT1]sub id: 1, ret: 18
 
2025-07-17 07:58:29,168  [DEBUG] 
 
2025-07-17 07:58:29,169  [DEBUG] [D][11:26:48][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:29,176  [DEBUG] [D][11:26:48][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
 
2025-07-17 07:58:29,180  [DEBUG] [D][11:26:48][SAL ]gsm power on ind rst[18]
 
2025-07-17 07:58:29,183  [DEBUG] [D][11:26:48][M2M ]m2m gsm power on, ret[0]
 
2025-07-17 07:58:29,189  [DEBUG] [D][11:26:48][COMM][Audio]exec status ready.
 
2025-07-17 07:58:29,191  [DEBUG] [D][11:26:48][COMM]Main Task receive event:1
 
2025-07-17 07:58:29,197  [DEBUG] [D][11:26:48][COMM]Main Task receive event:1 finished processing
 
2025-07-17 07:58:29,203  [DEBUG] [D][11:26:48][M2M ]m2m_task:m_m2m_thread_setting_queue:0
 
2025-07-17 07:58:29,205  [DEBUG] [D][11:26:48][M2M ]first set address
 
2025-07-17 07:58:29,210  [DEBUG] [D][11:26:48][M2M ]m2m switch to: M2M_GSM_INIT
 
2025-07-17 07:58:29,214  [DEBUG] [D][11:26:48][COMM]imu default ctrl x[0],y[0],z[10]
 
2025-07-17 07:58:29,220  [DEBUG] [D][11:26:48][M2M ]set time[24/10/29,11:26:48+32]
 
2025-07-17 07:58:29,223  [DEBUG] [D][11:26:48][CAT1]gsm read msg sub id: 31
 
2025-07-17 07:58:29,231  [DEBUG] [D][11:26:48][CAT1]tx ret[55] >>> AT+IMUCFG=200,1,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000
 
2025-07-17 07:58:29,232  [DEBUG] 
 
2025-07-17 07:58:29,235  [DEBUG] [D][11:26:48][M2M ]m2m switch to: M2M_GSM_INIT_ACK
 
2025-07-17 07:58:29,418  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,419  [DEBUG] OK
 
2025-07-17 07:58:29,419  [DEBUG] 
 
2025-07-17 07:58:29,422  [DEBUG] [D][11:26:49][CAT1]exec over: func id: 31, ret: 6
 
2025-07-17 07:58:29,431  [DEBUG] [D][11:26:49][CAT1]gsm read msg sub id: 32
 
2025-07-17 07:58:29,436  [DEBUG] [D][11:26:49][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10
 
2025-07-17 07:58:29,437  [DEBUG] 
 
2025-07-17 07:58:29,451  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,451  [DEBUG] OK
 
2025-07-17 07:58:29,452  [DEBUG] 
 
2025-07-17 07:58:29,456  [DEBUG] [D][11:26:49][CAT1]exec over: func id: 32, ret: 6
 
2025-07-17 07:58:29,465  [DEBUG] [D][11:26:49][CAT1]gsm read msg sub id: 5
 
2025-07-17 07:58:29,468  [DEBUG] [D][11:26:49][CAT1]tx ret[8] >>> AT+GSN
 
2025-07-17 07:58:29,469  [DEBUG] 
 
2025-07-17 07:58:29,518  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,518  [DEBUG] 868667086751622
 
2025-07-17 07:58:29,519  [DEBUG] 
 
2025-07-17 07:58:29,519  [DEBUG] OK
 
2025-07-17 07:58:29,526  [DEBUG] 
 
2025-07-17 07:58:29,530  [DEBUG] [D][11:26:49][CAT1]tx ret[9] >>> AT+CIMI
 
2025-07-17 07:58:29,530  [DEBUG] 
 
2025-07-17 07:58:29,547  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,550  [DEBUG] *************17
 
2025-07-17 07:58:29,551  [DEBUG] 
 
2025-07-17 07:58:29,551  [DEBUG] OK
 
2025-07-17 07:58:29,558  [DEBUG] 
 
2025-07-17 07:58:29,563  [DEBUG] [D][11:26:49][CAT1]tx ret[11] >>> AT+CMGF=0
 
2025-07-17 07:58:29,563  [DEBUG] 
 
2025-07-17 07:58:29,582  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,583  [DEBUG] OK
 
2025-07-17 07:58:29,592  [DEBUG] 
 
2025-07-17 07:58:29,595  [DEBUG] [D][11:26:49][CAT1]tx ret[15] >>> AT+CSCS="GSM"
 
2025-07-17 07:58:29,596  [DEBUG] 
 
2025-07-17 07:58:29,615  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,616  [DEBUG] OK
 
2025-07-17 07:58:29,625  [DEBUG] 
 
2025-07-17 07:58:29,629  [DEBUG] [D][11:26:49][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0
 
2025-07-17 07:58:29,630  [DEBUG] 
 
2025-07-17 07:58:29,648  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,649  [DEBUG] OK
 
2025-07-17 07:58:29,658  [DEBUG] 
 
2025-07-17 07:58:29,660  [DEBUG] [D][11:26:49][CAT1]tx ret[10] >>> AT+CPMS?
 
2025-07-17 07:58:29,661  [DEBUG] 
 
2025-07-17 07:58:29,678  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,684  [DEBUG] +CPMS: "ME",0,180,"ME",0,180,"ME",0,180
 
2025-07-17 07:58:29,684  [DEBUG] 
 
2025-07-17 07:58:29,685  [DEBUG] OK
 
2025-07-17 07:58:29,685  [DEBUG] 
 
2025-07-17 07:58:29,695  [DEBUG] [D][11:26:49][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,170,25,3
 
2025-07-17 07:58:29,696  [DEBUG] 
 
2025-07-17 07:58:29,713  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-17 07:58:29,714  [DEBUG] OK
 
2025-07-17 07:58:29,723  [DEBUG] 
 
2025-07-17 07:58:29,729  [DEBUG] [D][11:26:49][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0
 
2025-07-17 07:58:29,730  [DEBUG] 
 
2025-07-17 07:58:30,373  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,375  [DEBUG] OK
 
2025-07-17 07:58:30,384  [DEBUG] 
 
2025-07-17 07:58:30,387  [DEBUG] [D][11:26:50][CAT1]tx ret[14] >>> AT+QSCLKEX=1
 
2025-07-17 07:58:30,387  [DEBUG] 
 
2025-07-17 07:58:30,406  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,407  [DEBUG] OK
 
2025-07-17 07:58:30,416  [DEBUG] 
 
2025-07-17 07:58:30,420  [DEBUG] [D][11:26:50][CAT1]tx ret[12] >>> AT+CGATT=1
 
2025-07-17 07:58:30,421  [DEBUG] 
 
2025-07-17 07:58:30,440  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,441  [DEBUG] OK
 
2025-07-17 07:58:30,450  [DEBUG] 
 
2025-07-17 07:58:30,452  [DEBUG] [D][11:26:50][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-17 07:58:30,452  [DEBUG] 
 
2025-07-17 07:58:30,471  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,473  [DEBUG] +CGATT: 1
 
2025-07-17 07:58:30,474  [DEBUG] 
 
2025-07-17 07:58:30,474  [DEBUG] OK
 
2025-07-17 07:58:30,483  [DEBUG] 
 
2025-07-17 07:58:30,486  [DEBUG] [D][11:26:50][CAT1]tx ret[12] >>> AT+QIACT=1
 
2025-07-17 07:58:30,487  [DEBUG] 
 
2025-07-17 07:58:30,516  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,517  [DEBUG] OK
 
2025-07-17 07:58:30,526  [DEBUG] 
 
2025-07-17 07:58:30,531  [DEBUG] [D][11:26:50][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1
 
2025-07-17 07:58:30,533  [DEBUG] 
 
2025-07-17 07:58:30,549  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,550  [DEBUG] OK
 
2025-07-17 07:58:30,558  [DEBUG] 
 
2025-07-17 07:58:30,563  [DEBUG] [D][11:26:50][CAT1]tx ret[17] >>> AT+GPSMODULE=C4
 
2025-07-17 07:58:30,564  [DEBUG] 
 
2025-07-17 07:58:30,581  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,582  [DEBUG] OK
 
2025-07-17 07:58:30,583  [DEBUG] 
 
2025-07-17 07:58:30,586  [DEBUG] [D][11:26:50][CAT1]exec over: func id: 5, ret: 6
 
2025-07-17 07:58:30,589  [DEBUG] [D][11:26:50][CAT1]sub id: 5, ret: 6
 
2025-07-17 07:58:30,590  [DEBUG] 
 
2025-07-17 07:58:30,594  [DEBUG] [D][11:26:50][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:30,600  [DEBUG] [D][11:26:50][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
 
2025-07-17 07:58:30,605  [DEBUG] [D][11:26:50][M2M ]m2m gsm comm init done, ret[0]
 
2025-07-17 07:58:30,608  [DEBUG] [D][11:26:50][M2M ]M2M_GSM_INIT OK
 
2025-07-17 07:58:30,611  [DEBUG] [D][11:26:50][CAT1]gsm read msg sub id: 38
 
2025-07-17 07:58:30,616  [DEBUG] [D][11:26:50][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-17 07:58:30,619  [DEBUG] [D][11:26:50][SAL ]open socket ind id[4], rst[0]
 
2025-07-17 07:58:30,628  [DEBUG] [D][11:26:50][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-17 07:58:30,630  [DEBUG] [D][11:26:50][SAL ]Cellular task submsg id[8]
 
2025-07-17 07:58:30,644  [DEBUG] [D][11:26:50][SAL ]cellular OPEN socket size[144], msg->data[0x2005a0b0], socket[0]
 
2025-07-17 07:58:30,645  [DEBUG] [D][11:26:50][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-17 07:58:30,650  [DEBUG] [D][11:26:50][CAT1]tx ret[32] >>> AT+CCLK="24/10/29,11:26:48+32"
 
2025-07-17 07:58:30,651  [DEBUG] 
 
2025-07-17 07:58:30,657  [DEBUG] [D][11:26:50][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-17 07:58:30,685  [DEBUG] [D][11:26:50][COMM]Main Task receive event:4
 
2025-07-17 07:58:30,691  [DEBUG] [D][11:26:50][COMM]Main Task receive event:4 finished processing
 
2025-07-17 07:58:30,816  [DEBUG] [D][11:26:50][COMM]f:audio_resp_imu_data_parser. fail.[+IMUDATA=116,2053,23,131,-12,-9,-8,839711169,54107]
 
2025-07-17 07:58:30,862  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,863  [DEBUG] OK
 
2025-07-17 07:58:30,864  [DEBUG] 
 
2025-07-17 07:58:30,868  [DEBUG] [D][11:26:50][CAT1]exec over: func id: 38, ret: 6
 
2025-07-17 07:58:30,876  [DEBUG] [D][11:26:50][CAT1]gsm read msg sub id: 8
 
2025-07-17 07:58:30,878  [DEBUG] [D][11:26:50][CAT1]at ops open socket[0]
 
2025-07-17 07:58:30,883  [DEBUG] [D][11:26:50][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-17 07:58:30,883  [DEBUG] 
 
2025-07-17 07:58:30,896  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,897  [DEBUG] +CGATT: 1
 
2025-07-17 07:58:30,898  [DEBUG] 
 
2025-07-17 07:58:30,898  [DEBUG] OK
 
2025-07-17 07:58:30,907  [DEBUG] 
 
2025-07-17 07:58:30,910  [DEBUG] [D][11:26:50][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-17 07:58:30,910  [DEBUG] 
 
2025-07-17 07:58:30,928  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,931  [DEBUG] +CSQ: 27,99
 
2025-07-17 07:58:30,932  [DEBUG] 
 
2025-07-17 07:58:30,932  [DEBUG] OK
 
2025-07-17 07:58:30,940  [DEBUG] 
 
2025-07-17 07:58:30,943  [DEBUG] [D][11:26:50][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-17 07:58:30,943  [DEBUG] 
 
2025-07-17 07:58:30,961  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,964  [DEBUG] +QIACT: 1,1,1,"10.226.152.189"
 
2025-07-17 07:58:30,964  [DEBUG] 
 
2025-07-17 07:58:30,964  [DEBUG] OK
 
2025-07-17 07:58:30,965  [DEBUG] 
 
2025-07-17 07:58:30,980  [DEBUG] [D][11:26:50][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-17 07:58:30,981  [DEBUG] 
 
2025-07-17 07:58:30,995  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:30,996  [DEBUG] OK
 
2025-07-17 07:58:30,997  [DEBUG] 
 
2025-07-17 07:58:31,000  [DEBUG] [D][11:26:50][CAT1]exec over: func id: 8, ret: 6
 
2025-07-17 07:58:31,089  [DEBUG] [D][11:26:50][COMM]13762 imu init OK
 
2025-07-17 07:58:31,108  [DEBUG] [D][11:26:50][GNSS]recv submsg id[1]
 
2025-07-17 07:58:31,111  [DEBUG] [D][11:26:50][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
 
2025-07-17 07:58:31,117  [DEBUG] [D][11:26:50][GNSS]location recv gms init done evt success
 
2025-07-17 07:58:31,121  [DEBUG] [D][11:26:50][GNSS]GPS start. ret=0
 
2025-07-17 07:58:31,125  [DEBUG] [D][11:26:50][CAT1]gsm read msg sub id: 23
 
2025-07-17 07:58:31,127  [DEBUG] [D][11:26:50][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-17 07:58:31,128  [DEBUG] 
 
2025-07-17 07:58:31,141  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:31,152  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#UNKNOW"
 
2025-07-17 07:58:31,152  [DEBUG] 
 
2025-07-17 07:58:31,152  [DEBUG] OK
 
2025-07-17 07:58:31,152  [DEBUG] 
 
2025-07-17 07:58:31,155  [DEBUG] [D][11:26:50][CAT1]tx ret[12] >>> AT+GPSCFG?
 
2025-07-17 07:58:31,155  [DEBUG] 
 
2025-07-17 07:58:31,174  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:31,177  [DEBUG] +GPSCFG:0,0,115200,0,0,65504,0,1,1
 
2025-07-17 07:58:31,177  [DEBUG] 
 
2025-07-17 07:58:31,177  [DEBUG] OK
 
2025-07-17 07:58:31,178  [DEBUG] 
 
2025-07-17 07:58:31,189  [DEBUG] [D][11:26:50][CAT1]tx ret[14] >>> AT+GPSPORT=1
 
2025-07-17 07:58:31,189  [DEBUG] 
 
2025-07-17 07:58:31,209  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:31,209  [DEBUG] OK
 
2025-07-17 07:58:31,218  [DEBUG] 
 
2025-07-17 07:58:31,223  [DEBUG] [D][11:26:50][CAT1]tx ret[14] >>> AT+GPSFREQ=1
 
2025-07-17 07:58:31,225  [DEBUG] 
 
2025-07-17 07:58:31,241  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:31,241  [DEBUG] OK
 
2025-07-17 07:58:31,251  [DEBUG] 
 
2025-07-17 07:58:31,255  [DEBUG] [D][11:26:50][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0
 
2025-07-17 07:58:31,256  [DEBUG] 
 
2025-07-17 07:58:31,275  [DEBUG] [D][11:26:50][CAT1]opened : 0, 0
 
2025-07-17 07:58:31,277  [DEBUG] [D][11:26:50][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:31,284  [DEBUG] [D][11:26:50][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-17 07:58:31,289  [DEBUG] [D][11:26:50][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-17 07:58:31,295  [DEBUG] [D][11:26:50][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
 
2025-07-17 07:58:31,297  [DEBUG] [D][11:26:50][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:58:31,303  [DEBUG] [D][11:26:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:58:31,305  [DEBUG] [D][11:26:50][PROT]index:0 1730201210
 
2025-07-17 07:58:31,308  [DEBUG] [D][11:26:50][PROT]is_send:0
 
2025-07-17 07:58:31,311  [DEBUG] [D][11:26:50][PROT]sequence_num:0
 
2025-07-17 07:58:31,314  [DEBUG] [D][11:26:50][PROT]retry_timeout:0
 
2025-07-17 07:58:31,317  [DEBUG] [D][11:26:50][PROT]retry_times:1
 
2025-07-17 07:58:31,321  [DEBUG] [D][11:26:50][PROT]send_path:0x2
 
2025-07-17 07:58:31,325  [DEBUG] [D][11:26:50][PROT]min_index:0, type:0x4205, priority:0
 
2025-07-17 07:58:31,335  [DEBUG] [D][11:26:50][PROT]===========================================================
 
2025-07-17 07:58:31,340  [DEBUG] [W][11:26:50][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201210]
 
2025-07-17 07:58:31,345  [DEBUG] [D][11:26:50][PROT]===========================================================
 
2025-07-17 07:58:31,350  [DEBUG] [D][11:26:50][PROT]sending traceid [9999999999900001]
 
2025-07-17 07:58:31,353  [DEBUG] [D][11:26:50][PROT]Send_TO_M2M [1730201210]
 
2025-07-17 07:58:31,358  [DEBUG] [D][11:26:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-17 07:58:31,364  [DEBUG] [D][11:26:50][SAL ]sock send credit cnt[6]
 
2025-07-17 07:58:31,367  [DEBUG] [D][11:26:50][SAL ]sock send ind credit cnt[6]
 
2025-07-17 07:58:31,369  [DEBUG] [D][11:26:50][M2M ]m2m send data len[294]
 
2025-07-17 07:58:31,375  [DEBUG] [D][11:26:50][SAL ]Cellular task submsg id[10]
 
2025-07-17 07:58:31,386  [DEBUG] [D][11:26:50][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x2005a308] format[0]
 
2025-07-17 07:58:31,387  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-17 07:58:31,387  [DEBUG] OK
 
2025-07-17 07:58:31,388  [DEBUG] 
 
2025-07-17 07:58:31,393  [DEBUG] [D][11:26:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-17 07:58:31,395  [DEBUG] [D][11:26:50][CAT1]tx ret[13] >>> AT+GPSPWR=1
 
2025-07-17 07:58:31,395  [DEBUG] 
 
2025-07-17 07:58:32,740  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-17 07:58:32,741  [DEBUG] OK
 
2025-07-17 07:58:32,750  [DEBUG] 
 
2025-07-17 07:58:32,753  [DEBUG] [D][11:26:52][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F
 
2025-07-17 07:58:32,755  [DEBUG] 
 
2025-07-17 07:58:32,825  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-17 07:58:32,826  [DEBUG] OK
 
2025-07-17 07:58:32,835  [DEBUG] 
 
2025-07-17 07:58:32,839  [DEBUG] [D][11:26:52][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-17 07:58:32,840  [DEBUG] 
 
2025-07-17 07:58:32,922  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-17 07:58:32,934  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"
 
2025-07-17 07:58:32,934  [DEBUG] 
 
2025-07-17 07:58:32,934  [DEBUG] OK
 
2025-07-17 07:58:32,936  [DEBUG] 
 
2025-07-17 07:58:32,939  [DEBUG] [D][11:26:52][CAT1]tx ret[14] >>> AT+GPSMODE=1
 
2025-07-17 07:58:32,939  [DEBUG] 
 
2025-07-17 07:58:32,956  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-17 07:58:32,957  [DEBUG] OK
 
2025-07-17 07:58:32,957  [DEBUG] 
 
2025-07-17 07:58:32,960  [DEBUG] [D][11:26:52][CAT1]exec over: func id: 23, ret: 6
 
2025-07-17 07:58:32,964  [DEBUG] [D][11:26:52][CAT1]sub id: 23, ret: 6
 
2025-07-17 07:58:32,965  [DEBUG] 
 
2025-07-17 07:58:32,970  [DEBUG] [D][11:26:52][CAT1]gsm read msg sub id: 15
 
2025-07-17 07:58:32,973  [DEBUG] [D][11:26:52][CAT1]tx ret[17] >>> AT+QISEND=0,294
 
2025-07-17 07:58:32,974  [DEBUG] 
 
2025-07-17 07:58:32,981  [DEBUG] [D][11:26:52][CAT1]Send Data To Server[294][297] ... ->:
 
2025-07-17 07:58:33,011  [DEBUG] 0093B988113311331133113311331B88B4E60DB6A35944255C835251DE8451D1701645DC70F32D7429BDA1EFFB79D4CC289389C087A31A37A429710E6D6A0F7F6359E08A69513BBDC0663252B72478453EC57EC5F2658AD7A4A37666D93AFF4E2F04E1B7CCA4E65FD62119ED8F4235BECC90108A49763DF7A88D8C324AAEED1A2D1B4B480FC98A9370542F69837604EEE8124D
 
2025-07-17 07:58:33,015  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-17 07:58:33,017  [DEBUG] SEND OK
 
2025-07-17 07:58:33,018  [DEBUG] 
 
2025-07-17 07:58:33,021  [DEBUG] [D][11:26:52][CAT1]exec over: func id: 15, ret: 11
 
2025-07-17 07:58:33,023  [DEBUG] [D][11:26:52][CAT1]sub id: 15, ret: 11
 
2025-07-17 07:58:33,025  [DEBUG] 
 
2025-07-17 07:58:33,031  [DEBUG] [D][11:26:52][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:33,035  [DEBUG] [D][11:26:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-17 07:58:33,039  [DEBUG] [D][11:26:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-17 07:58:33,042  [DEBUG] [D][11:26:52][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:58:33,048  [DEBUG] [D][11:26:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:58:33,051  [DEBUG] [D][11:26:52][PROT]M2M Send ok [1730201212]
 
2025-07-17 07:58:33,121  [DEBUG] [D][11:26:52][GNSS]recv submsg id[1]
 
2025-07-17 07:58:33,124  [DEBUG] [D][11:26:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
 
2025-07-17 07:58:38,341  [DEBUG] [D][11:26:58][PROT]CLEAN,SEND:0
 
2025-07-17 07:58:38,352  [DEBUG] [D][11:26:58][PROT]CLEAN:0
 
2025-07-17 07:58:38,364  [DEBUG] [D][11:26:58][PROT]index:1 1730201218
 
2025-07-17 07:58:38,367  [DEBUG] [D][11:26:58][PROT]is_send:0
 
2025-07-17 07:58:38,369  [DEBUG] [D][11:26:58][PROT]sequence_num:1
 
2025-07-17 07:58:38,372  [DEBUG] [D][11:26:58][PROT]retry_timeout:0
 
2025-07-17 07:58:38,375  [DEBUG] [D][11:26:58][PROT]retry_times:3
 
2025-07-17 07:58:38,378  [DEBUG] [D][11:26:58][PROT]send_path:0x2
 
2025-07-17 07:58:38,383  [DEBUG] [D][11:26:58][PROT]min_index:1, type:0x4B02, priority:0
 
2025-07-17 07:58:38,390  [DEBUG] [D][11:26:58][PROT]===========================================================
 
2025-07-17 07:58:38,398  [DEBUG] [D][11:26:58][HSDK][0] flush to flash addr:[0xE45800] --- write len --- [256]
 
2025-07-17 07:58:38,403  [DEBUG] [W][11:26:58][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201218]
 
2025-07-17 07:58:38,410  [DEBUG] [D][11:26:58][PROT]===========================================================
 
2025-07-17 07:58:38,415  [DEBUG] [D][11:26:58][PROT]sending traceid [9999999999900002]
 
2025-07-17 07:58:38,418  [DEBUG] [D][11:26:58][PROT]Send_TO_M2M [1730201218]
 
2025-07-17 07:58:38,423  [DEBUG] [D][11:26:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-17 07:58:38,428  [DEBUG] [D][11:26:58][SAL ]sock send credit cnt[6]
 
2025-07-17 07:58:38,431  [DEBUG] [D][11:26:58][SAL ]sock send ind credit cnt[6]
 
2025-07-17 07:58:38,434  [DEBUG] [D][11:26:58][M2M ]m2m send data len[326]
 
2025-07-17 07:58:38,439  [DEBUG] [D][11:26:58][SAL ]Cellular task submsg id[10]
 
2025-07-17 07:58:38,448  [DEBUG] [D][11:26:58][SAL ]cellular SEND socket id[0] type[1], len[326], data[0x2005a0a8] format[0]
 
2025-07-17 07:58:38,454  [DEBUG] [D][11:26:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-17 07:58:38,457  [DEBUG] [D][11:26:58][CAT1]gsm read msg sub id: 15
 
2025-07-17 07:58:38,463  [DEBUG] [D][11:26:58][CAT1]tx ret[17] >>> AT+QISEND=0,326
 
2025-07-17 07:58:38,464  [DEBUG] 
 
2025-07-17 07:58:38,464  [DEBUG] [D][11:26:58][CAT1]Send Data To Server[326][329] ... ->:
 
2025-07-17 07:58:38,499  [DEBUG] 00A3B98A113311331133113311331B88B7F74628BE14EA93E2E000D44E1AA17B44F95339F2305EF6A6FDEC26E8B611B13E56EBD7D28A09F881BE4F403406D9DB747FE0F0A3FDD0CBF1F9BFD6B055636D12BACB991CDFF26B4EE5436F95C60485C2F06E94B0B1D9CF06280E2BF6C882A7891E885AF837AF56F67B192707102FA7377AC19252D68A65F2BA68C7BC4BDD2B53146B095B519DAA55AFBF845F30206871EFC9
 
2025-07-17 07:58:38,500  [DEBUG] [D][11:26:58][CAT1]<<< 
 
2025-07-17 07:58:38,500  [DEBUG] SEND OK
 
2025-07-17 07:58:38,501  [DEBUG] 
 
2025-07-17 07:58:38,501  [DEBUG] [D][11:26:58][CAT1]exec over: func id: 15, ret: 11
 
2025-07-17 07:58:38,507  [DEBUG] [D][11:26:58][CAT1]sub id: 15, ret: 11
 
2025-07-17 07:58:38,508  [DEBUG] 
 
2025-07-17 07:58:38,509  [DEBUG] [D][11:26:58][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:38,518  [DEBUG] [D][11:26:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-17 07:58:38,520  [DEBUG] [D][11:26:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-17 07:58:38,523  [DEBUG] [D][11:26:58][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:58:38,529  [DEBUG] [D][11:26:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:58:38,533  [DEBUG] [D][11:26:58][PROT]M2M Send ok [1730201218]
 
2025-07-17 07:58:43,755  [DEBUG] [D][11:27:03][PROT]CLEAN,SEND:1
 
2025-07-17 07:58:43,767  [DEBUG] [D][11:27:03][PROT]index:1 1730201223
 
2025-07-17 07:58:43,770  [DEBUG] [D][11:27:03][PROT]is_send:0
 
2025-07-17 07:58:43,772  [DEBUG] [D][11:27:03][PROT]sequence_num:1
 
2025-07-17 07:58:43,776  [DEBUG] [D][11:27:03][PROT]retry_timeout:0
 
2025-07-17 07:58:43,779  [DEBUG] [D][11:27:03][PROT]retry_times:2
 
2025-07-17 07:58:43,781  [DEBUG] [D][11:27:03][PROT]send_path:0x2
 
2025-07-17 07:58:43,786  [DEBUG] [D][11:27:03][PROT]min_index:1, type:0x4B02, priority:0
 
2025-07-17 07:58:43,793  [DEBUG] [D][11:27:03][PROT]===========================================================
 
2025-07-17 07:58:43,799  [DEBUG] [W][11:27:03][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201223]
 
2025-07-17 07:58:43,807  [DEBUG] [D][11:27:03][PROT]===========================================================
 
2025-07-17 07:58:43,812  [DEBUG] [D][11:27:03][PROT]sending traceid [9999999999900002]
 
2025-07-17 07:58:43,815  [DEBUG] [D][11:27:03][PROT]Send_TO_M2M [1730201223]
 
2025-07-17 07:58:43,821  [DEBUG] [D][11:27:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-17 07:58:43,824  [DEBUG] [D][11:27:03][SAL ]sock send credit cnt[6]
 
2025-07-17 07:58:43,829  [DEBUG] [D][11:27:03][SAL ]sock send ind credit cnt[6]
 
2025-07-17 07:58:43,832  [DEBUG] [D][11:27:03][M2M ]m2m send data len[326]
 
2025-07-17 07:58:43,835  [DEBUG] [D][11:27:03][SAL ]Cellular task submsg id[10]
 
2025-07-17 07:58:43,843  [DEBUG] [D][11:27:03][SAL ]cellular SEND socket id[0] type[1], len[326], data[0x2005a0a8] format[0]
 
2025-07-17 07:58:43,849  [DEBUG] [D][11:27:03][CAT1]gsm read msg sub id: 15
 
2025-07-17 07:58:43,851  [DEBUG] [D][11:27:03][CAT1]tx ret[17] >>> AT+QISEND=0,326
 
2025-07-17 07:58:43,851  [DEBUG] 
 
2025-07-17 07:58:43,857  [DEBUG] [D][11:27:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-17 07:58:43,861  [DEBUG] [D][11:27:03][CAT1]Send Data To Server[326][329] ... ->:
 
2025-07-17 07:58:43,893  [DEBUG] 00A3B98F113311331133113311331B88B7C36A272337ABC74F704DB7DC4CBB2A61D8930BBD1A5F1405E7371B76AC78AB8ED14F314167F2BEE85AF23E715F06F6F8A25718DAB756B21DBEF9A15490F15D204A04234EF7B101F7A7768FBF08B023E93CD7EDA3A715B61C46F8D5BC6E1CF68634A18EC24E089400CB710AE6C9D886043B7C1D86499F4CB555AF399386E1E80F3FB4011CCF232CA838D9CE2A2ABD8E489521
 
2025-07-17 07:58:43,895  [DEBUG] [D][11:27:03][COMM]IMU: 26475 MEMS ERROR when cali 0
 
2025-07-17 07:58:43,898  [DEBUG] [D][11:27:03][CAT1]<<< 
 
2025-07-17 07:58:43,899  [DEBUG] SEND OK
 
2025-07-17 07:58:43,900  [DEBUG] 
 
2025-07-17 07:58:43,904  [DEBUG] [D][11:27:03][CAT1]exec over: func id: 15, ret: 11
 
2025-07-17 07:58:43,907  [DEBUG] [D][11:27:03][CAT1]sub id: 15, ret: 11
 
2025-07-17 07:58:43,908  [DEBUG] 
 
2025-07-17 07:58:43,912  [DEBUG] [D][11:27:03][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:43,918  [DEBUG] [D][11:27:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-17 07:58:43,920  [DEBUG] [D][11:27:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-17 07:58:43,926  [DEBUG] [D][11:27:03][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:58:43,929  [DEBUG] [D][11:27:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:58:43,933  [DEBUG] [D][11:27:03][PROT]M2M Send ok [1730201223]
 
2025-07-17 07:58:49,156  [DEBUG] [D][11:27:08][PROT]CLEAN,SEND:1
 
2025-07-17 07:58:49,168  [DEBUG] [D][11:27:08][PROT]index:1 1730201228
 
2025-07-17 07:58:49,171  [DEBUG] [D][11:27:08][PROT]is_send:0
 
2025-07-17 07:58:49,174  [DEBUG] [D][11:27:08][PROT]sequence_num:1
 
2025-07-17 07:58:49,176  [DEBUG] [D][11:27:08][PROT]retry_timeout:0
 
2025-07-17 07:58:49,179  [DEBUG] [D][11:27:08][PROT]retry_times:1
 
2025-07-17 07:58:49,181  [DEBUG] [D][11:27:08][PROT]send_path:0x2
 
2025-07-17 07:58:49,187  [DEBUG] [D][11:27:08][PROT]min_index:1, type:0x4B02, priority:0
 
2025-07-17 07:58:49,193  [DEBUG] [D][11:27:08][PROT]===========================================================
 
2025-07-17 07:58:49,199  [DEBUG] [W][11:27:08][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201228]
 
2025-07-17 07:58:49,207  [DEBUG] [D][11:27:08][PROT]===========================================================
 
2025-07-17 07:58:49,212  [DEBUG] [D][11:27:08][PROT]sending traceid [9999999999900002]
 
2025-07-17 07:58:49,215  [DEBUG] [D][11:27:08][PROT]Send_TO_M2M [1730201228]
 
2025-07-17 07:58:49,221  [DEBUG] [D][11:27:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-17 07:58:49,225  [DEBUG] [D][11:27:08][SAL ]sock send credit cnt[6]
 
2025-07-17 07:58:49,230  [DEBUG] [D][11:27:08][SAL ]sock send ind credit cnt[6]
 
2025-07-17 07:58:49,233  [DEBUG] [D][11:27:08][M2M ]m2m send data len[326]
 
2025-07-17 07:58:49,236  [DEBUG] [D][11:27:08][SAL ]Cellular task submsg id[10]
 
2025-07-17 07:58:49,244  [DEBUG] [D][11:27:08][SAL ]cellular SEND socket id[0] type[1], len[326], data[0x2005a0a8] format[0]
 
2025-07-17 07:58:49,249  [DEBUG] [D][11:27:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-17 07:58:49,254  [DEBUG] [D][11:27:08][CAT1]gsm read msg sub id: 15
 
2025-07-17 07:58:49,258  [DEBUG] [D][11:27:08][CAT1]tx ret[17] >>> AT+QISEND=0,326
 
2025-07-17 07:58:49,259  [DEBUG] 
 
2025-07-17 07:58:49,262  [DEBUG] [D][11:27:08][CAT1]Send Data To Server[326][329] ... ->:
 
2025-07-17 07:58:49,293  [DEBUG] 00A3B980113311331133113311331B88B71F15F91CBDA73B71AF5C9109E6761ADBB2E5F5BD4CE7F256D56C7D44F44135331A6540AB6EA64CC5C6F724609FDA4ACD2044ABA0EACD0E6A9585FD00800EE283EFDCED501929C576265DB7A14E99FFF3DA57914B1BA7C94A662E6C7D9E12F267A39191365DAA0ED47E8988E7419289CD085196B2530E68E53D4117A3A3E09E613D8C16B640027147CD19B8980C1653CBD036
 
2025-07-17 07:58:49,293  [DEBUG] [D][11:27:08][CAT1]<<< 
 
2025-07-17 07:58:49,295  [DEBUG] SEND OK
 
2025-07-17 07:58:49,295  [DEBUG] 
 
2025-07-17 07:58:49,299  [DEBUG] [D][11:27:08][CAT1]exec over: func id: 15, ret: 11
 
2025-07-17 07:58:49,301  [DEBUG] [D][11:27:08][CAT1]sub id: 15, ret: 11
 
2025-07-17 07:58:49,302  [DEBUG] 
 
2025-07-17 07:58:49,307  [DEBUG] [D][11:27:08][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:58:49,313  [DEBUG] [D][11:27:08][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-17 07:58:49,318  [DEBUG] [D][11:27:08][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-17 07:58:49,321  [DEBUG] [D][11:27:08][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:58:49,327  [DEBUG] [D][11:27:08][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:58:49,329  [DEBUG] [D][11:27:08][PROT]M2M Send ok [1730201228]
 
2025-07-17 07:58:54,552  [DEBUG] [D][11:27:14][PROT]CLEAN,SEND:1
 
2025-07-17 07:58:54,562  [DEBUG] [D][11:27:14][PROT]CLEAN:1
 
2025-07-17 07:58:54,572  [DEBUG] [D][11:27:14][PROT]---> mem_auto_free PT_VARIABLE_CFG_REPORT msg data, point address:0x2000ee60
 
2025-07-17 07:59:18,381  [DEBUG] [D][11:27:38][GNSS]frm_wifi_scan_callback: scan timeout
 
2025-07-17 07:59:37,887  [DEBUG] [D][11:27:57][CAT1]closed : 0
 
2025-07-17 07:59:37,889  [DEBUG] [D][11:27:57][SAL ]Cellular task submsg id[67]
 
2025-07-17 07:59:37,892  [DEBUG] [D][11:27:57][SAL ]socket closed id[0]
 
2025-07-17 07:59:37,897  [DEBUG] [D][11:27:57][SAL ]socket remote close ind. id[4]
 
2025-07-17 07:59:37,903  [DEBUG] [D][11:27:57][SAL ]select read evt socket_id[4], p_data[0] len[0]
 
2025-07-17 07:59:37,905  [DEBUG] [D][11:27:57][M2M ]m2m select fd[4]
 
2025-07-17 07:59:37,911  [DEBUG] [D][11:27:57][M2M ]socket[4] Link is disconnected
 
2025-07-17 07:59:37,914  [DEBUG] [D][11:27:57][M2M ]tcpclient close[4]
 
2025-07-17 07:59:37,916  [DEBUG] [D][11:27:57][SAL ]socket[4] has closed
 
2025-07-17 07:59:37,922  [DEBUG] [D][11:27:57][PROT]recv_protocol_data_from_gprs ok
 
2025-07-17 07:59:37,927  [DEBUG] [D][11:27:57][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
 
2025-07-17 07:59:37,993  [DEBUG] [D][11:27:57][COMM]Main Task receive event:86
 
2025-07-17 07:59:38,001  [DEBUG] [W][11:27:57][PROT]remove success[1730201277],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-17 07:59:38,010  [DEBUG] [W][11:27:57][PROT]add success [1730201277],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-17 07:59:38,016  [DEBUG] [D][11:27:57][COMM]Main Task receive event:86 finished processing
 
2025-07-17 07:59:38,018  [DEBUG] [D][11:27:57][PROT]index:0 1730201277
 
2025-07-17 07:59:38,021  [DEBUG] [D][11:27:57][PROT]is_send:0
 
2025-07-17 07:59:38,024  [DEBUG] [D][11:27:57][PROT]sequence_num:2
 
2025-07-17 07:59:38,030  [DEBUG] [D][11:27:57][PROT]retry_timeout:0
 
2025-07-17 07:59:38,032  [DEBUG] [D][11:27:57][PROT]retry_times:1
 
2025-07-17 07:59:38,035  [DEBUG] [D][11:27:57][PROT]send_path:0x2
 
2025-07-17 07:59:38,040  [DEBUG] [D][11:27:57][PROT]min_index:0, type:0x8301, priority:0
 
2025-07-17 07:59:38,046  [DEBUG] [D][11:27:57][PROT]===========================================================
 
2025-07-17 07:59:38,052  [DEBUG] [W][11:27:57][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201277]
 
2025-07-17 07:59:38,060  [DEBUG] [D][11:27:57][PROT]===========================================================
 
2025-07-17 07:59:38,063  [DEBUG] [D][11:27:57][PROT]sending traceid [9999999999900004]
 
2025-07-17 07:59:38,069  [DEBUG] [D][11:27:57][PROT]Send_TO_M2M [1730201277]
 
2025-07-17 07:59:38,074  [DEBUG] [D][11:27:57][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-17 07:59:38,077  [DEBUG] [D][11:27:57][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-17 07:59:38,083  [DEBUG] [D][11:27:57][SAL ]open socket ind id[4], rst[0]
 
2025-07-17 07:59:38,088  [DEBUG] [D][11:27:57][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-17 07:59:38,094  [DEBUG] [D][11:27:57][SAL ]Cellular task submsg id[8]
 
2025-07-17 07:59:38,100  [DEBUG] [D][11:27:57][SAL ]cellular OPEN socket size[144], msg->data[0x20059ff0], socket[0]
 
2025-07-17 07:59:38,108  [DEBUG] [D][11:27:57][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-17 07:59:38,110  [DEBUG] [D][11:27:57][CAT1]gsm read msg sub id: 8
 
2025-07-17 07:59:38,113  [DEBUG] [D][11:27:57][CAT1]at ops open socket[0]
 
2025-07-17 07:59:38,118  [DEBUG] [D][11:27:57][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-17 07:59:38,119  [DEBUG] 
 
2025-07-17 07:59:38,121  [DEBUG] [D][11:27:57][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-17 07:59:38,124  [DEBUG] [D][11:27:57][CAT1]<<< 
 
2025-07-17 07:59:38,127  [DEBUG] +CGATT: 1
 
2025-07-17 07:59:38,127  [DEBUG] 
 
2025-07-17 07:59:38,127  [DEBUG] OK
 
2025-07-17 07:59:38,129  [DEBUG] 
 
2025-07-17 07:59:38,130  [DEBUG] [D][11:27:57][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-17 07:59:38,132  [DEBUG] 
 
2025-07-17 07:59:38,133  [DEBUG] [D][11:27:57][CAT1]<<< 
 
2025-07-17 07:59:38,134  [DEBUG] +CSQ: 31,99
 
2025-07-17 07:59:38,135  [DEBUG] 
 
2025-07-17 07:59:38,137  [DEBUG] OK
 
2025-07-17 07:59:38,137  [DEBUG] 
 
2025-07-17 07:59:38,141  [DEBUG] [D][11:27:57][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-17 07:59:38,142  [DEBUG] 
 
2025-07-17 07:59:38,144  [DEBUG] [D][11:27:57][CAT1]<<< 
 
2025-07-17 07:59:38,145  [DEBUG] +QIACT: 1,1,1,"10.226.152.189"
 
2025-07-17 07:59:38,146  [DEBUG] 
 
2025-07-17 07:59:38,147  [DEBUG] OK
 
2025-07-17 07:59:38,149  [DEBUG] 
 
2025-07-17 07:59:38,153  [DEBUG] [D][11:27:57][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-17 07:59:38,153  [DEBUG] 
 
2025-07-17 07:59:38,155  [DEBUG] [D][11:27:57][CAT1]<<< 
 
2025-07-17 07:59:38,156  [DEBUG] OK
 
2025-07-17 07:59:38,156  [DEBUG] 
 
2025-07-17 07:59:38,158  [DEBUG] [D][11:27:57][CAT1]exec over: func id: 8, ret: 6
 
2025-07-17 07:59:38,257  [DEBUG] [D][11:27:57][CAT1]opened : 0, 0
 
2025-07-17 07:59:38,260  [DEBUG] [D][11:27:57][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:59:38,266  [DEBUG] [D][11:27:57][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-17 07:59:38,271  [DEBUG] [D][11:27:57][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-17 07:59:38,276  [DEBUG] [D][11:27:57][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:1
 
2025-07-17 07:59:38,283  [DEBUG] [D][11:27:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-17 07:59:38,285  [DEBUG] [D][11:27:57][SAL ]sock send credit cnt[6]
 
2025-07-17 07:59:38,288  [DEBUG] [D][11:27:57][SAL ]sock send ind credit cnt[6]
 
2025-07-17 07:59:38,293  [DEBUG] [D][11:27:57][M2M ]m2m send data len[70]
 
2025-07-17 07:59:38,296  [DEBUG] [D][11:27:57][SAL ]Cellular task submsg id[10]
 
2025-07-17 07:59:38,305  [DEBUG] [D][11:27:57][SAL ]cellular SEND socket id[0] type[1], len[70], data[0x2005a010] format[0]
 
2025-07-17 07:59:38,310  [DEBUG] [D][11:27:57][CAT1]gsm read msg sub id: 15
 
2025-07-17 07:59:38,312  [DEBUG] [D][11:27:57][CAT1]tx ret[16] >>> AT+QISEND=0,70
 
2025-07-17 07:59:38,313  [DEBUG] 
 
2025-07-17 07:59:38,319  [DEBUG] [D][11:27:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-17 07:59:38,323  [DEBUG] [D][11:27:57][CAT1]Send Data To Server[70][70] ... ->:
 
2025-07-17 07:59:38,330  [DEBUG] 0023B98D113311331133113311331B88B6EFCEF0752C5A14504C61A502BCB42FFC1BAA
 
2025-07-17 07:59:38,331  [DEBUG] [D][11:27:57][CAT1]<<< 
 
2025-07-17 07:59:38,333  [DEBUG] SEND OK
 
2025-07-17 07:59:38,333  [DEBUG] 
 
2025-07-17 07:59:38,338  [DEBUG] [D][11:27:57][CAT1]exec over: func id: 15, ret: 11
 
2025-07-17 07:59:38,340  [DEBUG] [D][11:27:57][CAT1]sub id: 15, ret: 11
 
2025-07-17 07:59:38,341  [DEBUG] 
 
2025-07-17 07:59:38,346  [DEBUG] [D][11:27:58][SAL ]Cellular task submsg id[68]
 
2025-07-17 07:59:38,352  [DEBUG] [D][11:27:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-17 07:59:38,354  [DEBUG] [D][11:27:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-17 07:59:38,360  [DEBUG] [D][11:27:58][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:59:38,363  [DEBUG] [D][11:27:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:59:38,368  [DEBUG] [D][11:27:58][PROT]M2M Send ok [1730201278]
 
2025-07-17 07:59:38,371  [DEBUG] [D][11:27:58][PROT]CLEAN:0
 
2025-07-17 07:59:38,375  [DEBUG] [D][11:27:58][M2M ]m2m_task:m_m2m_thread_setting_queue:7
 
2025-07-17 07:59:38,380  [DEBUG] [D][11:27:58][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-17 07:59:38,386  [DEBUG] [D][11:27:58][M2M ]socket has connect, gsm_send_status:0
 
2025-07-17 07:59:38,388  [DEBUG] [D][11:27:58][M2M ]g_m2m_is_idle become 1
 
2025-07-17 07:59:38,394  [DEBUG] [D][11:27:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-17 07:59:57,327  [DEBUG] [D][11:28:16][COMM]msg 0226 loss. last_tick:0. cur_tick:100004. period:10000
 
2025-07-17 07:59:57,331  [DEBUG] 
 
2025-07-17 07:59:57,333  [DEBUG] [D][11:28:16][COMM]msg 0227 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-17 07:59:57,334  [DEBUG] 
 
2025-07-17 07:59:57,341  [DEBUG] [D][11:28:16][COMM]msg 0228 loss. last_tick:0. cur_tick:100005. period:10000
 
2025-07-17 07:59:57,342  [DEBUG] 
 
2025-07-17 07:59:57,346  [DEBUG] [D][11:28:16][COMM]msg 0261 loss. last_tick:0. cur_tick:100006. period:10000
 
2025-07-17 07:59:57,347  [DEBUG] 
 
2025-07-17 07:59:57,356  [DEBUG] [D][11:28:16][COMM]msg 0262 loss. last_tick:0. cur_tick:100006. period:10000
 
2025-07-17 07:59:57,357  [DEBUG] 
 
2025-07-17 07:59:57,360  [DEBUG] [D][11:28:16][COMM]msg 0263 loss. last_tick:0. cur_tick:100007. period:10000
 
2025-07-17 07:59:57,362  [DEBUG] 
 
2025-07-17 07:59:57,369  [DEBUG] [D][11:28:16][COMM]msg 0281 loss. last_tick:0. cur_tick:100007. period:10000
 
2025-07-17 07:59:57,370  [DEBUG] 
 
2025-07-17 07:59:57,375  [DEBUG] [D][11:28:16][COMM]msg 0282 loss. last_tick:0. cur_tick:100008. period:10000
 
2025-07-17 07:59:57,376  [DEBUG] 
 
2025-07-17 07:59:57,383  [DEBUG] [D][11:28:16][COMM]msg 0283 loss. last_tick:0. cur_tick:100008. period:10000
 
2025-07-17 07:59:57,384  [DEBUG] 
 
2025-07-17 07:59:57,389  [DEBUG] [D][11:28:16][COMM]msg 02A1 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-17 07:59:57,390  [DEBUG] 
 
2025-07-17 07:59:57,398  [DEBUG] [D][11:28:16][COMM]msg 02A2 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-17 07:59:57,399  [DEBUG] 
 
2025-07-17 07:59:57,402  [DEBUG] [D][11:28:16][COMM]msg 02A3 loss. last_tick:0. cur_tick:100009. period:10000
 
2025-07-17 07:59:57,402  [DEBUG] 
 
2025-07-17 07:59:57,411  [DEBUG] [D][11:28:16][COMM]msg 02C3 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-17 07:59:57,411  [DEBUG] 
 
2025-07-17 07:59:57,416  [DEBUG] [D][11:28:16][COMM]msg 02C4 loss. last_tick:0. cur_tick:100010. period:10000
 
2025-07-17 07:59:57,417  [DEBUG] 
 
2025-07-17 07:59:57,425  [DEBUG] [D][11:28:16][COMM]msg 02C5 loss. last_tick:0. cur_tick:100011. period:10000
 
2025-07-17 07:59:57,426  [DEBUG] 
 
2025-07-17 07:59:57,431  [DEBUG] [D][11:28:16][COMM]msg 02E3 loss. last_tick:0. cur_tick:100011. period:10000
 
2025-07-17 07:59:57,431  [DEBUG] 
 
2025-07-17 07:59:57,438  [DEBUG] [D][11:28:16][COMM]msg 02E4 loss. last_tick:0. cur_tick:100012. period:10000
 
2025-07-17 07:59:57,439  [DEBUG] 
 
2025-07-17 07:59:57,444  [DEBUG] [D][11:28:16][COMM]msg 02E5 loss. last_tick:0. cur_tick:100012. period:10000
 
2025-07-17 07:59:57,445  [DEBUG] 
 
2025-07-17 07:59:57,453  [DEBUG] [D][11:28:17][COMM]msg 0302 loss. last_tick:0. cur_tick:100013. period:10000
 
2025-07-17 07:59:57,454  [DEBUG] 
 
2025-07-17 07:59:57,458  [DEBUG] [D][11:28:17][COMM]msg 0303 loss. last_tick:0. cur_tick:100013. period:10000
 
2025-07-17 07:59:57,459  [DEBUG] 
 
2025-07-17 07:59:57,466  [DEBUG] [D][11:28:17][COMM]msg 0304 loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-17 07:59:57,467  [DEBUG] 
 
2025-07-17 07:59:57,472  [DEBUG] [D][11:28:17][COMM]msg 02E6 loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-17 07:59:57,473  [DEBUG] 
 
2025-07-17 07:59:57,480  [DEBUG] [D][11:28:17][COMM]msg 02E7 loss. last_tick:0. cur_tick:100014. period:10000
 
2025-07-17 07:59:57,481  [DEBUG] 
 
2025-07-17 07:59:57,486  [DEBUG] [D][11:28:17][COMM]msg 0305 loss. last_tick:0. cur_tick:100015. period:10000
 
2025-07-17 07:59:57,487  [DEBUG] 
 
2025-07-17 07:59:57,494  [DEBUG] [D][11:28:17][COMM]msg 0306 loss. last_tick:0. cur_tick:100015. period:10000
 
2025-07-17 07:59:57,495  [DEBUG] 
 
2025-07-17 07:59:57,500  [DEBUG] [D][11:28:17][COMM]msg 02A8 loss. last_tick:0. cur_tick:100016. period:10000
 
2025-07-17 07:59:57,501  [DEBUG] 
 
2025-07-17 07:59:57,509  [DEBUG] [D][11:28:17][COMM]msg 02A9 loss. last_tick:0. cur_tick:100016. period:10000
 
2025-07-17 07:59:57,509  [DEBUG] 
 
2025-07-17 07:59:57,514  [DEBUG] [D][11:28:17][COMM]msg 02AA loss. last_tick:0. cur_tick:100017. period:10000
 
2025-07-17 07:59:57,516  [DEBUG] 
 
2025-07-17 07:59:57,523  [DEBUG] [D][11:28:17][COMM]msg 02AB loss. last_tick:0. cur_tick:100017. period:10000
 
2025-07-17 07:59:57,523  [DEBUG] 
 
2025-07-17 07:59:57,528  [DEBUG] [D][11:28:17][COMM]msg 02AD loss. last_tick:0. cur_tick:100018. period:10000
 
2025-07-17 07:59:57,528  [DEBUG] 
 
2025-07-17 07:59:57,537  [DEBUG] [D][11:28:17][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100018. period:10000. j,i:0 53
 
2025-07-17 07:59:57,537  [DEBUG] 
 
2025-07-17 07:59:57,545  [DEBUG] [D][11:28:17][COMM]bat msg 02AE loss. last_tick:0. cur_tick:100019. period:10000. j,i:1 54
 
2025-07-17 07:59:57,546  [DEBUG] 
 
2025-07-17 07:59:57,553  [DEBUG] [D][11:28:17][COMM]bat msg 024A loss. last_tick:0. cur_tick:100019. period:10000. j,i:13 66
 
2025-07-17 07:59:57,554  [DEBUG] 
 
2025-07-17 07:59:57,562  [DEBUG] [D][11:28:17][COMM]bat msg 024B loss. last_tick:0. cur_tick:100019. period:10000. j,i:14 67
 
2025-07-17 07:59:57,562  [DEBUG] 
 
2025-07-17 07:59:57,569  [DEBUG] [D][11:28:17][COMM]bat msg 024C loss. last_tick:0. cur_tick:100020. period:10000. j,i:15 68
 
2025-07-17 07:59:57,570  [DEBUG] 
 
2025-07-17 07:59:57,578  [DEBUG] [D][11:28:17][COMM]bat msg 024D loss. last_tick:0. cur_tick:100020. period:10000. j,i:16 69
 
2025-07-17 07:59:57,578  [DEBUG] 
 
2025-07-17 07:59:57,586  [DEBUG] [D][11:28:17][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100021. period:10000. j,i:19 72
 
2025-07-17 07:59:57,587  [DEBUG] 
 
2025-07-17 07:59:57,595  [DEBUG] [D][11:28:17][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100022. period:10000. j,i:20 73
 
2025-07-17 07:59:57,595  [DEBUG] 
 
2025-07-17 07:59:57,603  [DEBUG] [D][11:28:17][COMM]bat msg 025A loss. last_tick:0. cur_tick:100022. period:10000. j,i:24 77
 
2025-07-17 07:59:57,603  [DEBUG] 
 
2025-07-17 07:59:57,611  [DEBUG] [D][11:28:17][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100022
 
2025-07-17 07:59:57,619  [DEBUG] [D][11:28:17][COMM]CAN message bat fault change: 0x06E61FFC->0x07FFFFFF 100023
 
2025-07-17 07:59:57,626  [DEBUG] [D][11:28:17][COMM]CAN fault change: 0x0000000300010F05->0x0000000300010F07 100023
 
