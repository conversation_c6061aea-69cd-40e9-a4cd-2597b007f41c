2025-07-16 07:58:52,927  [DEBUG]  *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:58:52,928  [DEBUG] flash is 24bit address mode

 
2025-07-16 07:58:52,931  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 07:58:52,934  [DEBUG] HW SW version: 5340 109

 
2025-07-16 07:58:52,936  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 07:58:52,940  [DEBUG] get_boot_mode 0
 
2025-07-16 07:58:52,940  [DEBUG] is_app_complete 0
 
2025-07-16 07:58:53,137  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:58:53,143  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 07:58:53,144  [DEBUG] [ADC]init adc success.

 
2025-07-16 07:58:53,768  [DEBUG] para ret:306,valid:aa

 
2025-07-16 07:58:53,852  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-16 07:58:53,855  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-16 07:58:53,861  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-16 07:58:53,870  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:58:53,875  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:58:53,877  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:58:53,883  [DEBUG] [W][11:26:37][FCTY]DeviceID    = ***************
 
2025-07-16 07:58:53,886  [DEBUG] [W][11:26:37][FCTY]HardwareID  = ***************
 
2025-07-16 07:58:53,892  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:58:53,894  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:58:53,897  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-16 07:58:53,902  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:58:53,905  [DEBUG] [W][11:26:37][FCTY]Bat         = 3864 mv
 
2025-07-16 07:58:53,908  [DEBUG] [W][11:26:37][FCTY]Current     = 450 ma
 
2025-07-16 07:58:53,914  [DEBUG] [W][11:26:37][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:58:53,919  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 658763,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:58:53,925  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 26
 
2025-07-16 07:58:53,928  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-16 07:58:53,931  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:58:53,937  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:58:53,939  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:58:53,942  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:58:53,945  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:58:53,947  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:58:53,953  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:58:53,956  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-16 07:58:54,308  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:58:54,712  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:58:54,714  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-16 07:58:54,717  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-16 07:58:54,720  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-16 07:58:54,727  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 07:58:54,728  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-16 07:58:54,739  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 07:58:54,746  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 07:58:55,931  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 07:58:55,938  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 07:59:01,296  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-16 07:59:01,314  [DEBUG] [D][11:26:45][COMM]Password OK
 
2025-07-16 07:59:01,317  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE45D00] --- write len --- [256]
 
2025-07-16 07:59:01,320  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:01,322  [DEBUG] [D][11:26:45][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,328  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,329  [DEBUG] [W][11:26:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:01,332  [DEBUG] [W][11:26:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:01,338  [DEBUG] [W][11:26:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:01,341  [DEBUG] [W][11:26:45][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:01,346  [DEBUG] [W][11:26:45][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:01,349  [DEBUG] [W][11:26:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:01,355  [DEBUG] [W][11:26:45][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:01,371  [DEBUG] [D][11:26:45][HSDK]need to erase for write: is[0x0] ie[0x4E00]
 
2025-07-16 07:59:01,373  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE45E00] --- write len --- [256]
 
2025-07-16 07:59:01,374  [DEBUG] [W][11:26:45][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:01,376  [DEBUG] [W][11:26:45][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:01,378  [DEBUG] [W][11:26:45][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:01,380  [DEBUG] [W][11:26:45][FCTY]Current     = 250 ma
 
2025-07-16 07:59:01,387  [DEBUG] [W][11:26:45][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:01,390  [DEBUG] [W][11:26:45][FCTY]TEMP= 26,BATID= 662484,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:01,398  [DEBUG] [W][11:26:45][FCTY]Ext battery vol = 1, adc = 67
 
2025-07-16 07:59:01,405  [DEBUG] [D][11:26:45][FCTY]Acckey1 vol = 5486 mv, Acckey2 vol = 379 mv
 
2025-07-16 07:59:01,407  [DEBUG] [W][11:26:45][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:01,408  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:01,411  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:01,417  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:01,419  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:01,422  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:01,425  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:01,430  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:01,436  [DEBUG] [D][11:26:45][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,441  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,451  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:01,452  [DEBUG] [D][11:26:45][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,458  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,463  [DEBUG] [W][11:26:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:01,470  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE45F00] --- write len --- [256]
 
2025-07-16 07:59:01,474  [DEBUG] [W][11:26:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:01,477  [DEBUG] [W][11:26:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:01,494  [DEBUG] [W][11:26:45][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:01,495  [DEBUG] [W][11:26:45][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:01,495  [DEBUG] [W][11:26:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:01,495  [DEBUG] [W][11:26:45][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:01,496  [DEBUG] [W][11:26:45][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:01,502  [DEBUG] [W][11:26:45][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:01,506  [DEBUG] [W][11:26:45][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:01,514  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46000] --- write len --- [256]
 
2025-07-16 07:59:01,515  [DEBUG] [W][11:26:45][FCTY]Current     = 250 ma
 
2025-07-16 07:59:01,518  [DEBUG] [W][11:26:45][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:01,527  [DEBUG] [W][11:26:45][FCTY]TEMP= 26,BATID= 661240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:01,529  [DEBUG] [W][11:26:45][FCTY]Ext battery vol = 1, adc = 64
 
2025-07-16 07:59:01,537  [DEBUG] [D][11:26:45][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 227 mv
 
2025-07-16 07:59:01,542  [DEBUG] [W][11:26:45][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:01,544  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:01,546  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:01,549  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:01,555  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:01,557  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:01,560  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:01,563  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:01,574  [DEBUG] [D][11:26:45][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,578  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,581  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:01,588  [DEBUG] [D][11:26:45][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,595  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,597  [DEBUG] [W][11:26:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:01,602  [DEBUG] [W][11:26:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:01,609  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46100] --- write len --- [256]
 
2025-07-16 07:59:01,613  [DEBUG] [W][11:26:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:01,619  [DEBUG] [W][11:26:45][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:01,621  [DEBUG] [W][11:26:45][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:01,624  [DEBUG] [W][11:26:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:01,630  [DEBUG] [W][11:26:45][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:01,633  [DEBUG] [W][11:26:45][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:01,638  [DEBUG] [W][11:26:45][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:01,642  [DEBUG] [W][11:26:45][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:01,645  [DEBUG] [W][11:26:45][FCTY]Current     = 250 ma
 
2025-07-16 07:59:01,647  [DEBUG] [W][11:26:45][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:01,656  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46200] --- write len --- [256]
 
2025-07-16 07:59:01,661  [DEBUG] [W][11:26:45][FCTY]TEMP= 26,BATID= 658763,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:01,666  [DEBUG] [W][11:26:45][FCTY]Ext battery vol = 1, adc = 72
 
2025-07-16 07:59:01,672  [DEBUG] [D][11:26:45][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 303 mv
 
2025-07-16 07:59:01,675  [DEBUG] [W][11:26:45][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:01,677  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:01,684  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:01,686  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:01,689  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:01,692  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:01,697  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:01,701  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:01,706  [DEBUG] [D][11:26:45][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,712  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,720  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:01,725  [DEBUG] [D][11:26:45][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,728  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,733  [DEBUG] [W][11:26:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:01,738  [DEBUG] [W][11:26:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:01,741  [DEBUG] [W][11:26:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:01,751  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46300] --- write len --- [256]
 
2025-07-16 07:59:01,753  [DEBUG] [W][11:26:45][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:01,759  [DEBUG] [W][11:26:45][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:01,761  [DEBUG] [W][11:26:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:01,767  [DEBUG] [W][11:26:45][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:01,769  [DEBUG] [W][11:26:45][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:01,772  [DEBUG] [W][11:26:45][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:01,777  [DEBUG] [W][11:26:45][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:01,781  [DEBUG] [W][11:26:45][FCTY]Current     = 250 ma
 
2025-07-16 07:59:01,783  [DEBUG] [W][11:26:45][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:01,789  [DEBUG] [W][11:26:45][FCTY]TEMP= 26,BATID= 661240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:01,794  [DEBUG] [W][11:26:45][FCTY]Ext battery vol = 1, adc = 72
 
2025-07-16 07:59:01,801  [DEBUG] [D][11:26:45][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 429 mv
 
2025-07-16 07:59:01,803  [DEBUG] [W][11:26:45][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:01,809  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:01,815  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46400] --- write len --- [256]
 
2025-07-16 07:59:01,817  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:01,823  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:01,825  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:01,828  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:01,831  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:01,836  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:01,843  [DEBUG] [D][11:26:45][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,848  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,853  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:01,858  [DEBUG] [D][11:26:45][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,864  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,866  [DEBUG] [W][11:26:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:01,872  [DEBUG] [W][11:26:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:01,878  [DEBUG] [W][11:26:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:01,880  [DEBUG] [W][11:26:45][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:01,890  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46500] --- write len --- [256]
 
2025-07-16 07:59:01,891  [DEBUG] [W][11:26:45][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:01,897  [DEBUG] [W][11:26:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:01,899  [DEBUG] [W][11:26:45][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:01,905  [DEBUG] [W][11:26:45][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:01,908  [DEBUG] [W][11:26:45][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:01,911  [DEBUG] [W][11:26:45][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:01,914  [DEBUG] [W][11:26:45][FCTY]Current     = 250 ma
 
2025-07-16 07:59:01,920  [DEBUG] [W][11:26:45][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:01,926  [DEBUG] [W][11:26:45][FCTY]TEMP= 26,BATID= 661240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:01,931  [DEBUG] [W][11:26:45][FCTY]Ext battery vol = 1, adc = 65
 
2025-07-16 07:59:01,937  [DEBUG] [D][11:26:45][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 303 mv
 
2025-07-16 07:59:01,940  [DEBUG] [W][11:26:45][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:01,942  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:01,948  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:01,951  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:01,954  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:01,957  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:01,959  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:01,968  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46600] --- write len --- [256]
 
2025-07-16 07:59:01,970  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:01,979  [DEBUG] [D][11:26:45][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:01,985  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:01,989  [DEBUG] [D][11:26:45][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 1.
 
2025-07-16 07:59:01,995  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:02,003  [DEBUG] [D][11:26:45][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:02,007  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:02,016  [DEBUG] [W][11:26:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:02,019  [DEBUG] [W][11:26:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:02,025  [DEBUG] [W][11:26:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:02,027  [DEBUG] [W][11:26:45][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:02,034  [DEBUG] [W][11:26:45][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:02,036  [DEBUG] [W][11:26:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:02,045  [DEBUG] [D][11:26:45][HSDK][0] flush to flash addr:[0xE46700] --- write len --- [256]
 
2025-07-16 07:59:02,047  [DEBUG] [W][11:26:45][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:02,053  [DEBUG] [W][11:26:45][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:02,056  [DEBUG] [W][11:26:45][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:02,059  [DEBUG] [W][11:26:45][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:02,064  [DEBUG] [W][11:26:45][FCTY]Current     = 250 ma
 
2025-07-16 07:59:02,066  [DEBUG] [W][11:26:45][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:02,073  [DEBUG] [W][11:26:45][FCTY]TEMP= 26,BATID= 660000,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:02,078  [DEBUG] [W][11:26:45][FCTY]Ext battery vol = 1, adc = 68
 
2025-07-16 07:59:02,084  [DEBUG] [D][11:26:45][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 177 mv
 
2025-07-16 07:59:02,087  [DEBUG] [W][11:26:45][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:02,089  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:02,095  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:02,098  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:02,100  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:02,126  [DEBUG] [W][11:26:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:02,132  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:02,134  [DEBUG] [W][11:26:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:02,140  [DEBUG] [D][11:26:45][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:02,145  [DEBUG] [D][11:26:45][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:02,147  [DEBUG] [D][11:26:45][COMM]8722 imu init OK
 
2025-07-16 07:59:02,154  [DEBUG] [D][11:26:45][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:02,156  [DEBUG] [D][11:26:45][CAT1]power_urc_cb ret[76]
 
2025-07-16 07:59:02,163  [DEBUG] [W][11:26:45][COMM]>>>>>Input command = AT+LOCKID=F050816381<<<<<
 
2025-07-16 07:59:02,169  [DEBUG] [D][11:26:45][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:02,170  [DEBUG] [D][11:26:46][COMM]dual bank write result:0
 
2025-07-16 07:59:02,174  [DEBUG] [D][11:26:46][COMM]set lockid successfully
 
2025-07-16 07:59:02,200  [DEBUG] [D][11:26:46][HSDK][0] flush to flash addr:[0xE46800] --- write len --- [256]
 
2025-07-16 07:59:02,206  [DEBUG] [W][11:26:46][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:02,211  [DEBUG] [D][11:26:46][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:02,218  [DEBUG] [D][11:26:46][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:02,221  [DEBUG] [W][11:26:46][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:02,224  [DEBUG] [W][11:26:46][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:02,229  [DEBUG] [W][11:26:46][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:02,232  [DEBUG] [W][11:26:46][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:02,238  [DEBUG] [W][11:26:46][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:02,240  [DEBUG] [W][11:26:46][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:02,246  [DEBUG] [W][11:26:46][FCTY]LockID      = F050816381
 
2025-07-16 07:59:02,350  [DEBUG] [D][11:26:46][HSDK][0] flush to flash addr:[0xE46900] --- write len --- [256]
 
2025-07-16 07:59:02,351  [DEBUG] [W][11:26:46][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:02,352  [DEBUG] [W][11:26:46][FCTY]BLEMacAddr   = DBD1902510CA
 
2025-07-16 07:59:02,354  [DEBUG] [W][11:26:46][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:02,355  [DEBUG] [W][11:26:46][FCTY]Current     = 250 ma
 
2025-07-16 07:59:02,356  [DEBUG] [W][11:26:46][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:02,356  [DEBUG] [W][11:26:46][FCTY]TEMP= 26,BATID= 660000,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:02,357  [DEBUG] [W][11:26:46][FCTY]Ext battery vol = 1, adc = 65
 
2025-07-16 07:59:02,357  [DEBUG] [D][11:26:46][FCTY]Acckey1 vol = 5477 mv, Acckey2 vol = 303 mv
 
2025-07-16 07:59:02,359  [DEBUG] [W][11:26:46][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:02,359  [DEBUG] [W][11:26:46][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:02,361  [DEBUG] [W][11:26:46][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:02,361  [DEBUG] [W][11:26:46][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:02,362  [DEBUG] [W][11:26:46][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:02,363  [DEBUG] [W][11:26:46][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:02,363  [DEBUG] [W][11:26:46][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:02,364  [DEBUG] [W][11:26:46][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:02,364  [DEBUG] [D][11:26:46][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:02,365  [DEBUG] [D][11:26:46][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:02,373  [DEBUG] [W][11:26:46][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-16 07:59:02,461  [DEBUG] [D][11:26:46][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:02,599  [DEBUG] [D][11:26:46][COMM]dual bank write result:0
 
2025-07-16 07:59:02,603  [DEBUG] [D][11:26:46][COMM]set lockname successfully
 
2025-07-16 07:59:02,867  [DEBUG] [D][11:26:46][COMM]9733 imu init OK
 
2025-07-16 07:59:02,880  [DEBUG] [D][11:26:46][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:03,141  [DEBUG] [D][11:26:46][COMM]msg 0223 loss. last_tick:0. cur_tick:10002. period:1000
 
2025-07-16 07:59:03,142  [DEBUG] 
 
2025-07-16 07:59:03,147  [DEBUG] [D][11:26:46][COMM]msg 0225 loss. last_tick:0. cur_tick:10002. period:1000
 
2025-07-16 07:59:03,148  [DEBUG] 
 
2025-07-16 07:59:03,155  [DEBUG] [D][11:26:46][COMM]msg 0229 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-16 07:59:03,157  [DEBUG] 
 
2025-07-16 07:59:03,160  [DEBUG] [D][11:26:46][COMM]msg 0601 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-16 07:59:03,161  [DEBUG] 
 
2025-07-16 07:59:03,169  [DEBUG] [D][11:26:46][COMM]CAN message fault change: 0x0008E00C71E22217->0x0008F80C71E2223F 10004
 
2025-07-16 07:59:03,176  [DEBUG] [D][11:26:46][COMM]CAN fault change: 0x0000000300010E05->0x0000000300010F05 10005
 
2025-07-16 07:59:05,419  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:59:05,421  [DEBUG] flash is 24bit address mode

 
2025-07-16 07:59:05,424  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 07:59:05,427  [DEBUG] HW SW version: 5340 109

 
2025-07-16 07:59:05,430  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 07:59:05,433  [DEBUG] get_boot_mode 0
 
2025-07-16 07:59:05,433  [DEBUG] is_app_complete 0
 
2025-07-16 07:59:05,633  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:59:05,635  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 07:59:05,637  [DEBUG] [ADC]init adc success.

 
2025-07-16 07:59:06,268  [DEBUG] para ret:306,valid:aa

 
2025-07-16 07:59:06,353  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-16 07:59:06,356  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-16 07:59:06,362  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-16 07:59:06,368  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:06,373  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:06,376  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:06,382  [DEBUG] [W][11:26:37][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:06,385  [DEBUG] [W][11:26:37][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:06,390  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:06,393  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:06,396  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:06,402  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:06,405  [DEBUG] [W][11:26:37][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:06,407  [DEBUG] [W][11:26:37][FCTY]Current     = 500 ma
 
2025-07-16 07:59:06,413  [DEBUG] [W][11:26:37][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:06,419  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:06,424  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 7
 
2025-07-16 07:59:06,427  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:06,429  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:06,436  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:06,439  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:06,441  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:06,445  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:06,447  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:06,453  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:06,455  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-16 07:59:06,812  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:59:07,216  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:59:07,219  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-16 07:59:07,222  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-16 07:59:07,224  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-16 07:59:07,231  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 07:59:07,233  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-16 07:59:07,244  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 07:59:07,251  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 07:59:08,435  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 07:59:08,442  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 07:59:19,124  [DEBUG] [W][11:26:50][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201210]
 
2025-07-16 07:59:23,575  [DEBUG] [W][11:26:54][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-16 07:59:23,580  [DEBUG] [D][11:26:54][COMM]Password OK
 
2025-07-16 07:59:23,583  [DEBUG] [W][11:26:54][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:23,591  [DEBUG] [D][11:26:54][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:23,594  [DEBUG] [D][11:26:54][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:23,598  [DEBUG] [W][11:26:54][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:23,604  [DEBUG] [W][11:26:54][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:23,607  [DEBUG] [W][11:26:54][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:23,612  [DEBUG] [W][11:26:54][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:23,615  [DEBUG] [W][11:26:54][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:23,621  [DEBUG] [W][11:26:54][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:23,627  [DEBUG] [D][11:26:54][HSDK][0] flush to flash addr:[0xE45600] --- write len --- [256]
 
2025-07-16 07:59:23,632  [DEBUG] [W][11:26:54][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:23,635  [DEBUG] [W][11:26:54][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:23,638  [DEBUG] [W][11:26:54][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:23,643  [DEBUG] [W][11:26:54][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:23,646  [DEBUG] [W][11:26:54][FCTY]Current     = 150 ma
 
2025-07-16 07:59:23,648  [DEBUG] [W][11:26:54][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:23,659  [DEBUG] [W][11:26:54][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:23,660  [DEBUG] [W][11:26:54][FCTY]Ext battery vol = 2, adc = 84
 
2025-07-16 07:59:23,666  [DEBUG] [D][11:26:54][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:23,671  [DEBUG] [W][11:26:54][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:23,674  [DEBUG] [W][11:26:54][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:23,676  [DEBUG] [W][11:26:54][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:23,683  [DEBUG] [W][11:26:54][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:23,686  [DEBUG] [W][11:26:54][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:23,688  [DEBUG] [W][11:26:54][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:23,698  [DEBUG] [D][11:26:54][HSDK][0] flush to flash addr:[0xE45700] --- write len --- [256]
 
2025-07-16 07:59:23,699  [DEBUG] [W][11:26:54][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:23,709  [DEBUG] [D][11:26:54][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:23,713  [DEBUG] [D][11:26:54][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:23,716  [DEBUG] [W][11:26:54][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:23,724  [DEBUG] [D][11:26:54][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:23,730  [DEBUG] [D][11:26:54][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:23,732  [DEBUG] [W][11:26:54][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:23,738  [DEBUG] [W][11:26:54][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:23,743  [DEBUG] [W][11:26:54][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:23,746  [DEBUG] [W][11:26:54][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:23,751  [DEBUG] [W][11:26:54][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:23,754  [DEBUG] [W][11:26:54][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:23,765  [DEBUG] [D][11:26:54][HSDK][0] flush to flash addr:[0xE45800] --- write len --- [256]
 
2025-07-16 07:59:23,766  [DEBUG] [W][11:26:54][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:23,768  [DEBUG] [W][11:26:54][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:23,774  [DEBUG] [W][11:26:54][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:23,777  [DEBUG] [W][11:26:54][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:23,780  [DEBUG] [W][11:26:54][FCTY]Current     = 150 ma
 
2025-07-16 07:59:23,786  [DEBUG] [W][11:26:54][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:23,791  [DEBUG] [W][11:26:55][FCTY]TEMP= 26,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:23,794  [DEBUG] [W][11:26:55][FCTY]Ext battery vol = 1, adc = 79
 
2025-07-16 07:59:23,800  [DEBUG] [D][11:26:55][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:23,805  [DEBUG] [W][11:26:55][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:23,808  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:23,814  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:23,816  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:23,822  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:23,825  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:23,831  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45900] --- write len --- [256]
 
2025-07-16 07:59:23,836  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:23,842  [DEBUG] [D][11:26:55][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:23,848  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:23,853  [DEBUG] [W][11:26:55][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:23,858  [DEBUG] [D][11:26:55][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:23,864  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:23,869  [DEBUG] [W][11:26:55][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:23,872  [DEBUG] [W][11:26:55][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:23,877  [DEBUG] [W][11:26:55][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:23,883  [DEBUG] [W][11:26:55][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:23,885  [DEBUG] [W][11:26:55][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:23,891  [DEBUG] [W][11:26:55][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:23,898  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45A00] --- write len --- [256]
 
2025-07-16 07:59:23,900  [DEBUG] [W][11:26:55][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:23,905  [DEBUG] [W][11:26:55][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:23,908  [DEBUG] [W][11:26:55][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:23,911  [DEBUG] [W][11:26:55][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:23,916  [DEBUG] [W][11:26:55][FCTY]Current     = 150 ma
 
2025-07-16 07:59:23,919  [DEBUG] [W][11:26:55][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:23,925  [DEBUG] [W][11:26:55][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:23,930  [DEBUG] [W][11:26:55][FCTY]Ext battery vol = 2, adc = 84
 
2025-07-16 07:59:23,937  [DEBUG] [D][11:26:55][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:23,939  [DEBUG] [W][11:26:55][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:23,944  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:23,947  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:23,953  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:23,955  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:23,958  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:23,968  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45B00] --- write len --- [256]
 
2025-07-16 07:59:23,970  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:23,978  [DEBUG] [D][11:26:55][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:23,983  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:23,986  [DEBUG] [W][11:26:55][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:23,994  [DEBUG] [D][11:26:55][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,001  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,003  [DEBUG] [W][11:26:55][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:24,008  [DEBUG] [W][11:26:55][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:24,011  [DEBUG] [W][11:26:55][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:24,017  [DEBUG] [W][11:26:55][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:24,022  [DEBUG] [W][11:26:55][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:24,025  [DEBUG] [W][11:26:55][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:24,032  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45C00] --- write len --- [256]
 
2025-07-16 07:59:24,036  [DEBUG] [W][11:26:55][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:24,039  [DEBUG] [W][11:26:55][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:24,044  [DEBUG] [W][11:26:55][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:24,047  [DEBUG] [W][11:26:55][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:24,049  [DEBUG] [W][11:26:55][FCTY]Current     = 150 ma
 
2025-07-16 07:59:24,052  [DEBUG] [W][11:26:55][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:24,061  [DEBUG] [W][11:26:55][FCTY]TEMP= 26,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:24,064  [DEBUG] [W][11:26:55][FCTY]Ext battery vol = 2, adc = 80
 
2025-07-16 07:59:24,070  [DEBUG] [D][11:26:55][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:24,075  [DEBUG] [W][11:26:55][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:24,078  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:24,084  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:24,086  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:24,090  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:24,095  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:24,101  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45D00] --- write len --- [256]
 
2025-07-16 07:59:24,106  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:24,112  [DEBUG] [D][11:26:55][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,118  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,123  [DEBUG] [W][11:26:55][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:24,128  [DEBUG] [D][11:26:55][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,134  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,139  [DEBUG] [W][11:26:55][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:24,141  [DEBUG] [W][11:26:55][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:24,147  [DEBUG] [W][11:26:55][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:24,150  [DEBUG] [W][11:26:55][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:24,156  [DEBUG] [W][11:26:55][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:24,158  [DEBUG] [W][11:26:55][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:24,165  [DEBUG] [D][11:26:55][HSDK]need to erase for write: is[0x0] ie[0x4E00]
 
2025-07-16 07:59:24,174  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45E00] --- write len --- [256]
 
2025-07-16 07:59:24,175  [DEBUG] [W][11:26:55][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:24,181  [DEBUG] [W][11:26:55][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:24,184  [DEBUG] [W][11:26:55][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:24,187  [DEBUG] [W][11:26:55][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:24,192  [DEBUG] [W][11:26:55][FCTY]Current     = 150 ma
 
2025-07-16 07:59:24,194  [DEBUG] [W][11:26:55][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:24,200  [DEBUG] [W][11:26:55][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:24,206  [DEBUG] [W][11:26:55][FCTY]Ext battery vol = 2, adc = 84
 
2025-07-16 07:59:24,212  [DEBUG] [D][11:26:55][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:24,214  [DEBUG] [W][11:26:55][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:24,221  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:24,223  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:24,226  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:24,232  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:24,234  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:24,243  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE45F00] --- write len --- [256]
 
2025-07-16 07:59:24,245  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:24,255  [DEBUG] [D][11:26:55][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,257  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,262  [DEBUG] [W][11:26:55][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:24,270  [DEBUG] [D][11:26:55][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,338  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,341  [DEBUG] [W][11:26:55][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:24,346  [DEBUG] [W][11:26:55][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:24,351  [DEBUG] [W][11:26:55][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:24,354  [DEBUG] [W][11:26:55][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:24,360  [DEBUG] [W][11:26:55][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:24,362  [DEBUG] [W][11:26:55][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:24,372  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE46000] --- write len --- [256]
 
2025-07-16 07:59:24,373  [DEBUG] [W][11:26:55][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:24,376  [DEBUG] [W][11:26:55][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:24,381  [DEBUG] [W][11:26:55][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:24,385  [DEBUG] [W][11:26:55][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:24,387  [DEBUG] [W][11:26:55][FCTY]Current     = 150 ma
 
2025-07-16 07:59:24,393  [DEBUG] [W][11:26:55][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:24,399  [DEBUG] [W][11:26:55][FCTY]TEMP= 26,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:24,403  [DEBUG] [W][11:26:55][FCTY]Ext battery vol = 2, adc = 83
 
2025-07-16 07:59:24,407  [DEBUG] [D][11:26:55][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 50 mv
 
2025-07-16 07:59:24,412  [DEBUG] [W][11:26:55][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:24,416  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:24,421  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:24,424  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:24,472  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:24,475  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:24,481  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE46100] --- write len --- [256]
 
2025-07-16 07:59:24,486  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:24,492  [DEBUG] [D][11:26:55][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,497  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,503  [DEBUG] [W][11:26:55][COMM]>>>>>Input command = AT+LOCKID=F050816382<<<<<
 
2025-07-16 07:59:24,508  [DEBUG] [D][11:26:55][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:24,516  [DEBUG] [D][11:26:55][COMM]dual bank write result:0
 
2025-07-16 07:59:24,519  [DEBUG] [D][11:26:55][COMM]set lockid successfully
 
2025-07-16 07:59:24,540  [DEBUG] [W][11:26:55][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:24,547  [DEBUG] [D][11:26:55][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,554  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,556  [DEBUG] [W][11:26:55][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:24,561  [DEBUG] [W][11:26:55][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:24,564  [DEBUG] [W][11:26:55][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:24,569  [DEBUG] [W][11:26:55][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:24,571  [DEBUG] [W][11:26:55][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:24,582  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE46200] --- write len --- [256]
 
2025-07-16 07:59:24,583  [DEBUG] [W][11:26:55][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:24,588  [DEBUG] [W][11:26:55][FCTY]LockID      = F050816382
 
2025-07-16 07:59:24,635  [DEBUG] [W][11:26:55][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:24,637  [DEBUG] [W][11:26:55][FCTY]BLEMacAddr   = E9EBA4620D90
 
2025-07-16 07:59:24,639  [DEBUG] [W][11:26:55][FCTY]Bat         = 3824 mv
 
2025-07-16 07:59:24,640  [DEBUG] [W][11:26:55][FCTY]Current     = 200 ma
 
2025-07-16 07:59:24,641  [DEBUG] [W][11:26:55][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:24,643  [DEBUG] [W][11:26:55][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD2
 
2025-07-16 07:59:24,645  [DEBUG] [W][11:26:55][FCTY]Ext battery vol = 2, adc = 81
 
2025-07-16 07:59:24,647  [DEBUG] [D][11:26:55][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:24,648  [DEBUG] [W][11:26:55][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:24,648  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_BOOT = 0.0.0
 
2025-07-16 07:59:24,649  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
 
2025-07-16 07:59:24,649  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_APP = 21.2.1
 
2025-07-16 07:59:24,649  [DEBUG] [D][11:26:55][HSDK][0] flush to flash addr:[0xE46300] --- write len --- [256]
 
2025-07-16 07:59:24,651  [DEBUG] [W][11:26:55][FCTY]CAT1_KERNEL_RTK = 1.2.4
 
2025-07-16 07:59:24,653  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_PLATFORM = C4
 
2025-07-16 07:59:24,659  [DEBUG] [W][11:26:55][FCTY]CAT1_GNSS_VERSION = V3465
 
2025-07-16 07:59:24,665  [DEBUG] [D][11:26:55][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:24,669  [DEBUG] [D][11:26:55][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:24,740  [DEBUG] [W][11:26:56][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-16 07:59:24,746  [DEBUG] [D][11:26:56][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:24,761  [DEBUG] [D][11:26:56][PROT]CLEAN,SEND:0
 
2025-07-16 07:59:24,774  [DEBUG] [D][11:26:56][PROT]CLEAN:0
 
2025-07-16 07:59:24,788  [DEBUG] [D][11:26:56][PROT]index:1 1730201216
 
2025-07-16 07:59:24,791  [DEBUG] [D][11:26:56][PROT]is_send:0
 
2025-07-16 07:59:24,794  [DEBUG] [D][11:26:56][PROT]sequence_num:1
 
2025-07-16 07:59:24,797  [DEBUG] [D][11:26:56][PROT]retry_timeout:0
 
2025-07-16 07:59:24,799  [DEBUG] [D][11:26:56][PROT]retry_times:3
 
2025-07-16 07:59:24,802  [DEBUG] [D][11:26:56][PROT]send_path:0x2
 
2025-07-16 07:59:24,807  [DEBUG] [D][11:26:56][PROT]min_index:1, type:0x4B02, priority:0
 
2025-07-16 07:59:24,814  [DEBUG] [D][11:26:56][PROT]===========================================================
 
2025-07-16 07:59:24,820  [DEBUG] [W][11:26:56][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201216]
 
2025-07-16 07:59:24,828  [DEBUG] [D][11:26:56][PROT]===========================================================
 
2025-07-16 07:59:24,833  [DEBUG] [D][11:26:56][PROT]sending traceid [9999999999900002]
 
2025-07-16 07:59:24,835  [DEBUG] [D][11:26:56][PROT]Send_TO_M2M [1730201216]
 
2025-07-16 07:59:24,841  [DEBUG] [D][11:26:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 07:59:24,843  [DEBUG] [D][11:26:56][SAL ]sock send credit cnt[6]
 
2025-07-16 07:59:24,849  [DEBUG] [D][11:26:56][SAL ]sock send ind credit cnt[6]
 
2025-07-16 07:59:24,856  [DEBUG] [D][11:26:56][M2M ]m2m send data len[326]
 
2025-07-16 07:59:24,857  [DEBUG] [D][11:26:56][SAL ]Cellular task submsg id[10]
 
2025-07-16 07:59:24,922  [DEBUG] [D][11:26:56][SAL ]cellular SEND socket id[0] type[1], len[326], data[0x2005a338] format[0]
 
2025-07-16 07:59:24,928  [DEBUG] [D][11:26:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 07:59:24,933  [DEBUG] [D][11:26:56][CAT1]gsm read msg sub id: 15
 
2025-07-16 07:59:24,936  [DEBUG] [D][11:26:56][CAT1]tx ret[17] >>> AT+QISEND=0,326
 
2025-07-16 07:59:24,936  [DEBUG] 
 
2025-07-16 07:59:24,940  [DEBUG] [D][11:26:56][CAT1]Send Data To Server[326][329] ... ->:
 
2025-07-16 07:59:24,973  [DEBUG] 00A3B98A113311331133113311331B88B7A7F7D4262796FABFA4CC9527E827193483422B59AC5F8589C366C708D9CFD92DE142D5F79D0B3A647DE3039860D6586D67C8842717C8B8B1CB5052473DA5916F0A52F9E4A35FD1B6320F1A8D9F0BF4E3243FD3EDC5F7C66703042A3FBCD6F7CFA744BA6E63B316F76145EED1B8EE799A32168935EA8C6C9B5641A6620D590EADDC4BB60CF4ECFB276B658448425A2299E137
 
2025-07-16 07:59:24,975  [DEBUG] [D][11:26:56][COMM]dual bank write result:0
 
2025-07-16 07:59:24,977  [DEBUG] [D][11:26:56][COMM]set lockname successfully
 
2025-07-16 07:59:25,063  [DEBUG] [D][11:26:56][CAT1]<<< 
 
2025-07-16 07:59:25,063  [DEBUG] SEND OK
 
2025-07-16 07:59:25,064  [DEBUG] 
 
2025-07-16 07:59:25,064  [DEBUG] [D][11:26:56][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 07:59:25,065  [DEBUG] [D][11:26:56][CAT1]sub id: 15, ret: 11
 
2025-07-16 07:59:25,066  [DEBUG] 
 
2025-07-16 07:59:25,067  [DEBUG] [D][11:26:56][SAL ]Cellular task submsg id[68]
 
2025-07-16 07:59:25,067  [DEBUG] [D][11:26:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 07:59:25,068  [DEBUG] [D][11:26:56][COMM]IMU: [989,5,75] ret=22 AWAKE!
 
2025-07-16 07:59:25,069  [DEBUG] [D][11:26:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 07:59:25,069  [DEBUG] [D][11:26:56][M2M ]g_m2m_is_idle become 1
 
2025-07-16 07:59:25,069  [DEBUG] [D][11:26:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 07:59:25,070  [DEBUG] [D][11:26:56][PROT]M2M Send ok [1730201216]
 
2025-07-16 07:59:25,467  [DEBUG] [D][11:26:56][COMM]IMU: [1001,4,56] ret=26 AWAKE!
 
2025-07-16 07:59:27,861  [DEBUG] ?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:59:27,862  [DEBUG] flash is 24bit address mode

 
2025-07-16 07:59:27,868  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 07:59:27,869  [DEBUG] HW SW version: 5340 109

 
2025-07-16 07:59:27,872  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 07:59:27,875  [DEBUG] get_boot_mode 0
 
2025-07-16 07:59:27,875  [DEBUG] is_app_complete 0
 
2025-07-16 07:59:28,073  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:59:28,075  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 07:59:28,077  [DEBUG] [ADC]init adc success.

 
2025-07-16 07:59:28,705  [DEBUG] para ret:306,valid:aa

 
2025-07-16 07:59:28,783  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-16 07:59:28,786  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-16 07:59:28,794  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-16 07:59:28,799  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:28,801  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:28,807  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:28,810  [DEBUG] [W][11:26:37][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:28,815  [DEBUG] [W][11:26:37][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:28,818  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:28,823  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:28,827  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:28,831  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:28,835  [DEBUG] [W][11:26:37][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:28,838  [DEBUG] [W][11:26:37][FCTY]Current     = 450 ma
 
2025-07-16 07:59:28,841  [DEBUG] [W][11:26:37][FCTY]VBUS        = 2600 mv
 
2025-07-16 07:59:28,849  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 670032,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:28,851  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 7
 
2025-07-16 07:59:28,857  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:28,860  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:28,863  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:28,865  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:28,872  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:28,874  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:28,877  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:28,880  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:28,884  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-16 07:59:29,242  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:59:29,646  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:59:29,649  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-16 07:59:29,652  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-16 07:59:29,656  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-16 07:59:29,661  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 07:59:29,663  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-16 07:59:29,675  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 07:59:29,681  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 07:59:30,864  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 07:59:30,870  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 07:59:33,659  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-16 07:59:33,682  [DEBUG] [D][11:26:42][COMM]Password OK
 
2025-07-16 07:59:33,684  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE44B00] --- write len --- [256]
 
2025-07-16 07:59:33,685  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:33,686  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:33,687  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:33,689  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:33,695  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:33,701  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:33,704  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:33,709  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:33,712  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:33,717  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:33,724  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE44C00] --- write len --- [256]
 
2025-07-16 07:59:33,726  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:33,731  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:33,734  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:33,737  [DEBUG] [W][11:26:42][FCTY]Current     = 300 ma
 
2025-07-16 07:59:33,743  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:33,749  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:33,752  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 33
 
2025-07-16 07:59:33,758  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:33,763  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:33,765  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:33,768  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:33,774  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:33,777  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:33,780  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:33,782  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:33,788  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:33,794  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:33,799  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:33,804  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:33,809  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:33,815  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:33,818  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:33,827  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE44D00] --- write len --- [256]
 
2025-07-16 07:59:33,832  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:33,835  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:33,840  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:33,843  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:33,848  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:33,851  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:33,854  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:33,860  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:33,862  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:33,869  [DEBUG] [D][11:26:42][HSDK]need to erase for write: is[0x0] ie[0x3E00]
 
2025-07-16 07:59:33,878  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE44E00] --- write len --- [256]
 
2025-07-16 07:59:33,879  [DEBUG] [W][11:26:42][FCTY]Current     = 300 ma
 
2025-07-16 07:59:33,882  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:33,890  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 668764,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:33,893  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 36
 
2025-07-16 07:59:33,899  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:33,901  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:33,908  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:33,910  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:33,913  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:33,916  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:33,921  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:33,925  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:33,927  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:33,936  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:33,938  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:33,945  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:33,951  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:33,957  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:33,959  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:33,965  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:33,972  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE44F00] --- write len --- [256]
 
2025-07-16 07:59:33,974  [DEBUG] [D][11:26:42][COMM]5689 imu init OK
 
2025-07-16 07:59:33,979  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:33,985  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:33,988  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:33,993  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:33,996  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:33,999  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:34,004  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:34,007  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:34,010  [DEBUG] [W][11:26:42][FCTY]Current     = 300 ma
 
2025-07-16 07:59:34,015  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:34,019  [DEBUG] [D][11:26:42][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:34,028  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45000] --- write len --- [256]
 
2025-07-16 07:59:34,032  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 668764,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:34,035  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 27
 
2025-07-16 07:59:34,042  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:34,047  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:34,049  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:34,052  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:34,057  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:34,060  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:34,063  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:34,065  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:34,068  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:34,078  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,083  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,088  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:34,093  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,100  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,102  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:34,108  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:34,113  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:34,120  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45100] --- write len --- [256]
 
2025-07-16 07:59:34,124  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:34,127  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:34,132  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:34,135  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:34,138  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:34,144  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:34,147  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:34,149  [DEBUG] [W][11:26:42][FCTY]Current     = 300 ma
 
2025-07-16 07:59:34,155  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:34,160  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:34,166  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 32
 
2025-07-16 07:59:34,169  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5559 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:34,175  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:34,178  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:34,186  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45200] --- write len --- [256]
 
2025-07-16 07:59:34,188  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:34,191  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:34,194  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:34,200  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:34,203  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:34,205  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:34,214  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,216  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,222  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:34,230  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,236  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,238  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:34,243  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:34,246  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:34,253  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:34,259  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45300] --- write len --- [256]
 
2025-07-16 07:59:34,264  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:34,266  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:34,272  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:34,275  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:34,277  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:34,283  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:34,286  [DEBUG] [W][11:26:42][FCTY]Current     = 300 ma
 
2025-07-16 07:59:34,289  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:34,295  [DEBUG] [D][11:26:42][CAT1]power_urc_cb ret[5]
 
2025-07-16 07:59:34,300  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 670032,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:34,303  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 37
 
2025-07-16 07:59:34,309  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:34,315  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:34,317  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:34,319  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:34,322  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:34,328  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:34,331  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:34,334  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:34,342  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45400] --- write len --- [256]
 
2025-07-16 07:59:34,345  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:34,350  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,356  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,361  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:34,369  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,373  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,378  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:34,383  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:34,386  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:34,391  [DEBUG] [W][11:26:42][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:34,394  [DEBUG] [W][11:26:42][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:34,399  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:34,406  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE45500] --- write len --- [256]
 
2025-07-16 07:59:34,411  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:34,414  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:34,416  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:34,421  [DEBUG] [W][11:26:42][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:34,424  [DEBUG] [W][11:26:42][FCTY]Current     = 250 ma
 
2025-07-16 07:59:34,428  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:34,433  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:34,438  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 36
 
2025-07-16 07:59:34,445  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:34,448  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:34,454  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:34,456  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:34,459  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:34,462  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:34,465  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:34,470  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:34,473  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:34,481  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,485  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,705  [DEBUG] [W][11:26:43][COMM]>>>>>Input command = AT+LOCKID=F050816383<<<<<
 
2025-07-16 07:59:34,792  [DEBUG] [D][11:26:43][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:34,926  [DEBUG] [D][11:26:43][COMM]dual bank write result:0
 
2025-07-16 07:59:34,928  [DEBUG] [D][11:26:43][COMM]set lockid successfully
 
2025-07-16 07:59:34,932  [DEBUG] [D][11:26:43][COMM]6855 imu init OK
 
2025-07-16 07:59:34,938  [DEBUG] [D][11:26:43][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:34,951  [DEBUG] [D][11:26:43][HSDK][0] flush to flash addr:[0xE45600] --- write len --- [256]
 
2025-07-16 07:59:34,956  [DEBUG] [W][11:26:43][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:34,961  [DEBUG] [D][11:26:43][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:34,968  [DEBUG] [D][11:26:43][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:34,972  [DEBUG] [W][11:26:43][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:34,975  [DEBUG] [W][11:26:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:34,981  [DEBUG] [W][11:26:43][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:34,984  [DEBUG] [W][11:26:43][FCTY]DeviceID    = *************92
 
2025-07-16 07:59:34,990  [DEBUG] [W][11:26:43][FCTY]HardwareID  = 868667087048861
 
2025-07-16 07:59:34,992  [DEBUG] [W][11:26:43][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:34,997  [DEBUG] [W][11:26:43][FCTY]LockID      = F050816383
 
2025-07-16 07:59:35,100  [DEBUG] [D][11:26:43][HSDK][0] flush to flash addr:[0xE45700] --- write len --- [256]
 
2025-07-16 07:59:35,101  [DEBUG] [W][11:26:43][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:35,102  [DEBUG] [W][11:26:43][FCTY]BLEMacAddr   = D4CAEFFD9E83
 
2025-07-16 07:59:35,105  [DEBUG] [W][11:26:43][FCTY]Bat         = 3844 mv
 
2025-07-16 07:59:35,106  [DEBUG] [W][11:26:43][FCTY]Current     = 250 ma
 
2025-07-16 07:59:35,106  [DEBUG] [W][11:26:43][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:35,107  [DEBUG] [W][11:26:43][FCTY]TEMP= 26,BATID= 668764,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:35,108  [DEBUG] [W][11:26:43][FCTY]Ext battery vol = 0, adc = 35
 
2025-07-16 07:59:35,110  [DEBUG] [D][11:26:43][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:35,111  [DEBUG] [W][11:26:43][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:35,112  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:35,112  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:35,113  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:35,113  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:35,114  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:35,114  [DEBUG] [W][11:26:43][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:35,115  [DEBUG] [W][11:26:43][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:35,115  [DEBUG] [D][11:26:43][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:35,115  [DEBUG] [D][11:26:43][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:35,117  [DEBUG] [W][11:26:44][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-16 07:59:35,204  [DEBUG] [D][11:26:44][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:35,342  [DEBUG] [D][11:26:44][COMM]dual bank write result:0
 
2025-07-16 07:59:35,345  [DEBUG] [D][11:26:44][COMM]set lockname successfully
 
2025-07-16 07:59:35,936  [DEBUG] [D][11:26:44][COMM]7866 imu init OK
 
2025-07-16 07:59:35,948  [DEBUG] [D][11:26:44][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:36,887  [DEBUG] [D][11:26:45][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 1.
 
2025-07-16 07:59:36,948  [DEBUG] [D][11:26:45][COMM]8877 imu init OK
 
2025-07-16 07:59:36,957  [DEBUG] [D][11:26:45][CAT1]power_urc_cb ret[76]
 
2025-07-16 07:59:36,962  [DEBUG] [D][11:26:45][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:37,959  [DEBUG] [D][11:26:46][COMM]9888 imu init OK
 
2025-07-16 07:59:37,972  [DEBUG] [D][11:26:46][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:38,078  [DEBUG] [D][11:26:46][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-16 07:59:38,079  [DEBUG] 
 
2025-07-16 07:59:38,083  [DEBUG] [D][11:26:46][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-16 07:59:38,083  [DEBUG] 
 
2025-07-16 07:59:39,925  [DEBUG] [D][11:26:46][COMM]msg 0229 loss. l?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:59:39,926  [DEBUG] flash is 24bit address mode

 
2025-07-16 07:59:39,929  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 07:59:39,932  [DEBUG] HW SW version: 5340 109

 
2025-07-16 07:59:39,935  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 07:59:39,938  [DEBUG] get_boot_mode 0
 
2025-07-16 07:59:39,938  [DEBUG] is_app_complete 0
 
2025-07-16 07:59:40,138  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 07:59:40,140  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 07:59:40,142  [DEBUG] [ADC]init adc success.

 
2025-07-16 07:59:40,769  [DEBUG] para ret:306,valid:aa

 
2025-07-16 07:59:40,865  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-16 07:59:40,868  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-16 07:59:40,874  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-16 07:59:40,882  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:40,887  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:40,889  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:40,895  [DEBUG] [W][11:26:37][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:40,897  [DEBUG] [W][11:26:37][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:40,903  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:40,906  [DEBUG] [W][11:26:37][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:40,909  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:40,914  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:40,917  [DEBUG] [W][11:26:37][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:40,920  [DEBUG] [W][11:26:37][FCTY]Current     = 400 ma
 
2025-07-16 07:59:40,926  [DEBUG] [W][11:26:37][FCTY]VBUS        = 4500 mv
 
2025-07-16 07:59:40,931  [DEBUG] [W][11:26:37][FCTY]TEMP= 0,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:40,938  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 0, adc = 0
 
2025-07-16 07:59:40,940  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:40,943  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:40,948  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:40,951  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:40,954  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:40,956  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:40,959  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:40,965  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:40,967  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-16 07:59:41,324  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:59:41,729  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 07:59:41,731  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-16 07:59:41,735  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-16 07:59:41,738  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-16 07:59:41,743  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 07:59:41,746  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-16 07:59:41,757  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 07:59:41,764  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 07:59:42,947  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 07:59:42,954  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 07:59:45,094  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+PWD=6789<<<<<
 
2025-07-16 07:59:45,097  [DEBUG] [D][11:26:41][COMM]Password OK
 
2025-07-16 07:59:45,105  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE46100] --- write len --- [256]
 
2025-07-16 07:59:45,108  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:45,116  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,122  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,125  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:45,129  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:45,136  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:45,138  [DEBUG] [W][11:26:41][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:45,143  [DEBUG] [W][11:26:41][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:45,146  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:45,152  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:45,158  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE46200] --- write len --- [256]
 
2025-07-16 07:59:45,161  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:45,166  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:45,169  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:45,172  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-16 07:59:45,177  [DEBUG] [W][11:26:41][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:45,182  [DEBUG] [W][11:26:41][FCTY]TEMP= 26,BATID= 666240,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:45,186  [DEBUG] [W][11:26:41][FCTY]Ext battery vol = 0, adc = 22
 
2025-07-16 07:59:45,193  [DEBUG] [D][11:26:41][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:45,198  [DEBUG] [W][11:26:41][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:45,200  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:45,203  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:45,208  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:45,211  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:45,214  [DEBUG] [W][11:26:41][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:45,216  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:45,222  [DEBUG] [W][11:26:41][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:45,229  [DEBUG] [D][11:26:41][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,234  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,239  [DEBUG] [W][11:26:41][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:45,244  [DEBUG] [D][11:26:41][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,250  [DEBUG] [D][11:26:41][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,252  [DEBUG] [W][11:26:41][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:45,261  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE46300] --- write len --- [256]
 
2025-07-16 07:59:45,267  [DEBUG] [W][11:26:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:45,269  [DEBUG] [W][11:26:41][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:45,274  [DEBUG] [W][11:26:41][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:45,277  [DEBUG] [W][11:26:41][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:45,283  [DEBUG] [W][11:26:41][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:45,286  [DEBUG] [W][11:26:41][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:45,289  [DEBUG] [W][11:26:41][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:45,294  [DEBUG] [W][11:26:41][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:45,297  [DEBUG] [W][11:26:41][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:45,306  [DEBUG] [D][11:26:41][HSDK][0] flush to flash addr:[0xE46400] --- write len --- [256]
 
2025-07-16 07:59:45,308  [DEBUG] [W][11:26:41][FCTY]Current     = 200 ma
 
2025-07-16 07:59:45,310  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:45,321  [DEBUG] [D][11:26:42][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
 
2025-07-16 07:59:45,322  [DEBUG] 
 
2025-07-16 07:59:45,326  [DEBUG] [D][11:26:42][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
 
2025-07-16 07:59:45,326  [DEBUG] 
 
2025-07-16 07:59:45,334  [DEBUG] [D][11:26:42][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:8 61
 
2025-07-16 07:59:45,334  [DEBUG] 
 
2025-07-16 07:59:45,343  [DEBUG] [D][11:26:42][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:9 62
 
2025-07-16 07:59:45,344  [DEBUG] 
 
2025-07-16 07:59:45,351  [DEBUG] [D][11:26:42][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
 
2025-07-16 07:59:45,352  [DEBUG] 
 
2025-07-16 07:59:45,359  [DEBUG] [D][11:26:42][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:11 64
 
2025-07-16 07:59:45,362  [DEBUG] 
 
2025-07-16 07:59:45,368  [DEBUG] [D][11:26:42][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:12 65
 
2025-07-16 07:59:45,369  [DEBUG] 
 
2025-07-16 07:59:45,373  [DEBUG] [D][11:26:42][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5018. period:500. j,i:21 74
 
2025-07-16 07:59:45,374  [DEBUG] 
 
2025-07-16 07:59:45,382  [DEBUG] [D][11:26:42][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:22 75
 
2025-07-16 07:59:45,383  [DEBUG] 
 
2025-07-16 07:59:45,391  [DEBUG] [D][11:26:42][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
 
2025-07-16 07:59:45,392  [DEBUG] 
 
2025-07-16 07:59:45,398  [DEBUG] [D][11:26:42][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:25 78
 
2025-07-16 07:59:45,399  [DEBUG] 
 
2025-07-16 07:59:45,407  [DEBUG] [D][11:26:42][COMM]bat msg 025C loss. last_tick:0. cur_tick:5020. period:500. j,i:26 79
 
2025-07-16 07:59:45,408  [DEBUG] 
 
2025-07-16 07:59:45,415  [DEBUG] [D][11:26:42][COMM]sd485 msg 025C loss. last_tick:0. cur_tick:5021. period:500. j,i:0 79
 
2025-07-16 07:59:45,420  [DEBUG] [D][11:26:42][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008E00C71E22217 5021
 
2025-07-16 07:59:45,430  [DEBUG] [D][11:26:42][COMM]CAN message bat fault change: 0x000600BC->0x06E61FFC 5021
 
2025-07-16 07:59:45,435  [DEBUG] [D][11:26:42][COMM]CAN message sd485 fault change: 0x0000000E->0x0000000F 5022
 
2025-07-16 07:59:45,443  [DEBUG] [D][11:26:42][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010E05 5022
 
2025-07-16 07:59:45,447  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:45,452  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-16 07:59:45,459  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5514 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:45,462  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:45,465  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:45,470  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:45,473  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:45,475  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:45,478  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:45,484  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:45,487  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:45,493  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,498  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,504  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:45,508  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,515  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,519  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:45,522  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:45,532  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46500] --- write len --- [256]
 
2025-07-16 07:59:45,537  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:45,539  [DEBUG] [W][11:26:42][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:45,545  [DEBUG] [W][11:26:42][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:45,548  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:45,550  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:45,556  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:45,559  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:45,562  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:45,567  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-16 07:59:45,570  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:45,579  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46600] --- write len --- [256]
 
2025-07-16 07:59:45,583  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:45,586  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 24
 
2025-07-16 07:59:45,593  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:45,598  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:45,600  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:45,604  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:45,606  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:45,611  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:45,615  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:45,618  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:45,620  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:45,629  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,635  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,637  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:45,644  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,651  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,653  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:45,659  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:45,664  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:45,670  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46700] --- write len --- [256]
 
2025-07-16 07:59:45,675  [DEBUG] [W][11:26:42][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:45,678  [DEBUG] [W][11:26:42][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:45,684  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:45,686  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:45,690  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:45,696  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:45,698  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:45,701  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-16 07:59:45,706  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:45,712  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:45,714  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 20
 
2025-07-16 07:59:45,721  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:45,727  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:45,729  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:45,738  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46800] --- write len --- [256]
 
2025-07-16 07:59:45,740  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:45,743  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:45,747  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:45,748  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:45,754  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:45,756  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:45,765  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,768  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,774  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:45,782  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,785  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,790  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:45,795  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:45,798  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:45,803  [DEBUG] [W][11:26:42][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:45,810  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46900] --- write len --- [256]
 
2025-07-16 07:59:45,815  [DEBUG] [W][11:26:42][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:45,818  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:45,823  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:45,826  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:45,828  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:45,834  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:45,837  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-16 07:59:45,839  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:45,848  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:45,851  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-16 07:59:45,857  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:45,860  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:45,865  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:45,868  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:45,871  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:45,874  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:45,879  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:45,881  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:45,888  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46A00] --- write len --- [256]
 
2025-07-16 07:59:45,893  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:45,899  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,904  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,911  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:45,915  [DEBUG] [D][11:26:42][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:45,921  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:45,926  [DEBUG] [W][11:26:42][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:45,929  [DEBUG] [W][11:26:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:45,935  [DEBUG] [W][11:26:42][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:45,938  [DEBUG] [W][11:26:42][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:45,943  [DEBUG] [W][11:26:42][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:45,945  [DEBUG] [W][11:26:42][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:45,955  [DEBUG] [D][11:26:42][HSDK][0] flush to flash addr:[0xE46B00] --- write len --- [256]
 
2025-07-16 07:59:45,957  [DEBUG] [W][11:26:42][FCTY]LockID      = FFFFFFFFFF
 
2025-07-16 07:59:45,960  [DEBUG] [W][11:26:42][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:45,965  [DEBUG] [W][11:26:42][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:45,968  [DEBUG] [W][11:26:42][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:45,973  [DEBUG] [W][11:26:42][FCTY]Current     = 200 ma
 
2025-07-16 07:59:45,976  [DEBUG] [W][11:26:42][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:46,029  [DEBUG] [W][11:26:42][FCTY]TEMP= 26,BATID= 667500,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:46,034  [DEBUG] [W][11:26:42][FCTY]Ext battery vol = 0, adc = 23
 
2025-07-16 07:59:46,038  [DEBUG] [D][11:26:42][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:46,044  [DEBUG] [W][11:26:42][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:46,046  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:46,048  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:46,055  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:46,057  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:46,060  [DEBUG] [W][11:26:42][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:46,063  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:46,068  [DEBUG] [W][11:26:42][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:46,074  [DEBUG] [D][11:26:42][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:46,079  [DEBUG] [D][11:26:42][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:46,082  [DEBUG] [D][11:26:42][COMM]5689 imu init OK
 
2025-07-16 07:59:46,088  [DEBUG] [D][11:26:42][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:46,094  [DEBUG] [W][11:26:42][COMM]>>>>>Input command = AT+LOCKID=F050816384<<<<<
 
2025-07-16 07:59:46,097  [DEBUG] [D][11:26:42][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:46,141  [DEBUG] [D][11:26:42][CAT1]power_urc_cb ret[5]
 
2025-07-16 07:59:46,170  [DEBUG] [D][11:26:43][COMM]dual bank write result:0
 
2025-07-16 07:59:46,172  [DEBUG] [D][11:26:43][COMM]set lockid successfully
 
2025-07-16 07:59:46,194  [DEBUG] [D][11:26:43][HSDK][0] flush to flash addr:[0xE46C00] --- write len --- [256]
 
2025-07-16 07:59:46,200  [DEBUG] [W][11:26:43][COMM]>>>>>Input command = AT+INFO<<<<<
 
2025-07-16 07:59:46,205  [DEBUG] [D][11:26:43][FCTY]==========System Info E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:46,211  [DEBUG] [D][11:26:43][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:46,216  [DEBUG] [W][11:26:43][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 07:59:46,218  [DEBUG] [W][11:26:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 07:59:46,224  [DEBUG] [W][11:26:43][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 07:59:46,227  [DEBUG] [W][11:26:43][FCTY]DeviceID    = ***************
 
2025-07-16 07:59:46,232  [DEBUG] [W][11:26:43][FCTY]HardwareID  = ***************
 
2025-07-16 07:59:46,235  [DEBUG] [W][11:26:43][FCTY]MoBikeID    = 9999999999
 
2025-07-16 07:59:46,241  [DEBUG] [W][11:26:43][FCTY]LockID      = F050816384
 
2025-07-16 07:59:46,303  [DEBUG] [D][11:26:43][HSDK][0] flush to flash addr:[0xE46D00] --- write len --- [256]
 
2025-07-16 07:59:46,305  [DEBUG] [W][11:26:43][FCTY]BLEFWVersion= 105
 
2025-07-16 07:59:46,306  [DEBUG] [W][11:26:43][FCTY]BLEMacAddr   = F5E51A65867A
 
2025-07-16 07:59:46,308  [DEBUG] [W][11:26:43][FCTY]Bat         = 3864 mv
 
2025-07-16 07:59:46,309  [DEBUG] [W][11:26:43][FCTY]Current     = 250 ma
 
2025-07-16 07:59:46,310  [DEBUG] [W][11:26:43][FCTY]VBUS        = 4400 mv
 
2025-07-16 07:59:46,311  [DEBUG] [W][11:26:43][FCTY]TEMP= 26,BATID= 668764,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 07:59:46,312  [DEBUG] [W][11:26:43][FCTY]Ext battery vol = 0, adc = 27
 
2025-07-16 07:59:46,316  [DEBUG] [D][11:26:43][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 0 mv
 
2025-07-16 07:59:46,316  [DEBUG] [W][11:26:43][FCTY]Bike Type flag is invalied
 
2025-07-16 07:59:46,317  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 07:59:46,317  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 07:59:46,318  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 07:59:46,318  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 07:59:46,319  [DEBUG] [W][11:26:43][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 07:59:46,319  [DEBUG] [W][11:26:43][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 07:59:46,320  [DEBUG] [W][11:26:43][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 07:59:46,320  [DEBUG] [D][11:26:43][FCTY]==================== E4_X42_E4_X50_668V3.1_daf31b22 ==========
 
2025-07-16 07:59:46,323  [DEBUG] [D][11:26:43][FCTY]==========Modules-nRF5340 ==========
 
2025-07-16 07:59:46,329  [DEBUG] [W][11:26:43][COMM]>>>>>Input command = AT+LOCKNAME=SE510<<<<<
 
2025-07-16 07:59:46,417  [DEBUG] [D][11:26:43][COMM]dual backup valid_state=0x11
 
2025-07-16 07:59:46,555  [DEBUG] [D][11:26:43][COMM]dual bank write result:0
 
2025-07-16 07:59:46,557  [DEBUG] [D][11:26:43][COMM]set lockname successfully
 
2025-07-16 07:59:46,837  [DEBUG] [D][11:26:43][COMM]6700 imu init OK
 
2025-07-16 07:59:46,850  [DEBUG] [D][11:26:43][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:47,849  [DEBUG] [D][11:26:44][COMM]7714 imu init OK
 
2025-07-16 07:59:47,862  [DEBUG] [D][11:26:44][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:48,862  [DEBUG] [D][11:26:45][COMM]8727 imu init OK
 
2025-07-16 07:59:48,873  [DEBUG] [D][11:26:45][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:48,973  [DEBUG] [D][11:26:45][COMM]f:set_ext_bat_state. EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 0.
 
2025-07-16 07:59:48,990  [DEBUG] [D][11:26:45][CAT1]power_urc_cb ret[76]
 
2025-07-16 07:59:49,873  [DEBUG] [D][11:26:46][COMM]9738 imu init OK
 
2025-07-16 07:59:49,886  [DEBUG] [D][11:26:46][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:50,143  [DEBUG] [D][11:26:46][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
 
2025-07-16 07:59:50,144  [DEBUG] 
 
2025-07-16 07:59:50,148  [DEBUG] [D][11:26:46][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
 
2025-07-16 07:59:50,149  [DEBUG] 
 
2025-07-16 07:59:50,156  [DEBUG] [D][11:26:46][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
 
2025-07-16 07:59:50,157  [DEBUG] 
 
2025-07-16 07:59:50,162  [DEBUG] [D][11:26:46][COMM]msg 0601 loss. last_tick:0. cur_tick:10005. period:1000
 
2025-07-16 07:59:50,163  [DEBUG] 
 
2025-07-16 07:59:50,171  [DEBUG] [D][11:26:46][COMM]CAN message fault change: 0x0008E00C71E22217->0x0008F80C71E2223F 10005
 
2025-07-16 07:59:50,178  [DEBUG] [D][11:26:46][COMM]CAN fault change: 0x0000000300010E05->0x0000000300010F05 10006
 
2025-07-16 07:59:50,885  [DEBUG] [D][11:26:47][COMM]10750 imu init OK
 
2025-07-16 07:59:50,898  [DEBUG] [D][11:26:47][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:51,835  [DEBUG] [D][11:26:48][CAT1]tx ret[4] >>> AT
 
2025-07-16 07:59:51,836  [DEBUG] 
 
2025-07-16 07:59:51,865  [DEBUG] [D][11:26:48][CAT1]<<< 

 
2025-07-16 07:59:51,866  [DEBUG] OK
 
2025-07-16 07:59:51,876  [DEBUG] 
 
2025-07-16 07:59:51,878  [DEBUG] [D][11:26:48][CAT1]tx ret[6] >>> ATE0
 
2025-07-16 07:59:51,879  [DEBUG] 
 
2025-07-16 07:59:51,896  [DEBUG] [D][11:26:48][COMM]imu error,enter wait
 
2025-07-16 07:59:51,908  [DEBUG] [D][11:26:48][CAT1]<<< 

 
2025-07-16 07:59:51,909  [DEBUG] OK
 
2025-07-16 07:59:51,920  [DEBUG] 
 
2025-07-16 07:59:51,925  [DEBUG] [D][11:26:48][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 07:59:51,926  [DEBUG] 
 
2025-07-16 07:59:51,943  [DEBUG] [D][11:26:48][CAT1]<<< 
 
2025-07-16 07:59:51,953  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"
 
2025-07-16 07:59:51,953  [DEBUG] 
 
2025-07-16 07:59:51,953  [DEBUG] OK
 
2025-07-16 07:59:51,953  [DEBUG] 
 
2025-07-16 07:59:51,957  [DEBUG] [D][11:26:48][CAT1]tx ret[10] >>> AT+CFUN?
 
2025-07-16 07:59:51,958  [DEBUG] 
 
2025-07-16 07:59:51,975  [DEBUG] [D][11:26:48][CAT1]<<< 
 
2025-07-16 07:59:51,977  [DEBUG] +CFUN: 1
 
2025-07-16 07:59:51,977  [DEBUG] 
 
2025-07-16 07:59:51,978  [DEBUG] OK
 
2025-07-16 07:59:51,980  [DEBUG] 
 
2025-07-16 07:59:51,980  [DEBUG] [D][11:26:48][CAT1]exec over: func id: 1, ret: 18
 
2025-07-16 07:59:51,986  [DEBUG] [D][11:26:48][CAT1]sub id: 1, ret: 18
 
2025-07-16 07:59:51,986  [DEBUG] 
 
2025-07-16 07:59:51,989  [DEBUG] [D][11:26:48][SAL ]Cellular task submsg id[68]
 
2025-07-16 07:59:51,996  [DEBUG] [D][11:26:48][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
 
2025-07-16 07:59:52,000  [DEBUG] [D][11:26:48][SAL ]gsm power on ind rst[18]
 
2025-07-16 07:59:52,003  [DEBUG] [D][11:26:48][M2M ]m2m gsm power on, ret[0]
 
2025-07-16 07:59:52,009  [DEBUG] [D][11:26:48][COMM][Audio]exec status ready.
 
2025-07-16 07:59:52,011  [DEBUG] [D][11:26:48][M2M ]m2m_task:m_m2m_thread_setting_queue:0
 
2025-07-16 07:59:52,016  [DEBUG] [D][11:26:48][M2M ]first set address
 
2025-07-16 07:59:52,018  [DEBUG] [D][11:26:48][M2M ]m2m switch to: M2M_GSM_INIT
 
2025-07-16 07:59:52,025  [DEBUG] [D][11:26:48][COMM]imu default ctrl x[0],y[0],z[10]
 
2025-07-16 07:59:52,028  [DEBUG] [D][11:26:48][M2M ]set time[24/10/29,11:26:48+32]
 
2025-07-16 07:59:52,034  [DEBUG] [D][11:26:48][CAT1]gsm read msg sub id: 31
 
2025-07-16 07:59:52,043  [DEBUG] [D][11:26:48][CAT1]tx ret[55] >>> AT+IMUCFG=200,1,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000
 
2025-07-16 07:59:52,044  [DEBUG] 
 
2025-07-16 07:59:52,045  [DEBUG] [D][11:26:48][M2M ]m2m switch to: M2M_GSM_INIT_ACK
 
2025-07-16 07:59:52,071  [DEBUG] [D][11:26:48][COMM]Main Task receive event:1
 
2025-07-16 07:59:52,077  [DEBUG] [D][11:26:48][COMM]Main Task receive event:1 finished processing
 
2025-07-16 07:59:52,236  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,237  [DEBUG] OK
 
2025-07-16 07:59:52,238  [DEBUG] 
 
2025-07-16 07:59:52,241  [DEBUG] [D][11:26:49][CAT1]exec over: func id: 31, ret: 6
 
2025-07-16 07:59:52,249  [DEBUG] [D][11:26:49][CAT1]gsm read msg sub id: 32
 
2025-07-16 07:59:52,253  [DEBUG] [D][11:26:49][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10
 
2025-07-16 07:59:52,254  [DEBUG] 
 
2025-07-16 07:59:52,269  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,269  [DEBUG] OK
 
2025-07-16 07:59:52,271  [DEBUG] 
 
2025-07-16 07:59:52,274  [DEBUG] [D][11:26:49][CAT1]exec over: func id: 32, ret: 6
 
2025-07-16 07:59:52,282  [DEBUG] [D][11:26:49][CAT1]gsm read msg sub id: 5
 
2025-07-16 07:59:52,285  [DEBUG] [D][11:26:49][CAT1]tx ret[8] >>> AT+GSN
 
2025-07-16 07:59:52,287  [DEBUG] 
 
2025-07-16 07:59:52,302  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,305  [DEBUG] ***************
 
2025-07-16 07:59:52,305  [DEBUG] 
 
2025-07-16 07:59:52,305  [DEBUG] OK
 
2025-07-16 07:59:52,314  [DEBUG] 
 
2025-07-16 07:59:52,317  [DEBUG] [D][11:26:49][CAT1]tx ret[9] >>> AT+CIMI
 
2025-07-16 07:59:52,317  [DEBUG] 
 
2025-07-16 07:59:52,335  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,338  [DEBUG] ***************
 
2025-07-16 07:59:52,338  [DEBUG] 
 
2025-07-16 07:59:52,338  [DEBUG] OK
 
2025-07-16 07:59:52,347  [DEBUG] 
 
2025-07-16 07:59:52,350  [DEBUG] [D][11:26:49][CAT1]tx ret[11] >>> AT+CMGF=0
 
2025-07-16 07:59:52,351  [DEBUG] 
 
2025-07-16 07:59:52,369  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,372  [DEBUG] OK
 
2025-07-16 07:59:52,380  [DEBUG] 
 
2025-07-16 07:59:52,383  [DEBUG] [D][11:26:49][CAT1]tx ret[15] >>> AT+CSCS="GSM"
 
2025-07-16 07:59:52,384  [DEBUG] 
 
2025-07-16 07:59:52,403  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,404  [DEBUG] OK
 
2025-07-16 07:59:52,412  [DEBUG] 
 
2025-07-16 07:59:52,416  [DEBUG] [D][11:26:49][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0
 
2025-07-16 07:59:52,417  [DEBUG] 
 
2025-07-16 07:59:52,434  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,435  [DEBUG] OK
 
2025-07-16 07:59:52,444  [DEBUG] 
 
2025-07-16 07:59:52,448  [DEBUG] [D][11:26:49][CAT1]tx ret[10] >>> AT+CPMS?
 
2025-07-16 07:59:52,449  [DEBUG] 
 
2025-07-16 07:59:52,466  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,471  [DEBUG] +CPMS: "ME",0,180,"ME",0,180,"ME",0,180
 
2025-07-16 07:59:52,471  [DEBUG] 
 
2025-07-16 07:59:52,472  [DEBUG] OK
 
2025-07-16 07:59:52,472  [DEBUG] 
 
2025-07-16 07:59:52,485  [DEBUG] [D][11:26:49][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,170,25,3
 
2025-07-16 07:59:52,486  [DEBUG] 
 
2025-07-16 07:59:52,502  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,504  [DEBUG] OK
 
2025-07-16 07:59:52,513  [DEBUG] 
 
2025-07-16 07:59:52,517  [DEBUG] [D][11:26:49][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0
 
2025-07-16 07:59:52,518  [DEBUG] 
 
2025-07-16 07:59:52,546  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,547  [DEBUG] OK
 
2025-07-16 07:59:52,555  [DEBUG] 
 
2025-07-16 07:59:52,558  [DEBUG] [D][11:26:49][CAT1]tx ret[14] >>> AT+QSCLKEX=1
 
2025-07-16 07:59:52,559  [DEBUG] 
 
2025-07-16 07:59:52,579  [DEBUG] [D][11:26:49][CAT1]<<< 
 
2025-07-16 07:59:52,580  [DEBUG] OK
 
2025-07-16 07:59:52,589  [DEBUG] 
 
2025-07-16 07:59:52,592  [DEBUG] [D][11:26:49][CAT1]tx ret[12] >>> AT+CGATT=1
 
2025-07-16 07:59:52,593  [DEBUG] 
 
2025-07-16 07:59:53,897  [DEBUG] [D][11:26:50][COMM]13762 imu init OK
 
2025-07-16 07:59:54,113  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-16 07:59:54,114  [DEBUG] OK
 
2025-07-16 07:59:54,124  [DEBUG] 
 
2025-07-16 07:59:54,127  [DEBUG] [D][11:26:50][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 07:59:54,127  [DEBUG] 
 
2025-07-16 07:59:54,145  [DEBUG] [D][11:26:50][CAT1]<<< 
 
2025-07-16 07:59:54,147  [DEBUG] +CGATT: 1
 
2025-07-16 07:59:54,148  [DEBUG] 
 
2025-07-16 07:59:54,148  [DEBUG] OK
 
2025-07-16 07:59:54,157  [DEBUG] 
 
2025-07-16 07:59:54,161  [DEBUG] [D][11:26:51][CAT1]tx ret[12] >>> AT+QIACT=1
 
2025-07-16 07:59:54,162  [DEBUG] 
 
2025-07-16 07:59:54,435  [DEBUG] [D][11:26:51][COMM]imu work error:[-1]. goto init
 
2025-07-16 07:59:54,922  [DEBUG] [D][11:26:51][CAT1]<<< 
 
2025-07-16 07:59:54,923  [DEBUG] OK
 
2025-07-16 07:59:54,932  [DEBUG] 
 
2025-07-16 07:59:54,936  [DEBUG] [D][11:26:51][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1
 
2025-07-16 07:59:54,938  [DEBUG] 
 
2025-07-16 07:59:54,955  [DEBUG] [D][11:26:51][CAT1]<<< 
 
2025-07-16 07:59:54,956  [DEBUG] OK
 
2025-07-16 07:59:54,965  [DEBUG] 
 
2025-07-16 07:59:54,968  [DEBUG] [D][11:26:51][CAT1]tx ret[17] >>> AT+GPSMODULE=C4
 
2025-07-16 07:59:54,969  [DEBUG] 
 
2025-07-16 07:59:54,986  [DEBUG] [D][11:26:51][CAT1]<<< 
 
2025-07-16 07:59:54,988  [DEBUG] OK
 
2025-07-16 07:59:54,988  [DEBUG] 
 
2025-07-16 07:59:54,992  [DEBUG] [D][11:26:51][CAT1]exec over: func id: 5, ret: 6
 
2025-07-16 07:59:54,995  [DEBUG] [D][11:26:51][CAT1]sub id: 5, ret: 6
 
2025-07-16 07:59:54,995  [DEBUG] 
 
2025-07-16 07:59:55,000  [DEBUG] [D][11:26:51][SAL ]Cellular task submsg id[68]
 
2025-07-16 07:59:55,006  [DEBUG] [D][11:26:51][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
 
2025-07-16 07:59:55,011  [DEBUG] [D][11:26:51][M2M ]m2m gsm comm init done, ret[0]
 
2025-07-16 07:59:55,013  [DEBUG] [D][11:26:51][M2M ]M2M_GSM_INIT OK
 
2025-07-16 07:59:55,017  [DEBUG] [D][11:26:51][CAT1]gsm read msg sub id: 38
 
2025-07-16 07:59:55,022  [DEBUG] [D][11:26:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
 
2025-07-16 07:59:55,025  [DEBUG] [D][11:26:51][SAL ]open socket ind id[4], rst[0]
 
2025-07-16 07:59:55,035  [DEBUG] [D][11:26:51][M2M ]tcpclient_start success host[bikeapi.mobike.com] port[9999]
 
2025-07-16 07:59:55,036  [DEBUG] [D][11:26:51][SAL ]Cellular task submsg id[8]
 
2025-07-16 07:59:55,045  [DEBUG] [D][11:26:51][SAL ]cellular OPEN socket size[144], msg->data[0x2005a060], socket[0]
 
2025-07-16 07:59:55,050  [DEBUG] [D][11:26:51][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
 
2025-07-16 07:59:55,056  [DEBUG] [D][11:26:51][CAT1]tx ret[32] >>> AT+CCLK="24/10/29,11:26:48+32"
 
2025-07-16 07:59:55,056  [DEBUG] 
 
2025-07-16 07:59:55,061  [DEBUG] [D][11:26:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
 
2025-07-16 07:59:55,087  [DEBUG] [D][11:26:51][COMM]Main Task receive event:4
 
2025-07-16 07:59:55,094  [DEBUG] [D][11:26:51][COMM]Main Task receive event:4 finished processing
 
2025-07-16 07:59:55,260  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,261  [DEBUG] OK
 
2025-07-16 07:59:55,262  [DEBUG] 
 
2025-07-16 07:59:55,265  [DEBUG] [D][11:26:52][CAT1]exec over: func id: 38, ret: 6
 
2025-07-16 07:59:55,273  [DEBUG] [D][11:26:52][CAT1]gsm read msg sub id: 8
 
2025-07-16 07:59:55,275  [DEBUG] [D][11:26:52][CAT1]at ops open socket[0]
 
2025-07-16 07:59:55,280  [DEBUG] [D][11:26:52][CAT1]tx ret[11] >>> AT+CGATT?
 
2025-07-16 07:59:55,281  [DEBUG] 
 
2025-07-16 07:59:55,294  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,295  [DEBUG] +CGATT: 1
 
2025-07-16 07:59:55,295  [DEBUG] 
 
2025-07-16 07:59:55,296  [DEBUG] OK
 
2025-07-16 07:59:55,305  [DEBUG] 
 
2025-07-16 07:59:55,307  [DEBUG] [D][11:26:52][CAT1]tx ret[8] >>> AT+CSQ
 
2025-07-16 07:59:55,308  [DEBUG] 
 
2025-07-16 07:59:55,327  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,328  [DEBUG] +CSQ: 31,99
 
2025-07-16 07:59:55,329  [DEBUG] 
 
2025-07-16 07:59:55,329  [DEBUG] OK
 
2025-07-16 07:59:55,337  [DEBUG] 
 
2025-07-16 07:59:55,340  [DEBUG] [D][11:26:52][CAT1]tx ret[11] >>> AT+QIACT?
 
2025-07-16 07:59:55,341  [DEBUG] 
 
2025-07-16 07:59:55,353  [DEBUG] [D][11:26:52][COMM]f:audio_resp_imu_data_parser. fail.[+IMUDAT,2,846886305,29903758,12693663,-659279706,9778]
 
2025-07-16 07:59:55,359  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,363  [DEBUG] +QIACT: 1,1,1,"10.251.6.180"
 
2025-07-16 07:59:55,363  [DEBUG] 
 
2025-07-16 07:59:55,364  [DEBUG] OK
 
2025-07-16 07:59:55,366  [DEBUG] 
 
2025-07-16 07:59:55,373  [DEBUG] [D][11:26:52][COMM]f:audio_resp_imu_data_parser. fail.[+IMUDATA=272,2050,4,117,0,8]
 
2025-07-16 07:59:55,381  [DEBUG] [D][11:26:52][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1
 
2025-07-16 07:59:55,381  [DEBUG] 
 
2025-07-16 07:59:55,392  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,392  [DEBUG] OK
 
2025-07-16 07:59:55,393  [DEBUG] 
 
2025-07-16 07:59:55,398  [DEBUG] [D][11:26:52][CAT1]exec over: func id: 8, ret: 6
 
2025-07-16 07:59:55,436  [DEBUG] [D][11:26:52][COMM]15300 imu init OK
 
2025-07-16 07:59:55,623  [DEBUG] [D][11:26:52][CAT1]opened : 0, 0
 
2025-07-16 07:59:55,627  [DEBUG] [D][11:26:52][SAL ]Cellular task submsg id[68]
 
2025-07-16 07:59:55,631  [DEBUG] [D][11:26:52][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
 
2025-07-16 07:59:55,637  [DEBUG] [D][11:26:52][SAL ]socket connect ind. id[4], rst[3]
 
2025-07-16 07:59:55,642  [DEBUG] [D][11:26:52][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
 
2025-07-16 07:59:55,645  [DEBUG] [D][11:26:52][M2M ]g_m2m_is_idle become 1
 
2025-07-16 07:59:55,651  [DEBUG] [D][11:26:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 07:59:55,653  [DEBUG] [D][11:26:52][PROT]index:0 1730201212
 
2025-07-16 07:59:55,656  [DEBUG] [D][11:26:52][PROT]is_send:0
 
2025-07-16 07:59:55,659  [DEBUG] [D][11:26:52][PROT]sequence_num:0
 
2025-07-16 07:59:55,662  [DEBUG] [D][11:26:52][PROT]retry_timeout:0
 
2025-07-16 07:59:55,665  [DEBUG] [D][11:26:52][PROT]retry_times:1
 
2025-07-16 07:59:55,667  [DEBUG] [D][11:26:52][PROT]send_path:0x2
 
2025-07-16 07:59:55,673  [DEBUG] [D][11:26:52][PROT]min_index:0, type:0x4205, priority:0
 
2025-07-16 07:59:55,681  [DEBUG] [D][11:26:52][PROT]===========================================================
 
2025-07-16 07:59:55,687  [DEBUG] [W][11:26:52][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201212]
 
2025-07-16 07:59:55,693  [DEBUG] [D][11:26:52][PROT]===========================================================
 
2025-07-16 07:59:55,698  [DEBUG] [D][11:26:52][PROT]sending traceid [9999999999900001]
 
2025-07-16 07:59:55,700  [DEBUG] [D][11:26:52][PROT]Send_TO_M2M [1730201212]
 
2025-07-16 07:59:55,707  [DEBUG] [D][11:26:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
 
2025-07-16 07:59:55,712  [DEBUG] [D][11:26:52][SAL ]sock send credit cnt[6]
 
2025-07-16 07:59:55,714  [DEBUG] [D][11:26:52][SAL ]sock send ind credit cnt[6]
 
2025-07-16 07:59:55,717  [DEBUG] [D][11:26:52][M2M ]m2m send data len[294]
 
2025-07-16 07:59:55,723  [DEBUG] [D][11:26:52][SAL ]Cellular task submsg id[10]
 
2025-07-16 07:59:55,732  [DEBUG] [D][11:26:52][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x2005a058] format[0]
 
2025-07-16 07:59:55,737  [DEBUG] [D][11:26:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
 
2025-07-16 07:59:55,739  [DEBUG] [D][11:26:52][CAT1]gsm read msg sub id: 15
 
2025-07-16 07:59:55,746  [DEBUG] [D][11:26:52][CAT1]tx ret[17] >>> AT+QISEND=0,294
 
2025-07-16 07:59:55,748  [DEBUG] 
 
2025-07-16 07:59:55,748  [DEBUG] [D][11:26:52][CAT1]Send Data To Server[294][297] ... ->:
 
2025-07-16 07:59:55,778  [DEBUG] 0093B988113311331133113311331B88B4DFBA8DEB0657ED488C7315B99F001BF356A11B5FFAECC95E15C1EA83CDC9C98E79C85B970CE254494A126B40B34D407B76606CCD205E13339D8312D1C42744019B17FE9C7526E5C1DCC215FC454D57B7DE49F50295B51DBFDEDE6D832A72C66E2C2A0A19311E9827451502689080794829EEBC654366F66F47B19671B94BB8FD921E
 
2025-07-16 07:59:55,779  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,779  [DEBUG] SEND OK
 
2025-07-16 07:59:55,780  [DEBUG] 
 
2025-07-16 07:59:55,781  [DEBUG] [D][11:26:52][CAT1]exec over: func id: 15, ret: 11
 
2025-07-16 07:59:55,787  [DEBUG] [D][11:26:52][CAT1]sub id: 15, ret: 11
 
2025-07-16 07:59:55,787  [DEBUG] 
 
2025-07-16 07:59:55,789  [DEBUG] [D][11:26:52][SAL ]Cellular task submsg id[68]
 
2025-07-16 07:59:55,799  [DEBUG] [D][11:26:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
 
2025-07-16 07:59:55,800  [DEBUG] [D][11:26:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
 
2025-07-16 07:59:55,803  [DEBUG] [D][11:26:52][M2M ]g_m2m_is_idle become 1
 
2025-07-16 07:59:55,809  [DEBUG] [D][11:26:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
 
2025-07-16 07:59:55,813  [DEBUG] [D][11:26:52][PROT]M2M Send ok [1730201212]
 
2025-07-16 07:59:55,931  [DEBUG] [D][11:26:52][GNSS]recv submsg id[1]
 
2025-07-16 07:59:55,935  [DEBUG] [D][11:26:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
 
2025-07-16 07:59:55,941  [DEBUG] [D][11:26:52][GNSS]location recv gms init done evt success
 
2025-07-16 07:59:55,943  [DEBUG] [D][11:26:52][GNSS]GPS start. ret=0
 
2025-07-16 07:59:55,948  [DEBUG] [D][11:26:52][CAT1]gsm read msg sub id: 23
 
2025-07-16 07:59:55,951  [DEBUG] [D][11:26:52][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 07:59:55,952  [DEBUG] 
 
2025-07-16 07:59:55,960  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,972  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#UNKNOW"
 
2025-07-16 07:59:55,973  [DEBUG] 
 
2025-07-16 07:59:55,973  [DEBUG] OK
 
2025-07-16 07:59:55,974  [DEBUG] 
 
2025-07-16 07:59:55,975  [DEBUG] [D][11:26:52][CAT1]tx ret[12] >>> AT+GPSCFG?
 
2025-07-16 07:59:55,976  [DEBUG] 
 
2025-07-16 07:59:55,994  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:55,996  [DEBUG] +GPSCFG:0,0,115200,0,0,65504,0,1,1
 
2025-07-16 07:59:55,997  [DEBUG] 
 
2025-07-16 07:59:55,997  [DEBUG] OK
 
2025-07-16 07:59:55,997  [DEBUG] 
 
2025-07-16 07:59:56,009  [DEBUG] [D][11:26:52][CAT1]tx ret[14] >>> AT+GPSPORT=1
 
2025-07-16 07:59:56,009  [DEBUG] 
 
2025-07-16 07:59:56,028  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:56,029  [DEBUG] OK
 
2025-07-16 07:59:56,037  [DEBUG] 
 
2025-07-16 07:59:56,041  [DEBUG] [D][11:26:52][CAT1]tx ret[14] >>> AT+GPSFREQ=1
 
2025-07-16 07:59:56,041  [DEBUG] 
 
2025-07-16 07:59:56,061  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:56,061  [DEBUG] OK
 
2025-07-16 07:59:56,070  [DEBUG] 
 
2025-07-16 07:59:56,074  [DEBUG] [D][11:26:52][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0
 
2025-07-16 07:59:56,074  [DEBUG] 
 
2025-07-16 07:59:56,094  [DEBUG] [D][11:26:52][CAT1]<<< 
 
2025-07-16 07:59:56,094  [DEBUG] OK
 
2025-07-16 07:59:56,103  [DEBUG] 
 
2025-07-16 07:59:56,105  [DEBUG] [D][11:26:52][CAT1]tx ret[13] >>> AT+GPSPWR=1
 
2025-07-16 07:59:56,106  [DEBUG] 
 
2025-07-16 07:59:57,519  [DEBUG] [D][11:26:54][CAT1]<<< 
 
2025-07-16 07:59:57,520  [DEBUG] OK
 
2025-07-16 07:59:57,529  [DEBUG] 
 
2025-07-16 07:59:57,532  [DEBUG] [D][11:26:54][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F
 
2025-07-16 07:59:57,533  [DEBUG] 
 
2025-07-16 07:59:57,604  [DEBUG] [D][11:26:54][CAT1]<<< 
 
2025-07-16 07:59:57,604  [DEBUG] OK
 
2025-07-16 07:59:57,614  [DEBUG] 
 
2025-07-16 07:59:57,619  [DEBUG] [D][11:26:54][CAT1]tx ret[21] >>> AT+GETVERSION=total
 
2025-07-16 07:59:57,619  [DEBUG] 
 
2025-07-16 07:59:57,647  [DEBUG] [D][11:26:54][CAT1]<<< 
 
2025-07-16 07:59:57,659  [DEBUG] +GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"
 
2025-07-16 07:59:57,659  [DEBUG] 
 
2025-07-16 07:59:57,660  [DEBUG] OK
 
2025-07-16 07:59:57,660  [DEBUG] 
 
2025-07-16 07:59:57,664  [DEBUG] [D][11:26:54][CAT1]tx ret[14] >>> AT+GPSMODE=1
 
2025-07-16 07:59:57,664  [DEBUG] 
 
2025-07-16 07:59:57,681  [DEBUG] [D][11:26:54][CAT1]<<< 
 
2025-07-16 07:59:57,682  [DEBUG] OK
 
2025-07-16 07:59:57,683  [DEBUG] 
 
2025-07-16 07:59:57,686  [DEBUG] [D][11:26:54][CAT1]exec over: func id: 23, ret: 6
 
2025-07-16 07:59:57,690  [DEBUG] [D][11:26:54][CAT1]sub id: 23, ret: 6
 
2025-07-16 07:59:57,690  [DEBUG] 
 
2025-07-16 07:59:57,951  [DEBUG] [D][11:26:54][GNSS]recv submsg id[1]
 
2025-07-16 07:59:57,955  [DEBUG] [D][11:26:54][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
 
