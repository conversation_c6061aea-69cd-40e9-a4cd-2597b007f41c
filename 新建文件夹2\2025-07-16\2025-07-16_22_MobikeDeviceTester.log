2025-07-16 22:01:42,656  [DEBUG] [W][12:29:43][PROT]remove success[1730204983],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:01:42,659  [DEBUG] [W][12:29:43][PROT]add success [1730204983],send_path[2],type[C001],priority[0],index[0],used[1]
 
2025-07-16 22:01:42,664  [DEBUG] [W][12:29:43][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730204983]
 
2025-07-16 22:01:42,669  [DEBUG] [E][12:29:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:01:42,672  [DEBUG] [E][12:29:43][M2M ]m2m send data len err[-1,102]
 
2025-07-16 22:01:42,678  [DEBUG] [E][12:29:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:01:42,681  [DEBUG] [E][12:29:43][PROT]M2M Send Fail [1730204983]
 
2025-07-16 22:01:54,589  [DEBUG] [W][12:29:55][GNSS]start sing locating
 
2025-07-16 22:01:54,596  [DEBUG] [W][12:29:55][PROT]remove success[1730204995],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:01:54,604  [DEBUG] [W][12:29:55][PROT]add success [1730204995],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:01:54,614  [DEBUG] [W][12:29:55][PROT]remove success[1730204995],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:01:54,621  [DEBUG] [W][12:29:55][PROT]add success [1730204995],send_path[2],type[4701],priority[0],index[1],used[1]
 
2025-07-16 22:01:54,631  [DEBUG] [W][12:29:55][PROT]remove success[1730204995],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:01:54,639  [DEBUG] [W][12:29:55][PROT]add success [1730204995],send_path[2],type[4705],priority[0],index[2],used[1]
 
2025-07-16 22:02:04,782  [DEBUG] [E][12:30:05][GNSS]GPS module no nmea data!
 
2025-07-16 22:02:14,797  [DEBUG] [E][12:30:15][GNSS]GPS module no nmea data!
 
2025-07-16 22:02:16,812  [DEBUG] [W][12:30:17][CAT1]gsm_module_reboot
 
2025-07-16 22:02:16,813  [DEBUG] 
 
2025-07-16 22:02:24,813  [DEBUG] [E][12:30:25][GNSS]GPS module no nmea data!
 
2025-07-16 22:02:34,825  [DEBUG] [E][12:30:35][GNSS]GPS module no nmea data!
 
2025-07-16 22:02:44,280  [DEBUG] [W][12:30:45][COMM]Power Off
 
2025-07-16 22:02:44,843  [DEBUG] [E][12:30:45][GNSS]GPS module no nmea data!
 
2025-07-16 22:02:54,862  [DEBUG] [E][12:30:55][GNSS]GPS module no nmea data!
 
2025-07-16 22:03:04,875  [DEBUG] [E][12:31:06][GNSS]GPS module no nmea data!
 
2025-07-16 22:03:14,886  [DEBUG] [E][12:31:16][GNSS]GPS module no nmea data!
 
2025-07-16 22:03:24,918  [DEBUG] [E][12:31:26][GNSS]GPS module no nmea data!
 
2025-07-16 22:03:34,939  [DEBUG] [E][12:31:36][GNSS]GPS module no nmea data!
 
2025-07-16 22:03:44,988  [DEBUG] [E][12:31:46][GNSS]GPS module no nmea data!
 
2025-07-16 22:03:55,030  [DEBUG] [E][12:31:56][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:05,050  [DEBUG] [E][12:32:06][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:15,062  [DEBUG] [E][12:32:16][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:25,100  [DEBUG] [E][12:32:26][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:35,138  [DEBUG] [E][12:32:36][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:36,425  [DEBUG] [E][12:32:37][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 22:04:45,158  [DEBUG] [E][12:32:46][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:46,475  [DEBUG] [W][12:32:47][CAT1]gsm_module_reboot
 
2025-07-16 22:04:46,476  [DEBUG] 
 
2025-07-16 22:04:55,190  [DEBUG] [E][12:32:56][GNSS]GPS module no nmea data!
 
2025-07-16 22:04:55,622  [DEBUG] [W][12:32:56][COMM]get bat state1 error
 
2025-07-16 22:04:55,626  [DEBUG] [W][12:32:56][COMM]get mc state information fail
 
2025-07-16 22:04:55,631  [DEBUG] [W][12:32:56][COMM]get mc speed information fail
 
2025-07-16 22:04:55,636  [DEBUG] [W][12:32:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 22:04:55,649  [DEBUG] [W][12:32:56][COMM]5F04 LocFail:reason:0x01;diff:45176;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 22:04:55,654  [DEBUG] [W][12:32:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 22:04:55,658  [DEBUG] [W][12:32:56][COMM]get mc power mode information fail
 
2025-07-16 22:04:55,667  [DEBUG] [W][12:32:56][PROT]remove success[1730205176],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 22:04:55,675  [DEBUG] [W][12:32:56][PROT]add success [1730205176],send_path[2],type[5F04],priority[0],index[3],used[1]
 
2025-07-16 22:04:57,933  [DEBUG] [W][12:32:59][PROT]remove success[1730205179],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 22:04:57,941  [DEBUG] [W][12:32:59][PROT]add success [1730205179],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 22:04:57,944  [DEBUG] [W][12:32:59][COMM]get soc error
 
2025-07-16 22:04:57,947  [DEBUG] [W][12:32:59][GNSS]stop locating
 
2025-07-16 22:04:57,950  [DEBUG] [W][12:32:59][GNSS]sing locating running
 
2025-07-16 22:04:57,956  [DEBUG] [E][12:32:59][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 22:04:57,964  [DEBUG] [W][12:32:59][PROT]remove success[1730205179],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 22:04:57,972  [DEBUG] [W][12:32:59][PROT]add success [1730205179],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 22:04:57,983  [DEBUG] [W][12:32:59][PROT]remove success[1730205179],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 22:04:57,992  [DEBUG] [W][12:32:59][PROT]add success [1730205179],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 22:04:58,000  [DEBUG] [W][12:32:59][PROT]remove success[1730205179],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 22:04:58,008  [DEBUG] [W][12:32:59][PROT]add success [1730205179],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 22:04:58,020  [DEBUG] [W][12:32:59][COMM]5A07 LocFail:reason:0x01;diff:45179;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 22:04:58,026  [DEBUG] [W][12:32:59][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 22:04:58,033  [DEBUG] [W][12:32:59][PROT]remove success[1730205179],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 22:04:58,042  [DEBUG] [W][12:32:59][PROT]add success [1730205179],send_path[2],type[5A07],priority[0],index[8],used[1]
 
2025-07-16 22:04:58,049  [DEBUG] [W][12:32:59][PROT]remove success[1730205179],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-16 22:04:58,059  [DEBUG] [W][12:32:59][PROT]add success [1730205179],send_path[2],type[D302],priority[0],index[9],used[1]
 
2025-07-16 22:05:17,300  [DEBUG] [W][12:33:18][COMM]Power Off
 
2025-07-16 22:06:42,108  [DEBUG] [E][12:34:43][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-16 22:06:42,111  [DEBUG] [E][12:34:43][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 22:06:42,120  [DEBUG] [E][12:34:43][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-16 22:06:42,131  [DEBUG] [W][12:34:43][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730205283]
 
2025-07-16 22:06:42,139  [DEBUG] [E][12:34:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:06:42,144  [DEBUG] [E][12:34:43][M2M ]m2m send data len err[-1,134]
 
2025-07-16 22:06:42,150  [DEBUG] [E][12:34:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:06:42,155  [DEBUG] [E][12:34:43][PROT]M2M Send Fail [1730205283]
 
2025-07-16 22:06:44,579  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:06:44,582  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:06:44,585  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:06:44,588  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:06:44,591  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:06:44,594  [DEBUG] get_boot_mode a5a5
 
2025-07-16 22:06:44,594  [DEBUG] is_app_complete 1
 
2025-07-16 22:06:44,793  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:06:44,795  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:06:44,797  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:06:45,425  [DEBUG] para ret:306,valid:aa

 
2025-07-16 22:06:45,441  [DEBUG] [W][12:34:45][COMM]BKP RESET_MODE[a5a5], reason[1-1]
 
2025-07-16 22:06:45,444  [DEBUG] [E][12:34:45][COMM]RESETREAS:0x00000008
 
2025-07-16 22:06:45,451  [DEBUG] [E][12:34:45][COMM]Multirider mode not support: 255
 
2025-07-16 22:06:45,455  [DEBUG] [W][12:34:45][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 22:06:45,458  [DEBUG] [W][12:34:45][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 22:06:45,464  [DEBUG] [W][12:34:45][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 22:06:45,467  [DEBUG] [W][12:34:45][FCTY]DeviceID    = 460130020284403
 
2025-07-16 22:06:45,472  [DEBUG] [W][12:34:45][FCTY]HardwareID  = 868667086862221
 
2025-07-16 22:06:45,475  [DEBUG] [W][12:34:45][FCTY]MoBikeID    = 9999999999
 
2025-07-16 22:06:45,481  [DEBUG] [W][12:34:45][FCTY]LockID      = F050821689
 
2025-07-16 22:06:45,484  [DEBUG] [W][12:34:45][FCTY]BLEFWVersion= 105
 
2025-07-16 22:06:45,490  [DEBUG] [W][12:34:45][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-16 22:06:45,492  [DEBUG] [W][12:34:45][FCTY]Bat         = 0 mv
 
2025-07-16 22:06:45,495  [DEBUG] [W][12:34:45][FCTY]Current     = 0 ma
 
2025-07-16 22:06:45,498  [DEBUG] [W][12:34:45][FCTY]VBUS        = 0 mv
 
2025-07-16 22:06:45,503  [DEBUG] [W][12:34:45][FCTY]TEMP= 0,BATID= 663732,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 22:06:45,509  [DEBUG] [W][12:34:45][FCTY]Ext battery vol = 1, adc = 75
 
2025-07-16 22:06:45,512  [DEBUG] [W][12:34:45][FCTY]Bike Type flag is invalied
 
2025-07-16 22:06:45,517  [DEBUG] [W][12:34:45][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 22:06:45,520  [DEBUG] [W][12:34:45][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 22:06:45,523  [DEBUG] [W][12:34:45][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 22:06:45,525  [DEBUG] [W][12:34:45][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 22:06:45,531  [DEBUG] [W][12:34:45][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 22:06:45,534  [DEBUG] [W][12:34:45][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 22:06:45,537  [DEBUG] [W][12:34:45][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 22:06:45,598  [DEBUG] [W][12:34:45][GNSS]start sing locating
 
2025-07-16 22:06:45,999  [DEBUG] [E][12:34:46][COMM]1x1 rx timeout
 
2025-07-16 22:06:46,403  [DEBUG] [E][12:34:46][COMM]1x1 rx timeout
 
2025-07-16 22:06:46,406  [DEBUG] [E][12:34:46][COMM]1x1 tp timeout
 
2025-07-16 22:06:46,408  [DEBUG] [E][12:34:46][COMM]1x1 error -3.
 
2025-07-16 22:06:46,412  [DEBUG] [W][12:34:46][COMM]Bat auth off fail, error:-1
 
2025-07-16 22:06:46,417  [DEBUG] [E][12:34:46][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 22:06:46,420  [DEBUG] [W][12:34:46][COMM]Init MC LOCK_STATE 2
 
2025-07-16 22:06:46,431  [DEBUG] [W][12:34:46][PROT]remove success[1730205286],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:06:46,437  [DEBUG] [W][12:34:46][PROT]add success [1730205286],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:06:47,525  [DEBUG] [W][12:34:47][PROT]remove success[1730205287],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:06:47,532  [DEBUG] [W][12:34:47][PROT]add success [1730205287],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 22:06:57,828  [DEBUG] [W][12:34:58][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730205298]
 
2025-07-16 22:07:04,550  [DEBUG] [W][12:35:04][PROT]remove success[1730205304],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:07:04,558  [DEBUG] [W][12:35:04][PROT]add success [1730205304],send_path[2],type[5103],priority[0],index[2],used[1]
 
2025-07-16 22:07:04,782  [DEBUG] [W][12:35:04][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730205304]
 
2025-07-16 22:07:10,185  [DEBUG] [W][12:35:10][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730205310]
 
2025-07-16 22:07:15,591  [DEBUG] [W][12:35:15][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730205315]
 
2025-07-16 22:07:20,997  [DEBUG] [W][12:35:21][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730205321]
 
2025-07-16 22:07:42,539  [DEBUG] [W][12:35:42][PROT]remove success[1730205342],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:07:42,548  [DEBUG] [W][12:35:42][PROT]add success [1730205342],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:07:42,554  [DEBUG] [W][12:35:42][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730205342]
 
2025-07-16 22:08:10,952  [DEBUG] [W][12:36:11][GNSS][RTK]enough, report now.
 
2025-07-16 22:08:11,370  [DEBUG] [E][12:36:11][COMM]1x1 rx timeout
 
2025-07-16 22:08:11,774  [DEBUG] [E][12:36:11][COMM]1x1 rx timeout
 
2025-07-16 22:08:11,777  [DEBUG] [E][12:36:11][COMM]1x1 tp timeout
 
2025-07-16 22:08:11,779  [DEBUG] [E][12:36:11][COMM]1x1 error -3.
 
2025-07-16 22:08:11,783  [DEBUG] [E][12:36:11][COMM]frm_mc_open_mos failed.
 
2025-07-16 22:08:11,791  [DEBUG] [W][12:36:11][PROT]remove success[1730205371],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:08:11,799  [DEBUG] [W][12:36:11][PROT]add success [1730205371],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 22:08:11,811  [DEBUG] [W][12:36:11][COMM]5A07 LocFail:reason:0x07;diff:5520;LocUsedTime:56;LocStatus|Type:3|000;HDOP:03;SatsView:12;SatsSNR35:00
 
2025-07-16 22:08:11,817  [DEBUG] [W][12:36:11][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205371]
 
2025-07-16 22:08:11,825  [DEBUG] [W][12:36:11][COMM]5A07 LocFail:GpsSpeed:00;alt:0051;lon:114340379   lat:23011996
 
2025-07-16 22:08:11,833  [DEBUG] [W][12:36:12][PROT]remove success[1730205372],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:08:11,841  [DEBUG] [W][12:36:12][PROT]add success [1730205372],send_path[2],type[5A07],priority[0],index[1],used[1]
 
2025-07-16 22:08:17,190  [DEBUG] [W][12:36:17][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205377]
 
2025-07-16 22:08:22,586  [DEBUG] [W][12:36:22][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205382]
 
2025-07-16 22:08:27,989  [DEBUG] [W][12:36:28][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205388]
 
2025-07-16 22:08:33,393  [DEBUG] [W][12:36:33][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205393]
 
2025-07-16 22:08:38,798  [DEBUG] [W][12:36:39][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205399]
 
2025-07-16 22:08:44,194  [DEBUG] [W][12:36:44][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205404]
 
2025-07-16 22:08:49,591  [DEBUG] [W][12:36:49][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205409]
 
2025-07-16 22:08:54,997  [DEBUG] [W][12:36:55][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205415]
 
2025-07-16 22:09:00,402  [DEBUG] [W][12:37:00][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730205420]
 
2025-07-16 22:09:05,819  [DEBUG] [W][12:37:06][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730205426]
 
2025-07-16 22:09:12,867  [DEBUG] [E][12:37:13][COMM]1x1 rx timeout
 
2025-07-16 22:09:13,275  [DEBUG] [E][12:37:13][COMM]1x1 rx timeout
 
2025-07-16 22:09:13,277  [DEBUG] [E][12:37:13][COMM]1x1 tp timeout
 
2025-07-16 22:09:13,280  [DEBUG] [E][12:37:13][COMM]1x1 error -3.
 
2025-07-16 22:09:13,283  [DEBUG] [E][12:37:13][COMM]frm_mc_open_mos failed.
 
2025-07-16 22:09:13,293  [DEBUG] [E][12:37:13][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 22:09:13,301  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:09:13,308  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[5E01],priority[3],index[0],used[1]
 
2025-07-16 22:09:13,317  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:09:13,323  [DEBUG] [W][12:37:13][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730205433]
 
2025-07-16 22:09:13,332  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[C001],priority[0],index[1],used[1]
 
2025-07-16 22:09:13,589  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:09:13,598  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[5006],priority[2],index[2],used[1]
 
2025-07-16 22:09:13,600  [DEBUG] [W][12:37:13][COMM]get soc error
 
2025-07-16 22:09:13,603  [DEBUG] [W][12:37:13][GNSS]stop locating
 
2025-07-16 22:09:13,606  [DEBUG] [W][12:37:13][GNSS]sing locating running
 
2025-07-16 22:09:13,612  [DEBUG] [E][12:37:13][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 22:09:13,620  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 22:09:13,627  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[5D05],priority[3],index[3],used[1]
 
2025-07-16 22:09:13,640  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 22:09:13,648  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[FF0E],priority[0],index[4],used[1]
 
2025-07-16 22:09:13,656  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 22:09:13,664  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[C001],priority[0],index[5],used[1]
 
2025-07-16 22:09:13,672  [DEBUG] [W][12:37:13][PROT]remove success[1730205433],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 22:09:13,681  [DEBUG] [W][12:37:13][PROT]add success [1730205433],send_path[2],type[D302],priority[0],index[6],used[1]
 
2025-07-16 22:09:18,739  [DEBUG] [W][12:37:18][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730205438]
 
2025-07-16 22:09:24,147  [DEBUG] [W][12:37:24][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730205444]
 
2025-07-16 22:09:29,562  [DEBUG] [W][12:37:29][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730205449]
 
2025-07-16 22:09:34,968  [DEBUG] [W][12:37:35][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730205455]
 
2025-07-16 22:09:40,372  [DEBUG] [W][12:37:40][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730205460]
 
2025-07-16 22:09:45,787  [DEBUG] [W][12:37:46][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730205466]
 
2025-07-16 22:09:51,204  [DEBUG] [W][12:37:51][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730205471]
 
2025-07-16 22:09:51,299  [DEBUG] [W][12:37:51][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730205471]
 
2025-07-16 22:09:51,394  [DEBUG] [W][12:37:51][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730205471]
 
2025-07-16 22:09:51,478  [DEBUG] [W][12:37:51][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730205471]
 
2025-07-16 22:09:56,871  [DEBUG] [W][12:37:57][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730205477]
 
2025-07-16 22:09:57,933  [DEBUG] [W][12:37:58][PROT]remove success[1730205478],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:09:57,941  [DEBUG] [W][12:37:58][PROT]add success [1730205478],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:10:02,278  [DEBUG] [W][12:38:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730205482]
 
2025-07-16 22:10:02,281  [DEBUG] [E][12:38:02][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:10:02,286  [DEBUG] [E][12:38:02][M2M ]m2m send data len err[-1,102]
 
2025-07-16 22:10:02,290  [DEBUG] [E][12:38:02][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:10:02,297  [DEBUG] [E][12:38:02][PROT]M2M Send Fail [1730205482]
 
2025-07-16 22:10:04,574  [DEBUG] [W][12:38:04][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205484]
 
2025-07-16 22:10:46,302  [DEBUG] [W][12:38:46][PROT]remove success[1730205526],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:10:46,311  [DEBUG] [W][12:38:46][PROT]add success [1730205526],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:10:46,316  [DEBUG] [W][12:38:46][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205526]
 
2025-07-16 22:11:27,308  [DEBUG] [W][12:39:27][PROT]remove success[1730205567],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:11:27,316  [DEBUG] [W][12:39:27][PROT]add success [1730205567],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:11:27,322  [DEBUG] [W][12:39:27][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205567]
 
2025-07-16 22:12:08,228  [DEBUG] [W][12:40:08][PROT]remove success[1730205608],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:12:08,234  [DEBUG] [W][12:40:08][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205608]
 
2025-07-16 22:12:08,241  [DEBUG] [W][12:40:08][PROT]add success [1730205608],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:12:49,241  [DEBUG] [W][12:40:49][PROT]remove success[1730205649],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:12:49,248  [DEBUG] [W][12:40:49][PROT]add success [1730205649],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:12:49,253  [DEBUG] [W][12:40:49][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205649]
 
2025-07-16 22:13:30,158  [DEBUG] [W][12:41:30][PROT]remove success[1730205690],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:13:30,166  [DEBUG] [W][12:41:30][PROT]add success [1730205690],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:13:30,171  [DEBUG] [W][12:41:30][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205690]
 
2025-07-16 22:14:11,090  [DEBUG] [W][12:42:11][PROT]remove success[1730205731],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:14:11,097  [DEBUG] [W][12:42:11][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205731]
 
2025-07-16 22:14:11,103  [DEBUG] [W][12:42:11][PROT]add success [1730205731],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:14:52,102  [DEBUG] [W][12:42:52][PROT]remove success[1730205772],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:14:52,111  [DEBUG] [W][12:42:52][PROT]add success [1730205772],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:14:52,117  [DEBUG] [W][12:42:52][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205772]
 
2025-07-16 22:15:33,038  [DEBUG] [W][12:43:33][PROT]remove success[1730205813],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:15:33,046  [DEBUG] [W][12:43:33][PROT]add success [1730205813],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:15:33,052  [DEBUG] [W][12:43:33][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205813]
 
2025-07-16 22:16:13,959  [DEBUG] [W][12:44:14][PROT]remove success[1730205854],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:16:13,968  [DEBUG] [W][12:44:14][PROT]add success [1730205854],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:16:13,972  [DEBUG] [W][12:44:14][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205854]
 
2025-07-16 22:16:54,985  [DEBUG] [W][12:44:55][PROT]remove success[1730205895],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:16:54,992  [DEBUG] [W][12:44:55][PROT]add success [1730205895],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:16:54,997  [DEBUG] [W][12:44:55][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205895]
 
2025-07-16 22:17:35,922  [DEBUG] [W][12:45:36][PROT]remove success[1730205936],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:17:35,927  [DEBUG] [W][12:45:36][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205936]
 
2025-07-16 22:17:35,934  [DEBUG] [W][12:45:36][PROT]add success [1730205936],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:18:16,840  [DEBUG] [W][12:46:17][PROT]remove success[1730205977],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:18:16,848  [DEBUG] [W][12:46:17][PROT]add success [1730205977],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:18:16,853  [DEBUG] [W][12:46:17][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730205977]
 
2025-07-16 22:18:57,860  [DEBUG] [W][12:46:58][PROT]remove success[1730206018],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:18:57,867  [DEBUG] [W][12:46:58][PROT]add success [1730206018],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:18:57,873  [DEBUG] [W][12:46:58][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206018]
 
2025-07-16 22:19:38,798  [DEBUG] [W][12:47:39][PROT]remove success[1730206059],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:19:38,806  [DEBUG] [W][12:47:39][PROT]add success [1730206059],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:19:38,811  [DEBUG] [W][12:47:39][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206059]
 
2025-07-16 22:20:19,735  [DEBUG] [W][12:48:20][PROT]remove success[1730206100],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:20:19,743  [DEBUG] [W][12:48:20][PROT]add success [1730206100],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:20:19,748  [DEBUG] [W][12:48:20][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206100]
 
2025-07-16 22:21:00,742  [DEBUG] [W][12:49:01][PROT]remove success[1730206141],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:21:00,750  [DEBUG] [W][12:49:01][PROT]add success [1730206141],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:21:00,755  [DEBUG] [W][12:49:01][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206141]
 
2025-07-16 22:21:41,647  [DEBUG] [W][12:49:41][PROT]remove success[1730206181],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:21:41,655  [DEBUG] [W][12:49:41][PROT]add success [1730206181],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:21:41,660  [DEBUG] [W][12:49:41][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206181]
 
2025-07-16 22:22:22,575  [DEBUG] [W][12:50:22][PROT]remove success[1730206222],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:22:22,584  [DEBUG] [W][12:50:22][PROT]add success [1730206222],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:22:22,589  [DEBUG] [W][12:50:22][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206222]
 
2025-07-16 22:23:03,590  [DEBUG] [W][12:51:03][PROT]remove success[1730206263],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:23:03,596  [DEBUG] [W][12:51:03][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206263]
 
2025-07-16 22:23:03,603  [DEBUG] [W][12:51:03][PROT]add success [1730206263],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:23:44,510  [DEBUG] [W][12:51:44][PROT]remove success[1730206304],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:23:44,519  [DEBUG] [W][12:51:44][PROT]add success [1730206304],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:23:44,524  [DEBUG] [W][12:51:44][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206304]
 
2025-07-16 22:24:25,533  [DEBUG] [W][12:52:25][PROT]remove success[1730206345],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:24:25,541  [DEBUG] [W][12:52:25][PROT]add success [1730206345],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:24:25,546  [DEBUG] [W][12:52:25][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206345]
 
2025-07-16 22:25:06,475  [DEBUG] [W][12:53:06][PROT]remove success[1730206386],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:25:06,483  [DEBUG] [W][12:53:06][PROT]add success [1730206386],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:25:06,488  [DEBUG] [W][12:53:06][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206386]
 
2025-07-16 22:25:50,021  [DEBUG] [W][12:53:50][PROT]remove success[1730206430],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:25:50,029  [DEBUG] [W][12:53:50][PROT]add success [1730206430],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:25:50,034  [DEBUG] [W][12:53:50][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206430]
 
2025-07-16 22:26:32,172  [DEBUG] [W][12:54:32][PROT]remove success[1730206472],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:26:32,180  [DEBUG] [W][12:54:32][PROT]add success [1730206472],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:26:32,186  [DEBUG] [W][12:54:32][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730206472]
 
2025-07-16 22:27:33,195  [DEBUG] [W][12:55:33][COMM]Power Off
 
2025-07-16 22:31:43,357  [DEBUG] [W][12:59:43][PROT]remove success[1730206783],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:31:43,365  [DEBUG] [W][12:59:43][PROT]add success [1730206783],send_path[2],type[C001],priority[0],index[0],used[1]
 
2025-07-16 22:31:43,371  [DEBUG] [W][12:59:43][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730206783]
 
2025-07-16 22:31:43,377  [DEBUG] [E][12:59:43][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:31:43,380  [DEBUG] [E][12:59:43][M2M ]m2m send data len err[-1,102]
 
2025-07-16 22:31:43,385  [DEBUG] [E][12:59:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:31:43,388  [DEBUG] [E][12:59:43][PROT]M2M Send Fail [1730206783]
 
2025-07-16 22:31:55,408  [DEBUG] [W][12:59:55][GNSS]start sing locating
 
2025-07-16 22:31:55,416  [DEBUG] [W][12:59:55][PROT]remove success[1730206795],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:31:55,424  [DEBUG] [W][12:59:55][PROT]add success [1730206795],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:31:55,433  [DEBUG] [W][12:59:55][PROT]remove success[1730206795],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:31:55,441  [DEBUG] [W][12:59:55][PROT]add success [1730206795],send_path[2],type[4701],priority[0],index[1],used[1]
 
2025-07-16 22:31:55,449  [DEBUG] [W][12:59:55][PROT]remove success[1730206795],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:31:55,459  [DEBUG] [W][12:59:55][PROT]add success [1730206795],send_path[2],type[4705],priority[0],index[2],used[1]
 
2025-07-16 22:32:05,670  [DEBUG] [E][13:00:06][GNSS]GPS module no nmea data!
 
2025-07-16 22:32:15,745  [DEBUG] [E][13:00:16][GNSS]GPS module no nmea data!
 
2025-07-16 22:32:17,537  [DEBUG] [W][13:00:17][CAT1]gsm_module_reboot
 
2025-07-16 22:32:17,537  [DEBUG] 
 
2025-07-16 22:32:25,810  [DEBUG] [E][13:00:26][GNSS]GPS module no nmea data!
 
2025-07-16 22:32:35,872  [DEBUG] [E][13:00:36][GNSS]GPS module no nmea data!
 
2025-07-16 22:32:45,143  [DEBUG] [W][13:00:45][COMM]Power Off
 
2025-07-16 22:32:45,957  [DEBUG] [E][13:00:46][GNSS]GPS module no nmea data!
 
2025-07-16 22:32:56,030  [DEBUG] [E][13:00:56][GNSS]GPS module no nmea data!
 
2025-07-16 22:33:06,106  [DEBUG] [E][13:01:06][GNSS]GPS module no nmea data!
 
2025-07-16 22:33:16,186  [DEBUG] [E][13:01:16][GNSS]GPS module no nmea data!
 
2025-07-16 22:33:26,254  [DEBUG] [E][13:01:26][GNSS]GPS module no nmea data!
 
2025-07-16 22:33:36,328  [DEBUG] [E][13:01:36][GNSS]GPS module no nmea data!
 
2025-07-16 22:33:46,404  [DEBUG] [E][13:01:46][GNSS]GPS module no nmea data!
 
2025-07-16 22:33:56,480  [DEBUG] [E][13:01:56][GNSS]GPS module no nmea data!
 
2025-07-16 22:34:06,553  [DEBUG] [E][13:02:06][GNSS]GPS module no nmea data!
 
2025-07-16 22:34:16,630  [DEBUG] [E][13:02:16][GNSS]GPS module no nmea data!
 
2025-07-16 22:34:26,703  [DEBUG] [E][13:02:27][GNSS]GPS module no nmea data!
 
2025-07-16 22:34:36,779  [DEBUG] [E][13:02:37][GNSS]GPS module no nmea data!
 
2025-07-16 22:34:37,076  [DEBUG] [E][13:02:37][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 22:34:46,854  [DEBUG] [E][13:02:47][GNSS]GPS module no nmea data!
 
2025-07-16 22:34:47,118  [DEBUG] [W][13:02:47][CAT1]gsm_module_reboot
 
2025-07-16 22:34:47,119  [DEBUG] 
 
2025-07-16 22:34:56,380  [DEBUG] [W][13:02:56][COMM]get bat state1 error
 
2025-07-16 22:34:56,382  [DEBUG] [W][13:02:56][COMM]get mc state information fail
 
2025-07-16 22:34:56,388  [DEBUG] [W][13:02:56][COMM]get mc speed information fail
 
2025-07-16 22:34:56,393  [DEBUG] [W][13:02:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 22:34:56,405  [DEBUG] [W][13:02:56][COMM]5F04 LocFail:reason:0x01;diff:46976;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 22:34:56,411  [DEBUG] [W][13:02:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 22:34:56,416  [DEBUG] [W][13:02:56][COMM]get mc power mode information fail
 
2025-07-16 22:34:56,424  [DEBUG] [W][13:02:56][PROT]remove success[1730206976],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 22:34:56,431  [DEBUG] [W][13:02:56][PROT]add success [1730206976],send_path[2],type[5F04],priority[0],index[3],used[1]
 
2025-07-16 22:34:58,688  [DEBUG] [W][13:02:59][PROT]remove success[1730206979],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 22:34:58,696  [DEBUG] [W][13:02:59][PROT]add success [1730206979],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 22:34:58,699  [DEBUG] [W][13:02:59][COMM]get soc error
 
2025-07-16 22:34:58,702  [DEBUG] [W][13:02:59][GNSS]stop locating
 
2025-07-16 22:34:58,704  [DEBUG] [W][13:02:59][GNSS]sing locating running
 
2025-07-16 22:34:58,710  [DEBUG] [E][13:02:59][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 22:34:58,718  [DEBUG] [W][13:02:59][PROT]remove success[1730206979],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 22:34:58,727  [DEBUG] [W][13:02:59][PROT]add success [1730206979],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 22:34:58,738  [DEBUG] [W][13:02:59][PROT]remove success[1730206979],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 22:34:58,746  [DEBUG] [W][13:02:59][PROT]add success [1730206979],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 22:34:58,754  [DEBUG] [W][13:02:59][PROT]remove success[1730206979],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 22:34:58,763  [DEBUG] [W][13:02:59][PROT]add success [1730206979],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 22:34:58,775  [DEBUG] [W][13:02:59][COMM]5A07 LocFail:reason:0x01;diff:46979;LocUsedTime:0;LocStatus|Type:2|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 22:34:58,780  [DEBUG] [W][13:02:59][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 22:34:58,788  [DEBUG] [W][13:02:59][PROT]remove success[1730206979],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 22:34:58,796  [DEBUG] [W][13:02:59][PROT]add success [1730206979],send_path[2],type[5A07],priority[0],index[8],used[1]
 
2025-07-16 22:34:58,804  [DEBUG] [W][13:02:59][PROT]remove success[1730206979],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-16 22:34:58,813  [DEBUG] [W][13:02:59][PROT]add success [1730206979],send_path[2],type[D302],priority[0],index[9],used[1]
 
2025-07-16 22:35:18,109  [DEBUG] [W][13:03:18][COMM]Power Off
 
2025-07-16 22:36:22,545  [DEBUG] [E][13:04:22][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-16 22:36:22,547  [DEBUG] [E][13:04:22][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 22:36:22,557  [DEBUG] [E][13:04:22][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-16 22:36:22,568  [DEBUG] [W][13:04:22][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730207062]
 
2025-07-16 22:36:22,576  [DEBUG] [E][13:04:22][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:36:22,581  [DEBUG] [E][13:04:22][M2M ]m2m send data len err[-1,134]
 
2025-07-16 22:36:22,587  [DEBUG] [E][13:04:22][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:36:22,591  [DEBUG] [E][13:04:22][PROT]M2M Send Fail [1730207062]
 
2025-07-16 22:36:25,033  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:36:25,035  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:36:25,038  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:36:25,041  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:36:25,043  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:36:25,046  [DEBUG] get_boot_mode a5a5
 
2025-07-16 22:36:25,048  [DEBUG] is_app_complete 1
 
2025-07-16 22:36:25,247  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:36:25,249  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:36:25,252  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:36:25,879  [DEBUG] para ret:306,valid:aa

 
2025-07-16 22:36:25,896  [DEBUG] [W][13:04:25][COMM]BKP RESET_MODE[a5a5], reason[1-1]
 
2025-07-16 22:36:25,899  [DEBUG] [E][13:04:25][COMM]RESETREAS:0x00000008
 
2025-07-16 22:36:25,906  [DEBUG] [E][13:04:25][COMM]Multirider mode not support: 255
 
2025-07-16 22:36:25,911  [DEBUG] [W][13:04:25][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 22:36:25,913  [DEBUG] [W][13:04:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 22:36:25,919  [DEBUG] [W][13:04:25][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 22:36:25,922  [DEBUG] [W][13:04:25][FCTY]DeviceID    = 460130020284403
 
2025-07-16 22:36:25,928  [DEBUG] [W][13:04:25][FCTY]HardwareID  = 868667086862221
 
2025-07-16 22:36:25,930  [DEBUG] [W][13:04:25][FCTY]MoBikeID    = 9999999999
 
2025-07-16 22:36:25,936  [DEBUG] [W][13:04:25][FCTY]LockID      = F050821689
 
2025-07-16 22:36:25,939  [DEBUG] [W][13:04:25][FCTY]BLEFWVersion= 105
 
2025-07-16 22:36:25,944  [DEBUG] [W][13:04:25][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-16 22:36:25,947  [DEBUG] [W][13:04:25][FCTY]Bat         = 0 mv
 
2025-07-16 22:36:25,950  [DEBUG] [W][13:04:25][FCTY]Current     = 0 ma
 
2025-07-16 22:36:25,953  [DEBUG] [W][13:04:25][FCTY]VBUS        = 0 mv
 
2025-07-16 22:36:25,958  [DEBUG] [W][13:04:25][FCTY]TEMP= 0,BATID= 662484,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 22:36:25,963  [DEBUG] [W][13:04:25][FCTY]Ext battery vol = 1, adc = 78
 
2025-07-16 22:36:25,967  [DEBUG] [W][13:04:25][FCTY]Bike Type flag is invalied
 
2025-07-16 22:36:25,972  [DEBUG] [W][13:04:25][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 22:36:25,975  [DEBUG] [W][13:04:25][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 22:36:25,978  [DEBUG] [W][13:04:25][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 22:36:25,981  [DEBUG] [W][13:04:25][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 22:36:25,987  [DEBUG] [W][13:04:25][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 22:36:25,989  [DEBUG] [W][13:04:25][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 22:36:25,992  [DEBUG] [W][13:04:25][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 22:36:26,069  [DEBUG] [W][13:04:25][GNSS]start sing locating
 
2025-07-16 22:36:26,470  [DEBUG] [E][13:04:26][COMM]1x1 rx timeout
 
2025-07-16 22:36:26,875  [DEBUG] [E][13:04:26][COMM]1x1 rx timeout
 
2025-07-16 22:36:26,878  [DEBUG] [E][13:04:26][COMM]1x1 tp timeout
 
2025-07-16 22:36:26,880  [DEBUG] [E][13:04:26][COMM]1x1 error -3.
 
2025-07-16 22:36:26,883  [DEBUG] [W][13:04:26][COMM]Bat auth off fail, error:-1
 
2025-07-16 22:36:26,888  [DEBUG] [E][13:04:26][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 22:36:26,891  [DEBUG] [W][13:04:26][COMM]Init MC LOCK_STATE 2
 
2025-07-16 22:36:26,902  [DEBUG] [W][13:04:26][PROT]remove success[1730207066],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:36:26,909  [DEBUG] [W][13:04:26][PROT]add success [1730207066],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:36:27,996  [DEBUG] [W][13:04:27][PROT]remove success[1730207067],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:36:28,003  [DEBUG] [W][13:04:27][PROT]add success [1730207067],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 22:36:39,173  [DEBUG] [W][13:04:38][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730207078]
 
2025-07-16 22:36:44,988  [DEBUG] [W][13:04:44][PROT]remove success[1730207084],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:36:44,995  [DEBUG] [W][13:04:44][PROT]add success [1730207084],send_path[2],type[5103],priority[0],index[2],used[1]
 
2025-07-16 22:36:46,145  [DEBUG] [W][13:04:45][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730207085]
 
2025-07-16 22:36:51,542  [DEBUG] [W][13:04:51][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730207091]
 
2025-07-16 22:36:56,941  [DEBUG] [W][13:04:56][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730207096]
 
2025-07-16 22:37:02,344  [DEBUG] [W][13:05:02][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730207102]
 
2025-07-16 22:38:03,673  [DEBUG] [W][13:06:03][COMM]Power Off
 
2025-07-16 22:38:13,443  [DEBUG] [W][13:06:13][PROT]remove success[1730207173],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:38:13,451  [DEBUG] [W][13:06:13][PROT]add success [1730207173],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:38:13,457  [DEBUG] [W][13:06:13][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730207173]
 
2025-07-16 22:38:26,565  [DEBUG] [E][13:06:26][GNSS]GPS module no nmea data!
 
2025-07-16 22:38:36,620  [DEBUG] [E][13:06:36][GNSS]GPS module no nmea data!
 
2025-07-16 22:38:46,681  [DEBUG] [E][13:06:46][GNSS]GPS module no nmea data!
 
2025-07-16 22:38:56,763  [DEBUG] [E][13:06:56][GNSS]GPS module no nmea data!
 
2025-07-16 22:39:06,851  [DEBUG] [E][13:07:06][GNSS]GPS module no nmea data!
 
2025-07-16 22:39:14,676  [DEBUG] [W][13:07:14][COMM]Power Off
 
2025-07-16 22:39:16,904  [DEBUG] [E][13:07:16][GNSS]GPS module no nmea data!
 
2025-07-16 22:39:26,958  [DEBUG] [E][13:07:26][GNSS]GPS module no nmea data!
 
2025-07-16 22:39:27,338  [DEBUG] [E][13:07:27][COMM]1x1 rx timeout
 
2025-07-16 22:39:27,743  [DEBUG] [E][13:07:27][COMM]1x1 rx timeout
 
2025-07-16 22:39:27,745  [DEBUG] [E][13:07:27][COMM]1x1 tp timeout
 
2025-07-16 22:39:27,748  [DEBUG] [E][13:07:27][COMM]1x1 error -3.
 
2025-07-16 22:39:27,752  [DEBUG] [E][13:07:27][COMM]frm_mc_open_mos failed.
 
2025-07-16 22:39:27,760  [DEBUG] [W][13:07:27][PROT]remove success[1730207247],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:39:27,768  [DEBUG] [W][13:07:27][PROT]add success [1730207247],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 22:39:27,781  [DEBUG] [W][13:07:27][COMM]5A07 LocFail:reason:0x07;diff:5449;LocUsedTime:107;LocStatus|Type:2|000;HDOP:01;SatsView:11;SatsSNR35:00
 
2025-07-16 22:39:27,788  [DEBUG] [W][13:07:27][COMM]5A07 LocFail:GpsSpeed:00;alt:0006;lon:114340451   lat:23012013
 
2025-07-16 22:39:27,796  [DEBUG] [W][13:07:27][PROT]remove success[1730207247],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:39:27,804  [DEBUG] [W][13:07:27][PROT]add success [1730207247],send_path[2],type[5A07],priority[0],index[1],used[1]
 
2025-07-16 22:39:27,812  [DEBUG] [W][13:07:27][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730207247]
 
2025-07-16 22:40:28,307  [DEBUG] [E][13:08:28][COMM]1x1 rx timeout
 
2025-07-16 22:40:28,712  [DEBUG] [E][13:08:28][COMM]1x1 rx timeout
 
2025-07-16 22:40:28,714  [DEBUG] [E][13:08:28][COMM]1x1 tp timeout
 
2025-07-16 22:40:28,717  [DEBUG] [E][13:08:28][COMM]1x1 error -3.
 
2025-07-16 22:40:28,719  [DEBUG] [E][13:08:28][COMM]frm_mc_open_mos failed.
 
2025-07-16 22:40:28,729  [DEBUG] [E][13:08:28][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 22:40:28,736  [DEBUG] [W][13:08:28][PROT]remove success[1730207308],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:40:28,745  [DEBUG] [W][13:08:28][PROT]add success [1730207308],send_path[2],type[5E01],priority[3],index[2],used[1]
 
2025-07-16 22:40:28,753  [DEBUG] [W][13:08:28][PROT]remove success[1730207308],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 22:40:28,761  [DEBUG] [W][13:08:28][PROT]add success [1730207308],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 22:40:31,035  [DEBUG] [W][13:08:30][PROT]remove success[1730207310],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 22:40:31,044  [DEBUG] [W][13:08:30][PROT]add success [1730207310],send_path[2],type[5006],priority[2],index[4],used[1]
 
2025-07-16 22:40:31,047  [DEBUG] [W][13:08:30][COMM]get soc error
 
2025-07-16 22:40:31,049  [DEBUG] [W][13:08:30][GNSS]stop locating
 
2025-07-16 22:40:31,055  [DEBUG] [E][13:08:30][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 22:40:31,063  [DEBUG] [W][13:08:30][PROT]remove success[1730207310],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 22:40:31,072  [DEBUG] [W][13:08:30][PROT]add success [1730207310],send_path[2],type[5D05],priority[3],index[5],used[1]
 
2025-07-16 22:40:31,080  [DEBUG] [W][13:08:30][PROT]remove success[1730207310],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 22:40:31,088  [DEBUG] [W][13:08:30][PROT]add success [1730207310],send_path[2],type[FF0E],priority[0],index[6],used[1]
 
2025-07-16 22:40:31,096  [DEBUG] [W][13:08:30][PROT]remove success[1730207310],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 22:40:31,107  [DEBUG] [W][13:08:30][PROT]add success [1730207310],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 22:40:31,766  [DEBUG] [W][13:08:31][COMM]Power Off
 
2025-07-16 22:40:31,779  [DEBUG] [W][13:08:31][PROT]remove success[1730207311],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 22:40:31,787  [DEBUG] [W][13:08:31][PROT]add success [1730207311],send_path[2],type[D302],priority[0],index[8],used[1]
 
2025-07-16 22:41:27,621  [DEBUG] [E][13:09:27][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:41:27,632  [DEBUG] [E][13:09:27][PROT]M2M Send Fail [1730207367]
 
2025-07-16 22:41:56,893  [DEBUG] [W][13:09:56][CAT1]gsm_module_reboot
 
2025-07-16 22:41:56,894  [DEBUG] 
 
2025-07-16 22:42:55,928  [DEBUG] [E][13:10:55][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 22:42:55,948  [DEBUG] [W][13:10:55][CAT1]gsm_module_reboot
 
2025-07-16 22:42:55,949  [DEBUG] 
 
2025-07-16 22:43:54,968  [DEBUG] [E][13:11:54][M2M ]M2M_GSM_PWR_ON FAIL SYSREST
 
2025-07-16 22:43:54,971  [DEBUG] [E][13:11:54][M2M ]M2M_GSM_PWR_ON GSM_ERROR
 
2025-07-16 22:43:54,980  [DEBUG] [E][13:11:54][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
 
2025-07-16 22:43:54,991  [DEBUG] [W][13:11:54][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730207514]
 
2025-07-16 22:43:55,001  [DEBUG] [E][13:11:54][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:43:55,005  [DEBUG] [E][13:11:54][M2M ]m2m send data len err[-1,198]
 
2025-07-16 22:43:55,011  [DEBUG] [E][13:11:54][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:43:55,014  [DEBUG] [E][13:11:54][PROT]M2M Send Fail [1730207514]
 
2025-07-16 22:43:57,685  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:43:57,687  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:43:57,690  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:43:57,695  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:43:57,697  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:43:57,700  [DEBUG] get_boot_mode a5a5
 
2025-07-16 22:43:57,701  [DEBUG] is_app_complete 1
 
2025-07-16 22:43:57,900  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:43:57,902  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:43:57,903  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:00,344  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:00,345  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:00,348  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:00,351  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:00,355  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:00,357  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:00,357  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:00,555  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:00,559  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:00,970  [DEBUG] [ADC]	*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:00,972  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:00,975  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:00,977  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:00,981  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:00,983  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:00,984  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:01,183  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:01,186  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:01,187  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:01,599  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:01,601  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:01,604  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:01,606  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:01,610  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:01,612  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:01,613  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:01,812  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:01,814  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:01,816  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:02,228  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:02,231  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:02,234  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:02,236  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:02,239  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:02,242  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:02,242  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:02,443  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:02,444  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:02,445  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:02,857  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:02,859  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:02,863  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:02,865  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:02,869  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:02,872  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:02,872  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:03,071  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:03,073  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:03,074  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:03,487  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:03,489  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:03,492  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:03,495  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:03,498  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:03,501  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:03,501  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:03,701  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:03,702  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:03,704  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:04,117  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:04,119  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:04,122  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:04,124  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:04,127  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:04,130  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:04,131  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:04,330  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:04,332  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:04,334  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:04,745  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:04,748  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:04,752  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:04,754  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:04,756  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:04,759  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:04,760  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:04,959  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:04,960  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:04,963  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:05,375  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:05,377  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:05,380  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:05,383  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:05,385  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:05,389  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:05,389  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:05,588  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:05,590  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:05,592  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:06,004  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:06,006  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:06,009  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:06,012  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:06,015  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:06,018  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:06,018  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:06,218  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:06,220  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:06,221  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:08,428  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:08,429  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:08,432  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:08,435  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:08,438  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:08,440  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:08,441  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:08,640  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:08,642  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:09,058  [DEBUG] [AD?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:09,061  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:09,064  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:09,066  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:09,069  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:09,072  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:09,073  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:09,273  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:09,273  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:09,275  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:09,688  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:09,690  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:09,693  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:09,696  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:09,699  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:09,702  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:09,702  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:09,902  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:09,904  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:09,905  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:10,317  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:10,319  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:10,322  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:10,326  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:10,328  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:10,331  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:10,331  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:10,531  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:10,532  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:10,534  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:12,434  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:12,436  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:12,439  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:12,442  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:12,446  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:12,447  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:12,448  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:12,648  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:12,649  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:13,065  [DEBUG] [ADC]init adc succe?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:13,066  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:13,070  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:13,072  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:13,075  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:13,078  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:13,078  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:13,277  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:13,279  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:13,281  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:13,694  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:13,695  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:13,698  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:13,701  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:13,704  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:13,708  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:13,708  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:13,907  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:13,909  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:13,910  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:14,328  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:14,329  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:14,329  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:14,330  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:14,334  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:14,337  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:14,337  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:14,536  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:14,539  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:14,540  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:14,951  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:14,955  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:14,958  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:14,961  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:14,964  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:14,966  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:14,966  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:15,166  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:15,168  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:15,170  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:15,582  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:15,584  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:15,588  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:15,590  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:15,593  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:15,596  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:15,596  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:15,795  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:15,797  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:15,799  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:16,212  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:16,214  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:16,217  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:16,219  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:16,222  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:16,225  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:16,226  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:16,425  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:16,427  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:16,428  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:16,843  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:16,844  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:16,846  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:16,849  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:16,852  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:16,855  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:16,856  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:17,054  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:17,056  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:17,057  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:17,471  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:17,472  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:17,475  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:17,478  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:17,482  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:17,484  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:17,484  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:17,683  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:17,685  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:17,687  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:18,100  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:18,102  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:18,105  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:18,108  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:18,110  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:18,113  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:18,113  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:18,312  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:18,315  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:18,317  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:18,728  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:18,731  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:18,734  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:18,737  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:18,740  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:18,743  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:18,743  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:18,943  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:18,944  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:18,946  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:19,358  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:19,360  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:19,364  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:19,367  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:19,370  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:19,372  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:19,372  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:19,572  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:19,574  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:19,576  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:19,988  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:19,990  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:19,994  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:19,996  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:20,000  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:20,002  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:20,002  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:20,201  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:20,203  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:20,205  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:20,617  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:20,620  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:20,623  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:20,626  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:20,628  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:20,631  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:20,632  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:20,831  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:20,833  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:20,835  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:21,247  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:21,248  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:21,251  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:21,254  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:21,257  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:21,261  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:21,261  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:21,459  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:21,462  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:21,464  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:21,876  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:21,879  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:21,881  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:21,884  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:21,888  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:21,891  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:21,891  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:22,091  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:22,092  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:22,093  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:22,507  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:22,510  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:22,511  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:22,514  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:22,517  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:22,519  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:22,519  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:22,719  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:22,721  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:22,723  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:23,135  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:23,137  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:23,140  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:23,143  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:23,146  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:23,148  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:23,150  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:23,349  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:23,351  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:23,352  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:23,764  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:23,766  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:23,769  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:23,772  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:23,776  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:23,779  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:23,779  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:23,978  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:23,980  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:23,982  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:24,394  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:24,396  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:24,399  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:24,401  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:24,404  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:24,408  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:24,408  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:24,607  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:24,609  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:24,611  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:26,512  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:26,514  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:26,518  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:26,520  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:26,523  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:26,526  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:26,527  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:26,726  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:26,728  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:27,141  [DEBUG] [ADC]init adc success.

*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:27,143  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:27,146  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:27,149  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:27,152  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:27,154  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:27,156  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:27,354  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:27,357  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:27,358  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:27,772  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:27,773  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:27,776  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:27,778  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:27,781  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:27,784  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:27,784  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:27,984  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:27,986  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:27,988  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:28,399  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:28,402  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:28,405  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:28,408  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:28,410  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:28,414  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:28,415  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:28,612  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:28,615  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:28,617  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:29,028  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:29,032  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:29,034  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:29,037  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:29,040  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:29,044  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:29,044  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:29,243  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:29,245  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:29,246  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:29,659  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:29,662  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:29,664  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:29,667  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:29,670  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:29,673  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:29,673  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:29,872  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:29,875  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:29,876  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:30,288  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:30,291  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:30,293  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:30,296  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:30,299  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:30,302  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:30,302  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:30,501  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:30,504  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:30,505  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:30,917  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:30,920  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:30,924  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:30,926  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:30,929  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:30,932  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:30,932  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:31,132  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:31,133  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:31,134  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:31,548  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:31,550  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:31,553  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:31,555  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:31,560  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:31,561  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:31,562  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:31,760  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:31,762  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:31,764  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:32,176  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:32,179  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:32,182  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:32,185  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:32,188  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:32,191  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:32,191  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:32,390  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:32,393  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:32,395  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:32,805  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:32,808  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:32,811  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:32,813  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:32,817  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:32,820  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:32,820  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:33,019  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:33,022  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:33,023  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:33,435  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:33,437  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:33,441  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:33,444  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:33,447  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:33,449  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:33,450  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:33,649  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:33,650  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:33,652  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:34,065  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:34,067  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:34,072  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:34,073  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:34,076  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:34,078  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:34,079  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:34,278  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:34,280  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:34,282  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:34,695  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:34,696  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:34,699  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:34,701  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:34,705  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:34,707  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:34,708  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:34,708  [DEBUG] Debug mode.

 
2025-07-16 22:44:44,710  [DEBUG] Cmd time out, try to boot app

 
2025-07-16 22:44:44,909  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:45,323  [DEBUG] [A*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:45,325  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:45,329  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:45,332  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:45,335  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:45,337  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:45,338  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:45,537  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:45,952  [DEBUG] [AD*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:45,955  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:45,957  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:45,961  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:45,964  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:45,966  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:45,967  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:46,167  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:46,168  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:46,169  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:46,582  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:46,585  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:46,587  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:46,590  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:46,593  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:46,595  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:46,595  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:46,796  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:46,797  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:46,798  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:47,211  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:47,213  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:47,216  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:47,219  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:47,222  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:47,225  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:47,225  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:47,424  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:47,427  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:47,428  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:47,840  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:47,842  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:47,845  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:47,848  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:47,851  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:47,854  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:47,854  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:48,054  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:48,056  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:48,058  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:44:48,470  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:48,472  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:48,475  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:48,478  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:48,481  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:48,483  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:48,483  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:48,486  [DEBUG] Debug mode.

 
2025-07-16 22:44:58,485  [DEBUG] Cmd time out, try to boot app

 
2025-07-16 22:44:58,685  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:59,098  [DEBUG] [A*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:59,101  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:59,104  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:59,106  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:59,109  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:59,112  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:59,112  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:59,313  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:59,728  [DEBUG] [AD*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:59,730  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:44:59,733  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:44:59,736  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:44:59,739  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:44:59,742  [DEBUG] get_boot_mode 0
 
2025-07-16 22:44:59,742  [DEBUG] is_app_complete 0
 
2025-07-16 22:44:59,941  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:44:59,943  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:44:59,945  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:00,357  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:00,359  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:00,362  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:00,364  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:00,368  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:00,371  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:00,371  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:00,570  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:00,572  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:00,573  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:02,776  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:02,778  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:02,781  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:02,783  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:02,787  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:02,789  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:02,790  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:02,988  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:02,992  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:03,403  [DEBUG] [ADC]i?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:03,406  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:03,409  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:03,411  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:03,414  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:03,417  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:03,418  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:03,616  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:03,619  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:03,621  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:05,795  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:05,797  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:05,801  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:05,803  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:05,808  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:05,809  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:05,809  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:06,009  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:06,011  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:06,427  [DEBUG] [ADC]init *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:06,429  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:06,432  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:06,435  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:06,437  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:06,440  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:06,441  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:06,641  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:06,642  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:06,644  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:07,056  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:07,060  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:07,061  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:07,064  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:07,067  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:07,070  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:07,070  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:07,269  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:07,271  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:07,273  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:07,685  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:07,688  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:07,691  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:07,693  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:07,696  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:07,700  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:07,700  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:07,899  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:07,901  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:07,902  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:08,314  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:08,317  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:08,319  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:08,323  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:08,326  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:08,329  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:08,329  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:08,527  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:08,530  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:08,531  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:08,944  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:08,946  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:08,950  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:08,952  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:08,957  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:08,957  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:08,958  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:09,158  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:09,159  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:09,161  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:09,572  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:09,576  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:09,579  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:09,582  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:09,584  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:09,587  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:09,588  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:09,787  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:09,788  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:09,790  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:10,203  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:10,205  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:10,209  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:10,211  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:10,216  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:10,216  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:10,217  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:10,417  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:10,418  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:10,420  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:10,832  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:10,834  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:10,838  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:10,840  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:10,843  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:10,846  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:10,847  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:11,045  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:11,048  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:11,049  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:11,462  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:11,464  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:11,467  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:11,469  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:11,473  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:11,476  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:11,476  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:11,675  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:11,677  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:11,679  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:12,091  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:12,093  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:12,096  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:12,099  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:12,102  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:12,105  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:12,105  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:12,306  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:12,306  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:12,308  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:12,721  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:12,723  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:12,726  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:12,729  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:12,732  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:12,735  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:12,735  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:12,934  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:12,935  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:12,937  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:13,350  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:13,352  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:13,356  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:13,359  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:13,361  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:13,364  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:13,365  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:13,564  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:13,566  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:13,567  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:13,978  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:13,981  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:13,985  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:13,987  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:13,990  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:13,993  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:13,994  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:14,193  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:14,195  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:14,196  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:14,609  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:14,611  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:14,614  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:14,617  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:14,620  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:14,622  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:14,622  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:14,822  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:14,825  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:14,826  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:15,237  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:15,239  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:15,243  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:15,245  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:15,248  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:15,251  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:15,251  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:15,450  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:15,454  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:15,455  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:15,866  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:15,869  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:15,872  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:15,875  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:15,877  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:15,881  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:15,881  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:16,081  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:16,083  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:16,084  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:16,496  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:16,499  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:16,501  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:16,504  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:16,507  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:16,510  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:16,510  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:16,710  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:16,712  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:16,713  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:17,126  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:17,128  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:17,131  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:17,134  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:17,137  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:17,140  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:17,140  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:17,339  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:17,341  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:17,343  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:17,755  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:17,758  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:17,760  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:17,763  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:17,766  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:17,769  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:17,769  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:17,969  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:17,971  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:17,973  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:18,385  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:18,386  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:18,389  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:18,392  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:18,395  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:18,398  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:18,398  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:18,598  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:18,600  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:18,603  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:19,014  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:19,016  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:19,019  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:19,021  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:19,024  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:19,027  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:19,027  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:19,227  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:19,229  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:19,231  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:19,642  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:19,645  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:19,648  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:19,650  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:19,653  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:19,656  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:19,658  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:19,659  [DEBUG] Debug mode.

 
2025-07-16 22:45:29,659  [DEBUG] Cmd time out, try to boot app

 
2025-07-16 22:45:29,858  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:30,271  [DEBUG] [A*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:30,274  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:30,276  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:30,279  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:30,282  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:30,285  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:30,285  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:30,485  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:30,901  [DEBUG] [AD*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:30,903  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:30,906  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:30,909  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:30,912  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:30,915  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:30,915  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:31,115  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:31,117  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:31,118  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:31,531  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:31,532  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:31,535  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:31,538  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:31,541  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:31,544  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:31,544  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:31,743  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:31,745  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:31,747  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:32,159  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:32,161  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:32,164  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:32,167  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:32,170  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:32,173  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:32,174  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:32,372  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:32,374  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:32,377  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:33,989  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:33,991  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:33,994  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:33,997  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:34,000  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:34,003  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:34,003  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:34,202  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:34,204  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:34,206  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:34,617  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:34,617  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:34,620  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:34,623  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:34,627  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:34,629  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:34,629  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:34,828  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:34,831  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:34,832  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:35,244  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:35,247  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:35,250  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:35,253  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:35,256  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:35,258  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:35,258  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:35,259  [DEBUG] Debug mode.

 
2025-07-16 22:45:45,260  [DEBUG] Cmd time out, try to boot app

 
2025-07-16 22:45:45,460  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:45,873  [DEBUG] [A*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:45,875  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:45,878  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:45,881  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:45,884  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:45,887  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:45,887  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:46,087  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:46,502  [DEBUG] [AD*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:46,504  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:46,507  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:46,509  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:46,514  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:46,516  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:46,516  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:46,714  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:46,718  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:46,719  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:47,130  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:47,133  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:47,136  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:47,140  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:47,141  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:47,145  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:47,146  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:47,345  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:47,347  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:47,349  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:47,760  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:47,762  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:47,765  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:47,768  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:47,771  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:47,775  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:47,775  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:47,974  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:47,976  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:47,979  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:48,389  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:48,392  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:48,395  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:48,398  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:48,401  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:48,404  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:48,404  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:48,603  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:48,605  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:48,607  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:49,019  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:49,021  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:49,025  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:49,027  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:49,029  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:49,033  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:49,033  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:49,233  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:49,235  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:49,236  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:49,648  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:49,651  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:49,654  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:49,656  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:49,659  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:49,662  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:49,662  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:49,862  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:49,864  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:49,865  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:50,278  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:50,280  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:50,283  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:50,285  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:50,288  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:50,292  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:50,292  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:50,491  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:50,494  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:50,495  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:50,907  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:50,909  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:50,912  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:50,915  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:50,919  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:50,921  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:50,921  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:51,120  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:51,123  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:51,124  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:51,535  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:51,539  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:51,542  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:51,544  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:51,547  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:51,550  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:51,551  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:51,750  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:51,752  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:51,754  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:52,165  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:52,168  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:52,171  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:52,174  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:52,176  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:52,180  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:52,181  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:52,379  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:52,382  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:52,383  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:52,795  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:52,797  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:52,801  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:52,803  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:52,806  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:52,809  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:52,809  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:53,009  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:53,011  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:53,012  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:53,425  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:53,427  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:53,430  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:53,432  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:53,436  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:53,438  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:53,439  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:53,638  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:53,641  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:53,642  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:54,055  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:54,057  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:54,060  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:54,063  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:54,066  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:54,068  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:54,068  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:54,267  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:54,270  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:54,271  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:54,684  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:54,686  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:54,689  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:54,693  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:54,695  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:54,698  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:54,698  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:54,896  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:54,900  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:54,901  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:57,234  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:57,236  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:57,239  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:57,244  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:57,245  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:57,248  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:57,249  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:57,448  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:57,863  [DEBUG] [ADC]Timer status: enab?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:57,866  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:57,868  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:57,872  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:57,875  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:57,878  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:57,878  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:58,077  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:58,080  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:45:58,081  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:45:58,493  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:45:58,495  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:45:58,498  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:45:58,501  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:45:58,503  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:45:58,507  [DEBUG] get_boot_mode 0
 
2025-07-16 22:45:58,507  [DEBUG] is_app_complete 0
 
2025-07-16 22:45:58,508  [DEBUG] Debug mode.

 
2025-07-16 22:46:08,509  [DEBUG] Cmd time out, try to boot app

 
2025-07-16 22:46:08,709  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:09,121  [DEBUG] [A*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:09,124  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:46:09,127  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:46:09,130  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:46:09,133  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:46:09,136  [DEBUG] get_boot_mode 0
 
2025-07-16 22:46:09,136  [DEBUG] is_app_complete 0
 
2025-07-16 22:46:09,335  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:11,277  [DEBUG] [AD*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:11,279  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:46:11,282  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:46:11,285  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:46:11,288  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:46:11,291  [DEBUG] get_boot_mode 0
 
2025-07-16 22:46:11,291  [DEBUG] is_app_complete 0
 
2025-07-16 22:46:11,490  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:11,906  [DEBUG] [ADC]Ti?*** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:11,909  [DEBUG] flash is 24bit address mode

 
2025-07-16 22:46:11,912  [DEBUG] SPI Flash init success, ID is 0xC84018

 
2025-07-16 22:46:11,915  [DEBUG] HW SW version: 5340 109

 
2025-07-16 22:46:11,918  [DEBUG] netcore sw 105, netboot sw 102
 
2025-07-16 22:46:11,921  [DEBUG] get_boot_mode 0
 
2025-07-16 22:46:11,921  [DEBUG] is_app_complete 0
 
2025-07-16 22:46:12,120  [DEBUG] *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
 
2025-07-16 22:46:12,122  [DEBUG] [ADC]Timer status: enabled

 
2025-07-16 22:46:12,124  [DEBUG] [ADC]init adc success.

 
2025-07-16 22:46:12,753  [DEBUG] para ret:306,valid:aa

 
2025-07-16 22:46:12,909  [DEBUG] [W][11:26:37][COMM]BKP RESET_MODE[a5a5], reason[0-0]
 
2025-07-16 22:46:12,913  [DEBUG] [E][11:26:37][COMM]RESETREAS:0x00000000
 
2025-07-16 22:46:12,918  [DEBUG] [E][11:26:37][COMM]Multirider mode not support: 255
 
2025-07-16 22:46:12,924  [DEBUG] [W][11:26:37][FCTY]BootVersion = SA_BOOT_V109
 
2025-07-16 22:46:12,929  [DEBUG] [W][11:26:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_668
 
2025-07-16 22:46:12,933  [DEBUG] [W][11:26:37][FCTY]BLEVersion = BLE_BE_105_102_100
 
2025-07-16 22:46:12,939  [DEBUG] [W][11:26:37][FCTY]DeviceID    = 460130020284403
 
2025-07-16 22:46:12,941  [DEBUG] [W][11:26:37][FCTY]HardwareID  = 868667086862221
 
2025-07-16 22:46:12,946  [DEBUG] [W][11:26:37][FCTY]MoBikeID    = 9999999999
 
2025-07-16 22:46:12,949  [DEBUG] [W][11:26:37][FCTY]LockID      = F050821689
 
2025-07-16 22:46:12,951  [DEBUG] [W][11:26:37][FCTY]BLEFWVersion= 105
 
2025-07-16 22:46:12,958  [DEBUG] [W][11:26:37][FCTY]BLEMacAddr   = CDC9905D6AEB
 
2025-07-16 22:46:12,960  [DEBUG] [W][11:26:37][FCTY]Bat         = 3844 mv
 
2025-07-16 22:46:12,963  [DEBUG] [W][11:26:37][FCTY]Current     = 150 ma
 
2025-07-16 22:46:12,969  [DEBUG] [W][11:26:37][FCTY]VBUS        = 4400 mv
 
2025-07-16 22:46:12,974  [DEBUG] [W][11:26:37][FCTY]TEMP= 25,BATID= 663732,BAT_TYPE = 0, BOARD_ID = 0xD1
 
2025-07-16 22:46:12,979  [DEBUG] [W][11:26:37][FCTY]Ext battery vol = 1, adc = 75
 
2025-07-16 22:46:12,982  [DEBUG] [W][11:26:37][FCTY]Bike Type flag is invalied
 
2025-07-16 22:46:12,985  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_BOOT =
 
2025-07-16 22:46:12,991  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_KERNEL =
 
2025-07-16 22:46:12,994  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_APP =
 
2025-07-16 22:46:12,997  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_GNSS =
 
2025-07-16 22:46:13,000  [DEBUG] [W][11:26:37][FCTY]CAT1_KERNEL_RTK =
 
2025-07-16 22:46:13,003  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_PLATFORM =
 
2025-07-16 22:46:13,007  [DEBUG] [W][11:26:37][FCTY]CAT1_GNSS_VERSION =
 
2025-07-16 22:46:13,010  [DEBUG] [W][11:26:37][GNSS]start sing locating
 
2025-07-16 22:46:13,369  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 22:46:13,774  [DEBUG] [E][11:26:38][COMM]1x1 rx timeout
 
2025-07-16 22:46:13,777  [DEBUG] [E][11:26:38][COMM]1x1 tp timeout
 
2025-07-16 22:46:13,780  [DEBUG] [E][11:26:38][COMM]1x1 error -3.
 
2025-07-16 22:46:13,784  [DEBUG] [W][11:26:38][COMM]Bat auth off fail, error:-1
 
2025-07-16 22:46:13,789  [DEBUG] [E][11:26:38][COMM][MC]exit stolen,get work mode err,rt:-3
 
2025-07-16 22:46:13,791  [DEBUG] [W][11:26:38][COMM]Init MC LOCK_STATE 2
 
2025-07-16 22:46:13,802  [DEBUG] [W][11:26:38][PROT]remove success[1730201198],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:46:13,809  [DEBUG] [W][11:26:38][PROT]add success [1730201198],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:46:14,995  [DEBUG] [W][11:26:39][PROT]remove success[1730201199],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:46:15,003  [DEBUG] [W][11:26:39][PROT]add success [1730201199],send_path[2],type[4B02],priority[0],index[1],used[1]
 
2025-07-16 22:46:27,183  [DEBUG] [W][11:26:52][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201212]
 
2025-07-16 22:46:34,163  [DEBUG] [W][11:26:59][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201219]
 
2025-07-16 22:46:39,563  [DEBUG] [W][11:27:04][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201224]
 
2025-07-16 22:46:44,967  [DEBUG] [W][11:27:09][PROT]SEND DATA TYPE:4B02, SENDPATH:0x2 [1730201229]
 
2025-07-16 22:47:36,846  [DEBUG] [W][11:28:01][PROT]remove success[1730201281],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:47:36,853  [DEBUG] [W][11:28:01][PROT]add success [1730201281],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:47:36,859  [DEBUG] [W][11:28:01][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201281]
 
2025-07-16 22:48:14,381  [DEBUG] [W][11:28:39][PROT]remove success[1730201319],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:48:14,389  [DEBUG] [W][11:28:39][PROT]add success [1730201319],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:48:14,394  [DEBUG] [W][11:28:39][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201319]
 
2025-07-16 22:48:30,847  [DEBUG] [W][11:28:55][GNSS][RTK]found position idx 36.
 
2025-07-16 22:48:31,301  [DEBUG] [E][11:28:56][COMM]1x1 rx timeout
 
2025-07-16 22:48:31,713  [DEBUG] [E][11:28:56][COMM]1x1 rx timeout
 
2025-07-16 22:48:31,716  [DEBUG] [E][11:28:56][COMM]1x1 tp timeout
 
2025-07-16 22:48:31,719  [DEBUG] [E][11:28:56][COMM]1x1 error -3.
 
2025-07-16 22:48:31,721  [DEBUG] [E][11:28:56][COMM]frm_mc_open_mos failed.
 
2025-07-16 22:48:31,730  [DEBUG] [W][11:28:56][PROT]remove success[1730201336],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:48:31,739  [DEBUG] [W][11:28:56][PROT]add success [1730201336],send_path[2],type[0306],priority[3],index[0],used[1]
 
2025-07-16 22:48:31,746  [DEBUG] [W][11:28:56][PROT]remove success[1730201336],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:48:31,755  [DEBUG] [W][11:28:56][PROT]add success [1730201336],send_path[2],type[6A01],priority[0],index[1],used[1]
 
2025-07-16 22:48:31,765  [DEBUG] [W][11:28:56][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201336]
 
2025-07-16 22:48:31,775  [DEBUG] [W][11:28:56][COMM]5A07 LocFail:reason:0x07;diff:11974;LocUsedTime:121;LocStatus|Type:3|000;HDOP:02;SatsView:14;SatsSNR35:00
 
2025-07-16 22:48:31,780  [DEBUG] [W][11:28:56][COMM]5A07 LocFail:GpsSpeed:00;alt:0063;lon:114340354   lat:23012019
 
2025-07-16 22:48:31,789  [DEBUG] [W][11:28:56][PROT]remove success[1730201336],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:48:31,797  [DEBUG] [W][11:28:56][PROT]add success [1730201336],send_path[2],type[5A07],priority[0],index[2],used[1]
 
2025-07-16 22:48:37,129  [DEBUG] [W][11:29:02][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201342]
 
2025-07-16 22:48:42,532  [DEBUG] [W][11:29:07][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201347]
 
2025-07-16 22:48:47,938  [DEBUG] [W][11:29:12][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201352]
 
2025-07-16 22:48:53,342  [DEBUG] [W][11:29:18][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201358]
 
2025-07-16 22:48:58,746  [DEBUG] [W][11:29:23][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201363]
 
2025-07-16 22:49:04,153  [DEBUG] [W][11:29:29][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201369]
 
2025-07-16 22:49:09,546  [DEBUG] [W][11:29:34][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201374]
 
2025-07-16 22:49:14,953  [DEBUG] [W][11:29:39][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201379]
 
2025-07-16 22:49:19,003  [DEBUG] [W][11:29:43][PROT]remove success[1730201383],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 22:49:19,011  [DEBUG] [W][11:29:43][PROT]add success [1730201383],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 22:49:20,359  [DEBUG] [W][11:29:45][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1730201385]
 
2025-07-16 22:49:25,779  [DEBUG] [W][11:29:50][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201390]
 
2025-07-16 22:49:31,005  [DEBUG] [W][11:29:55][GNSS]start sing locating
 
2025-07-16 22:49:31,013  [DEBUG] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:49:31,021  [DEBUG] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4205],priority[0],index[0],used[1]
 
2025-07-16 22:49:31,029  [DEBUG] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 22:49:31,038  [DEBUG] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4701],priority[0],index[4],used[1]
 
2025-07-16 22:49:31,046  [DEBUG] [W][11:29:55][PROT]remove success[1730201395],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 22:49:31,056  [DEBUG] [W][11:29:55][PROT]add success [1730201395],send_path[2],type[4705],priority[0],index[5],used[1]
 
2025-07-16 22:49:31,181  [DEBUG] [W][11:29:56][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201396]
 
2025-07-16 22:49:32,328  [DEBUG] [E][11:29:57][COMM]1x1 rx timeout
 
2025-07-16 22:49:32,731  [DEBUG] [E][11:29:57][COMM]1x1 rx timeout
 
2025-07-16 22:49:32,734  [DEBUG] [E][11:29:57][COMM]1x1 tp timeout
 
2025-07-16 22:49:32,737  [DEBUG] [E][11:29:57][COMM]1x1 error -3.
 
2025-07-16 22:49:32,740  [DEBUG] [E][11:29:57][COMM]frm_mc_open_mos failed.
 
2025-07-16 22:49:32,752  [DEBUG] [E][11:29:57][COMM]Fatal!!! missing comm with bat&CAN dev, set fatal code:0x17
 
2025-07-16 22:49:32,759  [DEBUG] [W][11:29:57][PROT]remove success[1730201397],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 22:49:32,767  [DEBUG] [W][11:29:57][PROT]add success [1730201397],send_path[2],type[5E01],priority[3],index[6],used[1]
 
2025-07-16 22:49:32,776  [DEBUG] [W][11:29:57][PROT]remove success[1730201397],send_path[2],type[0000],priority[0],index[7],used[0]
 
2025-07-16 22:49:32,785  [DEBUG] [W][11:29:57][PROT]add success [1730201397],send_path[2],type[C001],priority[0],index[7],used[1]
 
2025-07-16 22:49:33,639  [DEBUG] [W][11:29:58][PROT]remove success[1730201398],send_path[2],type[0000],priority[0],index[8],used[0]
 
2025-07-16 22:49:33,647  [DEBUG] [W][11:29:58][PROT]add success [1730201398],send_path[2],type[5006],priority[2],index[8],used[1]
 
2025-07-16 22:49:33,650  [DEBUG] [W][11:29:58][COMM]get soc error
 
2025-07-16 22:49:33,652  [DEBUG] [W][11:29:58][GNSS]stop locating
 
2025-07-16 22:49:33,655  [DEBUG] [W][11:29:58][GNSS]sing locating running
 
2025-07-16 22:49:33,660  [DEBUG] [E][11:29:58][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 22:49:33,669  [DEBUG] [W][11:29:58][PROT]remove success[1730201398],send_path[2],type[0000],priority[0],index[9],used[0]
 
2025-07-16 22:49:33,678  [DEBUG] [W][11:29:58][PROT]add success [1730201398],send_path[2],type[5D05],priority[3],index[9],used[1]
 
2025-07-16 22:49:33,689  [DEBUG] [W][11:29:58][PROT]remove success[1730201398],send_path[2],type[0000],priority[0],index[10],used[0]
 
2025-07-16 22:49:33,697  [DEBUG] [W][11:29:58][PROT]add success [1730201398],send_path[2],type[FF0E],priority[0],index[10],used[1]
 
2025-07-16 22:49:33,705  [DEBUG] [W][11:29:58][PROT]remove success[1730201398],send_path[2],type[0000],priority[0],index[11],used[0]
 
2025-07-16 22:49:33,713  [DEBUG] [W][11:29:58][PROT]add success [1730201398],send_path[2],type[C001],priority[0],index[11],used[1]
 
2025-07-16 22:49:33,723  [DEBUG] [W][11:29:58][PROT]remove success[1730201398],send_path[2],type[0000],priority[0],index[12],used[0]
 
2025-07-16 22:49:33,730  [DEBUG] [W][11:29:58][PROT]add success [1730201398],send_path[2],type[D302],priority[0],index[12],used[1]
 
2025-07-16 22:49:38,207  [DEBUG] [W][11:30:03][PROT]remove success[1730201403],send_path[2],type[0000],priority[0],index[13],used[0]
 
2025-07-16 22:49:38,214  [DEBUG] [W][11:30:03][PROT]add success [1730201403],send_path[2],type[5103],priority[0],index[13],used[1]
 
2025-07-16 22:49:38,697  [DEBUG] [W][11:30:03][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201403]
 
2025-07-16 22:49:44,104  [DEBUG] [W][11:30:08][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201408]
 
2025-07-16 22:49:49,504  [DEBUG] [W][11:30:14][PROT]SEND DATA TYPE:5E01, SENDPATH:0x2 [1730201414]
 
2025-07-16 22:49:54,921  [DEBUG] [W][11:30:19][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201419]
 
2025-07-16 22:50:00,324  [DEBUG] [W][11:30:25][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201425]
 
2025-07-16 22:50:05,728  [DEBUG] [W][11:30:30][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201430]
 
2025-07-16 22:50:11,142  [DEBUG] [W][11:30:36][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730201436]
 
2025-07-16 22:50:16,557  [DEBUG] [W][11:30:41][PROT]SEND DATA TYPE:6A01, SENDPATH:0x2 [1730201441]
 
2025-07-16 22:50:21,973  [DEBUG] [W][11:30:46][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730201446]
 
2025-07-16 22:50:22,072  [DEBUG] [W][11:30:46][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201446]
 
2025-07-16 22:50:22,154  [DEBUG] [W][11:30:47][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1730201447]
 
2025-07-16 22:50:27,570  [DEBUG] [W][11:30:52][PROT]SEND DATA TYPE:4701, SENDPATH:0x2 [1730201452]
 
2025-07-16 22:50:27,667  [DEBUG] [W][11:30:52][PROT]SEND DATA TYPE:4705, SENDPATH:0x2 [1730201452]
 
2025-07-16 22:50:27,984  [DEBUG] [W][11:30:52][PROT]remove success[1730201452],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:50:27,992  [DEBUG] [W][11:30:52][PROT]add success [1730201452],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:50:33,068  [DEBUG] [W][11:30:57][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201457]
 
2025-07-16 22:50:33,071  [DEBUG] [E][11:30:57][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:50:33,076  [DEBUG] [E][11:30:57][M2M ]m2m send data len err[-1,102]
 
2025-07-16 22:50:33,080  [DEBUG] [E][11:30:57][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:50:33,087  [DEBUG] [E][11:30:57][PROT]M2M Send Fail [1730201457]
 
2025-07-16 22:50:35,377  [DEBUG] [W][11:31:00][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730201460]
 
2025-07-16 22:50:35,473  [DEBUG] [W][11:31:00][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201460]
 
2025-07-16 22:50:35,569  [DEBUG] [W][11:31:00][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201460]
 
2025-07-16 22:50:40,961  [DEBUG] [W][11:31:05][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201465]
 
2025-07-16 22:50:46,367  [DEBUG] [W][11:31:11][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201471]
 
2025-07-16 22:50:51,771  [DEBUG] [W][11:31:16][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730201476]
 
2025-07-16 22:50:57,183  [DEBUG] [W][11:31:22][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201482]
 
2025-07-16 22:51:34,885  [DEBUG] [W][11:31:59][PROT]remove success[1730201519],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:51:34,893  [DEBUG] [W][11:31:59][PROT]add success [1730201519],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:51:34,898  [DEBUG] [W][11:31:59][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201519]
 
2025-07-16 22:52:15,831  [DEBUG] [W][11:32:40][PROT]remove success[1730201560],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:52:15,839  [DEBUG] [W][11:32:40][PROT]add success [1730201560],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:52:15,845  [DEBUG] [W][11:32:40][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201560]
 
2025-07-16 22:52:31,957  [DEBUG] [W][11:32:56][COMM]get bat state1 error
 
2025-07-16 22:52:31,960  [DEBUG] [W][11:32:56][COMM]get mc state information fail
 
2025-07-16 22:52:31,965  [DEBUG] [W][11:32:56][COMM]get mc speed information fail
 
2025-07-16 22:52:31,970  [DEBUG] [W][11:32:56][COMM]get rs485 Helmet information[fe] fail
 
2025-07-16 22:52:31,983  [DEBUG] [W][11:32:56][COMM]5F04 LocFail:reason:0x01;diff:41576;LocUsedTime:0;LocStatus|Type:3|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 22:52:31,987  [DEBUG] [W][11:32:56][COMM]5F04 LocFail:McSpeed:00;GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 22:52:31,992  [DEBUG] [W][11:32:56][COMM]get mc power mode information fail
 
2025-07-16 22:52:32,000  [DEBUG] [W][11:32:56][PROT]remove success[1730201576],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:52:32,009  [DEBUG] [W][11:32:56][PROT]add success [1730201576],send_path[2],type[5F04],priority[0],index[0],used[1]
 
2025-07-16 22:52:32,015  [DEBUG] [W][11:32:56][PROT]SEND DATA TYPE:5F04, SENDPATH:0x2 [1730201576]
 
2025-07-16 22:52:32,880  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:52:32,888  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5006],priority[2],index[0],used[1]
 
2025-07-16 22:52:32,891  [DEBUG] [W][11:32:57][COMM]get soc error
 
2025-07-16 22:52:32,894  [DEBUG] [W][11:32:57][GNSS]stop locating
 
2025-07-16 22:52:32,897  [DEBUG] [W][11:32:57][GNSS]sing locating running
 
2025-07-16 22:52:32,902  [DEBUG] [W][11:32:57][PROT]SEND DATA TYPE:5006, SENDPATH:0x2 [1730201577]
 
2025-07-16 22:52:32,908  [DEBUG] [E][11:32:57][COMM]Fatal!!! missing comm with Bat, set fatal code
 
2025-07-16 22:52:32,916  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[1],used[0]
 
2025-07-16 22:52:32,925  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5D05],priority[3],index[1],used[1]
 
2025-07-16 22:52:32,935  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[2],used[0]
 
2025-07-16 22:52:32,944  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[FF0E],priority[0],index[2],used[1]
 
2025-07-16 22:52:32,952  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[3],used[0]
 
2025-07-16 22:52:32,962  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[C001],priority[0],index[3],used[1]
 
2025-07-16 22:52:32,973  [DEBUG] [W][11:32:57][COMM]5A07 LocFail:reason:0x01;diff:41577;LocUsedTime:0;LocStatus|Type:3|000;HDOP:00;SatsView:00;SatsSNR35:00
 
2025-07-16 22:52:32,977  [DEBUG] [W][11:32:57][COMM]5A07 LocFail:GpsSpeed:00;alt:0000;lon:0   lat:0
 
2025-07-16 22:52:32,986  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[4],used[0]
 
2025-07-16 22:52:32,994  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[5A07],priority[0],index[4],used[1]
 
2025-07-16 22:52:33,002  [DEBUG] [W][11:32:57][PROT]remove success[1730201577],send_path[2],type[0000],priority[0],index[5],used[0]
 
2025-07-16 22:52:33,011  [DEBUG] [W][11:32:57][PROT]add success [1730201577],send_path[2],type[D302],priority[0],index[5],used[1]
 
2025-07-16 22:52:37,233  [DEBUG] [W][11:33:02][PROT]remove success[1730201582],send_path[2],type[0000],priority[0],index[6],used[0]
 
2025-07-16 22:52:37,241  [DEBUG] [W][11:33:02][PROT]add success [1730201582],send_path[2],type[5103],priority[0],index[6],used[1]
 
2025-07-16 22:52:38,284  [DEBUG] [W][11:33:03][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201583]
 
2025-07-16 22:52:41,369  [DEBUG] [W][11:33:06][PROT]remove success[1730201586],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:52:41,377  [DEBUG] [W][11:33:06][PROT]add success [1730201586],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:52:43,691  [DEBUG] [W][11:33:08][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201588]
 
2025-07-16 22:52:43,694  [DEBUG] [E][11:33:08][M2M ]tcpclient send_timeout: Invalid para
 
2025-07-16 22:52:43,699  [DEBUG] [E][11:33:08][M2M ]m2m send data len err[-1,134]
 
2025-07-16 22:52:43,703  [DEBUG] [E][11:33:08][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
 
2025-07-16 22:52:43,710  [DEBUG] [E][11:33:08][PROT]M2M Send Fail [1730201588]
 
2025-07-16 22:52:45,844  [DEBUG] [W][11:33:10][PROT]SEND DATA TYPE:5D05, SENDPATH:0x2 [1730201590]
 
2025-07-16 22:52:51,261  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:FF0E, SENDPATH:0x2 [1730201596]
 
2025-07-16 22:52:51,357  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:C001, SENDPATH:0x2 [1730201596]
 
2025-07-16 22:52:51,442  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:5A07, SENDPATH:0x2 [1730201596]
 
2025-07-16 22:52:51,526  [DEBUG] [W][11:33:16][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201596]
 
2025-07-16 22:52:56,921  [DEBUG] [W][11:33:21][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201601]
 
2025-07-16 22:53:02,327  [DEBUG] [W][11:33:27][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1730201607]
 
2025-07-16 22:53:07,745  [DEBUG] [W][11:33:32][PROT]SEND DATA TYPE:5103, SENDPATH:0x2 [1730201612]
 
2025-07-16 22:53:13,161  [DEBUG] [W][11:33:38][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201618]
 
2025-07-16 22:54:14,698  [DEBUG] [W][11:34:39][COMM]Power Off
 
2025-07-16 22:55:47,038  [DEBUG] [W][11:36:11][PROT]remove success[1730201771],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:55:47,046  [DEBUG] [W][11:36:11][PROT]add success [1730201771],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:55:47,051  [DEBUG] [W][11:36:11][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201771]
 
2025-07-16 22:56:27,935  [DEBUG] [W][11:36:52][PROT]remove success[1730201812],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:56:27,944  [DEBUG] [W][11:36:52][PROT]add success [1730201812],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:56:27,949  [DEBUG] [W][11:36:52][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201812]
 
2025-07-16 22:57:08,953  [DEBUG] [W][11:37:33][PROT]remove success[1730201853],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:57:08,962  [DEBUG] [W][11:37:33][PROT]add success [1730201853],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:57:08,967  [DEBUG] [W][11:37:33][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201853]
 
2025-07-16 22:57:49,877  [DEBUG] [W][11:38:14][PROT]remove success[1730201894],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:57:49,883  [DEBUG] [W][11:38:14][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201894]
 
2025-07-16 22:57:49,891  [DEBUG] [W][11:38:14][PROT]add success [1730201894],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:58:30,799  [DEBUG] [W][11:38:55][PROT]remove success[1730201935],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:58:30,807  [DEBUG] [W][11:38:55][PROT]add success [1730201935],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:58:30,812  [DEBUG] [W][11:38:55][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201935]
 
2025-07-16 22:59:11,815  [DEBUG] [W][11:39:36][PROT]remove success[1730201976],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:59:11,824  [DEBUG] [W][11:39:36][PROT]add success [1730201976],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:59:11,829  [DEBUG] [W][11:39:36][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730201976]
 
2025-07-16 22:59:52,743  [DEBUG] [W][11:40:17][PROT]remove success[1730202017],send_path[2],type[0000],priority[0],index[0],used[0]
 
2025-07-16 22:59:52,751  [DEBUG] [W][11:40:17][PROT]add success [1730202017],send_path[2],type[8301],priority[0],index[0],used[1]
 
2025-07-16 22:59:52,757  [DEBUG] [W][11:40:17][PROT]SEND DATA TYPE:8301, SENDPATH:0x2 [1730202017]
 
